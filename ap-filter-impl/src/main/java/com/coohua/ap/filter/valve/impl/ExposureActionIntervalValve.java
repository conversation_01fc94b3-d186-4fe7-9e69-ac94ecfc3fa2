package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdActionConstants;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.valves.DefaultAdValve;

import java.util.List;
import java.util.Map;

/**
 * 按广告类型的曝光控量
 * <AUTHOR> fan
 * @since : 2020.01.21
 */
public class ExposureActionIntervalValve extends DefaultAdValve {

    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            if (userMetaInfo.getCheckLimit()) {
                Map<String, String> exposureCache = getExposureCache(userMetaInfo.getUserId(), userMetaInfo.getApp());
                candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isExposureActionValid(exposureCache, userMetaInfo.getApp()));
            }
        }
    }


    private Map<String, String> getExposureCache(Long userId, App app) {
        String key = RedisBuilder.buildRedisAdActionTypeKey(userId,app.appId(), AdActionConstants.EXPOSURE);
        return SpringIoCManager.getAdFilterManager().getAdActionTypeNums(key);
    }
}
