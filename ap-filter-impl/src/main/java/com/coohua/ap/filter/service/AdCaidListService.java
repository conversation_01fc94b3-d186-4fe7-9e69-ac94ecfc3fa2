package com.coohua.ap.filter.service;

import com.coohua.ap.filter.valve.domain.MRate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.Set;

@Slf4j
@Service
public class AdCaidListService {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    @Autowired
    private MRate mRate;

    public static String AD_TEST_CAID = "ad:test:caid";

    @Scheduled(fixedDelay = 10 * 60 * 1000)
    public void setTestDeviceListByRedis(){
        try {
            Set<String> adTestList = apClusterRedisService.zrange(AD_TEST_CAID, 0, -1);
            if (adTestList == null) {
                log.warn("test.device.list加白的key不存在");
                return;
            }
            mRate.setTestDeviceList(adTestList);
            log.info("test.device.list加白刷新成功 {}", adTestList);
            if (adTestList.size() > 100) {
                Set<String> cleanList = apClusterRedisService.zrange(AD_TEST_CAID, 0, 49);
                if (!cleanList.isEmpty()) {
                    for (String element : cleanList)
                        apClusterRedisService.zrem(AD_TEST_CAID, element);
                }
            }
        } catch (Exception e) {
            log.error("test.device.list加白刷新失败 {}", e.toString());
        }

    }
}
