package com.coohua.ap.filter.manager;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.filter.valve.impl.*;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import com.coohua.ap.support.core.valves.chain.ValveChain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.jedis.exceptions.JedisMovedDataException;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2019-10-29
 */
@Slf4j
@Component
public class AdFilterManager {

    private ValveChain valveChain;

    @PostConstruct
    private void init() {
        valveChain = new DefaultValveChain();
        // 初始化过滤链
        valveChain.setFirst(new AppValve())
                .addValve(new AdPosValve())
                .addValve(new PlatformVersionValve())
                .addValve(new RegionValve())
                .addValve(new UserPackageValve())
                .addValve(new UserChannelValve())
                .addValve(new TailNumberValve())
                .addValve(new BrandValve())
                .addValve(new IncomeValve())
                .addValve(new RegisterTimeValve())
                .addValve(new RegisterLongerValve())
                .addValve(new ContainsPackageNameValve())
                .addValve(new TestAdAccountValve())
                .addValve(new NotContainsPackageNameValve())
                .addValve(new DailyExposureLimitValve())
                .addValve(new ExposureIntervalValve())
                .addValve(new DailyClickLimitValve())
                .addValve(new ClickIntervalValve())
                .addValve(new ExposureActionIntervalValve())
                .addValve(new RequestActionIntervalValve())
                .addValve(new RiskSkipKuaiShouValve())
                .addValve(new RiskSkipPlatformValve())
                .addValve(new StrategyIdValve());
    }


    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    public ValveChain getValveChain() {
        return this.valveChain;
    }


    public String buildRedisUserAdClickIntervalTimeStampKey(long adId, long userId, int appId) {
        return RedisConstants.USER_AD_CLICK_INTERVAL_TIMESTAMP_KEY_PRE + adId + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + getTag(userId);
    }

    public String buildRedisUserAdExposureIntervalTimeStampKey(long adId, long userId, int appId) {
        return RedisConstants.USER_AD_EXPOSURE_INTERVAL_TIMESTAMP_KEY_PRE + adId + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + getTag(userId);
    }

    public String buildRedisUserDayClickLimitKey(long adId, Long userId, int appId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.USER_AD_DAY_CLICK_LIMIT_KEY_PRE + adId + AdConstants.SYMBOL_COLON + getTag(userId) + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + today;
    }

    public String buildRedisUserDayExposureLimitKey(long adId, Long userId, int appId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.USER_AD_DAY_EXPOSURE_LIMIT_KEY_PRE + adId + AdConstants.SYMBOL_COLON + getTag(userId) + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + today;
    }

    public boolean isSkipKuaiShou(Long userId){
        try {
            String key = RedisBuilder.buildSkipKuaiShouUser(userId);
            String res = apClusterRedisService.get(key);
            if (StringUtils.isNotEmpty(res)){
                return Boolean.parseBoolean(res);
            }
        }catch (Exception e){
            log.error("Do Solve Error", e);
        }
        return false;
    }

    public List<Long> getHwSkipList(Integer appId,Long userId){
        try {
            String key = RedisBuilder.buildHuaweiSkipList(userId,appId);
            String res = apClusterRedisService.get(key);
            if (StringUtils.isNotEmpty(res)){
                return JSON.parseArray(res,Long.class);
            }
        }catch (Exception e){
            log.error("Do Solve Error", e);
        }
        return new ArrayList<>();
    }

    public Map<String, Response<String>> jedisClusterPipeline(long userId, List<String> keys) {
        try (Jedis jedis = apClusterRedisService.getResource(getTag(userId))) {
            Pipeline pipeline = jedis.pipelined();
            Map<String, Response<String>> ret = new HashMap<>();
            for (String key : keys) {
                ret.put(key, pipeline.get(key));
            }
            pipeline.sync();
            return ret;
        } catch (JedisMovedDataException jmde) {
            if (!CollectionUtils.isEmpty(keys)) {
                apClusterRedisService.get(keys.get(0));
            } else {
                apClusterRedisService.get("EmptyKey");
            }
        }
        return null;
    }

    private String getTag(Object userId) {
        return "{" + userId + "}";
    }

    public Map<String,String> getAdActionTypeNums(String key){
        return apClusterRedisService.hgetAll(key);
    }
}
