package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import redis.clients.jedis.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *  定向条件检索：用户广告日点击控量
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/16
 */
public class DailyClickLimitValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            if (userMetaInfo.getCheckLimit()) {
                Map<Long, Integer> allExpend = getAllExpend(candidates, userMetaInfo.getUserId(), userMetaInfo.getApp());
                candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isDailyClickLimitValid(allExpend));
            }
        }
    }

    private Map<Long, Integer> getAllExpend(List<Long> preList, Long userId, App app) {
        Map<Long, Integer> userDailyClick = new HashMap<>();

        List<String> keys = new ArrayList<>();
        Map<String, Long> userDayClickMap = new HashMap<>();

        for (Long adId : preList) {
            ThirdAd thirdAd = thirdAdContainer.getAd(adId);
            if (thirdAd == null) {
                continue;
            }

            if (thirdAd.getDayClickLimit() != AdConstants.AD_PUT_NO_DAY_CLICK_LIMIT && thirdAd.getDayClickLimit() > 0) {
                String userDayClickKey = SpringIoCManager.getAdFilterManager().buildRedisUserDayClickLimitKey(adId, userId, app.appId());
                userDayClickMap.put(userDayClickKey, adId);
                keys.add(userDayClickKey);
            }
        }

        if (keys.isEmpty()) {
            return userDailyClick;
        }

        Map<String, Response<String>> fromRedis = SpringIoCManager.getAdFilterManager().jedisClusterPipeline(userId, keys);

        if (fromRedis != null) {
            for (Map.Entry<String, Response<String>> entry : fromRedis.entrySet()) {
                String key = entry.getKey();
                Response<String> value = entry.getValue();
                userDailyClick.put(userDayClickMap.get(key), value != null ? Integer.parseInt(value.get()) : 0);
            }
        }

        return userDailyClick;
    }

}
