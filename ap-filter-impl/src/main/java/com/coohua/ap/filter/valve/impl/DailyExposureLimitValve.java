package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import redis.clients.jedis.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *  定向条件检索：用户广告日曝光控量
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/02/27
 */
public class DailyExposureLimitValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            if (userMetaInfo.getCheckLimit()) {
                Map<Long, Integer> allExpend = getAllExpend(candidates, userMetaInfo.getUserId(), userMetaInfo.getApp());
                candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isDailyExposureLimitValid(allExpend));
            }
        }
    }

    private Map<Long, Integer> getAllExpend(List<Long> preList, Long userId, App app) {
        Map<Long, Integer> userDailyExposure = new HashMap<>();

        List<String> keys = new ArrayList<>();
        Map<String, Long> userDayExposureMap = new HashMap<>();

        for (Long adId : preList) {
            ThirdAd thirdAd = thirdAdContainer.getAd(adId);
            if (thirdAd == null) {
                continue;
            }

            if (thirdAd.getDayExposureLimit() != AdConstants.AD_PUT_NO_DAY_EXPOSURE_LIMIT && thirdAd.getDayExposureLimit() > 0) {
                String userDayExposureKey = SpringIoCManager.getAdFilterManager().buildRedisUserDayExposureLimitKey(adId, userId, app.appId());
                userDayExposureMap.put(userDayExposureKey, adId);
                keys.add(userDayExposureKey);
            }
        }

        if (keys.isEmpty()) {
            return userDailyExposure;
        }

        Map<String, Response<String>> fromRedis = SpringIoCManager.getAdFilterManager().jedisClusterPipeline(userId, keys);

        if (fromRedis != null) {
            for (Map.Entry<String, Response<String>> entry : fromRedis.entrySet()) {
                String key = entry.getKey();
                Response<String> value = entry.getValue();
                userDailyExposure.put(userDayExposureMap.get(key), value != null && value.get() != null ? Integer.parseInt(value.get()) : 0);
            }
        }

        return userDailyExposure;
    }

}
