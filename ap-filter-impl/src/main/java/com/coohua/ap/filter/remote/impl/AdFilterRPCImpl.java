package com.coohua.ap.filter.remote.impl;

import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.dto.AdSearchDTO;
import com.coohua.ap.base.dto.AdSearchRequestDTO;
import com.coohua.ap.base.utils.Version;
import com.coohua.ap.filter.api.remote.api.AdFilterRPC;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.container.ThirdAdContainer;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.model.MLogModel;
import com.google.common.hash.HashCode;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import com.weibo.api.motan.config.springsupport.annotation.MotanService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019-10-29
 */
@MotanService(basicService = "ap-filterBasicServiceConfigBean")
@Service
@Slf4j
public class AdFilterRPCImpl implements AdFilterRPC {

    private static final List<Integer> adTypeList = new ArrayList<Integer>(){{
       add(AdType.TT_JU_HE_INSERT.type);
       add(AdType.TT_JU_HE_REWARD.type);
    }};

    public static ThreadLocal<Integer> holder = new ThreadLocal<>();
    public static ThreadLocal<Integer> randomHolder = new ThreadLocal<>();

    @Override
    public AdSearchDTO doSearch(AdSearchDTO adSearchDTO) {
        if (CollectionUtils.isEmpty(adSearchDTO.getAdRequest())) {
            return adSearchDTO;
        }

        ThirdAdContainer container = ContainerHolder.getThirdAdContainer();
        Map<Integer, List<Long>> validAdIdListCache = new HashMap<>();

        UserMetaForThirdAd userMetaForThirdAd = new UserMetaForThirdAd(adSearchDTO);
        Boolean isSkip = SpringIoCManager.getAdFilterManager().isSkipKuaiShou(userMetaForThirdAd.getUserId());
        userMetaForThirdAd.setIsSkipKuaiShou(isSkip);
        List<Long> needFilterAdList = new ArrayList<>();
        if (716 == adSearchDTO.getPersonas().getAppId()
                || 823 == adSearchDTO.getPersonas().getAppId()) {
            needFilterAdList = SpringIoCManager.getAdFilterManager()
                    .getHwSkipList(userMetaForThirdAd.getApp().appId(),userMetaForThirdAd.getUserId());
        }
        // 合并计算
        if (userMetaForThirdAd.getNeedSkipAdIdList() == null) {
            userMetaForThirdAd.setNeedSkipAdIdList(needFilterAdList);
        }else {
            List<Long> tempList = new ArrayList<>(userMetaForThirdAd.getNeedSkipAdIdList());
            tempList.addAll(needFilterAdList);
            userMetaForThirdAd.setNeedSkipAdIdList(tempList);
        }
        adSearchDTO.setConvertAdCache(Boolean.FALSE);
        for (AdSearchRequestDTO requestDTO : adSearchDTO.getAdRequest()) {
            List<Integer> adTypes = new ArrayList<>();
            adTypes.add(requestDTO.getAdType());
            adTypes.addAll(requestDTO.getDefaultAdType());
            adTypes.addAll(requestDTO.getAppDefaultAdType());
            adTypes.addAll(requestDTO.getAppDefaultGoldAdType());
            // 拉取全部时不进行M逻辑筛选..
            if (adSearchDTO.getPullAll() == null){
                adSearchDTO.setPullAll(Boolean.FALSE);
            }
            boolean isContainsAd = adTypes.stream().anyMatch(adTypeList::contains) && !adSearchDTO.getPullAll();
            boolean isVideo = adTypes.contains(AdType.TT_JU_HE_REWARD.type);
            Long adId = doAdSearch(requestDTO.getAdType(), userMetaForThirdAd, validAdIdListCache, container,isContainsAd,isVideo);
            requestDTO.setAdId(adId);
            // 检索广告中台打底广告
            List<Integer> defaultAdTypeList = requestDTO.getDefaultAdType();
            if (CollectionUtils.isNotEmpty(defaultAdTypeList)) {
                if (requestDTO.getDefaultAdId() == null) {
                    requestDTO.setDefaultAdId(new ArrayList<>());
                }
                for (int defaultAdType : defaultAdTypeList) {
                    Long defaultAdId = doAdSearch(defaultAdType, userMetaForThirdAd, validAdIdListCache, container,isContainsAd,isVideo);
                    requestDTO.getDefaultAdId().add(defaultAdId);
                    if (defaultAdId != 0) {
                        break;
                    }
                }
            }
            // 检索客户端非激励位置打底广告
            List<Integer> appDefaultAdTypeList = requestDTO.getAppDefaultAdType();
            if (CollectionUtils.isNotEmpty(appDefaultAdTypeList)) {
                if (requestDTO.getAppDefaultAdId() == null) {
                    requestDTO.setAppDefaultAdId(new ArrayList<>());
                }
                for (int defaultAdType : appDefaultAdTypeList) {
                    Long defaultAdId = doAdSearch(defaultAdType, userMetaForThirdAd, validAdIdListCache, container,isContainsAd,isVideo);
                    requestDTO.getAppDefaultAdId().add(defaultAdId);
                }
            }
            // 检索客户端激励位置打底广告
            List<Integer> appDefaultGoldAdTypeList = requestDTO.getAppDefaultGoldAdType();
            if (CollectionUtils.isNotEmpty(appDefaultGoldAdTypeList)) {
                if (requestDTO.getAppDefaultGoldAdId() == null) {
                    requestDTO.setAppDefaultGoldAdId(new ArrayList<>());
                }
                for (int defaultAdType : appDefaultGoldAdTypeList) {
                    Long defaultAdId = doAdSearch(defaultAdType, userMetaForThirdAd, validAdIdListCache, container,isContainsAd,isVideo);
                    requestDTO.getAppDefaultGoldAdId().add(defaultAdId);
                }
            }
            if (Integer.valueOf(1).equals(holder.get())){
                adSearchDTO.setConvertAdCache(Boolean.TRUE);
            }
        }

        holder.remove();
        randomHolder.remove();
        return adSearchDTO;
    }

    private Long doAdSearch(int adType, UserMetaForThirdAd userMetaForThirdAd, Map<Integer, List<Long>> validAdIdListCache, ThirdAdContainer container,Boolean isContainsReward,Boolean isVideo) {
        if (!container.isMyAdType(adType)) {
            return 0L;
        }


        boolean joinM = false;
        // 只对M的任务进行处理...
        if (isContainsReward) {
            int hashRate;
            // 分流请求开关 True 使用设备分流...
            Map<String,String> switchMap = SpringIoCManager.getSwitchMap();
            String upVersion = switchMap.get(String.valueOf(userMetaForThirdAd.getApp().appId()));
            if (StringUtils.isEmpty(upVersion)){
                // 默认采取HASH固定分流
                hashRate = hashRate(userMetaForThirdAd.getDeviceId());
            }else {
                hashRate = hashRate(userMetaForThirdAd.getDeviceId());
                if (Version.compare(upVersion,userMetaForThirdAd.getAppVersion()) <=0){
                    // 每次请求有10%的几率命中...
                    hashRate = randomRate();
                }
            }

            if (SpringIoCManager.getApp().contains(String.valueOf(userMetaForThirdAd.getApp().appId()))) {
                String key = userMetaForThirdAd.getApp().appId() + "_" + userMetaForThirdAd.getUserId() + "_";
                List<String> hitUserList = SpringIoCManager.getWitheUser().stream().filter(user -> user.startsWith(key)).collect(Collectors.toList());
                if (hitUserList.size() > 0){
                    try {
                        // 白名单用户命中
                        String keyUser = hitUserList.get(0);
                        int ret = Integer.parseInt(keyUser.split("_")[2]);
                        log.info("WhiteUser Hit,His DeviceId:{},HashRate:{}",userMetaForThirdAd.getDeviceId(),hashRate);
                        if (ret > hashRate){
                            if (!adTypeList.contains(adType)){
                                return 0L;
                            }
                            log.info("Whitelist UserId:{} join M,AdType:{}...",userMetaForThirdAd.getUserId(),adType);
                            joinM = true;
                        }else {
                            // 若不参与M 1065 广告剔除
                            if (adTypeList.contains(adType)){
                                return 0L;
                            }
                        }
                    }catch (Exception e){
                        log.warn("Solver Withe User Exception:",e);
                    }
                }else {
                    int ret = SpringIoCManager.getMRate();
                    if (isVideo){
                        Integer retTemp = SpringIoCManager.getMVideoRate().get(String.valueOf(userMetaForThirdAd.getApp().appId()));
                        if (retTemp!= null){
                            ret = retTemp;
                        }
                    }
                    // 若参与M 只保留1065
                    if (ret > hashRate){
                        if (!adTypeList.contains(adType)){
                            return 0L;
                        }
                        log.info("UserId:{} join M,AdType:{}...",userMetaForThirdAd.getUserId(),adType);
                        joinM = true;
                    }else {
                        // 若不参与M 1065 广告剔除
                        if (adTypeList.contains(adType)){
                            return 0L;
                        }
                    }
                }
            }
        }

        Integer joinTimes = holder.get();
        if (joinTimes == null){
            joinTimes = 0;
            holder.set(joinTimes);
        }

        if (validAdIdListCache.containsKey(adType)) {
            ThirdAd thirdAd = doAdRanking(validAdIdListCache.get(adType));
            joinM(joinM,container,thirdAd == null ? 0L : thirdAd.getAdId(),userMetaForThirdAd,joinTimes);
            return thirdAd == null ? 0L : thirdAd.getAdId();
        }

        List<Long> validAdIdList = container.getAdCollectionByAdType(adType,userMetaForThirdAd.getApp().appId());
        // 检查该类型是否已执行过检索逻辑，执行过则直接使用，否则执行指定type的检索
        SpringIoCManager.getAdFilterManager().getValveChain().getFirst().invoke(validAdIdList, userMetaForThirdAd);
        // 本次检索结果暂存
        validAdIdListCache.put(adType, validAdIdList);
        ThirdAd thirdAd = doAdRanking(validAdIdList);
        joinM(joinM,container,thirdAd == null ? 0L : thirdAd.getAdId(),userMetaForThirdAd,joinTimes);
        return thirdAd == null ? 0L : thirdAd.getAdId();
    }

    private ThirdAd doAdRanking(List<Long> validAdList) {
        if (CollectionUtils.isEmpty(validAdList)) {
            return null;
        } else if (validAdList.size() > 1) {//符合条件的多就从中挑一个
            return selectAdInfoDomain(validAdList);
        } else {//就一个就必须保证其能用
            return ContainerHolder.getThirdAdContainer().getAd(validAdList.get(0));
        }
    }

    private ThirdAd selectAdInfoDomain(List<Long> validAdList) {
        List<ThirdAd> thirdAds = new ArrayList<>();
        for (Long adId : validAdList) {
            ThirdAd target = ContainerHolder.getThirdAdContainer().getAd(adId);
            if (target == null) {
                continue;
            }
            thirdAds.add(target);
        }

        Collections.shuffle(thirdAds);
        ThirdAd thirdAd = null;

        long sum = 0;
        for (ThirdAd ad : thirdAds) {
            sum += ad.getBudgetValue();
        }

        int randomValue = new Double(Math.random() * sum).intValue() + 1; // +1： 修复边界值不可达的问题

        sum = 0;
        for (ThirdAd ad : thirdAds) {
            sum += ad.getBudgetValue();

            if (sum >= randomValue) {
                thirdAd = ad;
                break;
            }
        }

        return thirdAd;
    }

    private static int hashRate(String key) {
        HashFunction murmur3 = Hashing.murmur3_32();
        HashCode hashCode = murmur3.hashString(key, Charset.forName("UTF-8"));
        return Math.abs(hashCode.asInt()) % 100;
    }

    private static int randomRate(){
        Integer rate = randomHolder.get();
        if (rate == null) {
            Random random = new Random();
            rate = random.nextInt(100);
            randomHolder.set(rate);
        }
        return rate;
    }

    private void joinM(Boolean joinM,ThirdAdContainer thirdAdContainer,Long adId,UserMetaForThirdAd userMetaForThirdAd,Integer joinTimes){
        if (joinM && adId != 0 && joinTimes == 0){
            holder.set(1);
        }
    }

}
