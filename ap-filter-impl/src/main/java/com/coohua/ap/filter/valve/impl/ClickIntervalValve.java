package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *  定向条件检索：用户广告点击控频
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/16
 */
public class ClickIntervalValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            if (userMetaInfo.getCheckLimit()) {
                Map<Long, Long> allAdInterval = getAllAdInterval(candidates, userMetaInfo.getUserId(), userMetaInfo.getApp());
                candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isClickInvervalValid(allAdInterval));
            }
        }
    }

    private Map<Long, Long> getAllAdInterval(List<Long> preList, Long userId, App app) {
        Map<Long, Long> ret = new HashMap<>();

        Map<String, Long> userClickAdIntervalMap = new HashMap<>();
        List<String> keys = new ArrayList<>();
        for (Long adId : preList) {
            ThirdAd thirdAd = thirdAdContainer.getAd(adId);
            if (thirdAd == null) {
                continue;
            }

            if (thirdAd.getClickInterval() != AdConstants.AD_PUT_NO_CLICK_INTERVAL_LIMIT && thirdAd.getClickInterval() > 0) {
                String userClickAdIntervalKey = SpringIoCManager.getAdFilterManager().buildRedisUserAdClickIntervalTimeStampKey(adId, userId, app.appId());
                userClickAdIntervalMap.put(userClickAdIntervalKey, adId);
                keys.add(userClickAdIntervalKey);
            }
        }

        if (keys.isEmpty()) {
            return ret;
        }

        Map<String, Response<String>> fromRedis = SpringIoCManager.getAdFilterManager().jedisClusterPipeline(userId, keys);

        if (fromRedis != null) {
            for (Map.Entry<String, Response<String>> entry : fromRedis.entrySet()) {
                String key = entry.getKey();
                Response<String> value = entry.getValue();
                ret.put(userClickAdIntervalMap.get(key), value != null&& StringUtils.isNotBlank(value.get())? Long.parseLong(value.get()) : 0L);
            }
        }

        return ret;
    }
}
