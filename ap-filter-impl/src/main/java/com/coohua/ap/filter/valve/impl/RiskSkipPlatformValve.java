package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.valves.DefaultAdValve;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/9
 */
public class RiskSkipPlatformValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userInformation;
            if (userMetaInfo.getNeedSkipPlatform()){
                candidates.removeIf(adId -> thirdAdContainer.getAd(adId).isSkipPlatform(userMetaInfo.getSkipPlatform()));
            }
            candidates.removeIf(adId -> thirdAdContainer.getAd(adId).skipAdSpecial(
                    userMetaInfo.getActiveChannel(),
                    SpringIoCManager.isSkipAd(adId),1
            ));

            candidates.removeIf(adId -> thirdAdContainer.getAd(adId).skipAdSpecial(
                    userMetaInfo.getActiveChannel(),
                    SpringIoCManager.isSkipAdKs(adId),2
            ));
//            candidates.removeIf(adId -> thirdAdContainer.getAd(adId).skipModelXiaoMi(userMetaInfo.getBrand()));\
            // 小米华为跳过不看
            candidates.removeIf(adId -> thirdAdContainer.getAd(adId).skipModelMiAndHua(
                    userMetaInfo.getBrand(),
                    SpringIoCManager.isSkipAdModel(adId)
            ));

            // 正常广告过滤
            if (userMetaInfo.getNeedSkipAdIdList() != null) {
                candidates.removeIf(adId -> userMetaInfo.getNeedSkipAdIdList().contains(adId));
            }

        }
    }
}
