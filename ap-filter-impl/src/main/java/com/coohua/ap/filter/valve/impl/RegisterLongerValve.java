package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.valves.DefaultAdValve;

import java.util.List;

/**
 * <pre>
 * 定向条件检索：注册时长
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/16
 */
public class RegisterLongerValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isRegisterLongerValid(userMetaInfo.getRegisterTime()));
        }
    }
}
