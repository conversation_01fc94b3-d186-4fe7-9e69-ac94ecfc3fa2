package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <pre>
 *  定向条件检索：机型
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/02
 */
public class BrandValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isBrandValid(userMetaInfo.getBrand()));
        }
    }
}
