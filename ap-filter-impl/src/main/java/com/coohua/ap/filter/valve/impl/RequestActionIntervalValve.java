package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdActionConstants;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.valves.DefaultAdValve;

import java.util.List;
import java.util.Map;

/**
 * 按广告类型的请求控量
 * <AUTHOR> fan
 * @since  2020.01.21
 */
public class RequestActionIntervalValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            if (userMetaInfo.getCheckLimit()) {
                Map<String, String> requestCache = getRequestCache(userMetaInfo.getUserId(), userMetaInfo.getApp());
                candidates.removeIf(adId -> !thirdAdContainer.getAd(adId).isRequestActionValid(requestCache, userMetaInfo.getApp()));
            }
        }
    }

    private Map<String, String> getRequestCache(Long userId, App app) {
        String key = RedisBuilder.buildRedisAdActionTypeKey(userId,app.appId(), AdActionConstants.REQUEST);
        return SpringIoCManager.getAdFilterManager().getAdActionTypeNums(key);
    }
}
