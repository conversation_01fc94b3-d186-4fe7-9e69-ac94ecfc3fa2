package com.coohua.ap.filter;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-10-29
 */
@SpringBootApplication(scanBasePackages = "com.coohua.ap")
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
        "ap.filter.rpc.service",
        "ap.redis.cluster"}) // "ap.redis.single",

@EnableAutoChangeApolloConfig
@EnableMotan(namespace = "ap-filter")

//@EnableJedisClient(namespace = "ap-redis-single")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableScheduling

@Slf4j
public class ApFilterApplication {
    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        SpringApplication.run(ApFilterApplication.class, args);
        log.info("Ap filter application started in {} ms", (System.currentTimeMillis() - start));
    }
}
