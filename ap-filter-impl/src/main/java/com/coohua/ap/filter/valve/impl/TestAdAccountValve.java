package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <pre>
 *  定向条件检索：测试广告只返回给配置设备号
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/16
 */
@Slf4j
public class TestAdAccountValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userMetaForThirdAd;
            if (OSType.ANDROID == userMetaInfo.getOsType()) {
                return;
            }
            Set<Long> testAds = candidates.stream()
                    .filter(adId -> {
                        ThirdAd thirdAd = thirdAdContainer.getAd(adId);
                        if (thirdAd == null || Strings.isEmpty(thirdAd.getName())){
                            return false;
                        }
                        return thirdAd.getName().contains("测试")
                                && !SpringIoCManager.isTestCaid(userMetaInfo.getCaid());

                    })
                    .collect(Collectors.toSet());
            if (testAds.size() > 0) {
                log.info("跳过测试广告 {} {} {}", userMetaInfo.getUserId(), testAds.size(),userMetaInfo.getCaid());
            }
         //   candidates.removeIf(adId -> thirdAdContainer.getAd(adId).getName().contains("测试") && !SpringIoCManager.isTestCaid(userMetaInfo.getCaid()));
           candidates.removeIf(adId -> testAds.contains(adId));
        }
    }
}
