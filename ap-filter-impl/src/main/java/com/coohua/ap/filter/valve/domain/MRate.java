package com.coohua.ap.filter.valve.domain;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2021/3/31
 */
@Data
@Configuration
public class MRate {
    @Value("${m.rate}")
    private Integer mRate;

    @Value("#{${m.rate.video.map}}")
    private Map<String,Integer> mVideoRateMap;

    @Value("${m.withe.user}")
    private String witherUser;

    @Value("${m.join.app}")
    private String joinApp;

    @Value("#{${m.join.switch.map}}")
    private Map<String,String> switchMap;

    @ApolloJsonValue("${skip.ad.list:[169821,170749,184376,190182,186719,185713,185575,190177,169984,182803,183632,169984,185728]}")
    private List<Long> skipAdId;


    @ApolloJsonValue("${skip.ad.ks.list:[428114]}")
    private List<Long> skipKsAdId;

    @ApolloJsonValue("${skip.ad.model.list:[169821,168573,183260,185728]}")
    private List<Long> skipAdIdModel;

    //@ApolloJsonValue("${test.device.list:[]}")
    private Set<String> testDeviceList;
}
