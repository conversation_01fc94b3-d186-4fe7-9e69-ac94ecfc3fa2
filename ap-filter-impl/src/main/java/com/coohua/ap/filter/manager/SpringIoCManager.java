package com.coohua.ap.filter.manager;

import com.coohua.ap.filter.valve.domain.MRate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/16
 */
@Component
@Slf4j
public class SpringIoCManager implements ApplicationContextAware {
    private static ApplicationContext cpx = null;

    // 获取已经创建的spring容器
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringIoCManager.cpx = applicationContext;
    }

    private static <T>T getBean(Class<T> beanType) {
        return cpx.getBean(beanType);
    }

    public static AdFilterManager getAdFilterManager() {
        return getBean(AdFilterManager.class);
    }

    public static int getMRate(){
        return getBean(MRate.class).getMRate();
    }

    public static Map<String,Integer> getMVideoRate(){
        return getBean(MRate.class).getMVideoRateMap();
    }

    public static List<String> getWitheUser(){
        MRate mRate = getBean(MRate.class);
        if (StringUtils.isEmpty(mRate.getWitherUser())){
            return new ArrayList<>();
        }
        return Arrays.asList(mRate.getWitherUser().split(","));
    }

    public static List<String> getApp(){
        MRate mRate = getBean(MRate.class);
        if (StringUtils.isEmpty(mRate.getJoinApp())){
            return new ArrayList<>();
        }
        return Arrays.asList(mRate.getJoinApp().split(","));
    }

    public static Map<String,String> getSwitchMap(){
        return getBean(MRate.class).getSwitchMap();
    }

    public static Boolean isSkipAd(Long adId){
        return getBean(MRate.class).getSkipAdId().contains(adId);
    }

    public static Boolean isSkipAdKs(Long adId){
        return getBean(MRate.class).getSkipKsAdId().contains(adId);
    }

    public static Boolean isSkipAdModel(Long adId){
        return getBean(MRate.class).getSkipAdIdModel().contains(adId);
    }

    public static Boolean isTestCaid(String caid){
        return StringUtils.isNotBlank(caid) && getBean(MRate.class).getTestDeviceList().contains(caid);
    }

}
