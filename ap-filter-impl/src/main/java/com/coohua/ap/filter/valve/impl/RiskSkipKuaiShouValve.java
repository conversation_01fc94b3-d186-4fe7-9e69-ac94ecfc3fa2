package com.coohua.ap.filter.valve.impl;

import com.coohua.ap.filter.manager.SpringIoCManager;
import com.coohua.ap.filter.valve.domain.UserMetaForThirdAd;
import com.coohua.ap.support.core.container.ThirdAdContainer;
import com.coohua.ap.support.core.valves.DefaultAdValve;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/5
 */
public class RiskSkipKuaiShouValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForThirdAd) {
            UserMetaForThirdAd userMetaInfo = (UserMetaForThirdAd) userInformation;
            if (userMetaInfo.getIsSkipKuaiShou()){
                candidates.removeIf(adId -> thirdAdContainer.getAd(adId).isKuaiShouAd());
            }
        }
    }

}
