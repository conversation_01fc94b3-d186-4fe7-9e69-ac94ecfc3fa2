package com.coohua.ap.filter.valve.domain;

import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.base.constants.Province;
import com.coohua.ap.base.dto.AdSearchDTO;
import com.coohua.ap.base.dto.AdSearchPersonasDTO;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <pre>
 *  用户信息 for 第三方广告检索
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/14
 */
@Data
public class UserMetaForThirdAd {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户所属应用
     * @see App
     */
    private App app;
    /**
     * 用户本次检索广告位
     */
    private Integer posId;
    /**
     * 用户平台
     * @see OSType
     */
    private OSType osType;
    /**
     * 用户客户端版本号
     */
    private String appVersion;
    /**
     * 用户所属省份
     * @see Province
     */
    private Province province;
    /**
     * 是否受限区
     * true - 是，false - 否
     */
    private boolean filterRegion;
    /**
     * 用户总收入（积分：RMB单位：分）
     */
    private Integer income;
    /**
     * 用户注册时间
     */
    private Date registerTime;
    /**
     * 用户手机当前安装的所有应用的包名列表
     */
    private Set<String> pkgNames;
    /**
     * 当前JS域名
     */
    private String jsDomain;
    /**
     * 用户客户端包名
     */
    private String userPkg;
    /**
     * 用户渠道名
     */
    private String userChannel;
    /**
     * 用户机型
     */
    private String brand;

    private Long strategyId;

    private Long categoryId;

    private Boolean checkLimit;

    // 设备号
    private String deviceId;
    private String caid;

    // 是否跳过快手
    private Boolean isSkipKuaiShou;

    // 是否需要跳过某个平台
    private Boolean needSkipPlatform;

    // 跳过的平台
    private Integer skipPlatform;

    private Integer activeChannel;

    private Boolean isLockedArea;

    // 需要过滤的 ADID
    private List<Long> needSkipAdIdList;

    public UserMetaForThirdAd() {
    }

    public UserMetaForThirdAd(AdSearchDTO adSearchDTO) {
        transferFromAdSearch(adSearchDTO);
    }

    private void transferFromAdSearch(AdSearchDTO adSearchDTO) {
        AdSearchPersonasDTO personas = adSearchDTO.getPersonas();
        this.posId = adSearchDTO.getPosId();
        this.userId = personas.getUserId();
        this.app = AppBuilder.getById(personas.getAppId());
        this.osType = OSType.find(personas.getOs());
        this.appVersion = personas.getVersion();
        this.province = Province.find(personas.getProvinceCode());
        this.filterRegion = personas.getFilterRegion();
        this.income = personas.getIncome();
        this.registerTime = personas.getRegisterTime();
        this.pkgNames = personas.getInstalledAppList();
        this.userPkg = personas.getUserApkName();
        this.userChannel = personas.getUserChannel();
        this.brand = personas.getPhoneBrand();
        this.strategyId = adSearchDTO.getStrategyId();
        this.categoryId = adSearchDTO.getCategoryId();
        this.checkLimit = adSearchDTO.getCheckLimit();
        this.deviceId = personas.getDeviceId();
        this.needSkipPlatform = personas.getNtfFilter();
        this.skipPlatform = personas.getSkipPlatform();
        this.activeChannel = personas.getActiveChannel();
        this.isLockedArea = personas.getIsLockedArea();
        this.needSkipAdIdList = personas.getSkipAdIdList();
        this.caid = personas.getCaid();
    }

}
