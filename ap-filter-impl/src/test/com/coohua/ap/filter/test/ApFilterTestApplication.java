package com.coohua.ap.filter.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.base.dto.AdSearchDTO;
import com.coohua.ap.base.dto.AdSearchPersonasDTO;
import com.coohua.ap.base.dto.AdSearchRequestDTO;
import com.coohua.ap.filter.api.remote.api.AdFilterRPC;
import com.coohua.ap.support.core.domain.FilteredAdDefaultTypeStrategyConfig;
import com.coohua.ap.support.core.domain.FilteredAdTypeStrategyConfig;
import com.coohua.ap.support.core.domain.FilteredStrategyConfig;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.filter
 * @create_time 2019-10-29
 */
@SpringBootApplication(scanBasePackages = "com.coohua.ap")
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
        "ap.filter.rpc.service",
        "ap.redis.cluster", // "ap.redis.single",
        "ad.nap.datasource"})

@EnableAutoChangeApolloConfig
//@EnableMotan(namespace = "ap-filter")

//@EnableJedisClient(namespace = "ap-redis-single")
@EnableJedisClusterClient(namespace = "ap-cluster")

@EnableDataSource(namespace = "napdb", mapperPackages = "com.coohua.ap.filter.mapper")

@Slf4j
public class ApFilterTestApplication implements ApplicationContextAware {

    private static ApplicationContext cpx = null;

    // 获取已经创建的spring容器
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApFilterTestApplication.cpx = applicationContext;
    }

    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        SpringApplication.run(ApFilterTestApplication.class, args);
        log.info("Ap filter application started in {} ms", (System.currentTimeMillis() - start));

        AdFilterRPC adFilterRPC = cpx.getBean(AdFilterRPC.class);
        String json = "{\"adRequest\":[{\"adId\":0,\"adType\":1014,\"appDefaultAdId\":[],\"appDefaultAdType\":[1057],\"appDefaultGoldAdId\":[],\"appDefaultGoldAdType\":[1057],\"defaultAdId\":[],\"defaultAdType\":[1057]}],\"checkLimit\":false,\"personas\":{\"age\":0,\"anonymous\":1,\"appId\":1262,\"businessNewUser\":0,\"deviceId\":\"1519c9473175f8f6\",\"filterRegion\":false,\"gender\":0,\"income\":0,\"installedAppList\":[],\"internet\":0,\"isLockedArea\":false,\"ntfFilter\":false,\"os\":\"android\",\"phoneBrand\":\"redmi\",\"provinceCode\":\"11\",\"registerTime\":1719039450106,\"userApkName\":\"com.daluositt.hhhxinyi\",\"userChannel\":\"ttdlshonor\",\"userId\":1936181885,\"version\":\"1.0.1\"},\"posId\":1004397,\"pullAll\":false,\"strategyId\":12836}";
        //TODO test
        json = "";
        json = "{\"adRequest\":[{\"adId\":0,\"adType\":10151,\"appDefaultAdId\":[],\"appDefaultAdType\":[1083101,10081,10181,10152,1083102,10082,10182,10153,1083103,10083,10183,10154,1083104,10084,10184,10155,1083105,10085,10185,10156,1083106,10086,10186,10157,1083107,10087,10187,10158,1083108,10088,10188,10159,1083109,10089,10189,101510,1083110,100810,101810,10991,101511,1083111,100811,101811,101512,1083112,100812,101812,10992,101513,1083113,100813,101813,10993,101514,1083114,100814,101814,10994,101515,1083115,100815,101815,10995,101516,1083116,100816,101816,10996,10941,101517,1083117,100817,101817,101518,1083118,100818,101818,10997,101519,1083119,100819,101819,101520,1083120,100820,101820,101521,1083121,100821,101821,101522,1083122,100822,101822,101523,1083123,100823,101823,101524,1083124,100824,101824,10998,101525,1083125,100825,101825,101526,1083126,100826,101826,10942,101527,1083127,100827,101827,101528,1083128,100828,101828,101529,1083129,100829,101829,101530,1083130,100830,101830,10999,101531,1083131,100831,101831,101532,1083132,100832,101832,10943,101533,1083133,100833,101833,101534,1083134,100834,101834,109910,101535,1083135,100835,101835,101536,1083136,100836,101836,10944,101537,1083137,100837,101837,101538,1083138,100838,101838,109911,101539,1083139,100839,101839,101540,1083140,100840,101840,1015,1083141,1008,1018],\"appDefaultGoldAdId\":[],\"appDefaultGoldAdType\":[1083101,10081,10181,10152,1083102,10082,10182,10153,1083103,10083,10183,10154,1083104,10084,10184,10155,1083105,10085,10185,10156,1083106,10086,10186,10157,1083107,10087,10187,10158,1083108,10088,10188,10159,1083109,10089,10189,101510,1083110,100810,101810,10991,101511,1083111,100811,101811,101512,1083112,100812,101812,10992,101513,1083113,100813,101813,10993,101514,1083114,100814,101814,10994,101515,1083115,100815,101815,10995,101516,1083116,100816,101816,10996,10941,101517,1083117,100817,101817,101518,1083118,100818,101818,10997,101519,1083119,100819,101819,101520,1083120,100820,101820,101521,1083121,100821,101821,101522,1083122,100822,101822,101523,1083123,100823,101823,101524,1083124,100824,101824,10998,101525,1083125,100825,101825,101526,1083126,100826,101826,10942,101527,1083127,100827,101827,101528,1083128,100828,101828,101529,1083129,100829,101829,101530,1083130,100830,101830,10999,101531,1083131,100831,101831,101532,1083132,100832,101832,10943,101533,1083133,100833,101833,101534,1083134,100834,101834,109910,101535,1083135,100835,101835,101536,1083136,100836,101836,10944,101537,1083137,100837,101837,101538,1083138,100838,101838,109911,101539,1083139,100839,101839,101540,1083140,100840,101840,1015,1083141,1008,1018],\"defaultAdId\":[],\"defaultAdType\":[1083101,10081,10181,10152,1083102,10082,10182,10153,1083103,10083,10183,10154,1083104,10084,10184,10155,1083105,10085,10185,10156,1083106,10086,10186,10157,1083107,10087,10187,10158,1083108,10088,10188,10159,1083109,10089,10189,101510,1083110,100810,101810,10991,101511,1083111,100811,101811,101512,1083112,100812,101812,10992,101513,1083113,100813,101813,10993,101514,1083114,100814,101814,10994,101515,1083115,100815,101815,10995,101516,1083116,100816,101816,10996,10941,101517,1083117,100817,101817,101518,1083118,100818,101818,10997,101519,1083119,100819,101819,101520,1083120,100820,101820,101521,1083121,100821,101821,101522,1083122,100822,101822,101523,1083123,100823,101823,101524,1083124,100824,101824,10998,101525,1083125,100825,101825,101526,1083126,100826,101826,10942,101527,1083127,100827,101827,101528,1083128,100828,101828,101529,1083129,100829,101829,101530,1083130,100830,101830,10999,101531,1083131,100831,101831,101532,1083132,100832,101832,10943,101533,1083133,100833,101833,101534,1083134,100834,101834,109910,101535,1083135,100835,101835,101536,1083136,100836,101836,10944,101537,1083137,100837,101837,101538,1083138,100838,101838,109911,101539,1083139,100839,101839,101540,1083140,100840,101840,1015,1083141,1008,1018]}],\"categoryId\":19641,\"checkLimit\":false,\"personas\":{\"age\":0,\"anonymous\":2,\"appId\":1415,\"businessNewUser\":0,\"deviceId\":\"C23F3F6E5923435E94A6AA9510744AEE6c8c8dd70f50bb6e33c32823b6dfb127\",\"filterRegion\":false,\"gender\":0,\"income\":3500,\"installedAppList\":[],\"internet\":0,\"isLockedArea\":false,\"ntfFilter\":false,\"os\":\"android\",\"phoneBrand\":\"oppo\",\"provinceCode\":\"11\",\"registerTime\":1745473233371,\"userApkName\":\"com.jygoodtime.alz\",\"userChannel\":\"jysgoppo\",\"userId\":2214938295,\"version\":\"1.0.4\"},\"posId\":1005169,\"pullAll\":false,\"strategyId\":19641}";
        AdSearchDTO adSearchDTO = null;
        adSearchDTO = JSONObject.parseObject(json, AdSearchDTO.class);
        AdSearchDTO result = adFilterRPC.doSearch(adSearchDTO);
        System.out.println(JSON.toJSONString(result));
        System.out.println("over");
    }

    public static AdSearchDTO transferFromStrategyConfig(int posId, int appId, FilteredStrategyConfig filteredStrategyConfig) {
        AdSearchDTO adSearchDTO = new AdSearchDTO();
        adSearchDTO.setPosId(posId);
        AdSearchPersonasDTO personasDTO = new AdSearchPersonasDTO();
        adSearchDTO.setPersonas(personasDTO);
        List<AdSearchRequestDTO> requestDTOS = new ArrayList<>();
        adSearchDTO.setAdRequest(requestDTOS);

        personasDTO.setUserId(12345678L);
        personasDTO.setOs(OSType.find("android").getValue());
        personasDTO.setVersion("1.0.1.5");
        personasDTO.setProvinceCode("11");
        personasDTO.setFilterRegion(false);
        personasDTO.setIncome(100000);
        // 注册时间
        Date register = new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24 * 10L);
        personasDTO.setRegisterTime(register);

        personasDTO.setAppId(appId);
        personasDTO.setInstalledAppList(new HashSet<>());
        personasDTO.setUserApkName("com.xubai.taojinhao");
        personasDTO.setUserChannel("HD");
        personasDTO.setAnonymous(2);
        personasDTO.setAge(1);
        personasDTO.setGender(2);
        personasDTO.setInternet(1);
        personasDTO.setBusinessNewUser(1);
        personasDTO.setPhoneBrand("HUAWEI");

        Map<Integer, List<List<Integer>>> defaultAdTypeMap = getDefaultAdTypeMap(filteredStrategyConfig.getFilteredAdDefaultTypeConfig());
        Map<Integer, List<List<Integer>>> defaultAppAdTypeMap = getDefaultAdTypeMap(filteredStrategyConfig.getFilteredAppDefaultTypeConfig());
        Map<Integer, List<List<Integer>>> defaultAppAdTypeGoldMap = getDefaultAdTypeMap(filteredStrategyConfig.getFilteredAppDefaultTypeGoldConfig());

        List<FilteredAdTypeStrategyConfig> filteredAdTypeStrategyConfigs = filteredStrategyConfig.getFilteredAdTypeConfig();

        for (FilteredAdTypeStrategyConfig configDomain : filteredAdTypeStrategyConfigs) {
            int repeat = configDomain.getRepeat();
            List<Integer> adTypeList = configDomain.getAdTypeList();

            for (int i = 0; i < repeat; i++) {
                AdSearchRequestDTO requestDTO = new AdSearchRequestDTO();
                int targetType = adTypeList.get(ThreadLocalRandom.current().nextInt(adTypeList.size()));
                requestDTO.setAdType(targetType);
                List<Integer> adTypeLayer = getAdTypeLayers(defaultAdTypeMap.get(targetType));
                List<Integer> appAdTypeLayer = getAdTypeLayers(defaultAppAdTypeMap.get(targetType));
                List<Integer> appAdTypeGoldLayer = getAdTypeLayers(defaultAppAdTypeGoldMap.get(targetType));
                requestDTO.setDefaultAdType(adTypeLayer);
                requestDTO.setAppDefaultAdType(appAdTypeLayer);
                requestDTO.setAppDefaultGoldAdType(appAdTypeGoldLayer);
                requestDTOS.add(requestDTO);
            }
        }
        return adSearchDTO;
    }

    private static Map<Integer, List<List<Integer>>> getDefaultAdTypeMap(List<FilteredAdDefaultTypeStrategyConfig> defaultTypeConfig) {
        Map<Integer, List<List<Integer>>> ret = new HashMap<>();
        if (defaultTypeConfig == null) {
            return ret;
        }
        for (FilteredAdDefaultTypeStrategyConfig config : defaultTypeConfig) {
            ret.put(config.getTargetType(), config.getLayer());
        }
        return ret;
    }

    private static List<Integer> getAdTypeLayers(List<List<Integer>> layer) {
        List<Integer> ret = new ArrayList<>();
        if (CollectionUtils.isEmpty(layer)) {
            return ret;
        }
        for (List<Integer> list : layer) {
            ret.add(list.get(ThreadLocalRandom.current().nextInt(list.size())));
        }

        return ret;
    }
}
