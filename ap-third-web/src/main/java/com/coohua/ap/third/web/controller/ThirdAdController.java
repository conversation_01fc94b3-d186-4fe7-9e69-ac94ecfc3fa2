package com.coohua.ap.third.web.controller;

import com.coohua.ap.base.vo.WebMessage;
import com.coohua.ap.third.web.config.CommonConfig;
import com.coohua.tap.remote.constant.ProdLineEnum;
import com.coohua.tap.remote.dto.AppClientUserInfoDto;
import com.coohua.tap.remote.dto.ThirdAdDto;
import com.coohua.tap.remote.service.ThirdAdRpcService;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: jinyang fan
 * @date: 2020.02.10
 * @package: com.coohua.ap.third.web.controller
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/ap-third/3rd")
public class ThirdAdController {

    @MotanReferer(basicReferer = "tap-newBasicRefererConfigBean")
    private ThirdAdRpcService thirdAdRpcService;

    @Autowired
    private CommonConfig commonConfig;

    @RequestMapping("/getThirdAd")
    public WebMessage<ThirdAdDto> getThirdAd(Long userId, AppClientUserInfoDto appClientUserInfo){

        WebMessage webMessage = new WebMessage(0,"",null);

        appClientUserInfo.setCoohuaId(userId);

        ThirdAdDto thirdAdDto = null;

        if("on".equalsIgnoreCase(commonConfig.getDebug())){
            log.info("ThirdAdController: userId = {}, appClientUserInfo = {}",userId,appClientUserInfo);
        }

        try {
            thirdAdDto = thirdAdRpcService.getThirdAdDto(appClientUserInfo.getAdId(),appClientUserInfo, ProdLineEnum.BP);

            if("on".equalsIgnoreCase(commonConfig.getDebug())){
                log.info("ThirdAdController: thirdAdDto = {}",thirdAdDto);
            }

        } catch (Exception e) {
            log.error("get 3rd ad error ,userid = {}, appClientUserInfoDto = {} , e = ",userId,appClientUserInfo,e);
            webMessage.setResult(-1);
            webMessage.setMessage("获取api广告失败");
            webMessage.setResult(null);
            return webMessage;
        }

        //暂时处理pvUrl与clkUrl过多问题
        List<String> pvList = new ArrayList<>();
        List<String> clickUrl = new ArrayList<>();
        if(thirdAdDto.getPvUrls().size()>10){
            pvList = thirdAdDto.getPvUrls().subList(0,8);
            thirdAdDto.setPvUrls(pvList);
        }
        if(thirdAdDto.getCliUrls().size()>10){
            clickUrl = thirdAdDto.getCliUrls().subList(0,8);
            thirdAdDto.setCliUrls(clickUrl);
        }


        if(thirdAdDto!=null){
            webMessage.setResult(thirdAdDto);
        }

        return webMessage;
    }
}
