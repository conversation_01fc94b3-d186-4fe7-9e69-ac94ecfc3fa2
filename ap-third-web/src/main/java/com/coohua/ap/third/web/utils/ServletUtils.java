package com.coohua.ap.third.web.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Enumeration;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2019/8/29
 **/
@Slf4j
public class ServletUtils {

    public static String getHeaderInfo(HttpServletRequest request){
        StringBuilder stringBuilder = new StringBuilder();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()){
            String headerName = headerNames.nextElement();
            stringBuilder.append(headerName + "\t" + request.getHeader(headerName) + "\n");
        }
        return stringBuilder.toString();
    }

    public static String getParameters(HttpServletRequest request){
        String params = request.getQueryString();
        if (StringUtils.isNotBlank(params)) {
            return params;
        }
        try {
            StringBuilder stringBuilder = new StringBuilder();
            BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"));
            String inputStr;
            while ((inputStr = streamReader.readLine()) != null) {
                stringBuilder.append(inputStr);
            }
            return stringBuilder.toString();
        } catch (Exception e) {
            return null;
        }
    }

}
