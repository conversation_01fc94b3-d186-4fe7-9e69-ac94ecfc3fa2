package com.coohua.ap.third.web;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import top.zrbcool.pepper.boot.jedis.EnableJedisClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

/**
 * @author: jinyang fan
 * @date: 2020.02.10
 * @package: com.coohua.ap.third.web
 * @description:
 */

@SpringBootApplication(scanBasePackages = "com.coohua.ap.third.web")
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
        "bp.account.rpc.referer", "bp.user.rpc.referer", "ap.filter.rpc.referer", "ap.engine.rpc.referer","ad.tap.rpc.referer",
        "ap.gateway.rpc.service"
       })

@EnableAutoChangeApolloConfig

@EnableMotan(namespace = "bp-user")
@EnableMotan(namespace = "bp-account")
@EnableMotan(namespace = "ap-filter")
@EnableMotan(namespace = "ap-engine")
@EnableMotan(namespace = "ap-gateway")
@EnableMotan(namespace = "tap-new")


@Slf4j
public class ThirdAdWebApplication {

    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        SpringApplication.run(ThirdAdWebApplication.class, args);
        log.info("ThirdAdWebApplication started in {} ms", (System.currentTimeMillis() - start));
        System.out.println("ThirdAdWebApplication started ... ");
    }
}
