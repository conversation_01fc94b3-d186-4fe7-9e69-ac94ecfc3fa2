package com.coohua.ap.third.web.interceptor;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.third.web.config.CommonConfig;
import com.coohua.ap.third.web.constants.ThirdAdConstants;
import com.coohua.ap.third.web.utils.ServletUtils;
import com.coohua.bp.user.remote.api.UserRPC;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.dispatcher.web.interceptor
 * @create_time 2019-10-30
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean", application = "ap-gateway")
    private UserRPC userRPC;

    @Autowired
    private CommonConfig commonConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {

        try {
            String accessKey = request.getHeader("accessKey");
            String appId = request.getParameter("appId");

            if("on".equalsIgnoreCase(commonConfig.getDebug())){
                log.info("AuthInterceptor: accessKey = {}, appId = {}",accessKey,appId);
            }

            if (StringUtils.isEmpty(appId) || AppBuilder.getById(Integer.parseInt(appId)) == null || Integer.parseInt(appId) == 0) {
                throw new BusinessException(403, "Check appId fail, appId is empty or invalid. [appId] is " + appId);
            }

            // 用户有效性校验
            Boolean authResult = userRPC.checkAuth(accessKey, Long.parseLong(appId));
            if (authResult) {
                Long userId = Long.parseLong(accessKey.split(AdConstants.SYMBOL_UNDERLINE)[1]);
                request.setAttribute(ThirdAdConstants.USER_ID, userId);
                return true;
            } else {
                log.info("User accessKey is invalid. appId={}, accessKey={}", appId, accessKey);
            }
            return false;
        } catch (Exception e) {
            log.error("Controller Exception! path: {} \n headers: {} \n param: {} \n message: {}",
                    request.getRequestURI(),
                    ServletUtils.getHeaderInfo(request),
                    ServletUtils.getParameters(request),
                    e.getMessage(),
                    e);
        }
        return false;
    }
}
