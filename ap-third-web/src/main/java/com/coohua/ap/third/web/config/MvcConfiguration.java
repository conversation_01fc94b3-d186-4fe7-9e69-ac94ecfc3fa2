package com.coohua.ap.third.web.config;


import com.coohua.ap.third.web.controller.CommonHeaderArgumentResolver;
import com.coohua.ap.third.web.controller.UserIdMethodArgumentResolver;
import com.coohua.ap.third.web.interceptor.AuthInterceptor;
import com.coohua.ap.third.web.interceptor.CORSInterceptor;
import com.coohua.ap.third.web.interceptor.CommonHeaderInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * MVC配置，拦截器、跨域、etc...
 * <AUTHOR>
 */
@Configuration
public class MvcConfiguration {

    @Autowired
    private AuthInterceptor authInterceptor;

    @Autowired
    private CommonHeaderInterceptor commonHeaderInterceptor;

    @Bean
    public WebMvcConfigurer interceptor() {
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(authInterceptor).addPathPatterns("/**");
                registry.addInterceptor(commonHeaderInterceptor).addPathPatterns("/**");
                // 跨域许可配置
                registry.addInterceptor(new CORSInterceptor()).addPathPatterns(
                        "/**/ap-third/3rd/getThirdAd"
                );
            }

            @Override
            public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
                argumentResolvers.add(new UserIdMethodArgumentResolver());
                argumentResolvers.add(new CommonHeaderArgumentResolver());
            }

            @Override
            public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
                configurer.enable();
            }
        };
    }

}