package com.coohua.ap.third.web.utils;

import com.coohua.bp.user.remote.dto.CommonHeaderDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2019/8/27
 **/
public class CheckUtils {

    private static final String GPS_PATTERN = "\\d{0,3}\\.\\d+,\\d{0,3}\\.\\d+";
    private static final String MOBILE_PATTERN = "^1\\d{10}$";

    /**
     * 验证手机号(1开头的11位数字)
     *
     * @param mobile
     * @return
     */
    public static Boolean checkMobile(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            return mobile.matches(MOBILE_PATTERN);
        }
        return false;
    }

    public static Boolean checkGps(String gps){
        if(StringUtils.isNotBlank(gps)) {
            return gps.matches(GPS_PATTERN);
        }
        return false;
    }

    /**
     * 校验 Header 参数有效性
     *
     * @return
     */
    public static Boolean isHeaderParamValid(CommonHeaderDTO commonHeaderDTO) {
        // 兼容已上线的SDK。已上线的SDK没有传这些header信息，不能做非空校验
        // 但如果传了appVersion。说明已经是新版的SDK，需要做以下非空校验
        if (StringUtils.isEmpty(commonHeaderDTO.getAppVersion())) {
            return true;
        }
        Asserts.notNull(commonHeaderDTO.getDeviceId(), "deviceId");
        Asserts.notNull(commonHeaderDTO.getBrand(), "brand");
        Asserts.notNull(commonHeaderDTO.getGps(), "gps");
        Asserts.notNull(commonHeaderDTO.getOs(), "os");
        Asserts.notNull(commonHeaderDTO.getChannel(), "channel");
        Asserts.notNull(commonHeaderDTO.getRomVersion(), "romVersion");
        Asserts.notNull(commonHeaderDTO.getOsVersion(), "osVersion");
        return true;
    }
}
