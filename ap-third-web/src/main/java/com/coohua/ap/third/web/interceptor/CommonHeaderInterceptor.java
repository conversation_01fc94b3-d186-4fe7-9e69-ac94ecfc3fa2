package com.coohua.ap.third.web.interceptor;


import com.coohua.ap.third.web.config.CommonConfig;
import com.coohua.ap.third.web.constants.ThirdAdConstants;
import com.coohua.ap.third.web.utils.IpUtils;
import com.coohua.ap.third.web.utils.ServletUtils;

import com.coohua.tap.remote.dto.AppClientUserInfoDto;

import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/07
 */
@Slf4j
@Component
public class CommonHeaderInterceptor implements HandlerInterceptor {

    @Autowired
    private CommonConfig commonConfig;

    private static final Logger accessLog = LogManager.getLogger("access");

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {

        try {

            String adId = request.getParameter("adId");
            String model = request.getParameter("model");
            String os = request.getParameter("os");
            String osv = request.getParameter("osv");
            String os_version = request.getParameter("os_version");
            String h = request.getParameter("h");
            String w = request.getParameter("w");
            String connectiontype = request.getParameter("connectiontype");
            String imei = request.getParameter("imei");
            /*String ip = IpUtils.getReomteIp(request);*/
            String ip = request.getParameter("ip");
            String mac = request.getParameter("mac");
            String androidId = request.getParameter("androidId");
            String ua = request.getParameter("ua");
            String lat = request.getParameter("lat");
            String lon = request.getParameter("lon");

            String idfa = request.getParameter("idfa");
            String idfv = request.getParameter("idfv");

            String carrier = request.getParameter("carrier");
            String operator = request.getParameter("operator");
            String make = request.getParameter("make");
            String ppi = request.getParameter("ppi");
            String sh = request.getParameter("sh");
            String sw = request.getParameter("sw");

            String miToken = request.getParameter("miToken");
            String macro = request.getParameter("macro");
            String imsi = request.getParameter("imsi");
            String dpi = request.getParameter("dpi");

            String densityDpi = request.getParameter("densityDpi");
            String serial = request.getParameter("serial");
            String battery = request.getParameter("battery");
            String thirdAppVersion = request.getParameter("thirdAppVersion");
            String oaid = request.getParameter("oaid");
            String appVersion = request.getParameter("appVersion");

            AppClientUserInfoDto appClientUserInfo = new AppClientUserInfoDto();
            appClientUserInfo.setAdId(Integer.valueOf(adId));
            appClientUserInfo.setAppVersion(appVersion);
            appClientUserInfo.setModel(model);
            appClientUserInfo.setOs(os);
            appClientUserInfo.setOsv(osv);
            appClientUserInfo.setH(h);
            appClientUserInfo.setW(w);
            appClientUserInfo.setConnectiontype(connectiontype);
            appClientUserInfo.setImei(imei);
            appClientUserInfo.setIp(ip);
            appClientUserInfo.setMac(mac);
            appClientUserInfo.setAndroidId(androidId);
            appClientUserInfo.setUa(ua);
            appClientUserInfo.setLat(lat);
            appClientUserInfo.setLon(lon);
            appClientUserInfo.setIdfa(idfa);
            appClientUserInfo.setIdfv(idfv);
            appClientUserInfo.setCarrier(carrier);
            appClientUserInfo.setOperator(operator);
            appClientUserInfo.setMake(make);
            appClientUserInfo.setPpi(ppi);
            appClientUserInfo.setSw(sw);
            appClientUserInfo.setSh(sh);
            appClientUserInfo.setMiToken(miToken);
            appClientUserInfo.setMacro("1".equals(macro));
            appClientUserInfo.setDpi(dpi);
            appClientUserInfo.setImsi(imsi);
            appClientUserInfo.setOs_version(os_version);
            appClientUserInfo.setSerial(serial);
            appClientUserInfo.setDensityDpi(densityDpi);
            appClientUserInfo.setBattery(battery);
            appClientUserInfo.setThirdAppVersion(thirdAppVersion);
            appClientUserInfo.setOaid(oaid);
            if(StringUtils.isNotEmpty(imsi) && imsi.startsWith("4600")){
                appClientUserInfo.setCarrier(imsi.substring(0, 5));
            }

            if("on".equalsIgnoreCase(commonConfig.getDebug())){
                log.info("CommonHeaderInterceptor: appClientUserInfo = {}",appClientUserInfo);
            }

            request.setAttribute(ThirdAdConstants.THIRD_APP_USER,appClientUserInfo);

            return true;
        } catch (Exception e) {
            log.error("Controller Exception! path: {} \n headers: {} \n param: {} \n message: {}",
                    request.getRequestURI(),
                    ServletUtils.getHeaderInfo(request),
                    ServletUtils.getParameters(request),
                    e.getMessage(),
                    e);
        }
        return false;
    }
}
