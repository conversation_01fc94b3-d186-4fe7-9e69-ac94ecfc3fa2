<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bp-ap</artifactId>
        <groupId>com.coohua</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ap-support</artifactId>

    <packaging>pom</packaging>
    <modules>
        <module>ap-support-core</module>
        <module>ap-support-ad-third-spring</module>
        <module>ap-support-ad-third-bidding-spring</module>
        <module>ap-support-ad-custom-spring</module>
        <module>ap-support-ad-spring</module>
        <module>ap-support-config-global-spring</module>
        <module>ap-support-config-strategy-spring</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-web</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>