package com.coohua.ap.support.core.message;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.PropertyValueConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.coohua.ap.base.constants.MessageTag;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @since 2022/3/22
 */
@Slf4j
public class ThirdAdMessageHandler implements AdMessageHandler{

    private ConsumerBean consumerBean;

    @Override
    public void init() {
        // 设置MQ
        ConsumerBean consumerBean = new ConsumerBean();
        //配置文件
        Properties properties = SpringIoCUtils.getMQConfig().getMqPropertie();
        properties.setProperty(PropertyKeyConst.GROUP_ID, SpringIoCUtils.getMQConfig().getGroupId());
        //将消费者线程数固定为20个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "20");
        properties.put(PropertyKeyConst.MessageModel, PropertyValueConst.BROADCASTING);
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(SpringIoCUtils.getMQConfig().getTopic());
        subscription.setExpression(MessageTag.AD_CONFIG.getTag());
        subscriptionTable.put(subscription, (message, consumeContext) -> {
            try {
                this.onMessage(new String(message.getBody()));
                return Action.CommitMessage;
            } catch (Exception e) {
                //消费失败
                return Action.ReconsumeLater;
            }
        });
        //订阅多个topic如上面设置
        consumerBean.setSubscriptionTable(subscriptionTable);
        consumerBean.start();
        this.consumerBean = consumerBean;
        log.info("Rocket MQ Start.... Listen Topic {} TAG {}", SpringIoCUtils.getMQConfig().getTopic(),MessageTag.AD_CONFIG.getTag());
    }

    @Override
    public void onMessage(String message) {
        ThirdAdModel thirdAdModel = JSON.parseObject(message,ThirdAdModel.class);
        ThirdAd thirdAd = ContainerHolder.getThirdAdContainer().getAd(thirdAdModel.getId());
        // 收到MQ 更新广告到内存- 触发一次更新
        thirdAd.update();
    }

    @Override
    public void shutdown() {
        if (this.consumerBean != null) {
            this.consumerBean.shutdown();
            log.info("RocketMQ shutdown....");
        }
    }
}
