package com.coohua.ap.support.core.valves.impl.bidding;

import com.coohua.ap.base.domain.PlatformVersion;
import com.coohua.ap.support.core.components.BiddingAd;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/29
 */
public class VersionValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInfoBidding){
            UserInfoBidding userInfoBidding = (UserInfoBidding) userInformation;

            candidates.removeIf(id ->
                    !ContainerHolder.getBiddingContainer().getBidding(id)
                            .isPlatformVersionValid(userInfoBidding.getOsType(),
                                    userInfoBidding.getAppVersion()));
        }
    }
}
