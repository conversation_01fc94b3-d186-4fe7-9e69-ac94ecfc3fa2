package com.coohua.ap.support.core.container;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ApSupportConstants;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.components.AdSource;
import com.coohua.ap.support.core.components.AppInfo;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.container.thread.ThirdAdContainerScheduledThreadFactory;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.factory.ThirdAdFactory;
import com.coohua.ap.support.core.message.ThirdAdMessageHandler;
import com.coohua.ap.support.core.spring.RedisManager;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.config.EnvConfig;
import com.coohua.ap.support.core.spring.model.AdPosModel;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import com.coohua.ap.support.core.valves.impl.PutDatePeriodValve;
import com.coohua.ap.support.core.valves.impl.StateValve;
import com.coohua.ap.support.core.valves.impl.TimeBucketValve;
import com.coohua.ap.support.core.valves.impl.third.TotalBudgetValve;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.weibo.api.motan.util.ConcurrentHashSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.map.LRUMap;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <pre>
 * 第三方广告容器
 *  adMapper 和 adTypeMapper ，二者之间不用严格保证数据一致性，所以没做加锁操作。
 * </pre>
 * <AUTHOR>
 */
@Slf4j
@SpiMeta(name = ThirdAdContainer.CONTAINER_NAME)
public class ThirdAdContainer extends DefaultAdContainer {

    public static final String CONTAINER_NAME = "ThirdAdContainer"; // 容器名称

    // 所有广告详细信息映射表：广告ID -> 广告详情（包含invalid的广告）
    // 广告热门程度： 内存有上限，按照内存最大4w条有效广告设计
    private final Map<Long,ThirdAd> adMapper = Collections.synchronizedMap(new LRUMap<>(40000));

    // 广告类型 -> 该广告类型下的所有有效广告ID（不包含invalid的广告）
    private volatile Map<String, Set<Long>> adTypeMapper = new ConcurrentHashMap<>();

    private volatile Map<Integer, List<AppInfo>> appInfoMapper = new ConcurrentHashMap<>();

    // AppId -> 该应用下的所有有效广告位 由于PosId更新频率较低，不参与容器生命周期REFRESH 动作
    private volatile Map<Integer,List<AdPosModel>> adPosMapper = new ConcurrentHashMap<>();
    private volatile Map<Integer,AdPosModel> adPosBasicMapper = new ConcurrentHashMap<>();

    // 微信小程序广告映射
    private volatile Map<String,Long> wechatMiniAdBasicMapper = new ConcurrentHashMap<>();
    private static final List<Integer> wechatAdType = new ArrayList<Integer>(){{
        add(AdType.WECHAT_BANNER.type());
        add(AdType.WECHAT_VIDEO.type());
        add(AdType.WECHAT_INNER.type());
    }};

    private static final long FIXED_RATE = 1000 * 60 * 60 * 24; // 定时周期 30分钟全量刷新 降低更新频次 依赖MQ

    private ScheduledExecutorService scheduledExecutorService;

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }

    @Override
    public void initValveChain() {
        this.valveChain = new DefaultValveChain();
        this.valveChain.setFirst(new StateValve())
                .addValve(new PutDatePeriodValve())
                .addValve(new TimeBucketValve())
                .addValve(new TotalBudgetValve());
    }

    @Override
    public void initAdFactory() {
        this.adFactory = ThirdAdFactory.getInstance();
    }

    @Override
    public void setAdMessageHandler() {
        this.adMessageHandler = new ThirdAdMessageHandler();
        adMessageHandler.init();
    }

    @Override
    public ThirdAd getAd(Long adId) {
        if (adId == null) {
            return null;
        }
        ThirdAd thirdAd = adMapper.get(adId);
        if (thirdAd == null){
            try {
                thirdAd = getAdFromRedis(adId);
            }catch (Exception e){
                log.warn("get from Redis Id:{}",adId);
            }
            this.adMapper.put(adId,thirdAd);
            setAppInfo(thirdAd);
        }
        return thirdAd;
    }

    /**
     * 刷新三方app信息
     * @param thirdAd 瀑布流广告
     */
    private void setAppInfo(ThirdAd thirdAd) {
        AppInfo appInfo = new AppInfo(thirdAd);
        if (StringUtils.isNotEmpty(appInfo.getAppId())) {
            if (this.appInfoMapper.containsKey(appInfo.getProduct())) {
                List<AppInfo> appInfos = this.appInfoMapper.get(appInfo.getProduct());
                if (!appInfos.contains(appInfo)){
                    appInfos.add(appInfo);
                }
            } else {
                List<AppInfo> appInfoList = new ArrayList<>();
                appInfoList.add(appInfo);
                this.appInfoMapper.put(appInfo.getProduct(), appInfoList);
            }
        }
    }

    public List<ThirdAd> getAllAd(){
        return new ArrayList<>(adMapper.values());
    }

    @Override
    public List<Long> getAdCollectionByAdType(Integer adType, Integer appId) {
        if (adType == null) {
            return new ArrayList<>();
        }
        Set<Long> ret = adTypeMapper.get(buildAppKey(appId,adType));
        if (CollectionUtils.isEmpty(ret)) {
            return new ArrayList<>();
        }
        return new ArrayList<>(ret);
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new ThirdAdContainerScheduledThreadFactory();
    }

    @Override
    public boolean isMyAdType(int adType) {
        return ApSupportConstants.AD_SOURCE_THIRD.equals(AdTypeSub.getAdSource(adType));
    }

    public void refreshAd(Long adId){
        ThirdAd thirdAd = getAdFromRedis(adId);
        this.adMapper.put(adId,thirdAd);
        // 走过滤
        List<Long> preQueen = Lists.newArrayList(adId);
        this.valveChain.getFirst().invoke(preQueen, buildUserInformation());
        // 有效加入AdTypeList
        String key = buildAppKey(thirdAd.getSortedApps().get(0), thirdAd.getType());

        if (preQueen.size() > 0) {
            if (this.adTypeMapper.containsKey(key)) {
                this.adTypeMapper.get(key).add(adId);
            } else {
                this.adTypeMapper.put(key, Sets.newHashSet(adId));
            }
            setAppInfo(thirdAd);
        }else {
            if (this.adTypeMapper.containsKey(key)) {
                this.adTypeMapper.get(key).remove(adId);
            }
        }
        log.info("AD {} ALREADY UPDATED....",adId);
    }

    public void init(){
        super.init();
        // 加入到刷新计划
        if (!SpringIoCUtils.getBean(EnvConfig.class).isTest()) {
            log.info("===> Now PRD Env.. Create New scheduled And Listen new Topic");
            this.scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(this.threadFactory);
            scheduledExecutorService.scheduleAtFixedRate(this::refreshAdContainer, FIXED_RATE, FIXED_RATE, TimeUnit.MILLISECONDS);
            // 设置MQ
            this.setAdMessageHandler();
        }
    }


    public void refresh(){
        if (SpringIoCUtils.getBean(EnvConfig.class).isTest()){
            super.refresh();
        }
    }

    public void stoped(){
        super.stoped();
        scheduledExecutorService.shutdown();
        this.adMessageHandler.shutdown();
        log.info("scheduledExecutorService ShutDown...");
    }

    @Override
    public void refreshAdContainer() {
        Set<AppInfo> appInfoSet = new ConcurrentHashSet<>();
        long start = System.currentTimeMillis();
        // 按APP_更新-降低瞬时I/O
        // 构建新广告映射
        // 执行预过滤
        // 用过滤结果构建新的广告类型映射
        Map<String, Set<Long>> stepMap = new ConcurrentHashMap<>();
        Map<String, Long> wminMap = new ConcurrentHashMap<>();
        // 这里启动可以占用CPU和IO快速加载，但是刷新的时候机器得花一部分资源去处理HTTP请求
        // 一次1kADID的物料加载引起的卡顿会出现BrokenPipe再到整个容器因为请求阻塞导致健康检查的接口超时 导致K8 kill容器出现重启
        // 在重启过程中又没有新的部分处理请求 所以存在严重的不可用风险
        // 在单体物理机并未发现有这种现象，所以对资源占用较高的代码块需要考虑容器的耐用性
        if (this.getState() == ContainerState.NOT_INIT){
            log.info("Init Ad Entity Start...");
            AppBuilder.values()
            //TODO test 本地启动时使用，代码提交时注意
                    .stream().filter(f -> Objects.equals(f.getAppId(), 1645)).collect(Collectors.toList())
                    .parallelStream()
                    .forEach(app -> refreshAdInfoTr(app,appInfoSet,wminMap,stepMap));
            log.info("Init Ad Entity.Cost {} ms Success Load {} AdTypes {} AppInfos",
                    System.currentTimeMillis() - start,stepMap.size(),appInfoSet.size());
        }else {
            log.info("Refresh Ad Entity Start...");
            AppBuilder.values().forEach(app -> refreshAdInfoTr(app,appInfoSet,wminMap,stepMap));
            log.info("Refresh Ad Entity.Cost {} ms",System.currentTimeMillis() - start);
        }
        // 加载应用列表
        this.appInfoMapper = appInfoSet.stream().collect(Collectors.groupingBy(AppInfo::getProduct));
        this.adTypeMapper = stepMap;
        this.wechatMiniAdBasicMapper = wminMap;

        if (this.getState() == ContainerState.NOT_INIT){
            this.refreshAdPosAll();
        }
    }

    private void refreshAdInfoTr(App app,
                                 Set<AppInfo> appInfoSet,
                                 Map<String, Long> wminMap,
                                 Map<String, Set<Long>> stepMap){
        try {
            Map<Long,ThirdAd> thirdAdMap = new ConcurrentHashMap<>();
            List<Long> adIdList = getAdIdListFromRedis(app.appId());
            if (adIdList == null || adIdList.size() == 0) {
                // 未找到AD_ID
                return;
            }
            // todo 全量查存在性能问题
            List<ThirdAdModel> models = getAdModelListFromRedis(adIdList);
            if (models.size() == 0) {
                return;
            }
            List<Long> preQueue = new ArrayList<>();
            for (ThirdAdModel model : models) {
                ThirdAd transfer = new ThirdAd(model, this);
                preQueue.add(model.getId());
                thirdAdMap.put(model.getId(), transfer);
                this.adMapper.put(model.getId(),transfer);
            }
            this.valveChain.getFirst().invoke(preQueue, buildUserInformation());
            for (long adId : preQueue) {
                ThirdAd thirdAd = thirdAdMap.get(adId);
                AppInfo appInfo = new AppInfo(thirdAd);
                if (StringUtils.isNotEmpty(appInfo.getAppId())) {
                    appInfoSet.add(appInfo);
                }
                String key = buildAppKey(app.appId(), thirdAd.getType());
                if (stepMap.containsKey(key)) {
                    stepMap.get(key).add(adId);
                } else {
                    stepMap.put(key, Sets.newHashSet(adId));
                }
                if (wechatAdType.contains(thirdAd.getType())) {
                    wminMap.put(getPosId(thirdAd), thirdAd.getAdId());
                }
            }
            if (SpringIoCUtils.getBean(EnvConfig.class).isTest()){
                // 测试环境广告少 全量load
                if (thirdAdMap.size() > 0) {
                    this.adMapper.putAll(thirdAdMap);
                }
            }else {
                if (this.getState() == ContainerState.NOT_INIT) {
                    // 初始化init 后续靠请求维护
                    if (thirdAdMap.size() > 0) {
                        this.adMapper.putAll(thirdAdMap);
                    }
                }
            }
        }catch (Exception e){
            // Redis超时
            log.error("Refresh AdInfo Ex:",e);
        }
    }

    private String buildAppKey(Integer appId,Integer adType){
        return String.format("%s_%s",appId,adType);
    }

    // 生成预过滤条件
    private UserInformation buildUserInformation() {
        UserInformation userInformation = new UserInformation();
        userInformation.setAdSource(AdSource.THIRD);
        return userInformation;
    }

    private List<Long> getAdIdListFromRedis(Integer appId){
        String key = RedisBuilder.buildAppAdList(appId);
        List<Long> adIdList = SpringIoCUtils.getJedisClusterClient().getCache(key,new TypeReference<List<Long>>(){});
        if (adIdList == null || adIdList.size() == 0){
            ThirdAdFactory thirdAdFactory = (ThirdAdFactory) adFactory;
            adIdList = thirdAdFactory.queryAppIds(appId);
            if (adIdList != null && adIdList.size() > 0){
                SpringIoCUtils.getJedisClusterClient().set(key,JSON.toJSONString(adIdList));
            }
        }
        return adIdList;
    }

    private List<ThirdAdModel> getAdModelListFromRedis(List<Long> adIdList){
        List<ThirdAdModel> thirdAdModels = new ArrayList<>();
        List<String> keys = adIdList.stream().map(RedisBuilder::buildAdInfo).collect(Collectors.toList());
        Map<String, Response<String>> responseMap = RedisManager.redisClusterPipeline(keys);
        responseMap.forEach((k,v) ->{
            if (StringUtils.isNotEmpty(v.get())){
                thirdAdModels.add(JSON.parseObject(v.get(),ThirdAdModel.class));
            }
        });

        List<Long> getFromRedisCacheList = thirdAdModels.stream().map(ThirdAdModel::getId)
                .collect(Collectors.toList());
        List<Long> cantGetFromRedis = adIdList.stream().filter(adId -> !getFromRedisCacheList.contains(adId))
                .collect(Collectors.toList());
        // GET FROM Mysql
        if (cantGetFromRedis.size() > 0) {
            ThirdAdFactory thirdAdFactory = (ThirdAdFactory) this.adFactory;
            List<ThirdAdModel> notInRedisList = thirdAdFactory.queryByIds(cantGetFromRedis);
            if (notInRedisList != null && notInRedisList.size() > 0) {
                thirdAdModels.addAll(notInRedisList);
                // 更新到缓存
                refreshRedisAd(notInRedisList);
            }
        }

        return thirdAdModels;
    }

    private void refreshRedisAd(List<ThirdAdModel> thirdAdModels){
        try(Jedis jedis = SpringIoCUtils.getJedisClusterClient().getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)){
            Pipeline pipeline = jedis.pipelined();
            for (ThirdAdModel thirdAdModel : thirdAdModels) {
                String key = RedisBuilder.buildAdInfo(thirdAdModel.getId());
                pipeline.set(key,JSON.toJSONString(thirdAdModel));
            }
            pipeline.sync();
        }catch (Exception e){
            SpringIoCUtils.getJedisClusterClient().get("EmptyKey");
        }
    }

    private ThirdAd getAdFromRedis(Long adId){
        String key = RedisBuilder.buildAdInfo(adId);
        ThirdAdModel thirdAdModel = SpringIoCUtils.getJedisClusterClient().getCache(key,new TypeReference<ThirdAdModel>(){});
        if (thirdAdModel == null){
            ThirdAdFactory thirdAdFactory = (ThirdAdFactory) adFactory;
            thirdAdModel = thirdAdFactory.getNewestAd(adId);
            refreshRedisAd(Collections.singletonList(thirdAdModel));
        }
        return new ThirdAd(thirdAdModel,this);
    }

    /**
     * 在非容器生命周期当中，查询需要初始化工厂方法
     */
    public void refreshAdPosAll(){
        if (adFactory instanceof ThirdAdFactory) {
            ThirdAdFactory thirdAdFactory = (ThirdAdFactory) this.adFactory;
            //从数据库里查
            List<AdPosModel> adPosModelList = thirdAdFactory.getAllPosId();
            List<String> searchPointList = new ArrayList<>();
            searchPointList.add("激励视频");
            searchPointList.add("开屏");
            searchPointList.add("插屏");
            searchPointList.add("固定位");
            searchPointList.add("弹窗");
            searchPointList.add("banner");
            searchPointList.add("全屏视频");
            searchPointList.add("强制视频");
            searchPointList.add("自渲染大图");
            searchPointList.add("全屏视频五秒");

            this.adPosMapper = adPosModelList.stream()
                    .filter(adPosModel -> searchPointList.stream().anyMatch(r -> adPosModel.getName().contains(r)))
                    .collect(Collectors.groupingBy(AdPosModel::getAppId));

            this.adPosBasicMapper = adPosModelList.stream()
                    .filter(adPosModel -> searchPointList.stream().anyMatch(r -> adPosModel.getName().contains(r)))
                    .collect(Collectors.toMap(AdPosModel::getId,r->r,(r1,r2)->r2));
        }
    }

    public String getPosId(ThirdAd thirdAd){
        if (StringUtils.isNotEmpty(thirdAd.getExt().getAndroidPosId())){
            return thirdAd.getExt().getAndroidPosId();
        }
        if (StringUtils.isNotEmpty(thirdAd.getExt().getAndroidBakPosId())){
            return thirdAd.getExt().getAndroidBakPosId();
        }

        if (StringUtils.isNotEmpty(thirdAd.getExt().getIosPosId())){
            return thirdAd.getExt().getIosPosId();
        }
        if (StringUtils.isNotEmpty(thirdAd.getExt().getIosBakPosId())){
            return thirdAd.getExt().getIosBakPosId();
        }
        return "";
    }

    public List<AdPosModel> getAdPosList(Integer appId){
        return this.adPosMapper.get(appId);
    }

    public AdPosModel getAdPosById(Integer posId){
        return this.adPosBasicMapper.get(posId);
    }


    public Set<AppInfo> getAppInfoList(Integer appId){
        return new HashSet<>(this.appInfoMapper.get(appId));
    }

    public Long getAdIdFromPosId(String posId){
        return this.wechatMiniAdBasicMapper.get(posId);
    }

}
