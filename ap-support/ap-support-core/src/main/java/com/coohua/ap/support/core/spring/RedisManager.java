package com.coohua.ap.support.core.spring;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.utils.DateUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.jedis.exceptions.JedisMovedDataException;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Redis操作的封装
 * <AUTHOR>
 */
public class RedisManager {

    public static String buildRedisAdClickCounter(long adId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.NAP_AD_CLICK_COUNTER_PRE + adId + AdConstants.SYMBOL_COLON + today;
    }

    public static String buildRedisAdCharge(long adPlanId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.ECP_AD_CHARGE_PRE + adPlanId + AdConstants.SYMBOL_COLON + today;
    }

    public static String buildRedisAdChargeForClick(Long adPlanId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.ECP_AD_CLICK_CHARGE_PRE + adPlanId + AdConstants.SYMBOL_COLON + today;
    }

    public static String buildRedisAdChargeForExposure(Long adPlanId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.ECP_AD_EXPOSURE_CHARGE_PRE + adPlanId + AdConstants.SYMBOL_COLON + today;
    }

    public static String buildRedisAdClickHourCounter(long adId) {
        String hourTime = DateUtils.formatDate(new Date(), "yyyyMMddHH");
        return RedisConstants.NAP_AD_CLICK_HOUR_COUNTER_PRE + adId + AdConstants.SYMBOL_COLON + hourTime;
    }

//    public static Map<String, Response<String>> redisPipeline(List<String> keys) {
//        Map<String, Response<String>> ret = new HashMap<>();
//        SpringIoCUtils.getJedisClient().doExecute(jedis -> {
//            Pipeline pipeline = jedis.pipelined();
//            for (String key : keys) {
//                ret.put(key, pipeline.get(key));
//            }
//            pipeline.sync();
//        });
//        return ret;
//    }

    public static String buildAdvertiserKey(Integer advertiserId){
        String now = DateUtils.formatDateForYMD(new Date());
        return  RedisConstants.ECP_USER_ADVERTISER_DAILY + buildKey(advertiserId.toString());
    }

    public static String buildPlanKey(Long planId){
        return  RedisConstants.ECP_USER_PLAN + buildKey(planId.toString());
    }

    private static String buildKey(String... items) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < items.length; i++) {
            if (i == items.length - 1) {
                sb.append(items[i]);
            } else {
                sb.append(items[i]).append(AdConstants.SYMBOL_COLON);
            }
        }
        return sb.toString();
    }

    public static Map<String, Response<String>> redisClusterPipeline(List<String> keys) {
        if (!keys.isEmpty()) {
            try (Jedis jedis = SpringIoCUtils.getJedisClusterClient().getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
                Pipeline pipeline = jedis.pipelined();
                Map<String, Response<String>> ret = new HashMap<>();
                for (String key : keys) {
                    ret.put(key, pipeline.get(key));
                }
                pipeline.sync();
                return ret;
            } catch (JedisMovedDataException jmde) {
                SpringIoCUtils.getJedisClusterClient().get("EmptyKey");
            }
        }
        return new HashMap<>();
    }
}
