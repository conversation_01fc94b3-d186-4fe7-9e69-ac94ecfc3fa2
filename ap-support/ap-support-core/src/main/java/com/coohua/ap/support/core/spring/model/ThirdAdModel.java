package com.coohua.ap.support.core.spring.model;

import lombok.Data;

import java.util.Date;

/**
 * 第三方广告DB模型
 * <AUTHOR>
 */
@Data
public class ThirdAdModel {
    /**
     * ad_information表字段【广告基本信息】
     */
    // id
    private long id;
    // 广告名称
    private String name;
    // 广告类型
    private int type;
    // 投放日期
    private String putDatePeriod;
    // 投放时段
    private String timeBucket;
    // 投放状态，1-投放中，0-暂停投放
    private int state;
    // 广告投放位置，可同时指定多个，用逗号分隔。1-信息流／热搜，2-随机组，3-详情页推荐，4-详情页底部
    private String adPos;
    // 产品标识。 0-通用，1-锁屏，2-淘新闻（限制了产品后，只有对应产品才可以拉取到本条广告）
    private int product;
    // 创建时间
    private Date createTime;
    // 最后一次更新时间
    private Date updateTime;
    // 广告状态变更时间
    private Date stateUpdateTime;
    // Ecp平台广告ID
    private Integer ecpAdId;

    /**
     * ad_orientation表字段【定向条件信息】
     */
    // 投放区域限制
    private String region;
    // 性别，0-不限制，1-男，2-女
    private int sex;
    // 平台与版本限制，iOS/Android
    private String platformVersion;
    // 用户已安装包名限制
    private String app;
    // 尾号限定
    private String tailNumber;
    // 位置限定，可以指定广告出现在那些组中（0-29）
    private String position;
    // 总收入限定，四种情况：1) 不设置 2) 大于等于指定总收入 3) 小于等于指定总收入 4) 介于一个区间
    private String income;
    // 注册时间定向，四种情况：1) 不设置 2) 大于等于指定注册时间 3) 小于等于指定注册时间 4) 介于一个区间
    private String registerTime;
    // 注册时长定向，四种情况：1) 不设置 2) 大于等于指定注册时间 3) 小于等于指定注册时间 4) 介于一个区间
    private String registLonger;
    // 包含包名定向，多个包名之间，是或的关系
    private String containsPkg;
    // 不包含包名定向，多个包名之间，是且的关系
    private String notContainsPkg;

    // 用户使用的APP包名
    private String userPkg;

    // 渠道名
    private String channelId;
    // 机型
    private String brand;

    /**
     * ad_budget表字段【预算信息】
     */
    // 预算信息
    private String budget;
    // 点击间隔限制
    private int clickInterval;
    // 日点击数量限制
    private int dayClickLimit;
    // 曝光间隔限制
    private int exposureInterval;
    // 日曝光次数限制
    private int dayExposureLimit;

    private Integer ecpm;

    private Integer filterRegion;

    private Integer lockArea;
    private Integer lockActionPoint;

    private String abPoint;

    /**
     * ad_ext表字段【广告额外信息】
     */
    // 广告额外信息
    private String ext;
}
