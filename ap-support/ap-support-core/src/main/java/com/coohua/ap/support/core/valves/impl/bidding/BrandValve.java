package com.coohua.ap.support.core.valves.impl.bidding;

import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/7
 */
public class BrandValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInfoBidding){
            UserInfoBidding userInfoBidding = (UserInfoBidding) userInformation;

            // 若OPPO机型 移出Bidding列表
            if ("oppo".equalsIgnoreCase(userInfoBidding.getBrand())
                || "realme".equalsIgnoreCase(userInfoBidding.getBrand())
                || "oneplus".equalsIgnoreCase(userInfoBidding.getBrand())){
                candidates.removeIf(id -> ContainerHolder.getBiddingContainer().getBidding(id).getAdId().equals(415565L));
            }
            // 增加系统配置，使系统配置生效
            candidates.removeIf(id -> !ContainerHolder.getBiddingContainer().getBidding(id).isBrandValid(userInfoBidding.getBrand()));
        }
    }
}
