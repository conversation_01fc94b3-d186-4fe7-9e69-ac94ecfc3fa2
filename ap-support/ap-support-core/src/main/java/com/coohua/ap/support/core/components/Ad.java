package com.coohua.ap.support.core.components;


import com.coohua.ap.support.core.container.AdContainer;
import com.coohua.ap.support.core.container.lifecycle.AdLifeCycle;

/**
 * <pre>
 * 广告组件顶层接口
 * 依照OOP设计，所以尽可能避免对外暴露setter方法，对于非基本类型（或其包装类，及String），尽量不要暴露getter方法，满足OOP的封装原则。
 * </pre>
 * <AUTHOR>
 */
public interface Ad extends AdLifeCycle {

//    /**
//     * 设置广告是否失效
//     * @param valid true-有效，false-失效
//     */
//    void setValid(boolean valid);
//
//    /**
//     * 获取广告是否有效
//     * @return true-有效，false-失效
//     */
//    boolean isValid();
//
//    /**
//     * 依据当前广告信息，检查广告是否有效
//     * @return true-有效，false-失效
//     */
//    boolean checkValid();

    /**
     * 设置当前广告的所属容器
     * @param adContainer 广告容器
     */
    void setAdContainer(AdContainer adContainer);

    /**
     * 广告事件：当广告在召回列表中时
     * @param userId 用户ID
     */
    void onCandidate(Long userId);

    /**
     * 广告事件：当广告在检索过程中被过滤时
     * @param userId 用户ID
     * @param reason 被过滤的原因
     */
    void filtered(Long userId, String reason);

    /**
     * 广告事件：当广告被点击时
     * @param data 点击上报的数据
     */
    void onClick(Object data);

    /**
     * 广告事件：当广告被曝光时
     * @param data 曝光上报的数据
     */
    void onExposure(Object data);

    /**
     * 广告事件：当广告被投放时
     * @param data 请求上报的数据
     */
    void onServing(Object data);

    /**
     * 广告事件：第三方广告向第三方发送拉取广告请求时
     * @param data 请求数据
     */
    void onRequest(Object data);

    /**
     * 广告事件：第三方广告拉取成功时
     * @param data 请求数据
     */
    void onRequestSuccess(Object data);
}
