package com.coohua.ap.support.core.valves.impl.third;

import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.spring.RedisManager;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 广告总点击日预算过滤
 * <AUTHOR>
 */
public class TotalBudgetValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            UserInformation information = (UserInformation) userInformation;
            Map<Long, Long> adClick = getAllAdExpend(candidates);
            candidates.removeIf(adId -> !getAd(adId, information.getAdSource()).isBudgetValid(adClick));
        }

    }

    private Map<Long, Long> getAllAdExpend(List<Long> preList) {
        Map<Long, Long> ret = new HashMap<>();
        List<String> keys = new ArrayList<>();
        Map<String, Long> adIdTemp = new HashMap<>();
        for (long adId : preList) {
            ThirdAd thirdAd = thirdAdContainer.getAd(adId);
            if (thirdAd.isBudgetLimit()) {
                String key = RedisManager.buildRedisAdClickCounter(adId);
                keys.add(key);
                adIdTemp.put(key, adId);
            }
        }

        if (keys.isEmpty()) {
            return ret;
        }

        Map<String, Response<String>> fromRedis = RedisManager.redisClusterPipeline(keys);
        for (Map.Entry<String, Response<String>> entry : fromRedis.entrySet()) {
            String key = entry.getKey();
            Response<String> value = entry.getValue();
            ret.put(adIdTemp.get(key), value != null && StringUtils.isNotEmpty(value.get()) ? Long.parseLong(value.get()) : 0L);
        }

        return ret;
    }
}
