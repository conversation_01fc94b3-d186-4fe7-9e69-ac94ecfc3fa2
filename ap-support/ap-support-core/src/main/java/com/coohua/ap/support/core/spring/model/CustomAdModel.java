package com.coohua.ap.support.core.spring.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 直客广告DB模型
 * <AUTHOR>
 */
@Data
public class CustomAdModel {
    private Long balance; // 广告主现金余额

    private Long couponBalance; // 广告主优惠券余额

    private Date balanceAccountTime; // 广告主最后结算时间

    // ======== 广告计划 start ========
    // 广告计划ID
    private Long planId;

    // 广告计划名称
    private String planName;

    // 广告主ID
    private Integer advertiserId;

    // 广告计划状态
    private Integer planState;

    // 广告计划类型
    private Integer type;

    // 广告投放类型：1-匀速投放，2-加速投放
    private Integer deliveryType;

    // 广告计划日预算
    private Long dailyBudget;

    // 是否限制预算，1-限制（默认），0-不限制
    private Integer budgetLimit;

    // 广告计划创建时间
    private Date planCreateTime;

    // 广告计划最后一次更新时间
    private Date planUpdateTime;

    // 广告状态最后一次更新时间
    private Date stateUpdateTime;
    // ======== 广告计划 end ========

    // ======== 广告组 start ========

    // 广告组ID
    private Long groupId;

    // 广告组状态
    private Integer groupState;

    // 广告组名称
    private String groupName;

    // 广告投放：时间范围定向
    private String deliveryDatePeriod;

    // 广告投放：时段定向
    private String timeBucket;

    // 广告投放：产品定向
    private String product;

    // 广告投放：地域定向
    private String region;

    // 广告投放：是否匿名投放，0-不限制，1-只匿名，2-只非匿名
    private Integer anonymous;

    // 广告投放：性别定向
    private Integer sex;

    // 广告投放：新用户定向
    private Integer businessNewUser;

    // 广告投放：年龄定向
    private String age;

    // 广告投放：指定APP包名定向
    private String containsPkg;

    // 用户渠道定向
    private String userChannels;

    // 广告投放：指定不包含APP包名定向
    private String notContainsPkg;

    // 广告组创建时间
    private Date groupCreateTime;

    // 广告组最后一次更新时间
    private Date groupUpdateTime;

    // 广告投放：平台和版本定向（Android／iOS）
    private String platformVersion;

    // 广告投放：用户注册时间定向
    private String registerTime;

    // 广告投放：用户注册时长定向
    private String registLonger;

    // 广告投放：用户ID尾号定向
    private String tailNumber;

    // 广告投放：组号定向
    private Integer position;

    // 广告投放：用户总收入定向
    private String income;

    // 广告点击时间间隔限制
    private Integer clickInterval;

    // 广告日点击次数限制
    private Integer dayClickLimit;

    // ecp广告的广告位(由于数据库存储的是字符串，形如： 23,24 因此需要先用字符串接收)
    private String adPosStr;

//    // 广告位
//    private String adPos;
    // ======== 广告组 end ========

    // ======== 广告创意 start ========

    // 广告创意ID
    private Long originalityId;

    // 广告创意状态
    private Integer originalityState;

    // 广告创意名称
    private String originalityName;

    // 广告物料信息
    private String ext;

    // 广告点击单价／分享单价
    private Integer unitPrice;

    // 广告创意创建时间
    private Date originalityCreateTime;

    // 广告创意最后一次更新时间
    private Date originalityUpdateTime;

    // 广告创意一级分类
    private Integer firstCategoryId;

    // 广告创意二级分类
    private Integer secondCategoryId;
    // ======== 广告创意 end ========

    // 用户账户是否审核通过：1-通过
    private Integer accountState;
    // 广告创意是否审核通过：1-通过
    private Integer verifyState;
}
