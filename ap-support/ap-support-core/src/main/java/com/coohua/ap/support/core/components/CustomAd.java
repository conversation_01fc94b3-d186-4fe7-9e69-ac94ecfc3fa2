package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.configbean.CpcIndustryPriceZone;
import com.coohua.ap.base.configbean.TopAdPriceLevelConfig;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.CreativeIndustryTop;
import com.coohua.ap.base.constants.GlobalConfigKey;
import com.coohua.ap.base.domain.*;
import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.support.core.constants.DailyBudgetType;
import com.coohua.ap.support.core.container.CustomAdContainer;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.model.CustomAdModel;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 直客广告
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@ToString
public class CustomAd extends DefaultAd implements Comparable<CustomAd> {

    /**
     * 广告主ID
     */
    private Integer advertiserId;
    /**
     * 广告计划ID
     */
    private Long planId;
    /**
     * 广告组ID
     */
    private Long groupId;
    /**
     * 定向条件：是否投放匿名用户：0-不限制，1-只投放给匿名用户，2-只投放给非匿名用户
     */
    private Integer anonymous;
    /**
     * 定向条件：是否投放给指定性别用户性别：0-不限制，1-只投放给男性，2-只投放给女性
     */
    private Integer sex;
    /**
     * 定向条件：是否投放给新用户：0-不限制，1-只投放新用户，2-只投放老用户
     */
    private Integer businessNewUser;
    /**
     * 定向条件：是否投放给指定年龄段的用户：0：不限，1：17岁以下，2：18-24岁，3：25-34岁，4：35-44岁，5：45岁以上
     */
    private List<Integer> age;
    /**
     * 投放类型，1-匀速，2-加速
     */
    private Integer deliveryType;
    /**
     * 广告日预算控量类型：1-日消耗，2-日点击，3-日曝光
     */
    private DailyBudgetType dailyBudgetType;
    /**
     * 直客广告日预算
     */
    private Long dailyBudget;
    /**
     * 广告单价，单位：分
     */
    private Integer unitPrice;
    /**
     * 广告主最近一次结算时间
     */
    private Date balanceAccountTime;
    /**
     * 广告主余额（当日结算后，数据库中记录的余额）
     */
    private Long advertiserBalance;
    /**
     * 广告创意顶级分类
     *
     * @see CreativeIndustryTop
     */
    private CreativeIndustryTop industryTop;
    /**
     * 广告投放概率，例如：50，表示50%概率可以投放
     */
    private Integer hitProbability;
    /**
     * 直客广告详细物料信息
     */
    private CustomAdExt customAdExt;

    public CustomAd(CustomAdModel model, CustomAdContainer customAdContainer) {
        transfer(model, customAdContainer);
        this.created();
    }

    private void transfer(CustomAdModel model, CustomAdContainer customAdContainer) {
        this.adContainer = customAdContainer;
        this.adId = model.getOriginalityId();
        this.name = model.getOriginalityName();
        this.type = model.getType();
        this.adSource = AdSource.CUSTOM;
        this.state = model.getPlanState() == 1 && model.getGroupState() == 1 && model.getOriginalityState() == 1 && model.getAccountState() == 2 && model.getVerifyState() == 2 ? 1 : 0;
        this.createTime = model.getOriginalityUpdateTime();
        this.updateTime = findLatestDate(model);
        this.putDatePeriod = JSONObject.parseObject(model.getDeliveryDatePeriod(), PutDatePeriod.class);
        this.timeBucket = JSONObject.parseObject(model.getTimeBucket(), TimeBucket.class);
        this.adPos = convertCommaStringToIntListForAdPos(model.getAdPosStr());
        this.apps = convertCommaStringToIntList(model.getProduct());
        this.region = JSONObject.parseObject(model.getRegion(), Region.class);
        this.platformVersion = JSONObject.parseObject(model.getPlatformVersion(), PlatformVersion.class);
        this.containsPackages = JSONObject.parseArray(model.getContainsPkg(), String.class);
        this.notContainsPackages = JSONObject.parseArray(model.getNotContainsPkg(), String.class);
        this.userChannels = StringUtils.isBlank(model.getUserChannels()) ? new ArrayList<>() : JSONObject.parseArray(model.getUserChannels(),String.class);
        this.registerTimeOrientation = JSONObject.parseObject(model.getRegisterTime(), RegisterTimeOrientation.class);
        this.registLongerOrientation = StringUtils.isEmpty(model.getRegistLonger()) ? null : JSONObject.parseObject(model.getRegistLonger(), RegistLongerOrientation.class);
        this.tailNumber = StringUtils.isEmpty(model.getTailNumber()) ? new ArrayList<>() : Arrays.asList(model.getTailNumber().split(AdConstants.SYMBOL_COMMA));
        this.incomeOrientation = StringUtils.isEmpty(model.getIncome()) ? new IncomeOrientation().initDefault() : JSONObject.parseObject(model.getIncome(), IncomeOrientation.class);
        this.clickInterval = model.getClickInterval();
        this.dayClickLimit = model.getDayClickLimit();
        Budget budget = new Budget();
        budget.setFlag(model.getBudgetLimit());
        budget.setValue(model.getUnitPrice() == 0 ? model.getDailyBudget() : model.getDailyBudget() / model.getUnitPrice());
        this.budget = budget;
        this.deliveryType = 2; // 默认按照加速模式
        this.dailyBudgetType = getDailyBudgetType(model);
        this.dailyBudget = getDailyBudget(model.getDailyBudget(), this.dailyBudgetType);
        this.unitPrice = model.getUnitPrice();
        this.balanceAccountTime = model.getBalanceAccountTime();
        this.advertiserBalance = model.getBalance() + model.getCouponBalance();
        this.advertiserId = model.getAdvertiserId();
        this.planId = model.getPlanId();
        this.groupId = model.getGroupId();
        this.anonymous = model.getAnonymous() == null ? 0 : model.getAnonymous();
        this.sex = model.getSex() == null ? 0 : model.getSex();
        this.businessNewUser = model.getBusinessNewUser() == null ? 0 : model.getBusinessNewUser();
        this.age = StringUtils.isEmpty(model.getAge()) ? Lists.newArrayList(0) : JSONArray.parseArray(model.getAge(), Integer.class);
        this.industryTop = getAdIndustryTop(model.getFirstCategoryId(), model.getSecondCategoryId(), model.getType());
        this.customAdExt = new CustomAdExt(model.getExt());
        this.hitProbability = calculateProbability();
    }

    private CreativeIndustryTop getAdIndustryTop(Integer firstCategoryId, Integer secondCategoryId, int type) {
        if (type != AdType.ZHIKE_CPC.type) {
            return CreativeIndustryTop.ALL;
        }
        // 没有行业信息的情况 => 网赚
        if (firstCategoryId == null || secondCategoryId == null || firstCategoryId == 0 || secondCategoryId == 0) {
            return CreativeIndustryTop.WANG_ZHUAN;
        }
        if (secondCategoryId == AdConstants.INDUSTRY_TOP_WANG_ZHUAN) {
            return CreativeIndustryTop.WANG_ZHUAN;
        }
        if (firstCategoryId == AdConstants.INDUSTRY_TOP_FEI_ZHENG_QI) {
            return CreativeIndustryTop.FEI_ZHENG_QI;
        }

        return CreativeIndustryTop.ZHENG_QI;
    }

    private long getDailyBudget(long dailyBudget, DailyBudgetType dailyBudgetType) {
        if (dailyBudgetType == DailyBudgetType.EXPOSURE) {
            // ecp系统的单位为千次曝光,但数据库以分为单位存储(乘100)，因此转换为曝光数时，预算值 ➗ 100 ✖️ 1000
            dailyBudget = dailyBudget * 10;
        } else if (dailyBudgetType == DailyBudgetType.CLICK) {
            dailyBudget = dailyBudget / 100;
        }
        return dailyBudget;
    }

    private DailyBudgetType getDailyBudgetType(CustomAdModel model) {
        DailyBudgetType type = DailyBudgetType.COST;
        if (model.getType() == AdType.ZHIKE_DDZ.type || model.getType() == AdType.ZHIKE_READ60.type) {
            type = DailyBudgetType.CLICK;
        } else if (model.getType() == AdType.ZHIKE_OPEN_SCREEN.type) {
            type = DailyBudgetType.EXPOSURE;
        }
        return type;
    }

    private Date findLatestDate(CustomAdModel model) {
        Date maxDate = model.getPlanUpdateTime();
        if (DateUtils.compare(maxDate, model.getGroupUpdateTime()) < 0) {
            maxDate = model.getGroupUpdateTime();
        }
        if (DateUtils.compare(maxDate, model.getOriginalityUpdateTime()) < 0) {
            maxDate = model.getOriginalityUpdateTime();
        }
        return maxDate;
    }

    /**
     * 检查广告是否满足小时控量要求
     * @param hourBudget 广告当前小时已出量
     * @param hourLimit 每小时出量限制
     * @param hourNum 要均分的小时数
     * @return true - 有效，false - 无效
     */
    public boolean isHourBudgetValid(Map<Long, Long> hourBudget, Long hourLimit, Long hourNum) {
        Long adHourClick = hourBudget.get(adId);
        if (adHourClick == null || adHourClick <= 0) {
            return true;
        }

        if (budget.getValue() > 0) {
            hourLimit = Math.min(hourLimit, budget.getValue() / hourNum);
        }

        return adHourClick < hourLimit;
    }

    /**
     * 检查广告是否满足日控量要求
     * @param adPlanCost 按广告消耗控量，当前广告的日消耗
     * @param adPlanClick 按广告点击控量，当前广告的日点击
     * @param adPlanExposure 按广告曝光控量，当前广告的日曝光
     * @return true - 有效，false - 无效
     */
    public boolean isDailyBudgetValid(Map<Long, Long> adPlanCost, Map<Long, Long> adPlanClick, Map<Long, Long> adPlanExposure) {
        if (DailyBudgetType.COST == this.dailyBudgetType) {
            return isCostDailyBudgetValid(adPlanCost);
        }
        if (DailyBudgetType.CLICK == this.dailyBudgetType) {
            return isClickDailyBudgetValid(adPlanClick);
        }
        if (DailyBudgetType.EXPOSURE == this.dailyBudgetType) {
            return isExposureDailyBudgetValid(adPlanExposure);
        }
        return false;
    }

    /**
     * 检查CPW小程序广告是否满足控频投放要求
     * @return true - 有效，false - 无效
     */
    public boolean isMiniProgramCpwFrequencyLimitValid(String miniProgramNotLimitAdIds, Map<String, String> userMiniProgramFrequency, Map<Long, Long> miniProgramCpwFrequencyPlus,
                                                       Long miniProgramCpwFrequency, Long miniProgramCpwFrequencyShortTime) {
        if (this.type == AdType.ZHIKE_MP_CPW.type || (this.type == AdType.ZHIKE_CPC.type && this.customAdExt.getIsMiniProgram() == 1)) {
            if (StringUtils.isEmpty(this.customAdExt.getMiniProgramId())) {
                return false;
            }
            if (StringUtils.isNotEmpty(miniProgramNotLimitAdIds) && miniProgramNotLimitAdIds.contains(this.adId.toString())) {
                return true;
            }
            String lastClickTimeStr = userMiniProgramFrequency.get(this.customAdExt.getMiniProgramId());
            if (StringUtils.isEmpty(lastClickTimeStr)) {
                return true;
            }
            long lastClickTime = Long.parseLong(lastClickTimeStr);
            // 控频需特殊处理的小程序广告
            if (miniProgramCpwFrequencyPlus.containsKey(this.adId)) {
                if (System.currentTimeMillis() - lastClickTime > miniProgramCpwFrequencyPlus.get(this.adId)) {
                    return true;
                }
            }

            long miniProgramCpwFrequencyControl = this.customAdExt.getMiniNonRepeatPeriod() == 0
                    ? miniProgramCpwFrequency
                    : miniProgramCpwFrequencyShortTime;
            // 有点击的情况下，判断是否超时
            long gapHour = (System.currentTimeMillis() - lastClickTime) / 1000 / 60 / 60; // 小时为单位
            return gapHour > miniProgramCpwFrequencyControl;
        } else {
            return true;
        }
    }

    /**
     * 判断这个广告是否是小程序广告
     * @return true - 是，false - 不是
     */
    public boolean isMiniProgramAd() {
        return this.type == AdType.ZHIKE_MP_CPW.type || (this.type == AdType.ZHIKE_CPC.type && this.customAdExt.getIsMiniProgram() == 1);
    }

    private boolean isExposureDailyBudgetValid(Map<Long, Long> adPlanExposure) {
        return checkDailyBudgetValid(adPlanExposure.get(planId));
    }

    private boolean isClickDailyBudgetValid(Map<Long, Long> adPlanClick) {
        return checkDailyBudgetValid(adPlanClick.get(planId));
    }

    private boolean isCostDailyBudgetValid(Map<Long, Long> adPlanCost) {
        return checkDailyBudgetValid(adPlanCost.get(planId));
    }

    private boolean checkDailyBudgetValid(Long count) {
        if (count == null) {
            return true;
        }

        if (dailyBudget > 0 && count < dailyBudget) {
            return true;
        } else if (dailyBudget <= 0) {
            return false;
        } else {
            return false;
        }
    }

    public boolean isCpwMiniProgramUnbind() {
        // 只过滤CPW小程序广告，其他的广告直接放行
        if (type == AdType.ZHIKE_MP_CPW.type || (type == AdType.ZHIKE_CPC.type && customAdExt.getIsMiniProgram() == 1)) {
            return customAdExt.getMiniBindMiddlePage() == null || customAdExt.getMiniBindMiddlePage();
        } else {
            return true;
        }
    }

    private int calculateProbability() {
        // 2019-02-22 开屏广告因为不按点击计费，所以不受广告主余额低而导致的出量限速问题
        if (AdType.ZHIKE_OPEN_SCREEN.type == this.type) {
            return 100;
        }

        if (this.budget.getFlag() == AdConstants.NO_BUDGET_LIMIT) {
            return 100;
        }

        if (this.advertiserBalance <= 0) {
            return 0; // 余额不足，不投放
        }

        Map<Long, Integer> balanceMap = new HashMap<>();
        JSONArray array = JSONArray.parseArray(ContainerHolder.getGlobalConfigContainer().getConfig(App.MASTER.appId(), GlobalConfigKey.TOP_AD_HIT_LEVEL));
        for (int i = 0; i < array.size(); i++) {
            JSONObject data = array.getJSONObject(i);
            balanceMap.put(data.getLong("money"), data.getInteger("probability"));
        }

        int balanceProp = 100;
        Set<Long> keys = balanceMap.keySet();
        List<Long> ks = new ArrayList<>(keys);
        Collections.sort(ks); // 正序排序，优先匹配 配置中最小的
        for (Long level : ks) {
            if (this.advertiserBalance <= level) {
                balanceProp = balanceMap.get(level);
                break;
            }
        }

        TopAdPriceLevelConfig topAdPriceLevelConfig
                = ContainerHolder.getGlobalConfigContainer().getConfig(App.MASTER.appId(), GlobalConfigKey.TOP_AD_PRICE_LEVEL, TopAdPriceLevelConfig.class);

        CpcIndustryPriceZone cpcIndustryPriceZone
                = ContainerHolder.getGlobalConfigContainer().getConfig(App.MASTER.appId(), GlobalConfigKey.CPC_INDUSTRY_PRICE_ZONE, CpcIndustryPriceZone.class);

        int priceProp = 100;
        if (AdType.ZHIKE_CPC.type == this.type && this.customAdExt.getIsMiniProgram() != 1) {
            int industryMaxPrice = topAdPriceLevelConfig.getMaxPrice();
            int industryMinPrice = topAdPriceLevelConfig.getMinPrice();
            if (industryTop == CreativeIndustryTop.WANG_ZHUAN) {
                industryMaxPrice = cpcIndustryPriceZone.getWangZhuan().get(1);
                industryMinPrice = cpcIndustryPriceZone.getWangZhuan().get(0);
            } else if (industryTop == CreativeIndustryTop.FEI_ZHENG_QI) {
                industryMaxPrice = cpcIndustryPriceZone.getFeiZhengQi().get(1);
                industryMinPrice = cpcIndustryPriceZone.getFeiZhengQi().get(0);
            } else if (industryTop == CreativeIndustryTop.ZHENG_QI) {
                industryMaxPrice = cpcIndustryPriceZone.getZhengQi().get(1);
                industryMinPrice = cpcIndustryPriceZone.getZhengQi().get(0);
            }
            if (unitPrice >= industryMaxPrice) {
                priceProp = 100;
            } else if (unitPrice >= industryMinPrice) {
                priceProp = 100 * (unitPrice - industryMinPrice) / (industryMaxPrice - industryMinPrice);
            } else {
                priceProp = 0;
            }
        } else if ((AdType.ZHIKE_MP_CPW.type == this.type)
                || (AdType.ZHIKE_CPC.type == this.type && this.customAdExt.getIsMiniProgram() == 1)) {
            if (unitPrice <= topAdPriceLevelConfig.getMinMiniProgramPrice()) {
                priceProp = 0;
            }
        } else if (AdType.ZHIKE_DOWNLOAD.type == this.type) {
            if (unitPrice <= topAdPriceLevelConfig.getMinCpdPrice()) {
                priceProp = 0;
            }
        }

        int prop = balanceProp * priceProp / 100;

        // 计算日预算投放概率系数
        Integer baseNumber = ContainerHolder.getGlobalConfigContainer().getConfig(App.MASTER.appId(), GlobalConfigKey.DAILY_BUDGET_PROBABILITY_BASE_NUMBER, Integer.class);
        float dailyBudgetProp = dailyBudget.floatValue() / baseNumber.floatValue();
        if (dailyBudgetProp > 1.0F) {
            dailyBudgetProp = 1.0F;
        }
        float result = (float) prop * dailyBudgetProp;
        return (int) result;
    }

    @Override
    public int compareTo(CustomAd other) {
        return this.unitPrice.equals(other.getUnitPrice()) ? 0 : this.unitPrice > other.getUnitPrice() ? -1 : 1;
    }

    @Override
    public boolean isClickInvervalValid(Map<Long, Long> allAdInterval) {
        if (clickInterval == AdConstants.AD_PUT_NO_CLICK_INTERVAL_LIMIT) {
            return true;
        }

        Long userClickTimestamp = allAdInterval.get(adId);
        if (userClickTimestamp == null || userClickTimestamp == 0L) {
            return true;
        }
        /**
         *     原点击时间间隔逻辑，从数据库中获取点击时间间隔，
         *     return System.currentTimeMillis() - userClickTimestamp > clickInterval * 60 * 1000;
         *     暂时调整为点击时间间隔为两分钟
         *     时间：2020.7.1
         *     备注：测直客cpc静态图广告出量逻辑，受到限制
         *     产品：龚涛
         **/
        return System.currentTimeMillis() - userClickTimestamp > 2 * 60 * 1000;
    }

    @Override
    public boolean isDailyClickLimitValid(Map<Long, Integer> allExpend) {
        if (dayClickLimit == AdConstants.AD_PUT_NO_DAY_CLICK_LIMIT) {
            return true;
        }

        Integer userDayClick = allExpend.get(adId);
        if (userDayClick == null || userDayClick == 0) {
            return true;
        }

        /**
         *     原用户日点击次数
         *     return userDayClick < dayClickLimit;
         *     暂时调整为点击日点击次数为5次
         *     时间：2020.7.1
         *     备注：测直客cpc静态图广告出量逻辑，受到限制
         *     产品：龚涛
         *
         *     修改回限制原次数，因广告点击次数支持可配置
         *     时间：2020.7.16
         *     产品：季晓乐
         **/
        return userDayClick<dayClickLimit;
    }
}
