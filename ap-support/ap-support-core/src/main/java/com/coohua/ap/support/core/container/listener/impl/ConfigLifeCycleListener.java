package com.coohua.ap.support.core.container.listener.impl;

import com.coohua.ap.support.core.container.event.ConfigEvent;
import com.coohua.ap.support.core.container.event.ConfigEventType;
import com.coohua.ap.support.core.container.listener.ConfigListener;
import lombok.extern.slf4j.Slf4j;

/**
 * 配置生命周期事件监听器
 * <AUTHOR>
 */
@Slf4j
public class ConfigLifeCycleListener implements ConfigListener {
    @Override
    public void onEvent(ConfigEvent configEvent) {
        if (configEvent.getType() == ConfigEventType.CREATE) {
            log.info(configEvent.getType().name() + " created ... ");
        } else if (configEvent.getType() == ConfigEventType.UPDATE) {
            log.info(configEvent.getType().name() + " updated ... ");
        }
    }
}
