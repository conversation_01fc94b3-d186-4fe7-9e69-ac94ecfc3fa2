package com.coohua.ap.support.core.valves;

import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/10
 */
public abstract class DefaultValve implements Valve {

    private Valve next;

    @Override
    public Valve getNext() {
        return next;
    }

    @Override
    public void setNext(Valve valve) {
        this.next = valve;
    }

    @Override
    public void invoke(List<Long> candidates, Object userInformation) {
        doInvoke(candidates, userInformation);
        if (getNext() != null && !CollectionUtils.isEmpty(candidates)) {
            getNext().invoke(candidates, userInformation);
        }
    }

    protected abstract void doInvoke(List<Long> candidates, Object userInformation);
}
