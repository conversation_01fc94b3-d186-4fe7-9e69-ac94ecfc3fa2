package com.coohua.ap.support.core.container.listener.impl;

import com.coohua.ap.support.core.container.event.AdEvent;
import com.coohua.ap.support.core.container.event.AdEventType;
import com.coohua.ap.support.core.container.listener.AdListener;
import lombok.extern.slf4j.Slf4j;

/**
 * 广告行为事件监听器
 * <AUTHOR>
 */
@Slf4j
public class AdActionListener implements AdListener {
    @Override
    public void onAdEvent(AdEvent event) {
        if (event.getType() == AdEventType.ON_CLICK) {
//            log.info("ad click ... ");
        } else if (event.getType() == AdEventType.ON_EXPOSURE) {
//            log.info("ad exposure ... ");
        } else if (event.getType() == AdEventType.ON_CANDIDATE) {
            log.info("ad candidate ... ");
        } else if (event.getType() == AdEventType.ON_SERVING) {
            log.info("ad serving ... ");
        } else if (event.getType() == AdEventType.FILTERED) {
            log.info("ad filtered ... ");
        }
    }
}
