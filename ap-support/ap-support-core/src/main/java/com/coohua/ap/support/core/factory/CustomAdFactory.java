package com.coohua.ap.support.core.factory;


import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.mapper.custom.CustomAdMapper;
import com.coohua.ap.support.core.spring.model.CustomAdModel;

import java.util.List;

/**
 * 直客广告工厂
 * <AUTHOR>
 */
public class CustomAdFactory implements AdFactory {

    private CustomAdMapper customAdMapper;

    private static class CustomAdFactoryHolder {
        private static final CustomAdFactory INSTANCE = new CustomAdFactory();
    }

    private CustomAdFactory() {
        this.customAdMapper = SpringIoCUtils.getCustomAdMapper();
    }

    public static CustomAdFactory getInstance() {
        return CustomAdFactoryHolder.INSTANCE;
    }

    public CustomAdModel getNewestAd(Long adId) {
        if (adId == null) {
            return null;
        }
        return customAdMapper.getById(adId);
    }

    public List<CustomAdModel> getNewestAdByType(Integer adType) {
        if (adType == null) {
            return null;
        }
        return customAdMapper.getByType(adType);
    }

    public List<CustomAdModel> getAllNewestAd() {
        return customAdMapper.getAll();
    }
}
