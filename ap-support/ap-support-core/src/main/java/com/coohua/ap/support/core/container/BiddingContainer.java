package com.coohua.ap.support.core.container;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.AdTypeConvertVo;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ThirdPlatformType;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.components.Ad;
import com.coohua.ap.support.core.components.BiddingAd;
import com.coohua.ap.support.core.components.BiddingApp;
import com.coohua.ap.support.core.factory.ThirdAdFactory;
import com.coohua.ap.support.core.spring.model.BiddingAppModel;
import com.coohua.ap.support.core.spring.model.BiddingModel;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;
import com.coohua.ap.support.core.valves.impl.bidding.*;
import com.pepper.metrics.core.ThreadFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
@SpiMeta(name = BiddingContainer.CONTAINER_NAME)
public class BiddingContainer extends DefaultAdContainer{

    public static final String CONTAINER_NAME = "BiddingContainer"; // 容器名称


    private volatile Map<Long,BiddingAd> biddingAdMap = new ConcurrentHashMap<>();
    // AppId -> 该应用下有效的Bidding广告位,原则上一个AdType 只有一个广告位
    private volatile Map<Integer,List<BiddingAd>> biddingAdMapper = new ConcurrentHashMap<>();

    // AppId -> 该应用下可用的Bidding应用-用于广点通 单产品-os下 只有一个应用
    private volatile Map<Integer,BiddingApp> biddingAppMapper = new ConcurrentHashMap<>();

    private volatile List<Long> gdtAdIdList = new ArrayList<>();
    private volatile List<Long> biddingList = new ArrayList<>();


    public BiddingApp getBiddingApp(Integer product){
        return biddingAppMapper.get(product);
    }

    public List<BiddingAd> getBiddingAd(Integer product, UserInfoBidding userInfoBidding){
        List<BiddingAd> biddingAdList = biddingAdMapper.get(product);

        if (biddingAdList == null || biddingAdList.size() == 0){
            return null;
        }

        List<Long> preQueue = biddingAdList.stream().map(BiddingAd::getId).collect(Collectors.toList());
        // 过滤
        this.valveChain.getFirst().invoke(preQueue, userInfoBidding);

        return biddingAdList.stream().filter(r -> preQueue.contains(r.getId())).collect(Collectors.toList());
    }

    public boolean isBiddingAd(Long adId){
        return this.biddingList.contains(adId);
    }

    public boolean isGdtAds(Long adId){
        return this.gdtAdIdList.contains(adId);
    }

    @Override
    void initValveChain() {
        this.valveChain = new DefaultValveChain();
        this.valveChain.setFirst(new SwitchFlagValve())
                .addValve(new VersionValve())
                .addValve(new ChannelValve())
                .addValve(new BrandValve())
                .addValve(new RegionValve())
                .addValve(new SkipPlatformValve())
                .addValve(new UserValve())
                .addValve(new UserDspValve())
                .addValve(new TestAdAccountValve());
    }

    @Override
    void refreshAdContainer() {
        if (adFactory instanceof ThirdAdFactory){
            ThirdAdFactory thirdAdFactory = (ThirdAdFactory) adFactory;
            List<BiddingModel> biddingModelList = thirdAdFactory.getAllBiddingPosOpen();
            // 未删除都认为是 biddingAd 修复关闭导致的缓存广告识别异常
            this.biddingList = thirdAdFactory.getAlBidding();
            List<BiddingAd> biddingAdList = biddingModelList.stream().map(BiddingAd::new).collect(Collectors.toList());

            List<BiddingAd> biddingAdFilterList = biddingAdList.stream()
                    .filter(new SwitchFlagValve())
                    .collect(Collectors.toList());

            this.biddingAdMap = biddingAdFilterList.stream().collect(Collectors.toMap(BiddingAd::getId,r->r,(r1,r2)->r1));
            this.biddingAdMapper = biddingAdFilterList.stream().collect(Collectors.groupingBy(BiddingAd::getProduct));

            List<BiddingAppModel> biddingAppModels = thirdAdFactory.getAllBiddingApp();
            this.biddingAppMapper = biddingAppModels.stream().map(biddingAppModel -> {
                BiddingApp biddingApp = new BiddingApp();
                biddingApp.setProduct(biddingAppModel.getProduct());
                biddingApp.setAppName(biddingAppModel.getAppName());
                biddingApp.setPkgName(getConfigKey(biddingAppModel.getConfig()));
                BiddingAd biddingAd = biddingAdMapper.get(biddingAppModel.getProduct()).stream()
                        .filter(bidAd -> ThirdPlatformType.GDT.getCode().equals(bidAd.getPlatform()))
                        .findFirst()
                        .orElse(new BiddingAd());
                biddingApp.setAppId(biddingAd.getAppId());
                return biddingApp;
            }).collect(Collectors.toMap(BiddingApp::getProduct,r->r,(r1,r2)->r1));

            Map<Integer, AdTypeConvertVo> adTypeConvertVoMap = AdTypeSub.adTypeMapFl;
            List<Integer> gdtList = new ArrayList<>();
            adTypeConvertVoMap.forEach((k,v) -> {
                if (ThirdPlatformType.GDT.getDesc().equals(v.getAdSource())){
                    gdtList.add(k);
                }
            });

            this.gdtAdIdList = thirdAdFactory.getGdtAdIds(gdtList,new ArrayList<>(this.biddingAppMapper.keySet()));
        }
    }

    private String getConfigKey(String config){
        return JSONObject.parseObject(config).keySet().stream().findFirst().orElse(StringUtils.EMPTY);
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new ThreadFactory("BiddingContainerScheduledThread");
    }

    @Override
    boolean isMyAdType(int adType) {
        return false;
    }

    @Override
    public Ad getAd(Long adId) {
       return null;
    }

    public BiddingAd getBidding(Long id){
        return biddingAdMap.get(id);
    }

    @Override
    public List<Long> getAdCollectionByAdType(Integer adType, Integer appId) {
        return null;
    }

    @Override
    public void initAdFactory() {
        this.adFactory = ThirdAdFactory.getInstance();
    }

    @Override
    public void setAdMessageHandler() {

    }

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }
}
