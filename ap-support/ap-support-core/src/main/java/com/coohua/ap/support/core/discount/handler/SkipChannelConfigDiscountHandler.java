package com.coohua.ap.support.core.discount.handler;

import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 跳过渠道过滤
 */
public class SkipChannelConfigDiscountHandler extends AbstractConfigDiscountHandler {

    @Override
    protected void doFilter(List<Long> candidates, Map<Long, ApConfigDiscountVo> discountMap, UserMetaForStrategyConfig userInformation) {
        if (StringUtils.isEmpty(userInformation.getChannelId())) {
            return;
        }
        candidates.removeIf(configId -> discountMap.get(configId).isSkipChannelIdValid(userInformation.getChannelId()));
    }
}
