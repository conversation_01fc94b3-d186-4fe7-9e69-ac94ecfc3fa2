package com.coohua.ap.support.core.valves.impl.newcustom;

import com.coohua.ap.support.core.components.NewCustomAd;
import com.coohua.ap.support.core.constants.SwitchNormal;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9
 * 新后台-开关控制链
 */
public class SwitchAdValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            candidates.removeIf(adId -> !isSwitchOn(adId));
        }
    }

    private boolean isSwitchOn(Long adId){
        NewCustomAd newCustomAd = newCustomAdContainer.getAd(adId);

        return SwitchNormal.ON.status.equals(newCustomAd.getPlanSwitch())
                && SwitchNormal.ON.status.equals(newCustomAd.getIdeaSwitch())
                && SwitchNormal.ON.status.equals(newCustomAd.getAccountFlag());
    }
}
