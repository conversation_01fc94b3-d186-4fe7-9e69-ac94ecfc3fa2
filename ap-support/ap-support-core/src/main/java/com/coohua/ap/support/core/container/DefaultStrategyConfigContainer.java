package com.coohua.ap.support.core.container;

import com.coohua.ap.base.constants.AdConfigType;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.base.domain.*;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.components.StrategyBaseConfig;
import com.coohua.ap.support.core.components.StrategyOrientationConfig;
import com.coohua.ap.support.core.container.thread.StrategyConfigContainerScheduledThreadFactory;
import com.coohua.ap.support.core.domain.FilteredAdDefaultTypeStrategyConfig;
import com.coohua.ap.support.core.domain.FilteredAdTypeStrategyConfig;
import com.coohua.ap.support.core.domain.FilteredStrategyConfig;
import com.coohua.ap.support.core.factory.StrategyConfigFactory;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.config.PreQueryConfig;
import com.coohua.ap.support.core.spring.model.AdPosModel;
import com.coohua.ap.support.core.spring.model.StrategyBaseConfigModel;
import com.coohua.ap.support.core.spring.model.StrategyOrientationConfigModel;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import com.coohua.ap.support.core.valves.chain.ValveChain;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import com.coohua.ap.support.core.valves.impl.config.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 默认的策略配置容器实现
 * <AUTHOR>
 */
@SpiMeta(name = DefaultStrategyConfigContainer.CONTAINER_NAME)
@Slf4j
public class DefaultStrategyConfigContainer extends DefaultConfigContainer implements StrategyConfigContainer {

    private static final long FIXED_RATE = 1000 * 60 * 2;

    public static final String CONTAINER_NAME = "DefaultStrategyConfigContainer"; // 容器名称

    // 配置项
    private ConcurrentHashMap<Long, StrategyBaseConfig> baseConfigHolder = new ConcurrentHashMap<>();

    // 策略配置
    private ConcurrentHashMap<Integer, List<Long>> orientationConfigHolder = new ConcurrentHashMap<>();

    private ConcurrentHashMap<Long, StrategyOrientationConfig> orientationConfigMapping = new ConcurrentHashMap<>();

    ValveChain valveChain;

    @Override
    public void init() {
        super.init();
        initStrategyConfigFilterChain();
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new StrategyConfigContainerScheduledThreadFactory();
    }

    @Override
    void refreshConfigContainer() {
        long start = System.currentTimeMillis();
        if (configFactory instanceof StrategyConfigFactory) {
            StrategyConfigFactory factory = (StrategyConfigFactory) configFactory;
            updateBaseConfig(factory);
            updateOrientationConfig(factory);
        }
        if (this.getState() == ContainerState.NOT_INIT){
            SpringIoCUtils.getBean(PreQueryConfig.class).refreshPosNeedInfos();
        }
        log.info("策略配置容器刷新完成，耗时：{}ms", System.currentTimeMillis() - start);
//        System.out.println("策略配置容器刷新完成，耗时：" + (System.currentTimeMillis() - start) + "ms");
    }

    @Override
    protected long getFixedRate() {
        return FIXED_RATE;
    }

    @Override
    public void initConfigFactory() {
        this.configFactory = StrategyConfigFactory.getInstance();
    }

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }

    @Override
    public FilteredStrategyConfig chooseOptimalStrategy(Integer posId, UserMetaForStrategyConfig userMeta) {
        if (posId == null) {
            throw new BusinessException(400, "Get strategy config, but posId is null.");
        }

        // 必须要浅拷贝一下，否则会移除调全局变量[configOrientationHolder]的元素
        if (this.orientationConfigHolder.get(posId) == null) {
            return null;
        }

        List<Long> fullList = new ArrayList<>(this.orientationConfigHolder.get(posId));
        this.valveChain.getFirst().invoke(fullList, userMeta);
        if (CollectionUtils.isEmpty(fullList)) {
            // 筛选完策略无 兜底一个
            List<Long> defaultList = new ArrayList<>(this.orientationConfigHolder.get(posId));
            if (defaultList.size() == 0){
                return null;
            }
            List<StrategyOrientationConfig> resultList = convertIdToObj(defaultList);
            StrategyOrientationConfig result;
            if (defaultList.size() > 1){
                resultList = resultList.stream().filter(r -> r.getPriority() <=3).collect(Collectors.toList());
                if (resultList.size() == 0){
                    return null;
                }
                Collections.sort(resultList);
            }
            result = resultList.get(0);
            List<AdPosModel> adPosModels = ContainerHolder.getThirdAdContainer().getAdPosList(userMeta.getAppId());
            if (adPosModels != null && adPosModels.size() > 0){
                AdPosModel jl = adPosModels.stream().filter(r ->r.getName().contains("激励视频")).findFirst().orElse(new AdPosModel());
                if (!jl.getId().equals(result.getAdPos())){
                    // TODO 补充报警
                    return null;
                }
            }

            return convertToFiltered(result);
        }

        List<StrategyOrientationConfig> resultList = convertIdToObj(fullList);

        // 排序
        Collections.sort(resultList);
        StrategyOrientationConfig result = resultList.get(0); // 选取top 1
//        log.info("策略筛选结果：appId={}, posId={}, userId={}, id={}", userMeta.getAppId(), userMeta.getPosId(), userMeta.getUserId(), result.getId());
        Map<Integer,Map<Integer,Map<Integer,Map<Integer,List<String>>>>> appABIsBiddingTypeMap = userMeta.getAppABIsBiddingTypeMap();
        try{
            // ab测试 新老用户策略
            boolean isOldUser = false;
            try {
                if (userMeta.getRegisterDate() != null) {
                    isOldUser = userMeta.getRegisterDate().before(dateIncreaseByDay(new Date(), -7));
                    Long adABTestStrategyId = getAdABTestStrategyByUser(posId, userMeta, isOldUser);
                    if (adABTestStrategyId != null) {
                        List<StrategyOrientationConfig> strategyOrientationConfigs = resultList.stream().filter(bean -> adABTestStrategyId.equals(bean.getId())).collect(Collectors.toList());
                        if (!strategyOrientationConfigs.isEmpty()) {
                            return convertToFiltered(strategyOrientationConfigs.get(0));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("新老用户策略失败 {}", e.getMessage(), e);
            }

//            log.info("老用户特殊处理 开始 appABIsBiddingTypeMap : {} userMeta : {}", appABIsBiddingTypeMap, userMeta);
            //TODO gyj
            if(appABIsBiddingTypeMap != null
                && appABIsBiddingTypeMap.containsKey(userMeta.getAppId())
                && appABIsBiddingTypeMap.get(userMeta.getAppId()).containsKey(userMeta.getStrategyId())
                && appABIsBiddingTypeMap.get(userMeta.getAppId()).get(userMeta.getStrategyId()).containsKey(posId)
                && appABIsBiddingTypeMap.get(userMeta.getAppId()).get(userMeta.getStrategyId()).get(posId).containsKey(0)
            ){
                if(isOldUser){
                    List<StrategyOrientationConfig> strategyOrientationConfigs = resultList.stream().filter(bean -> {
                        return appABIsBiddingTypeMap.get(userMeta.getAppId()).get(userMeta.getStrategyId()).get(posId).get(0).contains(String.valueOf(bean.getId()));
                    }).collect(Collectors.toList());
                    if (strategyOrientationConfigs.isEmpty()) {
                        log.info("老用户特殊策略为空，走默认策略 appId {}, userId {}", userMeta.getAppId(), userMeta.getUserId());
                    } else {
                        return convertToFiltered(strategyOrientationConfigs.get(0));
                    }
                }
            }
        }catch (Exception e) {
            log.error("老用户特殊处理 appId {}, userId {}", userMeta.getAppId(), userMeta.getUserId(), e);
        }
        // 封装返回结果
        return convertToFiltered(result);
    }

    public FilteredStrategyConfig chooseOptimalStrategyForLockAd(Integer posId, UserMetaForStrategyConfig userMeta) {
        if (posId == null) {
            throw new BusinessException(400, "Get strategy config, but posId is null.");
        }

        if (this.orientationConfigHolder.get(posId) == null) {
            return null;
        }

        List<Long> fullList = new ArrayList<>(this.orientationConfigHolder.get(posId));
        this.valveChain.getFirst().invoke(fullList, userMeta);

        if (CollectionUtils.isEmpty(fullList)) {
            return null;
        }

        List<StrategyOrientationConfig> resultList = convertIdToObj(fullList);

        // 排序
        Collections.sort(resultList);
        StrategyOrientationConfig result = resultList.get(0); // 选取top 1

        // 封装返回结果
        return convertToFiltered(result);
    }

    private Long getAdABTestStrategyByUser(Integer posId, UserMetaForStrategyConfig userMeta, boolean isOldUser) {
        Map<Integer, Map<Integer, Map<String, Object>>> adABTestStrategyMap = userMeta.getAdABTestStrategyMap();

        if (MapUtils.isEmpty(adABTestStrategyMap)
                || !adABTestStrategyMap.containsKey(userMeta.getAppId())) return null;

        Map<Integer, Map<String, Object>> abStrategyMap = adABTestStrategyMap.get(userMeta.getAppId());

        Map<String, Object> adPosStrategyMap = abStrategyMap.get(userMeta.getStrategyId());

        if (MapUtils.isEmpty(adPosStrategyMap)) return null;

        Map<String, Map<Integer, String>> posStrategyMap = (Map<String, Map<Integer, String>>) adPosStrategyMap.get("waterFall");

        Map<Integer, String> posStrategy = new HashMap<>();
        if (isOldUser) {
            posStrategy = posStrategyMap.get("old");
        } else {
            posStrategy = posStrategyMap.get("new");
        }
        if (posStrategy.containsKey(String.valueOf(posId))) {
            return Long.valueOf(posStrategy.get(String.valueOf(posId)));
        }

        return null;
    }

    public static Date dateIncreaseByDay(Date date, int days) {

        Calendar cal = GregorianCalendar.getInstance(TimeZone
                .getTimeZone("GMT"));
        cal.setTime(date);
        cal.add(Calendar.DATE, days);

        return cal.getTime();
    }

    private FilteredStrategyConfig convertToFiltered(StrategyOrientationConfig result){
        FilteredStrategyConfig filteredStrategyConfig = new FilteredStrategyConfig();
        filteredStrategyConfig.setNtfFilter(result.needFilterNtfPlatform());
        filteredStrategyConfig.setSkipPlatform(Collections.singletonList(result.skipPlatform()));
        filteredStrategyConfig.setStrategyId(result.getId());
        filteredStrategyConfig.setCategoryId(result.getId());

        if (result.isNewConfig()) {
            filteredStrategyConfig.setFilteredAdTypeConfig(parseFilterConfAll(result.getBaseConfigIdByConfigType(ConfigType.PLACEMENT.code())));
            filteredStrategyConfig.setFilteredAdDefaultTypeConfig(parseFilterConf(result.getBaseConfigIdByConfigType(ConfigType.PLACEMENT.code())));
            filteredStrategyConfig.setFilteredAppDefaultTypeConfig(parseFilterConf(result.getBaseConfigIdByConfigType(ConfigType.PLACEMENT.code())));
            filteredStrategyConfig.setFilteredAppDefaultTypeGoldConfig(parseFilterConf(result.getBaseConfigIdByConfigType(ConfigType.PLACEMENT.code())));
        }else {
            filteredStrategyConfig.setFilteredAdTypeConfig(parseFilteredAdTypeConfig(result.getBaseConfigIdByConfigType(ConfigType.AD_CONFIG.code())));
            filteredStrategyConfig.setFilteredAdDefaultTypeConfig(parsesFilteredAdDefaultTypeConfig(result.getBaseConfigIdByConfigType(ConfigType.AD_DEFAULT_CONFIG.code())));
            filteredStrategyConfig.setFilteredAppDefaultTypeConfig(parsesFilteredAdDefaultTypeConfig(result.getBaseConfigIdByConfigType(ConfigType.APP_DEFAULT_CONFIG.code())));
            filteredStrategyConfig.setFilteredAppDefaultTypeGoldConfig(parsesFilteredAdDefaultTypeConfig(result.getBaseConfigIdByConfigType(ConfigType.APP_DEFAULT_GOLD_CONFIG.code())));
        }

        return filteredStrategyConfig;
    }

    public StrategyOrientationConfig getStrategyOrientationConfig(Long id) {
        return id == null ? null : this.orientationConfigMapping.get(id);
    }

    private List<StrategyOrientationConfig> convertIdToObj(List<Long> fullList) {
        List<StrategyOrientationConfig> ret = new ArrayList<>();
        for (Long id : fullList) {
            ret.add(orientationConfigMapping.get(id));
        }
        return ret;
    }

    private List<FilteredAdTypeStrategyConfig> parseFilterConfAll(Long id){
        List<FilteredAdTypeStrategyConfig> returnArrayList = new ArrayList<>();
        // 获取投放配置（顶层）
        if (id == null) {
            return null;
        }

        StrategyBaseConfig baseConfig = this.baseConfigHolder.get(id);
        if (baseConfig == null) {
            return null;
        }
        Object config = baseConfig.getConfig();

        AdConfigNewDomain adConfigNewDomain = null;
        if (config instanceof AdConfigNewDomain) {
            adConfigNewDomain = (AdConfigNewDomain) config;
        }

        if (adConfigNewDomain == null){
            return null;
        }
        for (AdConfigNewItemDomain itemDomain : adConfigNewDomain.getDomainList()) {
            if (AdConfigType.TOP_CONFIG.getCode().equals(itemDomain.getConfigType())) {
                itemDomain.getItemDomains().forEach(itemDomainX ->
                        itemDomainX.getItemDomains().forEach(itemDomainXR ->{
                            FilteredAdTypeStrategyConfig strategyConfig = new FilteredAdTypeStrategyConfig();
                            strategyConfig.setRepeat(itemDomainXR.getRepeat());
                            strategyConfig.setAdTypeList(itemDomainXR.getAdTypeList());
                            returnArrayList.add(strategyConfig);
                        }));
            }
        }

        return returnArrayList;
    }

    private List<FilteredAdDefaultTypeStrategyConfig> parseFilterConf(Long id){
        List<FilteredAdDefaultTypeStrategyConfig> returnArrayList = new ArrayList<>();

        if (id == null) {
            return null;
        }

        StrategyBaseConfig baseConfig = this.baseConfigHolder.get(id);
        if (baseConfig == null) {
            return null;
        }
        Object config = baseConfig.getConfig();

        AdConfigNewDomain adConfigNewDomain = null;
        if (config instanceof AdConfigNewDomain) {
            adConfigNewDomain = (AdConfigNewDomain) config;
        }

        if (adConfigNewDomain == null){
            return null;
        }

        // 获取激励打底配置
        for (AdConfigNewItemDomain itemDomain : adConfigNewDomain.getDomainList()) {
            if (!AdConfigType.TOP_CONFIG.getCode().equals(itemDomain.getConfigType())) {
                itemDomain.getWallItemDomains().forEach(itemDomainX ->{
                    FilteredAdDefaultTypeStrategyConfig strategyConfig = new FilteredAdDefaultTypeStrategyConfig();
                    strategyConfig.setTargetType(itemDomainX.getTargetType());
                    strategyConfig.setLayer(itemDomainX.getLayer());
                    returnArrayList.add(strategyConfig);
                });
            }
        }
        return returnArrayList;
    }

    private List<FilteredAdTypeStrategyConfig> parseFilteredAdTypeConfig(Long baseConfigId) {
        if (baseConfigId == null) {
            return null;
        }

        StrategyBaseConfig baseConfig = this.baseConfigHolder.get(baseConfigId);
        if (baseConfig == null) {
            return null;
        }

        AdConfigDomain adConfigDomain = null;
        Object config = baseConfig.getConfig();
        if (config instanceof AdConfigDomain) {
            adConfigDomain = (AdConfigDomain) config;
        }

        if (adConfigDomain == null) {
            return null;
        }

        List<FilteredAdTypeStrategyConfig> retList = new ArrayList<>();

        for (AdConfigItemDomain itemDomain : adConfigDomain.getItemDomains()) {
            FilteredAdTypeStrategyConfig strategyConfig = new FilteredAdTypeStrategyConfig();
            strategyConfig.setRepeat(itemDomain.getRepeat());
            strategyConfig.setAdTypeList(itemDomain.getAdTypeList());
            retList.add(strategyConfig);
        }

        return retList;
    }

    private List<FilteredAdDefaultTypeStrategyConfig> parsesFilteredAdDefaultTypeConfig(Long baseConfigId) {
        if (baseConfigId == null) {
            return null;
        }
        StrategyBaseConfig baseConfig = this.baseConfigHolder.get(baseConfigId);
        if (baseConfig == null) {
            return null;
        }

        AdDefaultConfigDomain adConfigDomain = null;
        Object config = baseConfig.getConfig();
        if (config instanceof AdDefaultConfigDomain) {
            adConfigDomain = (AdDefaultConfigDomain) config;
        }

        if (adConfigDomain == null) {
            return null;
        }

        List<FilteredAdDefaultTypeStrategyConfig> retList = new ArrayList<>();
        for (AdDefaultConfigItemDomain itemDomain : adConfigDomain.getItemDomains()) {
            FilteredAdDefaultTypeStrategyConfig strategyConfig = new FilteredAdDefaultTypeStrategyConfig();
            strategyConfig.setTargetType(itemDomain.getTargetType());
            strategyConfig.setLayer(itemDomain.getLayer());
            retList.add(strategyConfig);
        }

        return retList;
    }

    private void updateBaseConfig(StrategyConfigFactory configFactory) {
        Set<Long> updatedIds = new HashSet<>();

        // 批量查询刷新全局配置
        int pageSize = 10000;
        long lastId = 0;
        while (true) {
            List<StrategyBaseConfigModel> models = configFactory.queryBaseConfigByPage(lastId, pageSize);

            if (CollectionUtils.isEmpty(models)) {
                break;
            }

            for (StrategyBaseConfigModel model : models) {
                StrategyBaseConfig strategyBaseConfig = new StrategyBaseConfig(model);
                this.baseConfigHolder.put(strategyBaseConfig.getId(), strategyBaseConfig);
                updatedIds.add(strategyBaseConfig.getId());
            }

            lastId = models.get(models.size() - 1).getId();

            if (models.size() < pageSize) {
                break;
            }
        }

        this.baseConfigHolder.keySet().removeIf(id -> {
            boolean b = !updatedIds.contains(id);
            if (b) {
//                System.out.println("baseConfigHolder中移除id={}"+ id);
                log.info("baseConfigHolder中移除id={}", id);
            }
            return b;
        });
    }


    private void updateOrientationConfig(StrategyConfigFactory configFactory) {
        ConcurrentHashMap<Integer, List<Long>> newOrientationConfigHolder = new ConcurrentHashMap<>();
        Set<Long> updatedIds = new HashSet<>();

        int pageSize = 10000;
        long lastId = 0;
        while (true) {
            List<StrategyOrientationConfigModel> models = configFactory.queryOrientationConfigByPage(lastId, pageSize);

            if (CollectionUtils.isEmpty(models)) {
                break;
            }

            for (StrategyOrientationConfigModel model : models) {
                StrategyOrientationConfig domain = new StrategyOrientationConfig(model);

                this.orientationConfigMapping.put(domain.getId(), domain);

                updatedIds.add(domain.getId());

                if (newOrientationConfigHolder.containsKey(domain.getAdPos())) {
                    newOrientationConfigHolder.get(domain.getAdPos()).add(domain.getId());
                } else {
                    newOrientationConfigHolder.put(domain.getAdPos(), Lists.newArrayList(domain.getId()));
                }
            }

            lastId = models.get(models.size() - 1).getId();

            if (models.size() < pageSize) {
                break;
            }
        }

        // 清理广告位配置中不存在的 ID
        this.orientationConfigMapping.keySet().removeIf(id -> {
            boolean b = !updatedIds.contains(id);
            if (b) {
//                System.out.println("orientationConfigMapping中移除id={}" + id);
                log.info("orientationConfigMapping中移除id={}", id);
            }
            return b;
        });


        this.orientationConfigHolder = newOrientationConfigHolder;
    }

    public List<StrategyOrientationConfig> getAllStrategy(){
        return new ArrayList<>(orientationConfigMapping.values());
    }

    private void initStrategyConfigFilterChain() {
        this.valveChain = new DefaultValveChain();
        this.valveChain.setFirst(new AppValve())
                .addValve(new UserAdSwitchValve())
                .addValve(new OsValve())
                .addValve(new RegionValve())
                .addValve(new AnonymousValve())
                .addValve(new TailNumberValve())
                .addValve(new RegistValve())
                .addValve(new IncomeValve())
                .addValve(new UserPkgValve())
                .addValve(new ChannelValve())
                .addValve(new VersionValve())
                .addValve(new AbTestValve())
                .addValve(new ManufacturerValve())
                .addValve(new SdkVersionValve())
                .addValve(new UserDspValve());
    }
}
