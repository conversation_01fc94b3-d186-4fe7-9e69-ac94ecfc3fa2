package com.coohua.ap.support.core.components;

import com.coohua.ap.base.constants.AdTypeSub;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/10/20
 */
@Slf4j
@Getter
@Setter
@NoArgsConstructor
public class AppInfo {

    private Integer product;
    // 三方PosId
    private String appId;
    /**
     * @see com.coohua.ap.base.constants.ThirdPlatformType
     */
    private Integer thirdPlatform;

    /**
     * 0-Android 1-IOS
     */
    private Integer os;

    public AppInfo(ThirdAd thirdAd){
        try {
            if (thirdAd == null){
                return;
            }
            String appId = thirdAd.getPlatform() == 1 ? thirdAd.getExt().getAndroidAppId() : thirdAd.getExt().getIosAppId();
            if (StringUtils.isEmpty(appId)){
                return;
            }
            appId = appId.trim().replaceAll("/n", "");
            setAppId(appId);
            setProduct(thirdAd.getSortedApps().get(0));
            setThirdPlatform(AdTypeSub.getPlatform(thirdAd.getType()).getCode());
            setOs(thirdAd.getPlatform() == 1 ? 0 : 1);
        }catch (Exception e){
            log.error("init Error:",e);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppInfo appInfo = (AppInfo) o;
        return product.equals(appInfo.product) &&
                appId.equals(appInfo.appId) &&
                os.equals(appInfo.os);
    }

    @Override
    public int hashCode() {
        return Objects.hash(product, appId);
    }
}
