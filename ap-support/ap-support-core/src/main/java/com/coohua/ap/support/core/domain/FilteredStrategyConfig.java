package com.coohua.ap.support.core.domain;

import lombok.Data;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.gateway.web.container.domain
 * @create_time 2019-11-11
 */
@Data
public class FilteredStrategyConfig {

    private Long strategyId;

    private Long categoryId;
    // 广告配置
    private List<FilteredAdTypeStrategyConfig> filteredAdTypeConfig;

    // 投放端打底配置
    private List<FilteredAdDefaultTypeStrategyConfig> filteredAdDefaultTypeConfig;

    // 客户端非激励位打底配置
    private List<FilteredAdDefaultTypeStrategyConfig> filteredAppDefaultTypeConfig;

    // 客户端激励位打底配置
    private List<FilteredAdDefaultTypeStrategyConfig> filteredAppDefaultTypeGoldConfig;

    // 是否开启非投放用户的观看过滤
    private Boolean ntfFilter;

    // 跳过该平台广告的策略拉取
    private List<Integer> skipPlatform;

    private Boolean userFilter = false;

    private Integer activeChannel;

    private List<Long> skipAdIdList;
}
