package com.coohua.ap.support.core.factory;


import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.mapper.third.ThirdAdMapper;
import com.coohua.ap.support.core.spring.model.*;

import java.util.List;

/**
 * 第三方广告工厂
 * <AUTHOR>
 */
public class ThirdAdFactory implements AdFactory {

    private ThirdAdMapper thirdAdMapper;

    private static class ThirdAdFactoryHolder {
        private static final ThirdAdFactory INSTANCE = new ThirdAdFactory();
    }

    private ThirdAdFactory() {
        this.thirdAdMapper = SpringIoCUtils.getThirdAdMapper();
    }

    public static ThirdAdFactory getInstance() {
        return ThirdAdFactoryHolder.INSTANCE;
    }

    public ThirdAdModel getNewestAd(Long adId) {
        if (adId == null) {
            return null;
        }
        return thirdAdMapper.getById(adId);
    }

    public List<ThirdAdModel> getNewestAdByType(Integer adType) {
        if (adType == null) {
            return null;
        }
        return thirdAdMapper.getByType(adType);
    }

    public List<ThirdAdModel> getAllNewestAd() {
        return thirdAdMapper.getAll();
    }

    public List<AdPosModel> getAllPosId() { return thirdAdMapper.selectAllPos();}

    public List<BiddingModel> getAllBiddingPosOpen(){
        return thirdAdMapper.selectAllBiddingPos();
    }

    public List<Long> getAlBidding(){
        return thirdAdMapper.biddingIdList();
    }

    public List<Long> getGdtAdIds(List<Integer> typeList,List<Integer> appList){
        return thirdAdMapper.selectAllGdtAd(typeList,appList);
    }

    public List<BiddingAppModel> getAllBiddingApp(){
        return thirdAdMapper.selectAllBiddingApp();
    }

    public List<ThirdAdModel> queryByIds(List<Long> ids){
        return thirdAdMapper.getByIds(ids);
    }

    public List<Long> queryAppIds(Integer appId){
        return thirdAdMapper.queryAllIds(appId);
    }

}
