package com.coohua.ap.support.core.components;

import com.coohua.ap.base.constants.NewEcpAdType;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.model.NewCustomAdModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/9
 */
@Data
public class NewCustomAd extends DefaultAd{

    private static final String IMAGE_PREFIX = "http://images.coohua.com/";

    private Integer advertiserId;

    private Integer balance;

    private Integer advertiserBudget;

    private Integer budget;

    /**
     * 广告计划ID
     */
    private Long planId;

    /**
     * 广告创意id
     */
    private Long ideaId;

    /**
     * 付费方式 1-CPC,2-CPM
     */
    private Integer payType;

    /**
     * 广告单价，单位：分
     */
    private Integer unitPrice;

    /**
     * 计划开关
     */
    private Integer planSwitch;

    /**
     * 创意开关
     */
    private Integer ideaSwitch;

    /**
     * 广告主开关
     */
    private Integer accountFlag;

    /**
     * 投放日期类型
     */
    private Integer planDateType;

    private Date planStartDate;

    private Date planEndDate;

    private Integer planPeriodType;

    private String planPeriod;

    private Date deliveryTime;

    private Integer appId;

    private Integer videoId;


    private NewCustomAdExt customAdExt;


    public NewCustomAd(NewCustomAdModel model){
        this.advertiserId = model.getAdvertiserId();
        this.planId = Long.valueOf(model.getPlanId());
        this.ideaId = model.getIdeaId();
        this.unitPrice = model.getPayAmount();

        this.planSwitch = model.getPlanSwitch();
        this.ideaSwitch = model.getIdeaSwitch();
        this.accountFlag = model.getAccountFlag();
        this.planDateType = model.getPlanDateType();
        this.planStartDate = model.getPlanStartDate();
        this.planEndDate = model.getPlanEndDate();
        this.planPeriodType = model.getPlanPeriodType();
        this.planPeriod = model.getPlanPeriod();
        this.deliveryTime = model.getDeliveryTime();
        this.appId = Integer.valueOf(model.getChooseApp());
        this.videoId = model.getVideoId();
        this.clickInterval = model.getClickLimit();
        this.payType = model.getPayType();
        this.budget = model.getBudget();
        this.advertiserBudget = model.getAdvertiserBudget();
        this.balance = model.getBalance();

        // DEFAULT AD SET
        this.adId = model.getIdeaId();
        this.name = model.getIdeaTitle();
        this.type = NewEcpAdType.convertToAdType(model.getIdeaType());
        this.adSource = AdSource.CUSTOM;
        this.apps = Arrays.stream(model.getChooseAppList().split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toList());

        this.customAdExt = new NewCustomAdExt(){{
            setAdvertiserId(model.getAdvertiserId());
            setVideoUrl(model.getIdeaVedio());
            setAppPkgName(model.getPackageName());
            setDownloadUrl(model.getDownloadUrl());
            setStyle(NewEcpAdType.ownVideos.type.equals(model.getIdeaType())? 401:101);
            setTitle(model.getIdeaTitle());
            setContent(model.getIdeaDesc());
            setLogoUrl(IMAGE_PREFIX + model.getIdeaIconPic());
        }};

        this.adContainer = ContainerHolder.getNewCustomAdContainer();
        this.created();
    }
}
