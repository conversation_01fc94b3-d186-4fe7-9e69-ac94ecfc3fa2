package com.coohua.ap.support.core.spring.mapper.newcustom;

import com.coohua.ap.support.core.components.AdVideoSource;
import com.coohua.ap.support.core.spring.model.NewCustomAdModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9
 */
public interface EcpMapper {

     String base_sql = "SELECT " +
            " ap.id AS plan_id, " +
            " ap.ad_title AS plan_name, " +
            " ap.plan_switch, " +
            " ap.spread_type, " +
            " ap.download_url, " +
            " ap.landing_page_url, " +
            " ap.package_name, " +
            " ap.plan_date_type, " +
            " ap.plan_start_date, " +
            " ap.plan_end_date, " +
            " ap.plan_period_type, " +
            " ap.plan_period, " +
            " ap.budget, " +
            " ap.choose_app_list, " +
            " ai.id AS idea_id, " +
            " ai.idea_switch, " +
            " ai.idea_name, " +
            " ai.idea_type, " +
            " ai.idea_icon_pic, " +
            " ai.idea_vedio, " +
            " ai.idea_pic, " +
            " ai.idea_title, " +
            " ai.idea_desc, " +
            " ai.choose_app, " +
            " ai.pay_type, " +
            " ai.pay_amount, " +
            " ai.click_limit, " +
            " ai.video_id, " +
            " aa.account_flag, " +
            " ap.advertiser_id, " +
            " aa.budget AS advertiser_budget, " +
            " aa.balance " +
            "FROM " +
            " ad_idea ai " +
            "LEFT JOIN ad_plan ap ON ai.ad_id = ap.id " +
            "LEFT JOIN ad_advertiser aa ON aa.id = ap.advertiser_id ";

    @Select({ base_sql + " WHERE  ai.id = #{id}"})
    NewCustomAdModel getById(@Param("id") Long id);

    @Select({ base_sql + " WHERE  ai.idea_type = #{type}"})
    List<NewCustomAdModel> getByType(@Param("type") Integer type);

    // FIXME 本期只接入idea_type = 5 的广告
    @Select({ base_sql + " WHERE ai.del_flag = 0 AND ap.del_flag = 0 and ai.idea_type = 5"})
    List<NewCustomAdModel> getAll();

    @Select({
            "<script>",
            "SELECT video_id,delivery_time FROM ad_video_source WHERE video_id in ",
            "<foreach collection='videoIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>",
    })
    List<AdVideoSource> queryDeliveryTime(@Param("videoIds") List<Integer> videoId);

}
