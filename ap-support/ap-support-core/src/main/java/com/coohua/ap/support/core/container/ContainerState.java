package com.coohua.ap.support.core.container;

/**
 * 广告容器状态
 * <AUTHOR>
 */
public enum ContainerState {

    NOT_INIT(0, "未初始化"),
    INIT(1, "初始化完成"),
    STARTED(2, "容器启动完成"),
    REFRESH(3, "容器刷新"),
    STOP(4, "容器已经停止"),
    DESTROY(5, "容器被销毁");

    private int state;

    private String desc;

    ContainerState(int state, String desc) {
        this.state = state;
        this.desc = desc;
    }

    public int getState() {
        return state;
    }

    public String getDesc() {
        return desc;
    }
}
