package com.coohua.ap.support.core.spring.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/11/9
 */
@Data
public class NewCustomAdModel {

    // 计划相关
    private Integer planId;
    private String planName;
    private Integer planSwitch;
    private Integer spreadType;
    private String downloadUrl;
    private String landingPageUrl;
    private String packageName;
    private Integer planDateType;
    private Date planStartDate;
    private Date planEndDate;
    private Integer planPeriodType;
    private String planPeriod;
    private Integer budget;
    private String chooseAppList;

    // 创意相关
    private Long ideaId;
    private String ideaName;
    private Integer ideaSwitch;
    private Integer ideaType;
    private String ideaIconPic;
    private String ideaVedio;
    private String ideaPic;
    private String ideaTitle;
    private String ideaDesc;
    private Integer videoId;
    private Date deliveryTime;

    // 要投放的APPId
    private String chooseApp;
    private Integer payType;
    private Integer payAmount;
    private Integer clickLimit;

    // 广告主相关
    private Integer advertiserId;
    private Integer accountFlag;
    private Integer balance;
    private Integer advertiserBudget;


}
