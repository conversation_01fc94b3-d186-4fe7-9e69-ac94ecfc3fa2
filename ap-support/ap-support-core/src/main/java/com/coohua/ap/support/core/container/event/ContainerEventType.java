package com.coohua.ap.support.core.container.event;

/**
 * 容器事件类型
 * <AUTHOR>
 */
public enum ContainerEventType {

    STATE_CHANGE(1, "容器状态变更");

    private int eventType;

    private String desc;

    ContainerEventType(int eventType, String desc) {
        this.eventType = eventType;
        this.desc = desc;
    }

    public int getEventType() {
        return eventType;
    }

    public String getDesc() {
        return desc;
    }
}
