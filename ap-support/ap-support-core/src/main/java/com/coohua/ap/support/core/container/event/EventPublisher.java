package com.coohua.ap.support.core.container.event;

/**
 * 事件发布
 * <AUTHOR>
 */
public interface EventPublisher {
    /**
     * 发布广告事件
     * @param adEvent 广告事件
     */
    default void adEventPublish(AdEvent adEvent) {
    }

    /**
     * 发布广告容器事件
     * @param containerEvent 广告容器事件
     */
    default void adContainerEventPublish(ContainerEvent containerEvent) {
    }

    /**
     * 发布配置事件
     * @param configEvent 配置事件
     */
    default void configEventPublish(ConfigEvent configEvent) {
    }

    /**
     * 发布配置容器事件
     * @param containerEvent 配置容器事件
     */
    default void configContainerEventPublish(ContainerEvent containerEvent) {
    }
}
