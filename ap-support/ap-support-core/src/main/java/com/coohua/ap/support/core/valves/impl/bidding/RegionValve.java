package com.coohua.ap.support.core.valves.impl.bidding;

import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <pre>
 *  定向条件检索：地域
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/14
 */
public class RegionValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userMetaForThirdAd) {
        if (userMetaForThirdAd instanceof UserInfoBidding) {
            UserInfoBidding userMetaInfo = (UserInfoBidding) userMetaForThirdAd;
            // 锁区作用点(一道锁、二道锁)
            candidates.removeIf(adId -> !biddingContainer.getBidding(adId).isLockedArea(userMetaInfo.getLockedArea(), userMetaInfo.getIsFirstLockActionPoint()));
            candidates.removeIf(adId -> !biddingContainer.getBidding(adId).isRegionValid(userMetaInfo.getFilterRegion()));
        }
    }
}
