package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.support.core.components.StrategyOrientationConfig;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/25
 */
public class ManufacturerValve extends DefaultValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            if (StringUtils.isEmpty(userInfo.getManufacturer())) {
                return;
            }
            candidates.removeIf(configId -> !ContainerHolder.getStrategyConfigContainer().getStrategyOrientationConfig(configId).isManufacturerValid(userInfo.getManufacturer()));
        }
    }
}
