package com.coohua.ap.support.core.message;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.PropertyValueConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.coohua.ap.base.constants.MessageTag;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @since 2022/3/22
 */
@Slf4j
public class AppMessageHandler implements AdMessageHandler{

    private ConsumerBean consumerBean;

    @Override
    public void init() {
        // 设置MQ
        ConsumerBean consumerBean = new ConsumerBean();
        //配置文件
        Properties properties = SpringIoCUtils.getMQConfig().getMqPropertie();
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_APP_CONFIG");
        //将消费者线程数固定为20个 20为默认值
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "2");
        properties.put(PropertyKeyConst.MessageModel, PropertyValueConst.BROADCASTING);
        consumerBean.setProperties(properties);
        //订阅关系
        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(SpringIoCUtils.getMQConfig().getTopic());
        subscription.setExpression(MessageTag.APP.getTag());
        subscriptionTable.put(subscription, (message, consumeContext) -> {
            try {
                this.onMessage(new String(message.getBody()));
                return Action.CommitMessage;
            } catch (Exception e) {
                //消费失败
                return Action.ReconsumeLater;
            }
        });
        //订阅多个topic如上面设置
        consumerBean.setSubscriptionTable(subscriptionTable);
        consumerBean.start();
        this.consumerBean = consumerBean;
        log.info("Rocket MQ Start.... Listen Topic {} TAG:{}", SpringIoCUtils.getMQConfig().getTopic(),MessageTag.APP.getTag());
    }

    @Override
    public void onMessage(String message){
        log.info("MQ GET {} has changed,So Refresh..",message);
        // 更新APP 缓存
        ContainerHolder.getAppConfigContainer().topicRefresh();
        // 更新广告位
        log.info("===> RefreshAdPos All Current.");
        ContainerHolder.getThirdAdContainer().refreshAdContainer();
        ContainerHolder.getThirdAdContainer().refreshAdPosAll();
        log.info("===> Complete RefreshAdPos All Current.");
    }

    @Override
    public void shutdown() {
        if (this.consumerBean != null) {
            this.consumerBean.shutdown();
            log.info("[APP] RocketMQ shutdown....");
        }
    }
}
