package com.coohua.ap.support.core.container;

import com.coohua.ap.support.core.container.event.ConfigEvent;
import com.coohua.ap.support.core.container.event.ContainerEvent;
import com.coohua.ap.support.core.container.event.ContainerEventType;
import com.coohua.ap.support.core.container.listener.ConfigListener;
import com.coohua.ap.support.core.container.listener.ContainerListener;
import com.coohua.ap.support.core.container.listener.impl.ConfigContainerLifeCycleListener;
import com.coohua.ap.support.core.container.listener.impl.ConfigLifeCycleListener;
import com.coohua.ap.support.core.container.thread.ShutdownHook;
import com.coohua.ap.support.core.factory.ConfigFactory;
import com.coohua.ap.support.core.message.AdMessageHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.*;

/**
 * 配置容器的默认实现
 * <AUTHOR>
 */
@Slf4j
public abstract class DefaultConfigContainer implements ConfigContainer, Runnable {

    // 广告容器名称
    String name;

    private static final long FIXED_RATE = 1000 * 30; // 定时周期 30秒全量刷新

    // 容器状态
    private volatile ContainerState state;

    // 容器事件监听器
    private final List<ContainerListener> containerListeners = new CopyOnWriteArrayList<>();

    // 配置事件监听器
    private final List<ConfigListener> configListeners = new CopyOnWriteArrayList<>();

    private ScheduledExecutorService executorService;

    protected AdMessageHandler adMessageHandler;

    ThreadFactory threadFactory;

    ConfigFactory configFactory;

    public DefaultConfigContainer() {
        this.state = ContainerState.NOT_INIT;
    }

    @Override
    public void configEventPublish(ConfigEvent configEvent) {
        for (ConfigListener listener : configListeners) {
            listener.onEvent(configEvent);
        }
    }

    @Override
    public void configContainerEventPublish(ContainerEvent containerEvent) {
        for (ContainerListener listener : containerListeners) {
            listener.onContainerEvent(containerEvent);
        }
    }

    @Override
    public void addConfigContainerListener(ContainerListener containerListener) {
        this.containerListeners.add(containerListener);
    }

    @Override
    public void addConfigListener(ConfigListener configListener) {
        this.configListeners.add(configListener);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setState(ContainerState containerState) {
        this.state = containerState;
        ContainerEvent event = new ContainerEvent(this, state, ContainerEventType.STATE_CHANGE);
        configContainerEventPublish(event);
    }

    @Override
    public ContainerState getState() {
        return this.state;
    }

    @Override
    public void init() {
        if (this.state == ContainerState.NOT_INIT) {
            setName();
            initThreadFactory();
            executorService = Executors.newSingleThreadScheduledExecutor(threadFactory);
            initConfigFactory();
            refreshConfigContainer();
            addConfigContainerListener(new ConfigContainerLifeCycleListener(this));
            addConfigListener(new ConfigLifeCycleListener());
            Runtime.getRuntime().addShutdownHook(new ShutdownHook(this));
            setState(ContainerState.INIT);
        }
    }

    protected long getFixedRate() {
        return FIXED_RATE;
    }

    @Override
    public void started() {
        if (this.state == ContainerState.INIT) {
            // 启动定时任务
            executorService.scheduleAtFixedRate(this, getFixedRate(), getFixedRate(), TimeUnit.MILLISECONDS);
            setState(ContainerState.STARTED);
        }
    }

    @Override
    public void refresh() {
        if (this.state == ContainerState.STARTED || this.state == ContainerState.REFRESH) {
            refreshConfigContainer();
            setState(ContainerState.REFRESH);
        }
    }

    @Override
    public void stoped() {
        if (this.state == ContainerState.STARTED || this.state == ContainerState.REFRESH) {
            executorService.shutdown();
            setState(ContainerState.STOP);
            destroy();
        }
    }

    @Override
    public void destroy() {
        if (this.state == ContainerState.STOP) {
            setState(ContainerState.DESTROY);
        }
    }

    @Override
    public void run() {
        try {
            refresh();
        } catch (Exception ex) {
            // catch 住，避免因为报RuntimeException导致定时器线程挂掉。
            ex.printStackTrace();
            log.error("ConfigContainer [{}] refresh error. Message is : {}", this.name, ex.getMessage());
        }
    }

    abstract void initThreadFactory();

    abstract void refreshConfigContainer();
}
