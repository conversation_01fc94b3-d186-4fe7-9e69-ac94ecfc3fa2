package com.coohua.ap.support.core.domain;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 广告被过滤原因
 * <AUTHOR>
 */
@Data
public class FilteredReason {
    // 用户ID
    @NotNull
    private Long userId;
    // 原因
    @NotNull
    private String reason;

    public FilteredReason(@NotNull Long userId, @NotNull String reason) {
        this.userId = userId;
        this.reason = reason;
    }
}
