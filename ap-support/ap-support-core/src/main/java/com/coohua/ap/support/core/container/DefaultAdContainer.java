package com.coohua.ap.support.core.container;


import com.coohua.ap.base.extension.ExtensionLoader;
import com.coohua.ap.support.core.container.event.AdEvent;
import com.coohua.ap.support.core.container.event.ContainerEvent;
import com.coohua.ap.support.core.container.event.ContainerEventType;
import com.coohua.ap.support.core.container.listener.AdListener;
import com.coohua.ap.support.core.container.listener.ContainerListener;
import com.coohua.ap.support.core.container.listener.impl.AdActionListener;
import com.coohua.ap.support.core.container.listener.impl.AdContainerLifeCycleListener;
import com.coohua.ap.support.core.container.listener.impl.AdLifeCycleListener;
import com.coohua.ap.support.core.container.thread.ShutdownHook;
import com.coohua.ap.support.core.factory.AdFactory;
import com.coohua.ap.support.core.message.AdMessageHandler;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.*;

/**
 * 默认广告容器实现
 * <AUTHOR>
 */
@Slf4j
public abstract class DefaultAdContainer implements AdContainer, Runnable {

    // 广告容器名称
    String name;

    private static final long FIXED_RATE = 1000 * 30; // 定时周期 30秒全量刷新

    // 广告容器状态
    private volatile ContainerState state;

    // 广告容器事件监听器
    private final List<ContainerListener> containerListeners = new CopyOnWriteArrayList<>();

    // 广告事件监听器
    private final List<AdListener> adListeners = new CopyOnWriteArrayList<>();

    private ScheduledExecutorService executorService;

    ThreadFactory threadFactory;

    // 广告MQ消息处理
    protected AdMessageHandler adMessageHandler;

    // 广告过滤责任链构建器
    DefaultValveChain valveChain;

    // 广告工厂
    AdFactory adFactory;

    // 全局配置容器
    GlobalConfigContainer globalConfigContainer;

    public DefaultAdContainer() {
        this.state = ContainerState.NOT_INIT;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void addAdContainerListener(ContainerListener containerListener) {
        this.containerListeners.add(containerListener);
    }

    @Override
    public void addAdListener(AdListener adListener) {
        this.adListeners.add(adListener);
    }

    @Override
    public void setState(ContainerState adContainerState) {
        this.state = adContainerState;
        ContainerEvent event = new ContainerEvent(this, state, ContainerEventType.STATE_CHANGE);
        adContainerEventPublish(event);
    }

    @Override
    public ContainerState getState() {
        return this.state;
    }

    @Override
    public void run() {
        try {
            refresh();
        } catch (Exception ex) {
            // catch 住，避免因为报RuntimeException导致定时器线程挂掉。
            ex.printStackTrace();
            log.error("AdContainer [{}] refresh error. Message is : {}", this.name, ex.getMessage());
        }
    }

    @Override
    public void init() {
        if (this.state == ContainerState.NOT_INIT) {
            setName();
            initThreadFactory();
            executorService = Executors.newSingleThreadScheduledExecutor(threadFactory);
            initGlobalContainer();
            // 初始化过滤器责任链
            initValveChain();
            initAdFactory();
            // 初始化广告数据
            refreshAdContainer();
            // 初始化监听器
            initAdContainerListener();
            initAdListener();
            // 初始化JVM退出钩子
            Runtime.getRuntime().addShutdownHook(new ShutdownHook(this));
            setState(ContainerState.INIT);
        }
    }

    private void initGlobalContainer() {
        DefaultGlobalConfigContainer configContainer = ContainerHolder.getGlobalConfigContainer();
        configContainer.init();
        configContainer.started();
        globalConfigContainer = configContainer;
        DefaultAppConfigContainer appConfigContainer = ContainerHolder.getAppConfigContainer();
        appConfigContainer.init();
        appConfigContainer.started();
    }

    @Override
    public void started() {
        if (this.state == ContainerState.INIT) {
            // 启动定时任务
            executorService.scheduleAtFixedRate(this, FIXED_RATE, FIXED_RATE, TimeUnit.MILLISECONDS);
            setState(ContainerState.STARTED);
        }
    }

    @Override
    public void refresh() {
        if (this.state == ContainerState.STARTED || this.state == ContainerState.REFRESH) {
            refreshAdContainer();
            setState(ContainerState.REFRESH);
        }
    }

    @Override
    public void stoped() {
        if (this.state == ContainerState.STARTED || this.state == ContainerState.REFRESH) {
            executorService.shutdown();
            setState(ContainerState.STOP);
            destroy();
        }
    }

    @Override
    public void destroy() {
        if (this.state == ContainerState.STOP) {
            setState(ContainerState.DESTROY);
        }
    }

    @Override
    public void adEventPublish(AdEvent adEvent) {
        for (AdListener listener : adListeners) {
            listener.onAdEvent(adEvent);
        }
    }

    @Override
    public void adContainerEventPublish(ContainerEvent containerEvent) {
        for (ContainerListener listener : containerListeners) {
            listener.onContainerEvent(containerEvent);
        }
    }

    private void initAdContainerListener() {
        addAdContainerListener(new AdContainerLifeCycleListener(this));
        // 上游项目扩展
        List<ContainerListener> listeners = ExtensionLoader.getExtensionLoader(ContainerListener.class).getExtensions();
        for (ContainerListener listener : listeners) {
            addAdContainerListener(listener);
        }
    }

    private void initAdListener() {
        addAdListener(new AdLifeCycleListener());
        addAdListener(new AdActionListener());
        // 上游项目扩展
        List<AdListener> listeners = ExtensionLoader.getExtensionLoader(AdListener.class).getExtensions();
        for (AdListener listener : listeners) {
            addAdListener(listener);
        }
    }

    abstract void initValveChain();

    abstract void refreshAdContainer();

    abstract void initThreadFactory();

    abstract boolean isMyAdType(int adType);
}
