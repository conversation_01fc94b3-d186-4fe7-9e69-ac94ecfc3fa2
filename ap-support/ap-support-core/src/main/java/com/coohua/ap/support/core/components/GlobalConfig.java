package com.coohua.ap.support.core.components;

import com.coohua.ap.support.core.spring.model.GlobalConfigModel;
import lombok.Getter;
import lombok.ToString;

import java.util.Date;

/**
 * 全局配置实例
 * <AUTHOR>
 */
@ToString
public class GlobalConfig extends DefaultConfig {

    /**
     * 配置Key
     */
    @Getter
    private String key;
    /**
     * 配置Value
     */
    @Getter
    private String value;
    /**
     * 配置Comment
     */
    private String comment;
    /**
     * 配置创建时间
     */
    private Date createTime;
    /**
     * 配置最后一次修改时间
     */
    private Date updateTime;
    /**
     * 配置所属应用
     */
    @Getter
    private Integer product;

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public GlobalConfig(GlobalConfigModel model) {
        transfer(model);
        created();
    }

    private void transfer(GlobalConfigModel model) {
        this.setId(model.getId().longValue());
        this.key = model.getName();
        this.value = model.getValue();
        this.comment = model.getComment();
        this.createTime = model.getCreateTime();
        this.updateTime = model.getUpdateTime();
        this.product = model.getProduct();
    }
}
