package com.coohua.ap.support.core.container.event;

/**
 * 配置事件类型
 * <AUTHOR>
 */
public enum ConfigEventType {
    // 生命周期事件
    CREATE(1, "配置创建"),
    UPDATE(2, "配置更新");

    private int eventType;

    private String desc;

    ConfigEventType(int eventType, String desc) {
        this.eventType = eventType;
        this.desc = desc;
    }

    public int getEventType() {
        return eventType;
    }

    public String getDesc() {
        return desc;
    }

}
