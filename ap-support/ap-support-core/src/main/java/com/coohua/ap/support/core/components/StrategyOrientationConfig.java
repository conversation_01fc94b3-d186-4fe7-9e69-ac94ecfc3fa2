package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.base.constants.ThirdPlatformType;
import com.coohua.ap.base.utils.Version;
import com.coohua.ap.support.core.domain.ConfigIncome;
import com.coohua.ap.support.core.domain.ConfigRegist;
import com.coohua.ap.support.core.domain.ConfigTfPlatform;
import com.coohua.ap.support.core.domain.ConfigVersion;
import com.coohua.ap.support.core.spring.model.StrategyOrientationConfigModel;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/10
 */
@ToString
public class StrategyOrientationConfig extends DefaultConfig implements Comparable<StrategyOrientationConfig> {

    /**
     * 配置名称
     */
    private String name;
    /**
     * 所属广告位
     */
    @Getter
    private Integer adPos;
    /**
     * 应用类型，App.class
     */
    private Integer appId;
    /**
     * 平台定向：1-安卓／2-iOS
     */
    private Integer os;
    /**
     * 地域定向：1-受限区／2-非受限区／0-不限制
     */
    private Integer region;

    /**
     * 锁区作用点 0：所有锁区生效 1：一道锁生效 2-二道锁生效
     */
    private Integer lockActionPoint;

    private Integer regionSide;
    /**
     * 匿名用户定向：1-匿名用户／2-非匿名用户／0-不限制
     */
    private Integer anonymous;
    /**
     * 注册时间定向
     */
    private ConfigRegist regist;
    /**
     * 总收入定向
     */
    private ConfigIncome income;
    /**
     * 版本定向
     */
    private ConfigVersion version;
    /**
     * 投放平台定向
     */
    private ConfigTfPlatform tfPlatform;
    /**
     * 尾号定向
     */
    private String tailNumber;
    /**
     * 用户包名定向
     */
    private String userPkg;
    /**
     * 渠道id定向
     */
    private String channelId;

    /**
     * 手机品牌定向
     */
    private String manufacturer;


    private ConfigVersion sdkVersion;
    /**
     * 详细配置信息，配置类型 和 基本配置信息的ID的映射： <ConfigType, ConfigBaseDomain.id>
     */
    private Map<Integer, Long> config;
    /**
     * 状态，0-关闭／1-开启
     */
    private Integer state;

    @Getter
    private List<Integer> abTestList;
    /**
     * 权重，值越大，越优先选择此配置
     */
    @Getter
    private Integer priority;

    private Gson gson = new Gson();

    private String dsp;

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public int compareTo(StrategyOrientationConfig other) {
        return other.priority.compareTo(this.priority);
    }

    public StrategyOrientationConfig(StrategyOrientationConfigModel model) {
        setId(model.getId().longValue());
        this.name = model.getName();
        this.adPos = model.getAdPos();
        this.appId = model.getProduct();
        this.os = model.getOs();
        this.region = model.getRegion();
        this.regionSide = Optional.ofNullable(model.getRegionSide()).orElse(0);
        this.anonymous = model.getAnonymous();
        this.regist = JSONObject.parseObject(model.getRegist(), ConfigRegist.class);
        this.income = JSONObject.parseObject(model.getIncome(), ConfigIncome.class);
        this.version = JSONObject.parseObject(model.getVersion(), ConfigVersion.class);
        this.tailNumber = model.getTailNumber();
        this.userPkg = model.getUserPkg();
        this.channelId = model.getChannelId();
        this.manufacturer = model.getManufacturer();
        this.sdkVersion =  JSONObject.parseObject(model.getSdkVersion(),ConfigVersion.class);
        this.config = gson.fromJson(model.getConfig(), new TypeToken<Map<Integer, Long>>() {
        }.getType());
        this.state = model.getState();
        this.abTestList = StringUtils.isBlank(model.getAbTest())? new ArrayList<>():Arrays.stream(model.getAbTest().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        this.priority = model.getPriority();
        this.tfPlatform = JSONObject.parseObject(model.getTfPlatform(),ConfigTfPlatform.class);
        this.dsp = model.getDsp();
        this.lockActionPoint = model.getLockActionPoint();
    }

    /**
     * 校验是否满足APP策略条件
     *
     * @param appId 应用ID
     * @return true - 满足，false - 不满足
     */
    public boolean isAppValid(Integer appId) {
        if (appId == null) {
            return false;
        }
        return this.appId.equals(appId);
    }

    /**
     * 校验是否满足OS策略条件
     *
     * @param os 用户APP OS
     * @return true - 满足，false - 不满足
     */
    public boolean isOsValid(Integer os) {
        if (os == null) {
            return false;
        }
        return this.os.equals(os);
    }

    /**
     * 校验是否满足区域策略条件
     *
     * @param filterRegion 当前用户是否是受限地区用户，1-受限区／2-非受限区／0-不限制
     * @return true - 满足，false - 不满足
     */
    public boolean isRegionValid(Integer filterRegion) {
        if (this.regionSide.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
            return true;
        }

        if (filterRegion == null) {
            return true;
        }
        return filterRegion.equals(this.regionSide);
    }


    public boolean isLockedArea(Boolean isLockedArea) {
        if (this.region.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
            return true;
        }

        if (isLockedArea == null) {
            return false;
        }

        Boolean limit = this.region == 1;
        return isLockedArea.equals(limit);
    }

    /**
     * 校验是否满足锁区作用点策略条件

     * @param isLockedArea
     * @param isFirstLockActionPoint
     * @return
     */
    public boolean isLockedArea(Boolean isLockedArea, Boolean isFirstLockActionPoint) {
        if (this.region.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
            return true;
        }

        if (isLockedArea == null) {
            return false;
        }

        Boolean limit = this.region == 1;

        boolean lockResult = isLockedArea.equals(limit);

        if (isLockedArea && lockResult) {
            Integer actionPoint = Optional.ofNullable(this.lockActionPoint).orElse(AdConstants.DEFAULT_NO_LIMIT_CODE);

            if (actionPoint.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
                return true;
            }

            if (isFirstLockActionPoint == null) {
                return false;
            }

            if (actionPoint == 1 && isFirstLockActionPoint) {
                return true;
            }

            return actionPoint == 2 && !isFirstLockActionPoint;
        }

        return lockResult;
    }

    /**
     * 校验是否满足匿名策略条件
     *
     * @param anonymous 当前用户是否是匿名用户，1-匿名用户／2-非匿名用户／0-不限制
     * @return true - 满足，false - 不满足
     */
    public boolean isAnonymousValid(Integer anonymous) {
        if (this.anonymous.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
            return true;
        }

        if (anonymous == null) {
            return false;
        }

        return anonymous.equals(this.anonymous);
    }

    /**
     * 校验是否满足用户ID尾号策略条件
     *
     * @param userId 用户ID
     * @return true - 满足，false - 不满足
     */
    public boolean isTailNumberValid(Long userId) {
        if (userId == null) {
            return false;
        }
        if (StringUtils.isEmpty(this.tailNumber)) {
            return true;
        }

        String[] tailNumbers = this.tailNumber.split(AdConstants.SYMBOL_COMMA);
        boolean find = false;
        for (String tn : tailNumbers) {
            if (StringUtils.isNotEmpty(tn) && userId.toString().endsWith(tn)) {
                find = true;
                break;
            }
        }

        return find;
    }

    /**
     * 校验是否满足注册时间策略条件
     *
     * @param registSeconds 用户注册时间
     * @return true - 满足，false - 不满足
     */
    public boolean isRegistValid(long registSeconds) {
        if (this.regist.getLimit() == AdConstants.DEFAULT_NO_LIMIT_CODE
                || (StringUtils.isEmpty(this.regist.getStart()) && StringUtils.isEmpty(this.regist.getEnd()))) { // 不限制的情况
            return true;
        }

        return !((StringUtils.isNotEmpty(this.regist.getStart()) && StringUtils.isEmpty(this.regist.getEnd()) && Long.parseLong(this.regist.getStart()) > registSeconds)
                || (StringUtils.isEmpty(this.regist.getStart()) && StringUtils.isNotEmpty(this.regist.getEnd()) && Long.parseLong(this.regist.getEnd()) < registSeconds)
                || (StringUtils.isNotEmpty(this.regist.getStart()) && StringUtils.isNotEmpty(this.regist.getEnd()) && (Long.parseLong(this.regist.getStart()) > registSeconds || Long.parseLong(this.regist.getEnd()) < registSeconds)));
    }

    public boolean needRegisterValid(){
        // 不限制的情况
        return this.regist.getLimit() != AdConstants.DEFAULT_NO_LIMIT_CODE
                && (!StringUtils.isEmpty(this.regist.getStart()) || !StringUtils.isEmpty(this.regist.getEnd()));
    }

    /**
     * 校验是否满足总收入策略条件
     *
     * @param userIncome 用户总收入
     * @return true - 满足，false - 不满足
     */
    public boolean isIncomeValid(Integer userIncome) {
        if (userIncome == null) {
            userIncome = 0;
        }

        if (this.income.getLimit() == AdConstants.DEFAULT_NO_LIMIT_CODE
                || (StringUtils.isEmpty(this.income.getStart()) && StringUtils.isEmpty(this.income.getEnd()))) {
            return true;
        }

        return !((StringUtils.isNotEmpty(this.income.getStart()) && StringUtils.isEmpty(this.income.getEnd()) && Integer.parseInt(this.income.getStart()) > userIncome)
                || (StringUtils.isEmpty(this.income.getStart()) && StringUtils.isNotEmpty(this.income.getEnd()) && Integer.parseInt(this.income.getEnd()) < userIncome)
                || (StringUtils.isNotEmpty(this.income.getStart()) && StringUtils.isNotEmpty(this.income.getEnd()) && (Integer.parseInt(this.income.getStart()) > userIncome || Integer.parseInt(this.income.getEnd()) < userIncome)));
    }

    public boolean needUserPkgValid(){
        return !StringUtils.isEmpty(this.userPkg);
    }

    public boolean needLockArea(){
        return this.region != null && this.region != 0;
    }

    public boolean needFilterRegion(){
        return this.regionSide != null && this.regionSide != 0;
    }
    /**
     * 校验是否满足包名策略条件
     *
     * @param userPkgName 用户包名
     * @return true - 满足，false - 不满足
     */
    public boolean isUserPkgValid(String userPkgName) {
        if (StringUtils.isEmpty(this.userPkg)) {
            return true;
        }
        if (StringUtils.isEmpty(userPkgName)) {
            return false;
        }

        return Arrays.asList(this.userPkg.split(AdConstants.SYMBOL_COMMA)).contains(userPkgName);
    }

    /**
     * 校验是否满足渠道策略条件
     *
     * @param channelId 用户渠道
     * @return true - 满足，false - 不满足
     */
    public boolean isChannelIdValid(String channelId) {
        if (StringUtils.isEmpty(this.channelId)) {
            return true;
        }

        String[] channels = this.channelId.split(AdConstants.SYMBOL_COMMA);
        boolean find = false;
        for (String channel : channels) {
            if (channelId.startsWith(channel)) {
                find = true;
                break;
            }
        }
        return find;
    }


    public boolean isManufacturerValid(String manufacturer) {
        if (StringUtils.isEmpty(this.manufacturer)) {
            return true;
        }

        String[] manufacturers = this.manufacturer.split(AdConstants.SYMBOL_COMMA);
        boolean find = false;
        for (String manufacturerTemp : manufacturers) {
            if (manufacturer.startsWith(manufacturerTemp)) {
                find = true;
                break;
            }
        }
        return find;
    }

    /**
     * 校验是否满足版本策略条件
     *
     * @param userVersion 用户客户端版本
     * @return true - 满足，false - 不满足
     */
    public boolean isVersionValid(String userVersion) {
        if (StringUtils.isEmpty(userVersion)) {
            return false;
        }

        if (this.version.getLimit() == AdConstants.DEFAULT_NO_LIMIT_CODE
                || (StringUtils.isEmpty(this.version.getStart()) && StringUtils.isEmpty(this.version.getEnd()))) {
            return true;
        }

        return !((StringUtils.isNotEmpty(this.version.getStart()) && StringUtils.isEmpty(this.version.getEnd()) && Version.compare(this.version.getStart(), userVersion) > 0)
                || (StringUtils.isEmpty(this.version.getStart()) && StringUtils.isNotEmpty(this.version.getEnd()) && Version.compare(this.version.getEnd(), userVersion) < 0)
                || (StringUtils.isNotEmpty(this.version.getStart()) && StringUtils.isNotEmpty(this.version.getEnd()) && (Version.compare(this.version.getStart(), userVersion) > 0 || Version.compare(this.version.getEnd(), userVersion) < 0)));
    }

    /**
     * 校验SDK版本是否符合
     * @param userSdkVersion 用户sdkVersion
     * @return true-满足  false-不满足
     */
    public boolean isSdkVersionValid(String userSdkVersion) {
        if (StringUtils.isEmpty(userSdkVersion)) {
            return true;
        }

        if (this.sdkVersion == null){
            return true;
        }

        if (this.sdkVersion.getLimit() == AdConstants.DEFAULT_NO_LIMIT_CODE
                || (StringUtils.isEmpty(this.sdkVersion.getStart()) && StringUtils.isEmpty(this.sdkVersion.getEnd()))) {
            return true;
        }

        return !((StringUtils.isNotEmpty(this.sdkVersion.getStart()) && StringUtils.isEmpty(this.sdkVersion.getEnd()) && Version.compare(this.sdkVersion.getStart(), userSdkVersion) > 0)
                || (StringUtils.isEmpty(this.sdkVersion.getStart()) && StringUtils.isNotEmpty(this.sdkVersion.getEnd()) && Version.compare(this.sdkVersion.getEnd(), userSdkVersion) < 0)
                || (StringUtils.isNotEmpty(this.sdkVersion.getStart()) && StringUtils.isNotEmpty(this.sdkVersion.getEnd()) && (Version.compare(this.sdkVersion.getStart(), userSdkVersion) > 0 || Version.compare(this.sdkVersion.getEnd(), userSdkVersion) < 0)));
    }

    /**
     * 获取指定配置类型对应的配置项ID
     * @param configType 配置项类型
     * @return 配置项ID
     */
    public Long getBaseConfigIdByConfigType(Integer configType) {
        return this.config.get(configType);
    }

    /**
     * 判断是否通用型投放配置
     * @return TRUE 是
     */
    public Boolean isNewConfig(){
        long count = this.config.values().stream().filter(rx -> rx > 0).count();
        return this.config.containsKey(ConfigType.PLACEMENT.code()) && count == 1L;
    }

    /**
     * 是否启用平台过滤非投放用户
     * @return TRUE-启动
     */
    public boolean needFilterNtfPlatform(){
        if (this.tfPlatform == null){
            return false;
        }

        if (this.tfPlatform.getLimit()== AdConstants.DEFAULT_NO_LIMIT_CODE ||
                ThirdPlatformType.NONE.getCode().equals(this.tfPlatform.getPlatform())){
            return false;
        }

        ThirdPlatformType thirdPlatformType = ThirdPlatformType.get(this.tfPlatform.getPlatform());
        return ThirdPlatformType.needFilterType().contains(thirdPlatformType);
    }

    public  Integer skipPlatform(){
        if (needFilterNtfPlatform()) {
            return this.tfPlatform.getPlatform();
        }
        return 0;
    }

    public boolean isUserDsp(String userDsp){
        if (StringUtils.isBlank(this.dsp)){
            return false;
        }

        if (StringUtils.isBlank(userDsp)){
            return true;
        }

        if (this.dsp.equals(userDsp)){
            return false;
        }

        return true;
    }
}
