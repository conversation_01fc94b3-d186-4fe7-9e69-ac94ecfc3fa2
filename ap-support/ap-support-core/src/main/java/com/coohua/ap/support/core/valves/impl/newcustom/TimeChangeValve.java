package com.coohua.ap.support.core.valves.impl.newcustom;

import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.support.core.components.NewCustomAd;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9
 * 新后台-投放时段控制链
 */
public class TimeChangeValve extends DefaultAdValve {

    private static final Integer longtime = 0; // 从某天开始长期投放
    private static final Integer start_end = 1; // 设置开始和结束时间
    private static final String global_split = "-"; // 分隔符号

    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            candidates.removeIf(adId -> !isAdOnTime(adId));
        }
    }

    private boolean isAdOnTime(Long adId){
        NewCustomAd newCustomAd = newCustomAdContainer.getAd(adId);
        boolean flag;
        // 判断计划日期
        Date now = new Date();
        if (longtime.equals(newCustomAd.getPlanDateType())){
            if (now.after(newCustomAd.getPlanStartDate())){
                if (start_end.equals(newCustomAd.getPlanPeriodType()) && StringUtils.isNotEmpty(newCustomAd.getPlanPeriod())) {
                    String perFix = DateUtils.formatDateForYMD(now);
                    String[] timeSpan = newCustomAd.getPlanPeriod().split(global_split);
                    Date begin = DateUtils.parseDate(perFix + StringUtils.SPACE +  timeSpan[0],DateUtils.PATTERN_YMDHMS);
                    Date end = DateUtils.parseDate(perFix + StringUtils.SPACE + timeSpan[1],DateUtils.PATTERN_YMDHMS);
                    flag = now.after(begin) && now.before(end);
                }else {
                    flag = true;
                }
            }else {
                flag = false;
            }
        }else if (start_end.equals(newCustomAd.getPlanDateType())){
            if (now.after(newCustomAd.getPlanStartDate()) && now.before(newCustomAd.getPlanEndDate())) {
                if (start_end.equals(newCustomAd.getPlanPeriodType())) {
                    String perFix = DateUtils.formatDateForYMD(newCustomAd.getPlanStartDate());
                    String[] timeSpan = newCustomAd.getPlanPeriod().split(global_split);
                    Date begin = DateUtils.parseDate(perFix + timeSpan[0],DateUtils.PATTERN_YMDHMS);
                    Date end = DateUtils.parseDate(perFix + timeSpan[1],DateUtils.PATTERN_YMDHMS);
                    flag = now.after(begin) && now.before(end);
                } else {
                    flag = true;
                }
            } else {
                flag = false;
            }
        }else {
            flag = false;
        }
        return flag;
    }
}
