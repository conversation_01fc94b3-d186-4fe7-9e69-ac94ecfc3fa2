package com.coohua.ap.support.core.container;

import com.coohua.ap.base.extension.Scope;
import com.coohua.ap.base.extension.Spi;
import com.coohua.ap.support.core.container.listener.ConfigListener;
import com.coohua.ap.support.core.container.listener.ContainerListener;

/**
 * 配置相关容器
 * <AUTHOR>
 */
@Spi(scope = Scope.SINGLETON)
public interface ConfigContainer extends Container {

    /**
     * 添加配置容器事件监听器
     * @param containerListener 配置容器事件监听器
     */
    void addConfigContainerListener(ContainerListener containerListener);

    /**
     * 添加配置事件监听器
     * @param configListener 配置事件监听器
     */
    void addConfigListener(ConfigListener configListener);

    /**
     * 初始化配置工厂
     */
    void initConfigFactory();

}
