package com.coohua.ap.support.core.container.event;


import com.coohua.ap.support.core.container.Container;

import java.util.EventObject;

/**
 * 容器事件
 * <AUTHOR>
 */
public class ContainerEvent extends EventObject {
    private final Object data;
    private final ContainerEventType type;

    public ContainerEvent(Container source, Object data, ContainerEventType type) {
        super(source);
        this.data = data;
        this.type = type;
    }

    public Object getData() {
        return this.data;
    }

    public Container getContainer() {
        return (Container) getSource();
    }

    public ContainerEventType getType() {
        return this.type;
    }

}
