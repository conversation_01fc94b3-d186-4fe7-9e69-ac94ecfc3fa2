package com.coohua.ap.support.core.spring;

import com.coohua.ap.base.extension.ExtensionLoader;
import com.coohua.ap.support.core.container.*;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 广告容器实例暂存
 * <AUTHOR>
 */
public class ContainerHolder {

    private static volatile Map<String, AdContainer> adContainerMap = new ConcurrentHashMap<>();
    private static volatile Map<String, ConfigContainer> configContainerMap = new ConcurrentHashMap<>();

    private static volatile boolean initThird = false;
    private static volatile boolean initCustom = false;
    private static volatile boolean initGlobalConfig = false;
    private static volatile boolean initAppConfig = false;
    private static volatile boolean initStrategyConfig = false;
    private static volatile boolean initNewCustom = false;
    private static volatile boolean initBidding = false;

    private static Lock thirdLock = new ReentrantLock();
    private static Lock customLock = new ReentrantLock();
    private static Lock globalConfigLock = new ReentrantLock();
    private static Lock appConfigLock = new ReentrantLock();
    private static Lock strategyConfigLock = new ReentrantLock();
    private static Lock newCustomLock = new ReentrantLock();
    private static Lock biddingLock = new ReentrantLock();

    public static ThirdAdContainer getThirdAdContainer() {
        if (!initThird) {
            try {
                thirdLock.lock();
                if (!initThird) {
                    AdContainer thirdAdContainer = ExtensionLoader.getExtensionLoader(AdContainer.class).getExtension(ThirdAdContainer.CONTAINER_NAME);
                    if (thirdAdContainer != null) {
                        adContainerMap.put(ThirdAdContainer.CONTAINER_NAME, thirdAdContainer);
                    }
                    initThird = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                thirdLock.unlock();
            }
        }
        return (ThirdAdContainer) adContainerMap.get(ThirdAdContainer.CONTAINER_NAME);
    }

    public static BiddingContainer getBiddingContainer() {
        if (!initBidding) {
            try {
                biddingLock.lock();
                if (!initBidding) {
                    AdContainer biddingContainer = ExtensionLoader.getExtensionLoader(AdContainer.class).getExtension(BiddingContainer.CONTAINER_NAME);
                    if (biddingContainer != null) {
                        adContainerMap.put(BiddingContainer.CONTAINER_NAME, biddingContainer);
                    }
                    initBidding = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                biddingLock.unlock();
            }
        }
        return (BiddingContainer) adContainerMap.get(BiddingContainer.CONTAINER_NAME);
    }

    public static CustomAdContainer getCustomAdContainer() {
        if (!initCustom) {
            try {
                customLock.lock();
                if (!initCustom) {
                    AdContainer customAdContainer = ExtensionLoader.getExtensionLoader(AdContainer.class).getExtension(CustomAdContainer.CONTAINER_NAME);
                    if (customAdContainer != null) {
                        adContainerMap.put(CustomAdContainer.CONTAINER_NAME, customAdContainer);
                    }
                    initCustom = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                customLock.unlock();
            }
        }
        return (CustomAdContainer) adContainerMap.get(CustomAdContainer.CONTAINER_NAME);
    }

    public static DefaultGlobalConfigContainer getGlobalConfigContainer() {
        if (!initGlobalConfig) {
            try {
                globalConfigLock.lock();
                if (!initGlobalConfig) {
                    ConfigContainer configContainer = ExtensionLoader.getExtensionLoader(ConfigContainer.class).getExtension(DefaultGlobalConfigContainer.CONTAINER_NAME);
                    if (configContainer != null) {
                        configContainerMap.put(DefaultGlobalConfigContainer.CONTAINER_NAME, configContainer);
                    }
                    initGlobalConfig = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                globalConfigLock.unlock();
            }
        }
        return (DefaultGlobalConfigContainer) configContainerMap.get(DefaultGlobalConfigContainer.CONTAINER_NAME);
    }


    public static DefaultAppConfigContainer getAppConfigContainer() {
        if (!initAppConfig) {
            try {
                appConfigLock.lock();
                if (!initAppConfig) {
                    ConfigContainer configContainer = ExtensionLoader.getExtensionLoader(ConfigContainer.class).getExtension(DefaultAppConfigContainer.CONTAINER_NAME);
                    if (configContainer != null) {
                        configContainerMap.put(DefaultAppConfigContainer.CONTAINER_NAME, configContainer);
                    }
                    initAppConfig = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                appConfigLock.unlock();
            }
        }
        return (DefaultAppConfigContainer) configContainerMap.get(DefaultAppConfigContainer.CONTAINER_NAME);
    }

    public static DefaultStrategyConfigContainer getStrategyConfigContainer() {
        if (!initStrategyConfig) {
            try {
                strategyConfigLock.lock();
                if (!initStrategyConfig) {
                    ConfigContainer configContainer = ExtensionLoader.getExtensionLoader(ConfigContainer.class).getExtension(DefaultStrategyConfigContainer.CONTAINER_NAME);
                    if (configContainer != null) {
                        configContainerMap.put(DefaultStrategyConfigContainer.CONTAINER_NAME, configContainer);
                    }
                    initStrategyConfig = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                strategyConfigLock.unlock();
            }
        }
        return (DefaultStrategyConfigContainer) configContainerMap.get(DefaultStrategyConfigContainer.CONTAINER_NAME);
    }

    public static NewCustomAdContainer getNewCustomAdContainer(){
        if (!initNewCustom) {
            try {
                newCustomLock.lock();
                if (!initNewCustom) {
                    AdContainer newCustomAdContainer = ExtensionLoader.getExtensionLoader(AdContainer.class).getExtension(NewCustomAdContainer.CONTAINER_NAME);
                    if (newCustomAdContainer != null) {
                        adContainerMap.put(NewCustomAdContainer.CONTAINER_NAME, newCustomAdContainer);
                    }
                    initNewCustom = true;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                newCustomLock.unlock();
            }
        }
        return (NewCustomAdContainer) adContainerMap.get(NewCustomAdContainer.CONTAINER_NAME);
    }

}
