package com.coohua.ap.support.core.spring.mapper.custom;

import com.coohua.ap.support.core.spring.model.CustomAdModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 直客广告DB操作
 * <AUTHOR>
 */
public interface CustomAdMapper {

    @Select("SELECT "
            + " a.id AS 'planId', "
            + " a.name AS 'planName', "
            + " a.advertiser_id AS 'advertiserId', "
            + " a.state AS 'planState', "
            + " a.type AS 'type', "
            + " a.delivery_type AS 'deliveryType', "
            + " a.daily_budget AS 'dailyBudget', "
            + " a.budget_limit AS 'budgetLimit', "
            + " a.create_time AS 'planCreateTime', "
            + " a.update_time AS 'planUpdateTime', "
            + " a.state_update_time AS 'stateUpdateTime', "
            + " b.id AS 'groupId', "
            + " b.state AS 'groupState', "
            + " b.name AS 'groupName', "
            + " b.delivery_date_period AS 'deliveryDatePeriod', "
            + " b.time_bucket AS 'timeBucket', "
            + " b.product AS 'product', "
            + " b.region AS 'region', "
            + " b.anonymous AS 'anonymous', "
            + " b.sex AS 'sex', "
            + " b.new_user AS 'businessNewUser', "
            + " b.phone_brand AS 'phoneBrand', "
            + " b.age AS 'age', "
            + " b.contains_pkg AS 'containsPkg', "
            + " b.not_contains_pkg AS 'notContainsPkg', "
            + " b.user_channels AS 'userChannels', "
            + " b.create_time AS 'groupCreateTime', "
            + " b.update_time AS 'groupUpdateTime', "
            + " b.platform_version AS 'platformVersion', "
            + " b.register_time AS 'registerTime', "
            + " b.regist_longer AS 'registLonger', "
            + " b.tail_number AS 'tailNumber', "
            + " b.position AS 'position', "
            + " b.income AS 'income', "
            + " b.click_interval AS 'clickInterval', "
            + " b.day_click_limit AS 'dayClickLimit', "
            + " b.new_ad_pos AS 'adPosStr', "
            + " c.id AS 'originalityId', "
            + " c.state AS 'originalityState', "
            + " c.name AS 'originalityName', "
            + " c.ext AS 'ext', "
            + " c.unit_price AS 'unitPrice', "
            + " c.create_time AS 'originalityCreateTime', "
            + " c.update_time AS 'originalityUpdateTime', "
            + " c.first_category_id AS 'firstCategoryId', "
            + " c.second_category_id AS 'secondCategoryId', "
            + " d.balance AS 'balance', "
            + " d.coupon_balance AS 'couponBalance', "
            + " e.balance_account_time AS 'balanceAccountTime', "
            + " f.state AS 'accountState', "
            + " c.verify_state AS 'verifyState' "
            + " FROM "
            + " tb_ad_plan a "
            + " INNER JOIN tb_ad_group b ON a.`id` = b.`plan_id` "
            + " inner JOIN tb_ad_originality c ON b.`id` = c.`group_id` "
            + " INNER JOIN tb_user_finance d ON c.`advertiser_id` = d.`advertiser_id` "
            + " inner join tb_user_account f on d.advertiser_id = f.id "
            + " LEFT JOIN tb_user_costtime e ON f.`id` = e.`advertiser_id` "
            + " where c.id = #{adId} ")
    CustomAdModel getById(@Param("adId") long adId);

    @Select("SELECT "
            + " a.id AS 'planId', "
            + " a.name AS 'planName', "
            + " a.advertiser_id AS 'advertiserId', "
            + " a.state AS 'planState', "
            + " a.type AS 'type', "
            + " a.delivery_type AS 'deliveryType', "
            + " a.daily_budget AS 'dailyBudget', "
            + " a.budget_limit AS 'budgetLimit', "
            + " a.create_time AS 'planCreateTime', "
            + " a.update_time AS 'planUpdateTime', "
            + " a.state_update_time AS 'stateUpdateTime', "
            + " b.id AS 'groupId', "
            + " b.state AS 'groupState', "
            + " b.name AS 'groupName', "
            + " b.delivery_date_period AS 'deliveryDatePeriod', "
            + " b.time_bucket AS 'timeBucket', "
            + " b.product AS 'product', "
            + " b.region AS 'region', "
            + " b.anonymous AS 'anonymous', "
            + " b.sex AS 'sex', "
            + " b.new_user AS 'businessNewUser', "
            + " b.phone_brand AS 'phoneBrand', "
            + " b.age AS 'age', "
            + " b.contains_pkg AS 'containsPkg', "
            + " b.not_contains_pkg AS 'notContainsPkg', "
            + " b.user_channels AS 'userChannels', "
            + " b.create_time AS 'groupCreateTime', "
            + " b.update_time AS 'groupUpdateTime', "
            + " b.platform_version AS 'platformVersion', "
            + " b.register_time AS 'registerTime', "
            + " b.regist_longer AS 'registLonger', "
            + " b.tail_number AS 'tailNumber', "
            + " b.position AS 'position', "
            + " b.income AS 'income', "
            + " b.click_interval AS 'clickInterval', "
            + " b.day_click_limit AS 'dayClickLimit', "
            + " b.new_ad_pos AS 'adPosStr', "
            + " c.id AS 'originalityId', "
            + " c.state AS 'originalityState', "
            + " c.name AS 'originalityName', "
            + " c.ext AS 'ext', "
            + " c.unit_price AS 'unitPrice', "
            + " c.create_time AS 'originalityCreateTime', "
            + " c.update_time AS 'originalityUpdateTime', "
            + " c.first_category_id AS 'firstCategoryId', "
            + " c.second_category_id AS 'secondCategoryId', "
            + " d.balance AS 'balance', "
            + " d.coupon_balance AS 'couponBalance', "
            + " e.balance_account_time AS 'balanceAccountTime', "
            + " f.state AS 'accountState', "
            + " c.verify_state AS 'verifyState' "
            + " FROM "
            + " tb_ad_plan a "
            + " INNER JOIN tb_ad_group b ON a.`id` = b.`plan_id` "
            + " inner JOIN tb_ad_originality c ON b.`id` = c.`group_id` "
            + " INNER JOIN tb_user_finance d ON c.`advertiser_id` = d.`advertiser_id` "
            + " inner join tb_user_account f on d.advertiser_id = f.id "
            + " LEFT JOIN tb_user_costtime e ON f.`id` = e.`advertiser_id` "
            + " WHERE `is_bp_ap` = 1 ")
    List<CustomAdModel> getAll();

    @Select("SELECT "
            + " a.id AS 'planId', "
            + " a.name AS 'planName', "
            + " a.advertiser_id AS 'advertiserId', "
            + " a.state AS 'planState', "
            + " a.type AS 'type', "
            + " a.delivery_type AS 'deliveryType', "
            + " a.daily_budget AS 'dailyBudget', "
            + " a.budget_limit AS 'budgetLimit', "
            + " a.create_time AS 'planCreateTime', "
            + " a.update_time AS 'planUpdateTime', "
            + " a.state_update_time AS 'stateUpdateTime', "
            + " b.id AS 'groupId', "
            + " b.state AS 'groupState', "
            + " b.name AS 'groupName', "
            + " b.delivery_date_period AS 'deliveryDatePeriod', "
            + " b.time_bucket AS 'timeBucket', "
            + " b.product AS 'product', "
            + " b.region AS 'region', "
            + " b.anonymous AS 'anonymous', "
            + " b.sex AS 'sex', "
            + " b.new_user AS 'businessNewUser', "
            + " b.phone_brand AS 'phoneBrand', "
            + " b.age AS 'age', "
            + " b.contains_pkg AS 'containsPkg', "
            + " b.not_contains_pkg AS 'notContainsPkg', "
            + " b.user_channels AS 'userChannels', "
            + " b.create_time AS 'groupCreateTime', "
            + " b.update_time AS 'groupUpdateTime', "
            + " b.platform_version AS 'platformVersion', "
            + " b.register_time AS 'registerTime', "
            + " b.regist_longer AS 'registLonger', "
            + " b.tail_number AS 'tailNumber', "
            + " b.position AS 'position', "
            + " b.income AS 'income', "
            + " b.click_interval AS 'clickInterval', "
            + " b.day_click_limit AS 'dayClickLimit', "
            + " b.new_ad_pos AS 'adPosStr', "
            + " c.id AS 'originalityId', "
            + " c.state AS 'originalityState', "
            + " c.name AS 'originalityName', "
            + " c.ext AS 'ext', "
            + " c.unit_price AS 'unitPrice', "
            + " c.create_time AS 'originalityCreateTime', "
            + " c.update_time AS 'originalityUpdateTime', "
            + " c.first_category_id AS 'firstCategoryId', "
            + " c.second_category_id AS 'secondCategoryId', "
            + " d.balance AS 'balance', "
            + " d.coupon_balance AS 'couponBalance', "
            + " e.balance_account_time AS 'balanceAccountTime', "
            + " f.state AS 'accountState', "
            + " c.verify_state AS 'verifyState' "
            + " FROM "
            + " tb_ad_plan a "
            + " INNER JOIN tb_ad_group b ON a.`id` = b.`plan_id` "
            + " inner JOIN tb_ad_originality c ON b.`id` = c.`group_id` "
            + " INNER JOIN tb_user_finance d ON c.`advertiser_id` = d.`advertiser_id` "
            + " inner join tb_user_account f on d.advertiser_id = f.id "
            + " LEFT JOIN tb_user_costtime e ON f.`id` = e.`advertiser_id` "
            + " where `is_bp_ap` = 1 and a.type = #{adType} ")
    List<CustomAdModel> getByType(@Param("adType") int adType);
}
