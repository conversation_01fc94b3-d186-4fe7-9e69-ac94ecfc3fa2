package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.gateway.web.container.valves
 * @create_time 2019-11-08
 */
@Slf4j
public class AppValve extends DefaultValve {

    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            candidates.removeIf(configId -> !ContainerHolder.getStrategyConfigContainer().getStrategyOrientationConfig(configId).isAppValid(userInfo.getAppId()));
        }
    }
}
