package com.coohua.ap.support.core.container;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.container.thread.GlobalConfigContainerScheduledThreadFactory;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.factory.GlobalConfigFactory;
import com.coohua.ap.support.core.spring.model.GlobalConfigModel;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认的全局配置容器实现
 * <AUTHOR>
 */
@Slf4j
@SpiMeta(name = DefaultGlobalConfigContainer.CONTAINER_NAME)
public class DefaultGlobalConfigContainer extends DefaultConfigContainer implements GlobalConfigContainer {

    private static final long FIXED_RATE = 1000 * 60 * 2;

    public static final String CONTAINER_NAME = "DefaultGlobalConfigContainer"; // 容器名称

    // 暂存所有全局配置，Key的格式：appId_key
    private volatile Map<String, String> globalConfigMapper = new ConcurrentHashMap<>();

    private Gson gson = new Gson();


    @Override
    protected long getFixedRate() {
        return FIXED_RATE;
    }

    @Override
    public <T> T getConfig(int appId, String key, Class<T> clazz) {
        String value = getConfig(appId, key);
        if(value == null){
            return null;
        }

        return JSONObject.parseObject(value, clazz);
    }

    @Override
    public <T> T getConfig(int appId, String key, Type type) {
        String value = getConfig(appId, key);
        if(value == null){
            return null;
        }
        return gson.fromJson(value, type);
    }

    public <T> T getConfigNoEx(int appId, String key, T defaultV,Type type) {
        try {
            String value = getConfig(appId, key);
            if(value == null){
                return defaultV;
            }
            return gson.fromJson(value,type);
        }catch (Exception e){
            return defaultV;
        }
    }

    @Override
    public String getConfig(int appId, String key) {
        String realKey = buildKey(appId, key);
        String value = globalConfigMapper.get(realKey);
        if (value == null) {
            realKey = buildKey(App.MASTER.appId(),key);
            value = globalConfigMapper.get(realKey);
        }
        return value;
    }

    @Override
    public void initConfigFactory() {
        this.configFactory = GlobalConfigFactory.getInstance();
    }

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new GlobalConfigContainerScheduledThreadFactory();
    }

    @Override
    void refreshConfigContainer() {
        if (configFactory instanceof GlobalConfigFactory) {
            long startTime = System.currentTimeMillis();
            GlobalConfigFactory factory = (GlobalConfigFactory) configFactory;
            // 批量查询刷新全局配置
            int pageSize = 10000;
            long lastId = 0;
            while (true) {
                List<GlobalConfigModel> models = factory.queryByPage(lastId, pageSize);

                if (CollectionUtils.isEmpty(models)) {
                    break;
                }

                processModels(models);

                lastId = models.get(models.size() - 1).getId();

                if (models.size() < pageSize) {
                    break;
                }
            }
            log.info("全局配置容器初始化完成, 耗时: {}ms", System.currentTimeMillis() - startTime);
//            System.out.println("全局配置容器初始化完成, 耗时: " + (System.currentTimeMillis() - startTime) + "ms");
        }
    }


    private void processModels(List<GlobalConfigModel> models) {
        for (GlobalConfigModel model : models) {
            this.globalConfigMapper.put(buildKey(model.getProduct(), model.getName()), model.getValue());
        }
    }

    private String buildKey(int appId, String key) {
        return appId + "_" + key;
    }
}
