package com.coohua.ap.support.core.discount.handler;

import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import com.weibo.api.motan.util.CollectionUtil;

import java.util.List;
import java.util.Map;

/**
 * 打折系数抽象类
 */
public abstract class AbstractConfigDiscountHandler implements ConfigDiscountHandler {

    private ConfigDiscountHandler next;

    public ConfigDiscountHandler setNext(ConfigDiscountHandler handler) {
        this.next = handler;
        return next;
    }

    public void filter(List<Long> candidates, Map<Long, ApConfigDiscountVo> discountMap, UserMetaForStrategyConfig userInformation) {

        if (CollectionUtil.isEmpty(candidates)) return;

        doFilter(candidates, discountMap, userInformation);

        if (next != null) {
            next.filter(candidates, discountMap, userInformation);
        }
    }

    protected abstract void doFilter(List<Long> candidates, Map<Long, ApConfigDiscountVo> discountMap, UserMetaForStrategyConfig userInformation);
}
