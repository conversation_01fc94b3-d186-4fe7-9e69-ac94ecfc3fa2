package com.coohua.ap.support.core.spring.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
@Data
public class BiddingModel {
    private Integer id;
    // 内部产品Id
    private Integer product;
    // 内部广告Id
    private Long adId;
    private String name;
    // 广告类型
    private Integer adPosType;
    // 广告扩展
    private String ext;
    // 我方广告类型
    private Integer adType;

    private Integer state;

    private Integer switchFlag;

    private String strategyId;

    private String platformVersion;

    private Integer startEcpm;

    private Integer endEcmp;

    private String channelId;

    private String tailNumber;

    private Integer priority;

    private Integer biddingType;

    private Integer playType;

    private Integer filterRegion;

    private Integer lockArea;

    private String brand;

    private String dsp;

    private Integer lockActionPoint;

}
