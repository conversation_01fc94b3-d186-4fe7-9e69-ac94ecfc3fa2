package com.coohua.ap.support.core.container;

import com.coohua.ap.support.core.exception.ConfigClassCastException;

import java.lang.reflect.Type;

/**
 * 全局配置容器
 * <AUTHOR>
 */
public interface GlobalConfigContainer {

    /**
     * 获取配置
     * @param appId 应用ID
     * @param key 配置KEY
     * @param clazz 类型转换
     * @return 如果包含这个KEY，返回给定类型的Value，如果不包含，返回null。
     */
    <T> T getConfig(int appId, String key, Class<T> clazz);

    /**
     * 获取配置
     * @param appId 应用ID
     * @param key 配置KEY
     * @param type 类型转换
     * @return @return 如果包含这个KEY，返回给定类型的Value，如果不包含，返回null。
     */
    <T> T getConfig(int appId, String key, Type type);

    /**
     * 获取配置的原始值
     * @param appId 应用ID
     * @param key 配置KEY
     * @return 数据库中的原始值
     */
    String getConfig(int appId, String key);
}
