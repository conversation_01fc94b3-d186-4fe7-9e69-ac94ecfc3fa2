package com.coohua.ap.support.core.discount.handler;

import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;

import java.util.List;
import java.util.Map;

/**
 * 打折系数处理器
 */
public interface ConfigDiscountHandler {

    ConfigDiscountHandler setNext(ConfigDiscountHandler handler);

    void filter(List<Long> candidates, Map<Long, ApConfigDiscountVo> discountMap, UserMetaForStrategyConfig userInformation);
}
