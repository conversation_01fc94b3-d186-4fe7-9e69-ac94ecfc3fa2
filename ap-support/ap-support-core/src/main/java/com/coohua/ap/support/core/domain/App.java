package com.coohua.ap.support.core.domain;

import lombok.*;

/**
 * <AUTHOR>
 * @since 2023/4/26
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class App {

    public final static App MASTER = new App(){{
        setAppId(-1);
        setProductName("广告中台");
    }};


    private String product;
    private Integer appId;
    private String productName;
    private String pkg;
    private String productGroup;

    public Integer appId(){
        return this.appId;
    }

    public String desc() {
        return this.productName;
    }
}
