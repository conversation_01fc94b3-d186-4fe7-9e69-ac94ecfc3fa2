package com.coohua.ap.support.core.container.event;

/**
 * 广告事件类型
 * <AUTHOR>
 */
public enum AdEventType {

    // 生命周期事件
    CREATE(1, "广告创建"),
    UPDATE(2, "广告更新"),
    DESTROY(3, "广告销毁"),

    // 广告行为事件
    ON_CANDIDATE(4, "广告进入候选列表时"),
    FILTERED(5, "广告被过滤时"),
    ON_SERVING(6, "广告被投放出去时"),
    ON_CLICK(7, "广告被点击时"),
    ON_EXPOSURE(8, "广告被曝光时"),
    ON_REQUEST(9, "第三方广告向第三方拉取广告时"),
    ON_REQUESTS_SUCCESS(10,"第三方广告拉取成功时"),
    ;

    private int eventType;

    private String desc;

    AdEventType(int eventType, String desc) {
        this.eventType = eventType;
        this.desc = desc;
    }

    public int getEventType() {
        return eventType;
    }

    public String getDesc() {
        return desc;
    }
}
