package com.coohua.ap.support.core.spring.model;

import lombok.Data;

import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-11-08
 */
@Data
public class StrategyBaseConfigModel {
    // 主键
    private Integer id;
    // 配置项名称
    private String name;
    // 所属产品
    private Integer product;
    // 配置类型
    private Integer type;
    // 配置
    private String config;
    // 描述
    private String comment;
    // 状态
    private Integer state;
    // 创建时间
    private Date createTime;
    // 最后一次修改时间
    private Date updateTime;
}
