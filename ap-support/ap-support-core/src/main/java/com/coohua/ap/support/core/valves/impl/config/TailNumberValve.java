package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-11-11
 */
@Slf4j
public class TailNumberValve extends DefaultValve {

    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            candidates.removeIf(configId -> !ContainerHolder.getStrategyConfigContainer().getStrategyOrientationConfig(configId).isTailNumberValid(userInfo.getUserId()));
        }
    }
}
