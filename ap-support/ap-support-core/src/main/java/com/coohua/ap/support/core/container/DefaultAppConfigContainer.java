package com.coohua.ap.support.core.container;

import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.container.thread.AppConfigContainerScheduledThreadFactory;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.factory.AppConfigFactory;
import com.coohua.ap.support.core.message.AppMessageHandler;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.config.EnvConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/5/4
 */
@Slf4j
@SpiMeta(name = DefaultAppConfigContainer.CONTAINER_NAME)
public class DefaultAppConfigContainer  extends DefaultConfigContainer{

    public static final String CONTAINER_NAME = "DefaultAppConfigContainer"; // 容器名称

    private static final long FIXED_RATE = 1000 * 60 * 60 * 24; // 定时周期 30分钟全量刷新 降低更新频次 依赖MQ

    private ScheduledExecutorService scheduledExecutorService;

    @Override
    public void initConfigFactory() {
        this.configFactory = AppConfigFactory.getInstance();
    }

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new AppConfigContainerScheduledThreadFactory();
    }

    public void topicRefresh(){
        this.refreshConfigContainer();
    }

    @Override
    void refreshConfigContainer() {
        long time =  System.currentTimeMillis();
        if (this.configFactory instanceof AppConfigFactory){
            List<App> appList =((AppConfigFactory) this.configFactory).getAllApp();
            AppBuilder.refreshBasicApp(appList);
            log.info("==> Load [App] Cost {} ms",System.currentTimeMillis() - time);
        }
    }

    public void init(){
        super.init();
        // 加入到刷新计划
        if (!SpringIoCUtils.getBean(EnvConfig.class).isTest()) {
            if (this.getState() == ContainerState.INIT) {
                log.info("===> Now PRD Env.. Create New scheduled And Listen Topic");
                this.scheduledExecutorService = Executors.newSingleThreadScheduledExecutor(this.threadFactory);
                scheduledExecutorService.scheduleAtFixedRate(this::refreshConfigContainer, FIXED_RATE, FIXED_RATE, TimeUnit.MILLISECONDS);
                // 设置MQ
                this.setAdMessageHandler();
            }
        }
    }

    public void setAdMessageHandler() {
        this.adMessageHandler = new AppMessageHandler();
        adMessageHandler.init();
    }

    public void stoped(){
        super.stoped();
        scheduledExecutorService.shutdown();
        this.adMessageHandler.shutdown();
        log.info("[APP] scheduledExecutorService ShutDown...");
    }


    public void refresh(){
        if (SpringIoCUtils.getBean(EnvConfig.class).isTest()){
            super.refresh();
        }
    }
}
