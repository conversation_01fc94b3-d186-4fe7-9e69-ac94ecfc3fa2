package com.coohua.ap.support.core.components;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 视频源数据
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AdVideoSource implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer videoId;

    private String videoName;

    private String appName;

    private Date editTime;

    private String director;

    private String editor;

    private String actor;

    private String videographer;

    private String eTag;

    private String videoUrl;

    private String videoProperty;

    private Date onlineTime;

    private Date deliveryTime;

    private String ideaInfo;

    private Date createTime;

    private Date updateTime;

}
