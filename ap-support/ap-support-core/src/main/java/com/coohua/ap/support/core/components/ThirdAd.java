package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.domain.*;
import com.coohua.ap.support.core.container.ThirdAdContainer;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * 第三方广告
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@ToString
@Slf4j
public class ThirdAd extends DefaultAd {
    /**
     * 广告详细物料信息
     *
     * @see ThirdAdExt
     */
    private ThirdAdExt ext;

    public ThirdAd() {
    }

    public ThirdAd(ThirdAdModel model, ThirdAdContainer adContainer) {
        transfer(model, adContainer);
        this.created();
    }

    private void transfer(ThirdAdModel model, ThirdAdContainer adContainer) {
        this.adContainer = adContainer;
        this.adId = model.getId();
        this.name = model.getName();
        this.type = model.getType();
        this.adSource = AdSource.THIRD;
        this.state = model.getState();
        this.createTime = model.getCreateTime();
        this.updateTime = model.getUpdateTime();
        this.putDatePeriod = JSONObject.parseObject(model.getPutDatePeriod(), PutDatePeriod.class);
        this.timeBucket = JSONObject.parseObject(model.getTimeBucket(), TimeBucket.class);
        this.adPos = convertCommaStringToIntListForAdPos(model.getAdPos());
        this.apps = Lists.newArrayList(model.getProduct());
        this.region = JSONObject.parseObject(model.getRegion(), Region.class);
        this.platformVersion = JSONObject.parseObject(model.getPlatformVersion(), PlatformVersion.class);
        this.containsPackages = StringUtils.isEmpty(model.getContainsPkg()) ? new ArrayList<>() : JSONArray.parseArray(model.getContainsPkg(), String.class);
        this.notContainsPackages = StringUtils.isEmpty(model.getNotContainsPkg()) ? new ArrayList<>() : JSONArray.parseArray(model.getNotContainsPkg(), String.class);
        RegisterTimeOrientation registerTimeOrientation = JSONObject.parseObject(model.getRegisterTime(), RegisterTimeOrientation.class);
        if (StringUtils.isNotEmpty(registerTimeOrientation.getStartTime()) && registerTimeOrientation.getStartTime().contains("NaN")) {
            registerTimeOrientation.setStartTime("");
        }
        if (StringUtils.isNotEmpty(registerTimeOrientation.getEndTime()) && registerTimeOrientation.getEndTime().contains("NaN")) {
            registerTimeOrientation.setEndTime("");
        }
        if (StringUtils.isEmpty(registerTimeOrientation.getStartTime()) && StringUtils.isEmpty(registerTimeOrientation.getEndTime())) {
            registerTimeOrientation.setNoLimit(1);
        }
        this.registerTimeOrientation = registerTimeOrientation;
        this.registLongerOrientation = StringUtils.isEmpty(model.getRegistLonger()) ? null : JSONObject.parseObject(model.getRegistLonger(), RegistLongerOrientation.class);
        this.tailNumber = StringUtils.isEmpty(model.getTailNumber()) ? new ArrayList<>() : Arrays.asList(model.getTailNumber().split(AdConstants.SYMBOL_COMMA));
        this.incomeOrientation = StringUtils.isEmpty(model.getIncome()) ? new IncomeOrientation().initDefault() : JSONObject.parseObject(model.getIncome(), IncomeOrientation.class);
        this.clickInterval = model.getClickInterval();
        this.dayClickLimit = model.getDayClickLimit();
        this.exposureInterval = model.getExposureInterval();
        this.dayExposureLimit = model.getDayExposureLimit();
        this.budget = StringUtils.isEmpty(model.getBudget()) ? new Budget().initDefault() : JSONObject.parseObject(model.getBudget(), Budget.class);
        this.userPkg = StringUtils.isEmpty(model.getUserPkg()) ? new ArrayList<>() : Arrays.asList(model.getUserPkg().split(AdConstants.SYMBOL_COMMA));
        this.userChannels = StringUtils.isEmpty(model.getChannelId()) ? new ArrayList<>() : Arrays.asList(model.getChannelId().split(AdConstants.SYMBOL_COMMA));
        String brand = model.getBrand();
        if (StringUtils.isNotEmpty(brand)) {
            this.brandLimit = JSONObject.parseObject(brand, BrandLimitDomain.class);
        } else {
            BrandLimitDomain brandLimitDomain = new BrandLimitDomain();
            brandLimitDomain.setBrandLimit(0);
            brandLimitDomain.setBrandLimitList(new ArrayList<>());
            this.brandLimit = brandLimitDomain;
        }
        this.ecpm = model.getEcpm();
        this.abPoint = model.getAbPoint();
        this.filterRegion = model.getFilterRegion();
        this.lockArea = model.getLockArea();
        this.ext = new ThirdAdExt(model.getExt());
    }

}
