package com.coohua.ap.support.core.container;

import com.coohua.ap.support.core.domain.App;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/4/26
 */
@Slf4j
public class AppBuilder {
    private static Map<Integer, App> basicMap = new ConcurrentHashMap<>();
    private static Map<String, App> productMap = new ConcurrentHashMap<>();

    private final static App DEFAULT_APP = new App(){{
        setAppId(0);
        setProduct("NONE");
        setProductName("未知产品");
    }};

    // 半小时强制更新一次
    public static void refreshBasicApp(List<App> appList){
        log.info("===> Start Refresh App RoadMap..");
        if (appList.size() >0){
            if (appList.size() >= basicMap.values().size()){
                basicMap = appList.stream().collect(Collectors.toMap(App::getAppId,r->r,(r1,r2)->r2));
            }
            if (appList.size() >= productMap.values().size()){
                productMap = appList.stream().collect(Collectors.toMap(App::getProduct,r->r,(r1,r2)->r2));
            }
        }
        log.info("===> Complete Refresh App RoadMap..");
    }

    public static App getById(Integer id){
        return basicMap.get(id);
    }

    public static App getByProduct(String product){
        return productMap.getOrDefault(product,DEFAULT_APP);
    }

    public static App like(String appName) {
        for (App app : basicMap.values()) {
            if (appName.contains(app.desc())) {
                return app;
            }
        }
        return null;
    }


    public static List<App> values() {
        return new ArrayList<>(basicMap.values());
    }
}
