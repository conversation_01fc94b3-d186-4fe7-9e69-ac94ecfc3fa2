package com.coohua.ap.support.core.valves.impl.bidding;

import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.support.core.components.BiddingAd;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class TestAdAccountValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInfoBidding){
            UserInfoBidding userInfoBidding = (UserInfoBidding) userInformation;
            if (OSType.ANDROID == userInfoBidding.getOsType()) {
                return;
            }
            Set<Long> testAds = candidates.stream()
                    .filter(adId -> {
                        BiddingAd bidding = biddingContainer.getBidding(adId);
                        if (bidding == null || Strings.isEmpty(bidding.getName())){
                            return false;
                        }
                        return bidding.getName().contains("测试")
                                && BooleanUtils.isFalse(userInfoBidding.getIsTestUser());

                    })
                    .collect(Collectors.toSet());
            if (testAds.size() > 0) {
                log.info("跳过测试广告Bidding {} {}", userInfoBidding.getUserId(), testAds.size());
                candidates.removeIf(adId -> testAds.contains(adId));
            }
        }
    }
}