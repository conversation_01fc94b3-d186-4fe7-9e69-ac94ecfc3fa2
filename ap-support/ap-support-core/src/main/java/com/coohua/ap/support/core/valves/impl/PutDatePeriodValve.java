package com.coohua.ap.support.core.valves.impl;

import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;

import java.util.List;

/**
 * 广告投放日期过滤
 * <AUTHOR>
 */
public class PutDatePeriodValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            UserInformation information = (UserInformation) userInformation;
            candidates.removeIf(adId -> !getAd(adId, information.getAdSource()).isPutDatePeriodValid());
        }
    }
}
