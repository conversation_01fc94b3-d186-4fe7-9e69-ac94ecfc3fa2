package com.coohua.ap.support.core.container;

import com.coohua.ap.support.core.container.event.EventPublisher;
import com.coohua.ap.support.core.container.lifecycle.ContainerLifeCycle;

/**
 * 容器声明
 * <AUTHOR>
 */
public interface Container extends ContainerLifeCycle, EventPublisher {
    /**
     * 设置广告容器名称
     */
    void setName();

    /**
     * 获取广告容器名称
     */
    String getName();

    /**
     * 设置广告容器状态
     * @param adContainerState 广告容器状态
     * @see ContainerState
     */
    void setState(ContainerState adContainerState);

    /**
     * 获取广告容器状态
     * @see ContainerState
     */
    ContainerState getState();
}
