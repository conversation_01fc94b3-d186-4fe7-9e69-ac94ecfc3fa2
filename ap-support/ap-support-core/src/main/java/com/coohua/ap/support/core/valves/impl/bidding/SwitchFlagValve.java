package com.coohua.ap.support.core.valves.impl.bidding;

import com.coohua.ap.support.core.components.BiddingAd;
import com.coohua.ap.support.core.valves.DefaultAdValve;

import java.util.List;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
public class SwitchFlagValve extends DefaultAdValve implements Predicate<BiddingAd> {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        candidates.removeIf(id -> biddingContainer.getBidding(id).getSwitchFlag().equals(0));
        candidates.removeIf(id -> biddingContainer.getBidding(id).getState().equals(0));
    }

    @Override
    public boolean test(BiddingAd biddingAd) {
        return !biddingAd.getSwitchFlag().equals(0);
    }
}
