package com.coohua.ap.support.core.spring.mapper.config;

import com.coohua.ap.support.core.spring.model.GlobalConfigModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 全局配置DB操作
 * <AUTHOR>
 */
public interface GlobalConfigMapper {

    @Select("       select `id` AS 'id',                                  " +
            "       `name` AS 'name',                                 " +
            "       `value` AS 'value',                               " +
            "       `comment` AS 'comment',                               " +
            "       `create_time` AS 'createTime',                        " +
            "       `update_time` AS 'updateTime',                                " +
            "       `product` AS 'product'                                " +
            "       from `bp-ap`.tb_ap_config                                     ")
    List<GlobalConfigModel> getAllConfig();

    @Select(" select `id` AS 'id', " +
            " `name` AS 'name', " +
            " `value` AS 'value', " +
            " `comment` AS 'comment', " +
            " `create_time` AS 'createTime', " +
            " `update_time` AS 'updateTime', " +
            " `product` AS 'product' " +
            " from tb_ap_config " +
            " where `id` = #{id} ")
    GlobalConfigModel getById(@Param("id") int id);

    @Select(" select `id` AS 'id', " +
            " `name` AS 'name', " +
            " `value` AS 'value', " +
            " `comment` AS 'comment', " +
            " `create_time` AS 'createTime', " +
            " `update_time` AS 'updateTime', " +
            " `product` AS 'product' " +
            " from `bp-ap`.tb_ap_config " +
            " where id > #{lastId} order by id limit #{pageSize} ")
    List<GlobalConfigModel> queryByPage(@Param("lastId") long lastId, @Param("pageSize") int pageSize);
}
