package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.AdConfigType;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.base.domain.*;
import com.coohua.ap.base.utils.ListUtils;
import com.coohua.ap.support.core.spring.model.StrategyBaseConfigModel;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/10
 */
@ToString
public class StrategyBaseConfig extends DefaultConfig {
    /**
     * 配置项名称
     */
    private String name;
    /**
     * 配置项所属产品线
     */
    private Integer product;
    /**
     * 配置项类型
     */
    private Integer type;
    /**
     * 配置值
     */
    @Getter
    private Object config;
    /**
     * 配置说明
     */
    private String comment;
    /**
     * 配置状态
     * 0-无效，1-有效
     */
    private Integer state;

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public StrategyBaseConfig(StrategyBaseConfigModel model) {
        setId(model.getId().longValue());
        this.name = model.getName();
        this.product = model.getProduct();
        this.type = model.getType();
        this.comment = model.getComment();
        this.state = model.getState();
        if (this.type == ConfigType.AD_CONFIG.code()) {
            this.config = parseAdConfig(model.getConfig());
        } else if (this.type == ConfigType.AD_DEFAULT_CONFIG.code()
                || this.type == ConfigType.APP_DEFAULT_CONFIG.code()
                || this.type == ConfigType.APP_DEFAULT_GOLD_CONFIG.code()) {
            this.config = parseDefaultAdConfig(model.getConfig());
        } else if (this.type == ConfigType.PLACEMENT.code()){
            this.config = parseAdConfigNew(model.getConfig());
        }
    }

    private AdConfigDomain parseAdConfig(String config) {
        AdConfigDomain adConfigDomain = new AdConfigDomain();
        List<AdConfigItemDomain> ret = new CopyOnWriteArrayList<>();

        JSONArray array = JSONArray.parseArray(config);
        for (int index = 0; index < array.size(); index++) {
            JSONObject item = array.getJSONObject(index);
            AdConfigItemDomain itemDomain = new AdConfigItemDomain();
            itemDomain.setRepeat(item.getInteger("repeat"));
            itemDomain.setAdTypeList(ListUtils.parseRateStringToList(item.getString("adType")));
            ret.add(itemDomain);
        }

        adConfigDomain.setItemDomains(ret);
        return adConfigDomain;
    }

    private AdConfigNewDomain parseAdConfigNew(String config){
        if (StringUtils.isEmpty(config)){
            return new AdConfigNewDomain();
        }
        AdConfigNewDomain newDomain = new AdConfigNewDomain();
        List<AdConfigNewVo> adConfigNewVoList = JSONArray.parseArray(config,AdConfigNewVo.class);
        List<AdConfigNewItemDomain> adConfigNewItemDomains = new ArrayList<>();
        Map<Integer,List<AdConfigNewVo>> configMap = adConfigNewVoList.parallelStream()
                .collect(Collectors.groupingBy(AdConfigNewVo::getConfigType));
        configMap.forEach((configType,configList) -> {
            AdConfigNewItemDomain domain = new AdConfigNewItemDomain();
            domain.setConfigType(configType);
            if (AdConfigType.BASE_CONFIG.getCode().equals(configType)){
                domain.setWallItemDomains(configList.stream()
                        .map(this::convertConfig).collect(Collectors.toList()));
            }else if (AdConfigType.TOP_CONFIG.getCode().equals(configType)){
                domain.setItemDomains(configList.stream()
                        .map(configVo -> {
                            AdConfigDomain defaultConfigItemDomain = new AdConfigDomain();
                            defaultConfigItemDomain.setItemDomains(Collections.singletonList(new AdConfigItemDomain(){{
                                setRepeat(configVo.getRepeat());
                                setAdTypeList(ListUtils.parseRateStringToList(configVo.getAdType()));
                            }}));
                            return defaultConfigItemDomain;
                        }).collect(Collectors.toList()));
            }else if (AdConfigType.WATERFALLS_CONFIG.getCode().equals(configType)){
                domain.setWallItemDomains(configList.stream()
                        .map(this::convertConfig).collect(Collectors.toList()));
            }

            adConfigNewItemDomains.add(domain);
        });

        newDomain.setDomainList(adConfigNewItemDomains);
        return newDomain;
    }

    private AdDefaultConfigItemDomain convertConfig(AdConfigNewVo configVo){
        AdDefaultConfigItemDomain defaultConfigItemDomain = new AdDefaultConfigItemDomain();
        defaultConfigItemDomain.setTargetType(configVo.getTargetType());
        defaultConfigItemDomain.setLayer(configVo.getLayer().stream()
                .map(ListUtils::parseRateStringToList).collect(Collectors.toList()));
        return defaultConfigItemDomain;
    }

    private AdDefaultConfigDomain parseDefaultAdConfig(String config) {
        AdDefaultConfigDomain configDomain = new AdDefaultConfigDomain();
        List<AdDefaultConfigItemDomain> itemDomains = new CopyOnWriteArrayList<>();

        JSONArray array = JSONArray.parseArray(config);
        for (int index = 0; index < array.size(); index++) {
            JSONObject item = array.getJSONObject(index);
            AdDefaultConfigItemDomain itemDomain = new AdDefaultConfigItemDomain();
            itemDomain.setTargetType(item.getInteger("targetType"));

            JSONArray innerArray = item.getJSONArray("layer");
            List<List<Integer>> retLayer = new CopyOnWriteArrayList<>();
            for (int j = 0; j < innerArray.size(); j++) {
                String types = innerArray.getString(j);
                retLayer.add(ListUtils.parseRateStringToList(types));
            }
            itemDomain.setLayer(retLayer);

            itemDomains.add(itemDomain);
        }
        configDomain.setItemDomains(itemDomains);
        return configDomain;
    }
}
