package com.coohua.ap.support.core.container;

import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ApSupportConstants;
import com.coohua.ap.support.core.components.DefaultAd;
import com.coohua.ap.support.core.spring.ContainerHolder;

import java.util.List;

/**
 * 广告容器工厂，根据method参数，从不同的容器获取广告并返回
 * <AUTHOR>
 */
public class AdMetaInfoFactory {

    public static DefaultAd getAd(Long adId) {
        if (adId == null) {
            return null;
        }
//        if (adId >= ApSupportConstants.THIRD_AND_CUSTOM_AD_ID_CRITICAL_VALUE) { // 直客广告
//            if (adId >= ApSupportConstants.NEW_CUSTOMER_AD_ID) {
//                // 新版直客广告
//                return ContainerHolder.getNewCustomAdContainer().getAd(adId);
//            }
//            return ContainerHolder.getCustomAdContainer().getAd(adId);
//        }
        return ContainerHolder.getThirdAdContainer().getAd(adId);
    }

}
