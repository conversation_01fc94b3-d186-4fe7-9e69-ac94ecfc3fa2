package com.coohua.ap.support.core.valves.impl.newcustom;

import com.coohua.ap.support.core.components.NewCustomAd;
import com.coohua.ap.support.core.spring.RedisManager;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import redis.clients.jedis.Builder;
import redis.clients.jedis.BuilderFactory;
import redis.clients.jedis.Response;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/9
 * 新后台-广告预算控制链
 */
public class DailyCostValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        // 计划控制链
        if (userInformation instanceof UserInformation) {

            Map<String,Long> planCostMap = new HashMap<>();
            Map<String,Long> advertiserDailyCostMap = new HashMap<>();

            filterMap(candidates,advertiserDailyCostMap,planCostMap);
            candidates.removeIf(adId -> !isAmountUseful(adId,advertiserDailyCostMap,planCostMap));
        }
    }

    private void filterMap(List<Long> candidates, Map<String,Long> advertiserDailyCostMap,Map<String,Long> planCostMap){

        List<NewCustomAd> newCustomAdList = candidates.stream().map(newCustomAdContainer::getAd).collect(Collectors.toList());
        List<String> advertiserKeyList = newCustomAdList.stream()
                .map(newCustomAd -> RedisManager.buildAdvertiserKey(newCustomAd.getAdvertiserId())).collect(Collectors.toList());

        List<String> planKeyList = newCustomAdList.stream()
                .map(newCustomAd -> RedisManager.buildPlanKey(newCustomAd.getPlanId())).collect(Collectors.toList());
        Map<String, Response<String>> advertiserResMap = RedisManager.redisClusterPipeline(advertiserKeyList);
        Map<String, Response<String>> planResMap = RedisManager.redisClusterPipeline(planKeyList);

        advertiserResMap.keySet().forEach(advertiserKey -> {
            Response<String> response = advertiserResMap.get(advertiserKey);
            Long cost =  Long.parseLong(response.get() == null ? "0":response.get()) / 1000;
            advertiserDailyCostMap.put(advertiserKey,cost);
        });

        planResMap.keySet().forEach(planKey ->{
            Response<String> response = planResMap.get(planKey);

            Long cost =  Long.parseLong(response.get() == null ? "0":response.get()) / 1000;
            advertiserDailyCostMap.put(planKey,cost);
        });
    }

    private boolean isAmountUseful(Long adId,Map<String,Long> advertiserDailyCostMap,Map<String,Long> planCostMap){
        NewCustomAd newCustomAd = newCustomAdContainer.getAd(adId);
        // 检查广告预算
        if (newCustomAd.getBudget() < planCostMap.getOrDefault(
                RedisManager.buildPlanKey(newCustomAd.getPlanId()),0L)){
            return false;
        }
        // 检查广告主余额
        if (newCustomAd.getBalance() < advertiserDailyCostMap.getOrDefault(
                RedisManager.buildAdvertiserKey(newCustomAd.getAdvertiserId()), 0L)){
            return false;
        }

        // 广告主日预算控制
        return newCustomAd.getAdvertiserBudget() >= advertiserDailyCostMap.getOrDefault(
                RedisManager.buildAdvertiserKey(newCustomAd.getAdvertiserId()), 0L);
    }
}
