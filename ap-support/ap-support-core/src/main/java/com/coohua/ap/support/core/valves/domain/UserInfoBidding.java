package com.coohua.ap.support.core.valves.domain;

import com.coohua.ap.base.constants.OSType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/29
 */
@Data
public class UserInfoBidding {

    private String appVersion;

    private Long userId;

    private Integer appId;

    private OSType osType;

    private String channel;

    private String brand;

    private Boolean filterRegion;

    private Boolean lockedArea;

    private List<Integer> skipAdPlatformList;

    private List<Long> directSkipAdList;

    private List<String> skipTTAppIdList;

    private List<Long> skipAdList;

    private List<Long> skipAdListKs;

    private Integer activeChannel;

    private Boolean isTestUser;

    private String dsp;

    /**
     * 锁区作用点
     */
    private Boolean isFirstLockActionPoint;
    private List<Integer> enableLockActionPointList;
}
