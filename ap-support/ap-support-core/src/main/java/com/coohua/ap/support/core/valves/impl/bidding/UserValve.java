package com.coohua.ap.support.core.valves.impl.bidding;

import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/16
 */
public class UserValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInfoBidding){
            UserInfoBidding userInfoBidding = (UserInfoBidding) userInformation;

            candidates.removeIf(id ->
                    !ContainerHolder.getBiddingContainer().getBidding(id)
                            .isTailNumberValid(userInfoBidding.getUserId()));
        }
    }
}
