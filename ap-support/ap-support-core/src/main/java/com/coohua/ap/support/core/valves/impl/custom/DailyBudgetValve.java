package com.coohua.ap.support.core.valves.impl.custom;

import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.support.core.components.CustomAd;
import com.coohua.ap.support.core.spring.RedisManager;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import redis.clients.jedis.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 直客广告日预算控制
 * <AUTHOR>
 */
public class DailyBudgetValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            Map<Long, Long> adPlanCost = new HashMap<>(); // 记录直客广告已经消耗的日预算
            Map<Long, Long> adPlanClick = new HashMap<>(); // 记录直客广告已经消耗的日点击预算
            Map<Long, Long> adPlanExposure = new HashMap<>(); // 记录直客广告已经消耗的日曝光预算
            fillDailyBudgetMap(adPlanCost, adPlanClick, adPlanExposure, candidates);
            candidates.removeIf(adId -> !customAdContainer.getAd(adId).isDailyBudgetValid(adPlanCost, adPlanClick, adPlanExposure));
        }
    }

    private void fillDailyBudgetMap(Map<Long, Long> adPlanCost, Map<Long, Long> adPlanClick, Map<Long, Long> adPlanExposure, List<Long> preList) {
        List<String> keys = new ArrayList<>();
        Map<String, Long> planIdTemp = new HashMap<>();
        for (Long adId : preList) {
            CustomAd customAd = customAdContainer.getAd(adId);
            // 广告计划的日消耗
            String costKey = RedisManager.buildRedisAdCharge(customAd.getPlanId());
            keys.add(costKey);
            planIdTemp.put(costKey, customAd.getPlanId());
            // 暂时没有地方写入下面两个key，先去掉，后面有需求时再加回来
            // 广告计划的日点击
//            String clickKey = RedisManager.buildRedisAdChargeForClick(customAd.getPlanId());
//            keys.add(clickKey);
//            planIdTemp.put(clickKey, customAd.getPlanId());
            // 广告计划的日曝光
//            String exposureKey = RedisManager.buildRedisAdChargeForExposure(customAd.getPlanId());
//            keys.add(exposureKey);
//            planIdTemp.put(exposureKey, customAd.getPlanId());
        }

        if (keys.isEmpty()) {
            return;
        }

        Map<String, Response<String>> fromRedis = RedisManager.redisClusterPipeline(keys);

        for (Map.Entry<String, Response<String>> entry : fromRedis.entrySet()) {
            String key = entry.getKey();
            Response<String> value = entry.getValue();
            if (key.startsWith(RedisConstants.ECP_AD_CHARGE_PRE)) {
                adPlanCost.put(planIdTemp.get(key), value != null && value.get() != null ? Long.parseLong(value.get()) : -1L);
            }
//            else if (key.startsWith(RedisConstants.ECP_AD_CLICK_CHARGE_PRE)) {
//                adPlanClick.put(planIdTemp.get(key), value != null && value.get() != null ? Long.parseLong(value.get()) : -1L);
//            } else if (key.startsWith(RedisConstants.ECP_AD_EXPOSURE_CHARGE_PRE)) {
//                adPlanExposure.put(planIdTemp.get(key), value != null && value.get() != null ? Long.parseLong(value.get()) : -1L);
//            }
        }
    }
}
