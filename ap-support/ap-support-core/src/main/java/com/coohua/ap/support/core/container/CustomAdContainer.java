package com.coohua.ap.support.core.container;

import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ApSupportConstants;
import com.coohua.ap.base.constants.GlobalConfigKey;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.components.AdSource;
import com.coohua.ap.support.core.components.CustomAd;
import com.coohua.ap.support.core.container.thread.CustomAdContainerScheduledThreadFactory;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.factory.CustomAdFactory;
import com.coohua.ap.support.core.spring.model.CustomAdModel;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import com.coohua.ap.support.core.valves.impl.PutDatePeriodValve;
import com.coohua.ap.support.core.valves.impl.StateValve;
import com.coohua.ap.support.core.valves.impl.TimeBucketValve;
import com.coohua.ap.support.core.valves.impl.custom.CpwUnbindValve;
import com.coohua.ap.support.core.valves.impl.custom.DailyBudgetValve;
import com.coohua.ap.support.core.valves.impl.custom.HourBudgetValve;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 直客广告容器
 *
 * <AUTHOR>
 */
@SpiMeta(name = CustomAdContainer.CONTAINER_NAME)
public class CustomAdContainer extends DefaultAdContainer {

    public static final String CONTAINER_NAME = "CustomAdContainer"; // 容器名称

    // 所有广告详细信息映射表：广告ID -> 广告详情（包含invalid的广告）
    private volatile Map<Long, CustomAd> adMapper = new ConcurrentHashMap<>();

    // 广告类型 -> 该广告类型下的所有有效广告ID（不包含invalid的广告）
    private volatile Map<Integer, List<Long>> adTypeMapper = new ConcurrentHashMap<>();

    // 广告组 -> 广告组下所有广告（包含invalid的广告）
    private volatile Map<Long, List<Long>> groupToAdMapper = new ConcurrentHashMap<>();

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }

    @Override
    void initValveChain() {
        Long hourLimit = globalConfigContainer.getConfig(App.MASTER.appId(), GlobalConfigKey.CUSTOM_BUDGET_HOUR_LIMIT, Long.class);
        Long hourNum = globalConfigContainer.getConfig(App.MASTER.appId(), GlobalConfigKey.CUSTOM_BUDGET_HOUR_NUM, Long.class);
        this.valveChain = new DefaultValveChain();
        this.valveChain.setFirst(new StateValve())
                .addValve(new PutDatePeriodValve())
                .addValve(new TimeBucketValve())
                .addValve(new DailyBudgetValve())
                .addValve(new HourBudgetValve(hourLimit, hourNum))
                .addValve(new CpwUnbindValve());
    }

    @Override
    void refreshAdContainer() {
        if (adFactory instanceof CustomAdFactory) {
            Map<Long, CustomAd> newAdMapper = new ConcurrentHashMap<>();
            Map<Integer, List<Long>> newAdTypeMapper = new ConcurrentHashMap<>();
            Map<Long, List<Long>> newGroupToAdMapper = new ConcurrentHashMap<>();

            CustomAdFactory customAdFactory = (CustomAdFactory) adFactory;
            List<CustomAdModel> models = customAdFactory.getAllNewestAd();

            // 构建新广告映射
            for (CustomAdModel model : models) {
                CustomAd transfer = new CustomAd(model, this);
                newAdMapper.put(transfer.getAdId(), transfer);
            }
            this.adMapper = newAdMapper;

            // 执行预过滤
            List<Long> preQueue = new ArrayList<>();
            for (CustomAd customAd : newAdMapper.values()) {
                preQueue.add(customAd.getAdId());
            }
            UserInformation userInformation = new UserInformation();
            userInformation.setAdSource(AdSource.CUSTOM);
            this.valveChain.getFirst().invoke(preQueue, userInformation);

            for (long adId : preQueue) {
                CustomAd customAd = newAdMapper.get(adId);
                if (newAdTypeMapper.containsKey(customAd.getType())) {
                    newAdTypeMapper.get(customAd.getType()).add(adId);
                } else {
                    newAdTypeMapper.put(customAd.getType(), Lists.newArrayList(adId));
                }
                buildGroupToAdMapper(newGroupToAdMapper, customAd);
            }

            this.adTypeMapper = newAdTypeMapper;
            this.groupToAdMapper = newGroupToAdMapper;
        }
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new CustomAdContainerScheduledThreadFactory();
    }

    @Override
    public boolean isMyAdType(int adType) {
        return ApSupportConstants.AD_SOURCE_CUSTOM.equals(AdTypeSub.getAdSource(adType));
    }

    @Override
    public CustomAd getAd(Long adId) {
        return this.adMapper.get(adId);
    }

    @Override
    public List<Long> getAdCollectionByAdType(Integer adType, Integer appId) {
        return this.adTypeMapper.get(adType);
    }

    /**
     * 获取所有有效的广告ID
     *
     * @return 有效广告ID列表
     */
    public List<Long> getAllValidAdIdList() {
        List<Long> ret = new ArrayList<>();
        for (List<Long> validAdId : this.adTypeMapper.values()) {
            ret.addAll(validAdId);
        }
        return ret;
    }

    /**
     * 获取指定广告组下的所有广告ID
     * @param groupId 指定的广告组
     * @return groupId下的所有广告ID
     */
    public List<Long> getAdIdListUnderTargetGroup(Long groupId) {
        if (groupId == null) {
            return null;
        }
        return this.groupToAdMapper.get(groupId);
    }

    @Override
    public void initAdFactory() {
        this.adFactory = CustomAdFactory.getInstance();
    }

    @Override
    public void setAdMessageHandler() {

    }

    private void buildGroupToAdMapper(Map<Long, List<Long>> newGroupToAdMapper, CustomAd customAd) {
        if (newGroupToAdMapper.containsKey(customAd.getGroupId())) {
            newGroupToAdMapper.get(customAd.getGroupId()).add(customAd.getAdId());
        } else {
            newGroupToAdMapper.put(customAd.getGroupId(), Lists.newArrayList(customAd.getAdId()));
        }
    }

}
