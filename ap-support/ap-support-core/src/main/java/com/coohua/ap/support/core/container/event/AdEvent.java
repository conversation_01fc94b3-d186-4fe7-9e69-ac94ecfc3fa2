package com.coohua.ap.support.core.container.event;


import com.coohua.ap.support.core.components.Ad;

import java.util.EventObject;

/**
 * 广告事件
 * <AUTHOR>
 */
public class AdEvent extends EventObject {
    private final Object data;
    private final AdEventType type;

    public AdEvent(Ad source, Object data, AdEventType type) {
        super(source);
        this.data = data;
        this.type = type;
    }

    public Object getData() {
        return this.data;
    }

    public Ad getAd() {
        return (Ad) getSource();
    }

    public AdEventType getType() {
        return this.type;
    }
}
