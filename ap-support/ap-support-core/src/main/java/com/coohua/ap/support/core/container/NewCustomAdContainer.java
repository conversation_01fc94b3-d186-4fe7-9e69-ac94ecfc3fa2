package com.coohua.ap.support.core.container;

import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ApSupportConstants;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.support.core.components.AdSource;
import com.coohua.ap.support.core.components.AdVideoSource;
import com.coohua.ap.support.core.components.NewCustomAd;
import com.coohua.ap.support.core.container.thread.NewCustomAdContainerScheduledThreadFactory;
import com.coohua.ap.support.core.factory.NewCustomAdFactory;
import com.coohua.ap.support.core.spring.model.NewCustomAdModel;
import com.coohua.ap.support.core.valves.chain.DefaultValveChain;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import com.coohua.ap.support.core.valves.impl.newcustom.DailyCostValve;
import com.coohua.ap.support.core.valves.impl.newcustom.SwitchAdValve;
import com.coohua.ap.support.core.valves.impl.newcustom.TimeChangeValve;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/11/6
 */
@SpiMeta(name = NewCustomAdContainer.CONTAINER_NAME)
public class NewCustomAdContainer extends DefaultAdContainer{

    public static final String CONTAINER_NAME = "NewCustomAdContainer"; // 容器名称

    // 所有广告详细信息映射表：广告ID -> 广告详情（包含invalid的广告）
    private volatile Map<Long, NewCustomAd> ecpAdMapper = new ConcurrentHashMap<>();

    // 广告类型 -> 该广告类型下的所有有效广告ID（不包含invalid的广告）
    private volatile Map<Integer, List<Long>> ecpAdTypeMapper = new ConcurrentHashMap<>();

    private volatile Map<Integer, List<Long>> ecpAppAdMapper = new ConcurrentHashMap<>();

    @Override
    void initValveChain() {
        this.valveChain = new DefaultValveChain();
        // 开关 -> 投放时段 -> 日花费
        valveChain.setFirst(new SwitchAdValve())
                .addValve(new TimeChangeValve())
                .addValve(new DailyCostValve());
    }

    @Override
    void refreshAdContainer() {
        if (adFactory instanceof NewCustomAdFactory) {
            NewCustomAdFactory newCustomAdFactory = (NewCustomAdFactory) adFactory;

            List<NewCustomAdModel> newCustomAdModelList = newCustomAdFactory.getAllNewestAd();
            Map<Integer, Date> timeMap =  newCustomAdFactory.queryDeliveryTime(
                    new ArrayList<>(newCustomAdModelList.stream().map(NewCustomAdModel::getVideoId).collect(Collectors.toSet())))
                    .stream().collect(Collectors.toMap(AdVideoSource::getVideoId,AdVideoSource::getDeliveryTime,(a1,a2)->a1));
            // 载入最新的广告信息至容器
            this.ecpAdMapper = newCustomAdModelList.stream()
                    .peek(newCustomModel -> newCustomModel.setDeliveryTime(timeMap.get(newCustomModel.getVideoId())))
                    .collect(Collectors.toMap(NewCustomAdModel::getIdeaId, NewCustomAd::new,(k1, k2) -> k1));

            // 执行预过滤
            List<Long> preQueue = new ArrayList<>(this.ecpAdMapper.keySet());
            this.valveChain.getFirst().invoke(preQueue, new UserInformation(){{setAdSource(AdSource.CUSTOM);}});

            // 按照广告类型过滤
            Map<Integer, List<Long>> newEcpAdTypeMapper = new ConcurrentHashMap<>();
            preQueue.forEach(ad -> {
                NewCustomAd newCustomAd = this.ecpAdMapper.get(ad);
                if (newEcpAdTypeMapper.containsKey(newCustomAd.getType())) {
                    newEcpAdTypeMapper.get(newCustomAd.getType()).add(ad);
                } else {
                    newEcpAdTypeMapper.put(newCustomAd.getType(), Lists.newArrayList(ad));
                }
            });

            this.ecpAdTypeMapper = newEcpAdTypeMapper;
            List<NewCustomAd> newCustomAdList = preQueue.stream().map(ecpAdMapper::get).collect(Collectors.toList());
            this.ecpAppAdMapper = getAppIdMap(newCustomAdList);
        }
    }

    @Override
    void initThreadFactory() {
        this.threadFactory = new NewCustomAdContainerScheduledThreadFactory();
    }

    @Override
    boolean isMyAdType(int adType) {
        return ApSupportConstants.AD_SOURCE_CUSTOM.equals(AdTypeSub.getAdSource(adType));
    }

    @Override
    public NewCustomAd getAd(Long adId) {
        return this.ecpAdMapper.get(adId);
    }

    @Override
    public List<Long> getAdCollectionByAdType(Integer adType, Integer appId) {
        return this.ecpAdTypeMapper.get(adType);
    }

    /**
     * 获取所有有效的广告ID
     *
     * @return 有效广告ID列表
     */
    public List<Long> getAllValidAdIdList() {
        List<Long> ret = new ArrayList<>();
        for (List<Long> validAdId : this.ecpAdTypeMapper.values()) {
            ret.addAll(validAdId);
        }
        return ret;
    }

    /**
     * 获取定向的APP下所有的广告id合集
     * @return appId-广告Id合集
     */
    public Map<Integer,List<Long>> getAppIdMap(){
        return this.ecpAppAdMapper;
    }

    private Map<Integer,List<Long>> getAppIdMap(List<NewCustomAd> newCustomAdList) {
        Map<Integer,List<Long>> resMap = new HashMap<>();
        for (NewCustomAd newCustomAd : newCustomAdList){
            List<Integer> appList = newCustomAd.getSortedApps();
            for (Integer appId: appList){
                List<Long> adIdList = resMap.get(appId);
                if (adIdList == null || adIdList.size() == 0){
                    adIdList = new ArrayList<>();
                }
                if (!adIdList.contains(newCustomAd.getAdId())){
                    adIdList.add(newCustomAd.getAdId());
                }
                resMap.put(appId,adIdList);
            }
        }
        return resMap;
    }


    @Override
    public void initAdFactory() {
        this.adFactory = NewCustomAdFactory.getInstance();
    }

    @Override
    public void setAdMessageHandler() {

    }

    @Override
    public void setName() {
        this.name = CONTAINER_NAME;
    }

}
