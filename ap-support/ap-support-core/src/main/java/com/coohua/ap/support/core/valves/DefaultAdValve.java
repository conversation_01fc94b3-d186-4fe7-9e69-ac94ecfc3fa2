package com.coohua.ap.support.core.valves;

import com.coohua.ap.support.core.components.Ad;
import com.coohua.ap.support.core.components.AdSource;
import com.coohua.ap.support.core.components.DefaultAd;
import com.coohua.ap.support.core.container.BiddingContainer;
import com.coohua.ap.support.core.container.CustomAdContainer;
import com.coohua.ap.support.core.container.NewCustomAdContainer;
import com.coohua.ap.support.core.container.ThirdAdContainer;
import com.coohua.ap.support.core.spring.ContainerHolder;

import java.util.Iterator;

/**
 * 第三方广告过滤 责任链
 * <AUTHOR>
 */
public abstract class DefaultAdValve extends DefaultValve {

    protected ThirdAdContainer thirdAdContainer = ContainerHolder.getThirdAdContainer();

    protected CustomAdContainer customAdContainer = ContainerHolder.getCustomAdContainer();

    protected NewCustomAdContainer newCustomAdContainer = ContainerHolder.getNewCustomAdContainer();

    protected BiddingContainer biddingContainer = ContainerHolder.getBiddingContainer();

    protected DefaultAd getAd(Long adId, AdSource adSource) {
        if (adSource == null) {
            DefaultAd ad = thirdAdContainer.getAd(adId);
            if (ad == null) {
                ad = customAdContainer.getAd(adId);
            }
            return ad;
        }
        return AdSource.THIRD == adSource ? thirdAdContainer.getAd(adId) : customAdContainer.getAd(adId);
    }

    protected void filteredAd(Ad ad, Iterator<Long> iterator, String reason, Long userId) {
        iterator.remove();
        ad.filtered(userId, reason);
    }

}
