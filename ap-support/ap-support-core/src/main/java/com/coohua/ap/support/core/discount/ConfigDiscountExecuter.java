package com.coohua.ap.support.core.discount;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.support.core.discount.handler.*;
import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 打折系数执行器
 */
@Component
@Slf4j
public class ConfigDiscountExecuter {

    /**
     * 缓存打折配置
     * 数据量很少，缓存到内存
     */
    private final Cache<String, Map<Long, ApConfigDiscountVo>> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES) // 设置缓存过期时间
            .build();

    private static final String DISCOUNT_LOCAL_CACHE_KEY = "config:discounts:%s";

    /**
     * 缓存打折配置的广告位
     * 数据量很少，缓存到内存
     */
    private final Cache<String, String> posTypeCache = CacheBuilder.newBuilder()
            .expireAfterWrite(120, TimeUnit.MINUTES) // 设置缓存过期时间
            .build();

    private static final String DISCOUNT_POS_TYPE_LOCAL_CACHE_KEY = "discountPostTypeLocalCache";

    private ConfigDiscountHandler handler;

    public ConfigDiscountExecuter() {
        initHandlers();
    }

    /**
     * 构建责任链
     */
    private void initHandlers() {
        handler = new AppConfigDiscountHandler();
        handler.setNext(new OsConfigDiscountHandler())
                .setNext(new AppVersionConfigDiscountHandler())
                .setNext(new SdkVersionConfigDiscountHandler())
                .setNext(new ChannelIdConfigDiscountHandler())
                .setNext(new SkipChannelConfigDiscountHandler())
                .setNext(new AbTestConfigDiscountHandler());
    }

    /**
     * 选择当前用户的最优打折配置
     * @param apUserInfo 用户信息
     * @return
     */
    public Map<String, ApConfigDiscountVo> chooseConfigDiscount(UserMetaForStrategyConfig apUserInfo) {
//        Map<Long, ApConfigDiscountVo> discountMap = getAllDisCountFromRedis();

        Map<Long, ApConfigDiscountVo> discountMap = getDiscountMap(apUserInfo.getAppId());

        if (discountMap.isEmpty()) return null;

        List<Long> configDiscountIdList = discountMap
                .values()
                .stream()
                .filter(e -> 1 == e.getIsEnabled())
                .map(ApConfigDiscountVo::getId)
                .collect(Collectors.toList());

        if (configDiscountIdList.isEmpty()) return null;

        // 筛选打折配置
        this.handler.filter(configDiscountIdList, discountMap, apUserInfo);

        if (configDiscountIdList.isEmpty()) return null;

        List<ApConfigDiscountVo> discountVos = new ArrayList<>();

        for (Long id : configDiscountIdList) {
            discountVos.add(discountMap.get(id));
        }


        // 根据adPos，选择最优
        return getOptimalDiscountVos(discountVos);
    }

    /**
     * 根据adPos，选择最优
     * @param discountVos
     * @return
     */
    private Map<String, ApConfigDiscountVo> getOptimalDiscountVos(List<ApConfigDiscountVo> discountVos) {
        Map<String, ApConfigDiscountVo> optimalDiscounts = new HashMap<>();

        for (ApConfigDiscountVo vo : discountVos) {
            String adPosAllStr = vo.getAdPos();
            // 拆分 adPos 字段
            if ("all".equals(adPosAllStr)) {
                adPosAllStr = posTypeCache.getIfPresent(DISCOUNT_POS_TYPE_LOCAL_CACHE_KEY);

                if (adPosAllStr == null) {
                    // 如果缓存中没有，则从数据库中获取
                    List<String> adPosList = getAllPosTypeFromRedis();
                    adPosAllStr = String.join(AdConstants.SYMBOL_COMMA, adPosList);
                    posTypeCache.put(DISCOUNT_POS_TYPE_LOCAL_CACHE_KEY, adPosAllStr);
                }
            }
            String[] adPosArray = adPosAllStr.split(AdConstants.SYMBOL_COMMA);

            for (String adPos : adPosArray) {
                adPos = adPos.trim(); // 去除前后空格

                // 获取当前 adPos 对应的最优对象
                ApConfigDiscountVo currentOptimal = optimalDiscounts.get(adPos);

                // 如果当前对象的 priority 更高，则更新最优对象
                if (currentOptimal == null || currentOptimal.compareTo(vo) > 0) {
                    optimalDiscounts.put(adPos, vo);
                }
            }
        }

        // 将所有最优对象封装成列表返回
        return optimalDiscounts;
    }

    private Map<Long, ApConfigDiscountVo> getDiscountMap(Integer appId) {
        String localKey = String.format(DISCOUNT_LOCAL_CACHE_KEY, appId);
        Map<Long, ApConfigDiscountVo> discountMap = cache.getIfPresent(localKey);

        if (discountMap == null) {
            discountMap = getAllDisCountFromRedis(appId);
            if (MapUtils.isNotEmpty(discountMap)) {
                cache.put(localKey, discountMap);
            }
        }

        return discountMap;
    }

    private List<String> getAllPosTypeFromRedis() {
        try {
            JedisClusterClient jedisClusterClient = SpringIoCUtils.getJedisClusterClient();
            Set<String> smembers = jedisClusterClient.smembers(RedisConstants.CONFIG_DISCOUNT_AD_POS_TYPE);
            if (CollectionUtils.isEmpty(smembers)) {
                return Collections.emptyList();
            }
            return new ArrayList<>(smembers);
        } catch (Exception e) {
            log.warn("getAllPosTypeFromRedis error", e);
        }
        return Collections.emptyList();
    }

    /**
     * 从redis中获取所有打折配置
     * @return
     */
    private Map<Long, ApConfigDiscountVo> getAllDisCountFromRedis(Integer appId) {
        Map<Long, ApConfigDiscountVo> discountVoMap = new HashMap<>();
        Map<String, String> map = new HashMap<>();


        try (Jedis jedis = SpringIoCUtils.getJedisClusterClient().getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            String key1 = RedisBuilder.buildConfigDiscount(String.valueOf(appId));
            String key2 = RedisBuilder.buildConfigDiscount("all");
            // 批量获取两个hash key下的所有数据
            Pipeline pipelined = jedis.pipelined();
            pipelined.hgetAll(key1);
            pipelined.hgetAll(key2);
            List<Object> results = pipelined.syncAndReturnAll();
            if (CollectionUtils.isNotEmpty(results)) {
                for (Object result : results) {
                    if (result == null) {
                        continue;
                    }
                    if (result instanceof Map) {
                        map.putAll((Map<String, String>) result);
                    }
                }
            }

        } catch (Exception e) {
            log.warn("getAllDisCountFromRedis error", e);
        }

        if (map == null || map.isEmpty()) return discountVoMap;

        for (Map.Entry<String, String> entry : map.entrySet()) {
            ApConfigDiscountVo discountVo = JSONObject.parseObject(entry.getValue(), ApConfigDiscountVo.class);
            discountVoMap.put(discountVo.getId(), discountVo);
        }

        return discountVoMap;

    }

}
