package com.coohua.ap.support.core.valves.impl.custom;

import com.coohua.ap.support.core.spring.RedisManager;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Response;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 每小时广告控量
 * <AUTHOR>
 */
public class HourBudgetValve extends DefaultAdValve {
    // 每小时的限制出量
    private Long hourLimit;
    // 拆分的小时数
    private Long hourNum;

    public HourBudgetValve(Long hourLimit, Long hourNum) {
        this.hourLimit = hourLimit;
        this.hourNum = hourNum;
    }

    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            Map<Long, Long> hourBudget = fillHourBudgetMap(candidates);
            candidates.removeIf(adId -> !customAdContainer.getAd(adId).isHourBudgetValid(hourBudget, hourLimit, hourNum));
        }
    }

    private Map<Long, Long> fillHourBudgetMap(List<Long> preList) {
        Map<Long, Long> adHourClick = new HashMap<>();

        Map<String, Long> keyAdIdMap = new HashMap<>();
        List<String> keys = new ArrayList<>();
        for (Long adId : preList) {
            String redisKey = RedisManager.buildRedisAdClickHourCounter(adId);
            keyAdIdMap.put(redisKey, adId);
            keys.add(redisKey);
        }
        Map<String, Response<String>> fromRedis = RedisManager.redisClusterPipeline(keys);

        for (Map.Entry<String, Response<String>> entry : fromRedis.entrySet()) {
            String key = entry.getKey();
            Response<String> value = entry.getValue();
            adHourClick.put(keyAdIdMap.get(key), value != null && StringUtils.isNotEmpty(value.get()) ? Long.parseLong(value.get()) : 0L);
        }

        return adHourClick;
    }
}
