package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-11-11
 */
@Slf4j
public class RegionValve extends DefaultValve {

    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            // 锁区作用点(一道锁、二道锁)
            candidates.removeIf(configId -> !ContainerHolder.getStrategyConfigContainer().getStrategyOrientationConfig(configId).isLockedArea(userInfo.getIsLockedArea(), userInfo.getIsFirstLockActionPoint()));
            candidates.removeIf(configId -> !ContainerHolder.getStrategyConfigContainer().getStrategyOrientationConfig(configId).isRegionValid(userInfo.getFilterRegion()));

            // App
            if (900 == userInfo.getAppId() && OSType.ANDROID.getCode() ==userInfo.getOs() && candidates.contains(9754L)){
                // 判断用户是否是 OCPC 或者是审核渠道
                if ("ksshenhe".equals(userInfo.getChannelId())){
                    log.warn("命中安天整改策略 用户 {} {},使用指定策略",userInfo.getAppId(),userInfo.getUserId());
                    // 非OCPC只保留9754
                    candidates.removeIf(configId -> 9754L != configId);
                }
            }
        }
    }
}
