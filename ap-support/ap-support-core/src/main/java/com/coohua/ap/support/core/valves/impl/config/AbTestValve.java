package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.support.core.components.StrategyOrientationConfig;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/2/27
 */
public class AbTestValve extends DefaultValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            List<StrategyOrientationConfig> configs = candidates.stream()
                    .map(ContainerHolder.getStrategyConfigContainer()::getStrategyOrientationConfig)
                    .filter(config -> config.getAbTestList() != null && config.getAbTestList().contains(userInfo.getCategoryId()))
                    .collect(Collectors.toList());
            if (configs.size() > 0){
                List<Long> joinAbTest = configs.stream().map(StrategyOrientationConfig::getId).collect(Collectors.toList());
                // 只保留参与AB的策略
                candidates.removeIf(id -> !joinAbTest.contains(id));
            }
        }
    }
}
