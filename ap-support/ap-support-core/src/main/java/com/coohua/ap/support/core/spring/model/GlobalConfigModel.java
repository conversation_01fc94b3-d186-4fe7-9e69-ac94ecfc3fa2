package com.coohua.ap.support.core.spring.model;

import lombok.Data;

import java.util.Date;

/**
 * 全局配置DB模型
 * <AUTHOR>
 */
@Data
public class GlobalConfigModel {

    /**
     * 配置ID
     */
    private Integer id;
    /**
     * 配置Key
     */
    private String name;
    /**
     * 配置值
     */
    private String value;
    /**
     * 说明
     */
    private String comment;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后修改时间
     */
    private Date updateTime;
    /**
     * 所属应用
     */
    private Integer product;
}
