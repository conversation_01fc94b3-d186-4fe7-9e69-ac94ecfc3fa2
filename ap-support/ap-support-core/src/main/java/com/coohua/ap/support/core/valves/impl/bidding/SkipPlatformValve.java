package com.coohua.ap.support.core.valves.impl.bidding;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInfoBidding;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/10
 */
@Slf4j
public class SkipPlatformValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInfoBidding) {
            UserInfoBidding userMetaInfo = (UserInfoBidding) userInformation;
            candidates.removeIf(adId -> !biddingContainer.getBidding(adId).isSkipPlatform(userMetaInfo.getSkipAdPlatformList()));
            candidates.removeIf(adId -> biddingContainer.getBidding(adId)
                    .skipAdSpecial(userMetaInfo.getActiveChannel(), userMetaInfo.getSkipAdList(),1));

            candidates.removeIf(adId -> biddingContainer.getBidding(adId)
                    .skipAdSpecial(userMetaInfo.getActiveChannel(), userMetaInfo.getSkipAdListKs(),2));

            boolean contains = userMetaInfo.getSkipTTAppIdList()
                    .contains(String.format("%s:%s",userMetaInfo.getAppId(),userMetaInfo.getOsType().getCode()));
            candidates.removeIf(adId -> biddingContainer.getBidding(adId)
                    .skipTTAdSpecialAppId(userMetaInfo.getActiveChannel(), contains));

            candidates.removeIf(adId -> biddingContainer.getBidding(adId)
                    .skipSpAdSpecial(userMetaInfo.getActiveChannel()));

            // 若在跳过列表里 直接删除
            if (userMetaInfo.getDirectSkipAdList() != null) {
                candidates.removeIf(adId -> userMetaInfo.getDirectSkipAdList()
                        .contains(biddingContainer.getBidding(adId).getAdId()));
            }
        }
    }
}
