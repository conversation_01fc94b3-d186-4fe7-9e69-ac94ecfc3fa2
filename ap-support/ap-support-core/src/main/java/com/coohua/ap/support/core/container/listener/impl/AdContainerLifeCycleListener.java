package com.coohua.ap.support.core.container.listener.impl;

import com.coohua.ap.support.core.container.AdContainer;
import com.coohua.ap.support.core.container.ContainerState;
import com.coohua.ap.support.core.container.event.ContainerEvent;
import com.coohua.ap.support.core.container.event.ContainerEventType;
import com.coohua.ap.support.core.container.listener.ContainerListener;
import lombok.extern.slf4j.Slf4j;

/**
 * 广告容器生命周期事件监听器
 * <AUTHOR>
 */
@Slf4j
public class AdContainerLifeCycleListener implements ContainerListener {

    private AdContainer adContainer;

    public AdContainerLifeCycleListener(AdContainer adContainer) {
        this.adContainer = adContainer;
    }

    @Override
    public void onContainerEvent(ContainerEvent event) {
        if (event.getType() == ContainerEventType.STATE_CHANGE) {
            if (event.getData() instanceof ContainerState) {
                ContainerState changeState = (ContainerState) event.getData();
                if (ContainerState.INIT == changeState) {
                    log.info(adContainer.getName() + " init ... ");
                } else if (ContainerState.STARTED == changeState) {
                    log.info(adContainer.getName() + " started ... ");
                } else if (ContainerState.REFRESH == changeState) {
                    log.info(adContainer.getName() + " refresh ... ");
                } else if (ContainerState.STOP == changeState) {
                    log.info(adContainer.getName() + " stop ... ");
                } else if (ContainerState.DESTROY == changeState) {
                    log.info(adContainer.getName() + " destroy ... ");
                }
            }
        }
    }
}
