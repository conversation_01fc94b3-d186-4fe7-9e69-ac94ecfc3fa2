package com.coohua.ap.support.core.spring;

import com.coohua.ap.support.core.spring.config.MqConfig;
import com.coohua.ap.support.core.spring.mapper.config.GlobalConfigMapper;
import com.coohua.ap.support.core.spring.mapper.config.StrategyConfigMapper;
import com.coohua.ap.support.core.spring.mapper.custom.CustomAdMapper;
import com.coohua.ap.support.core.spring.mapper.newcustom.EcpMapper;
import com.coohua.ap.support.core.spring.mapper.third.ThirdAdMapper;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

/**
 * Spring IoC 容器工具类
 * <AUTHOR>
 */
@Component
public class SpringIoCUtils implements ApplicationContextAware {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient jedisClusterClient;


    private static ApplicationContext cpx = null;

    public static <T>T getBean(Class<T> beanType) {
        return cpx.getBean(beanType);
    }

    // 获取已经创建的spring容器
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringIoCUtils.cpx = applicationContext;
    }

    public static CustomAdMapper getCustomAdMapper() {
        return getBean(CustomAdMapper.class);
    }

    public static EcpMapper getEcpMapper(){
        return getBean(EcpMapper.class);
    }

    public static ThirdAdMapper getThirdAdMapper() {
        return getBean(ThirdAdMapper.class);
    }

    public static GlobalConfigMapper getGlobalConfigMapper() {
        return getBean(GlobalConfigMapper.class);
    }

    public static StrategyConfigMapper getStrategyConfigMapper() {
        return getBean(StrategyConfigMapper.class);
    }

    public static JedisClusterClient getJedisClusterClient() {
        return getBean(JedisClusterClient.class);
    }

    public static MqConfig getMQConfig(){
        return getBean(MqConfig.class);
    }

//    public static JedisClient getJedisClient() {
//        return getBean(JedisClient.class);
//    }
}
