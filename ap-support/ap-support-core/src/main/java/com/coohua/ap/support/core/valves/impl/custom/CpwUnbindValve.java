package com.coohua.ap.support.core.valves.impl.custom;

import com.coohua.ap.support.core.valves.DefaultAdValve;
import com.coohua.ap.support.core.valves.domain.UserInformation;

import java.util.List;

/**
 * 小程序广告已解绑过滤
 * <AUTHOR>
 */
public class CpwUnbindValve extends DefaultAdValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserInformation) {
            candidates.removeIf(adId -> !customAdContainer.getAd(adId).isCpwMiniProgramUnbind());
        }
    }
}
