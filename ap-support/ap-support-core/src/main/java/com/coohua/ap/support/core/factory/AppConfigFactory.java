package com.coohua.ap.support.core.factory;

import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.mapper.config.AppMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/4
 */
public class AppConfigFactory implements ConfigFactory {

    private AppMapper appMapper;

    private static class AppConfigFactoryHolder {
        private static final AppConfigFactory INSTANCE = new AppConfigFactory();
    }

    private AppConfigFactory() {
        this.appMapper = SpringIoCUtils.getBean(AppMapper.class);
    }

    public static AppConfigFactory getInstance() {
        return AppConfigFactory.AppConfigFactoryHolder.INSTANCE;
    }



    public List<App> getAllApp() {
        return appMapper.queryAllAppList();
    }


}
