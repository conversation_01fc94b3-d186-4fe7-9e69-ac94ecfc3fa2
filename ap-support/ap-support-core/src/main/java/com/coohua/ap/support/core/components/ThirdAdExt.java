package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.reflect.TypeToken;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 第三方广告物料信息
 * <AUTHOR>
 */
@Getter
@ToString
public class ThirdAdExt {
    //激励视频endCard引导概率，百分制
    private int endCardReward;
    //是否使用自家的endCard 0:不使用，1：使用
    private Integer selfEndCard;
    // iOS首选SDK广告位ID
    private String iosPosId;
    // iOS备选SDK广告位ID
    private String iosBakPosId;
    // Android首选广告位ID
    private String androidPosId;
    // Android备选广告位ID
    private String androidBakPosId;
    // iOS SDK广告应用ID
    private String iosAppId;
    // Android SDK广告应用ID
    private String androidAppId;
    // 是否是模板广告
    private Boolean template;
    // 模板广告大小图设置，0-大图，1-小图
    private Integer templateImgSize;
    // 下载激活时间，单位：秒
    private Integer activationTime;
    // 广告样式
    private Integer style;
    // 广告标题
    private String title;
    // 广告摘要
    private String content;
    // 广告落地页地址
    private String clkUrl;
    private String adm; // 视频资源链接
    // 视频时长
    private Integer duration;
    // 广告图片物料地址
    private List<String> imgUrl;
    // 广告曝光回调地址
    private List<String> impTrackUrl;
    // 广告点击回调地址
    private List<String> clkTrackUrl;
    // 当前广告是否可被M替换 默认可替换
    private Boolean convertAd;


    ThirdAdExt(String modelStr) {
        transfer(modelStr);
    }

    private void transfer(String modelStr) {
        JSONObject data = JSONObject.parseObject(modelStr);
        this.endCardReward  = data.getInteger("endCardReward");
        this.selfEndCard = data.getInteger("selfEndCard");
        this.iosPosId = data.getString("iosPosId");
        this.iosBakPosId = data.getString("iosBakPosId");
        this.androidPosId = data.getString("androidPosId");
        this.androidBakPosId = data.getString("androidBakPosId");
        this.iosAppId = data.getString("iosAppId");
        this.androidAppId = data.getString("androidAppId");
        this.template = data.getBoolean("template");
        this.templateImgSize = data.getInteger("templateImgSize");
        this.activationTime = data.getInteger("activationTime");
        this.style = data.getInteger("style");
        this.title = data.getString("title");
        this.content = data.getString("content");
        this.clkUrl = data.getString("clkUrl");
        String imgUrl = data.getString("imgUrl");
        this.imgUrl = StringUtils.isEmpty(imgUrl) ? new ArrayList<>() : data.getObject("imgUrl", new TypeToken<List<String>>(){}.getType());
        String impTrackUrl = data.getString("impTrackUrl");
        this.impTrackUrl = StringUtils.isEmpty(impTrackUrl) ? new ArrayList<>() : data.getObject("impTrackUrl", new TypeToken<List<String>>(){}.getType());
        String clkTrackUrl = data.getString("clkTrackUrl");
        this.clkTrackUrl = StringUtils.isEmpty(clkTrackUrl) ? new ArrayList<>() : data.getObject("clkTrackUrl", new TypeToken<List<String>>(){}.getType());
        this.convertAd = Boolean.TRUE;
        this.adm = data.getString("adm");
        this.duration = data.getInteger("duration");
    }
}

