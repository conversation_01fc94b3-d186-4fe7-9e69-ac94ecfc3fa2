package com.coohua.ap.support.core.components;

import com.coohua.ap.base.constants.*;
import com.coohua.ap.base.domain.*;
import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.base.utils.Version;
import com.coohua.ap.support.core.container.AdContainer;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.container.event.AdEvent;
import com.coohua.ap.support.core.container.event.AdEventType;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.domain.FilteredReason;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.google.gson.reflect.TypeToken;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * 广告组件通用实现
 *
 * <AUTHOR>
 */
@ToString
public abstract class DefaultAd implements Ad {
    // ======= 广告基础信息-begin =======
    /**
     * 广告ID
     */
    @NotNull
    @Getter
    protected Long adId;
    /**
     * 广告名称
     */
    @NotNull
    @Getter
    protected String name;
    /**
     * 广告类型
     *
     * @see com.coohua.ap.base.constants.AdType
     */
    @NotNull
    @Getter
    protected Integer type;

    @Getter
    protected Integer timeout;
    /**
     * 广告源
     *
     * @see AdSource
     */
    @Getter
    protected AdSource adSource;
    /**
     * 广告状态
     * 0-关闭，1-可投放
     */
    @Getter
    protected Integer state;
    /**
     * 广告创意的创建时间
     */
    @Getter
    protected Date createTime;

    /**
     * 广告分层价格
     */
    @Getter
    protected Integer ecpm;
    /**
     * <pre>
     * 如果是直客广告，这个值表示：
     *      广告计划、广告组、广告创意中，距离当前时间最近的更新时间
     * 如果是第三方广告，这个值表示：
     *      广告创意的最后更新时间
     * </pre>
     */
    @Getter
    protected Date updateTime;
    // ======= 广告基础信息-end =======

    // ======= 广告定向信息-begin =======
    /**
     * 定向条件：投放日期
     *
     * @see PutDatePeriod
     */
    protected PutDatePeriod putDatePeriod;
    /**
     * 定向条件：投放时段
     *
     * @see TimeBucket
     */
    protected TimeBucket timeBucket;
    /**
     * 定向条件：投放广告位
     */
    protected List<Integer> adPos;
    /**
     * 定向条件：投放APP
     *
     * @see App
     */
    protected List<Integer> apps;
    /**
     * 定向条件：地域限制
     *
     * @see Region
     */
    @Getter
    protected Region region;
    /**
     * 定向条件：客户端平台&版本
     *
     * @see PlatformVersion
     */
    protected PlatformVersion platformVersion;
    /**
     * 定向条件：包含包名定向
     */
    protected List<String> containsPackages;
    /**
     * 定向条件：不包含包名定向
     */
    protected List<String> notContainsPackages;
    /**
     * 定向条件：注册时间定向
     *
     * @see RegisterTimeOrientation
     */
    protected RegisterTimeOrientation registerTimeOrientation;
    /**
     * 定向条件：注册时长定向
     *
     * @see RegistLongerOrientation
     */
    protected RegistLongerOrientation registLongerOrientation;
    /**
     * 定向条件：用户ID尾号定向
     */
    protected List<String> tailNumber;
    /**
     * 定向条件：用户总收入定向，分四种情况：1) 不设置 2) 大于等于指定总收入 3) 小于等于指定总收入 4) 介于一个区间
     *
     * @see IncomeOrientation
     */
    protected IncomeOrientation incomeOrientation; // 总收入限定，
    /**
     * 定向条件：广告点击时间间隔限制
     */
    @Getter
    protected Integer clickInterval;
    /**
     * 定向条件：广告日点击次数限制
     */
    @Getter
    protected Integer dayClickLimit;
    /**
     * 定向条件：广告曝光时间间隔限制
     */
    @Getter
    protected Integer exposureInterval;
    /**
     * 定向条件：广告日曝光次数限制
     */
    @Getter
    protected Integer dayExposureLimit;

    @Getter
    protected String abPoint;

    @Getter
    protected Integer filterRegion;

    @Getter
    protected Integer lockArea;

    /**
     * 定向条件：用户包名定向
     */
    protected List<String> userPkg;
    /**
     * 定向条件：用户渠道定向
     */
    protected List<String> userChannels;
    /**
     * 定向条件：广告日预算限制
     *
     * @see Budget
     */
    protected Budget budget;
    /**
     * 定向条件：机型
     *
     * @see BrandLimitDomain
     */
    protected BrandLimitDomain brandLimit;

    // ======= 广告定向信息-end =======

    /**
     * 设置广告所属容器
     */
    protected AdContainer adContainer;

    @Override
    public void setAdContainer(AdContainer adContainer) {
        this.adContainer = adContainer;
    }

    // 广告生命周期事件默认处理
    @Override
    public void created() {
        handleAdEvent(adId, AdEventType.CREATE);
    }

    @Override
    public void update() {
        handleAdEvent(adId, AdEventType.UPDATE);
    }

    @Override
    public void destroy() {
        handleAdEvent(adId, AdEventType.DESTROY);
    }

    // 广告行为事件处理
    @Override
    public void onCandidate(Long userId) {
        handleAdEvent(userId, AdEventType.ON_CANDIDATE);
    }

    @Override
    public void filtered(Long userId, String reason) {
        handleAdEvent(new FilteredReason(userId, reason), AdEventType.FILTERED);
    }

    @Override
    public void onClick(Object data) {
        handleAdEvent(data, AdEventType.ON_CLICK);
    }

    @Override
    public void onExposure(Object data) {
        handleAdEvent(data, AdEventType.ON_EXPOSURE);
    }

    @Override
    public void onServing(Object data) {
        handleAdEvent(data, AdEventType.ON_SERVING);
    }

    @Override
    public void onRequest(Object data) {
        handleAdEvent(data, AdEventType.ON_REQUEST);
    }

    @Override
    public void onRequestSuccess(Object data) {
        handleAdEvent(data, AdEventType.ON_REQUESTS_SUCCESS);
    }

    private void handleAdEvent(Object data, AdEventType adEventType) {
        adContainer.adEventPublish(new AdEvent(this, data, adEventType));
    }

    List<Integer> convertCommaStringToIntListForAdPos(String commaString) {
        if (StringUtils.isEmpty(commaString)) {
            return new ArrayList<>();
        }
        String[] arr = commaString.split(AdConstants.SYMBOL_COMMA);
        List<Integer> items = new ArrayList<>(arr.length);
        for (String str : arr) {
            if (StringUtils.isNotEmpty(str.trim())) {
                items.add(Integer.parseInt(str));
            }
        }

        return items;
    }

    // 需将ecpAppId转化为中台appId
    List<Integer> convertCommaStringToIntList(String commaString) {
        if (StringUtils.isEmpty(commaString)) {
            return new ArrayList<>();
        }
        String[] arr = commaString.split(AdConstants.SYMBOL_COMMA);
        List<Integer> items = new ArrayList<>(arr.length);
        for (String str : arr) {
            if (StringUtils.isNotEmpty(str.trim())) {
                int ecpAppId = Integer.parseInt(str.trim());
                if (ecpAppId == 0) {
                    continue;
                }
                App app = AppBuilder.getById(ecpAppId);
                if (app == null) {
                    continue;
                }
                items.add(app.appId());
            }
        }

        return items;
    }

    public boolean isPutDatePeriodValid() {
        if (putDatePeriod == null) {
            return true;
        }
        String startDate = putDatePeriod.getStartDate();
        String endDate = putDatePeriod.getEndDate();
        String now = DateUtils.formatDate(new Date(), DateUtils.PATTERN_YMD);

        // 判断 当前日期是否在投放有效期内
        if (StringUtils.isEmpty(startDate)) {
            startDate = DateUtils.formatDate(new Date(), DateUtils.PATTERN_YMD);
        }

        // 校验永久投放情况下的投放开始时间
        if (putDatePeriod.getForever() == AdConstants.AD_PUT_DATE_PERIOD_FOREVER_YES) {
            // 不在投放时间范围内，跳过
            return DateUtils.compare(now, startDate) >= 0;
        }

        return DateUtils.compare(now, startDate) >= 0 && StringUtils.isNotEmpty(endDate) && DateUtils.compare(now, endDate) <= 0;
    }

    public boolean isTimeBucketValid() {
        int week = Calendar.getInstance().get(Calendar.DAY_OF_WEEK); // 获取星期，从周日开始，1-星期日，2-星期一。。。7-星期六
        int weekNo = WeekNo.WEEK_NO_MAP.get(week);

        Date now = new Date();
        Date todayStartTime = DateUtils.parseDate(DateUtils.formatDate(now, "yyyyMMdd"), "yyyyMMdd");

        long halfhours = (now.getTime() - todayStartTime.getTime()) / 1000 / 60 / 30; // 时段槽
        if (timeBucket == null || timeBucket.getAllday() == 1) { // 没有配置时段，不限制
            return true;
        }

        try {
            int[][] bucket = timeBucket.getBucket();
            return bucket[weekNo][(int) halfhours] == 1;
        } catch (IndexOutOfBoundsException ex) {
            ex.printStackTrace();
        }
        return true;
    }

    public boolean isBudgetValid(Map<Long, Long> adClick) {
        Long adTodayClick = adClick.get(adId);
        if (adTodayClick == null) {
            return true;
        }

        if (budget == null) {
            return true;
        }

        return budget.getFlag() == AdConstants.NO_BUDGET_LIMIT
                || adTodayClick < budget.getValue();
    }

    /**
     * 广告是否有日预算限制
     *
     * @return true - 有限制，false - 无限制
     */
    public boolean isBudgetLimit() {
        return AdConstants.NO_BUDGET_LIMIT != budget.getFlag();
    }

    /**
     * 是否满足指定应用定向条件
     *
     * @param userApp 用户所属应用
     * @return true - 满足，false - 不满足
     */
    public boolean isAppValid(App userApp) {
        return this.apps.contains(userApp.appId());
    }

    /**
     * 是否满足广告位投放条件
     *
     * @param posId 广告位ID
     * @return true - 满足，false - 不满足
     */
    public boolean isPosIdValid(Integer posId) {
        return this.adPos.contains(posId);
    }

    /**
     * 是否满足策略定向
     * @param strategyId
     * @return
     */
    public boolean isAbFilter(Long strategyId){
        if (StringUtils.isEmpty(abPoint)){
            return true;
        }
        return abPoint.contains(strategyId.toString());
    }

    /**
     * 是否满足平台版本投放条件
     *
     * @param osType     用户平台
     * @param appVersion 用户版本
     * @return true - 满足，false - 不满足
     */
    public boolean isPlatformVersionValid(OSType osType, String appVersion) {
        if (platformVersion == null) {
            return true;
        }

        if (platformVersion.getPlatform() == AdConstants.AD_PUT_PLATFORM_ALL) {
            return (osType == OSType.ANDROID && versionCompare(platformVersion.getAndroidStartVersion(), platformVersion.getAndRoidEndVersion(), appVersion))
                    || (osType == OSType.IOS && versionCompare(platformVersion.getIosStartVersion(), platformVersion.getIosEndVersion(), appVersion));
        } else if (platformVersion.getPlatform() == AdConstants.AD_PUT_PLATFORM_ANDROID) {// 限制平台，则在指定平台下，限制版本
            return osType == OSType.ANDROID && versionCompare(platformVersion.getAndroidStartVersion(), platformVersion.getAndRoidEndVersion(), appVersion);
        } else if (platformVersion.getPlatform() == AdConstants.AD_PUT_PLATFORM_IOS) {
            return osType == OSType.IOS && versionCompare(platformVersion.getIosStartVersion(), platformVersion.getIosEndVersion(), appVersion);
        }
        return false;
    }

    private boolean versionCompare(String startVersion, String endVersion, String version) {
        if (StringUtils.isEmpty(startVersion) && StringUtils.isEmpty(endVersion)) { // 开始版本和结束版本都是空的时候，不限制
            return true;
        } else if (StringUtils.isNotEmpty(startVersion) && StringUtils.isEmpty(endVersion)) {
            return Version.compare(version, startVersion) >= 0;
        } else if (StringUtils.isEmpty(startVersion) && StringUtils.isNotEmpty(endVersion)) {
            return Version.compare(version, endVersion) <= 0;
        } else if (StringUtils.isNotEmpty(startVersion) && StringUtils.isNotEmpty(endVersion)) {
            return Version.compare(version, startVersion) >= 0 && Version.compare(version, endVersion) <= 0;
        }
        return false;
    }

    /**
     * 是否满足地域投放条件
     *
     * @param province       用户所属省份
     * @param isFilterRegion 用户是否属于受限区
     * @return true - 满足，false - 不满足
     */
    public boolean isRegionValid(Province province, boolean isFilterRegion) {
        if (region == null || region.getAll() == AdConstants.AD_PUT_REGION_ALL) {
            return true;
        }

        if (province == null) {
            return true;
        }

        if (Province.GUANGDONG == province && !isFilterRegion) {
            return region.getRegion().size() != 3
                    || !region.getRegion().contains(11)
                    || !region.getRegion().contains(31)
                    || !region.getRegion().contains(44);
        } else {
            return region.getRegion().contains(Integer.parseInt(province.code()));
        }
    }

    /**
     * 是否满足用户ID尾号投放条件
     *
     * @param userId 用户ID
     * @return true - 满足，false - 不满足
     */
    public boolean isTailNumberValid(Long userId) {
        if (CollectionUtils.isEmpty(tailNumber)) {
            return true;
        }

        for (String tn : tailNumber) {
            if (StringUtils.isEmpty(tn.trim())) {
                continue;
            }
            if (userId.toString().endsWith(tn)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否满足用户总收入投放条件
     *
     * @param income 用户总收入，单位：分
     * @return true - 满足，false - 不满足
     */
    public boolean isIncomeValid(Integer income) {
        if (incomeOrientation == null || incomeOrientation.getNoLimit() == AdConstants.AD_INCOME_NO_LIMIT) {
            return true;
        }

        int startIncome = incomeOrientation.getStartIncome() == null || incomeOrientation.getStartIncome() < 0 ? 0 : incomeOrientation.getStartIncome();
        int endIncome = incomeOrientation.getEndIncome() == null || incomeOrientation.getEndIncome() < 0 ? 0 : incomeOrientation.getEndIncome();

        return (startIncome > 0 && endIncome == 0 && income > startIncome)
                || (startIncome == 0 && endIncome > 0 && income < endIncome)
                || (startIncome > 0 && endIncome > 0 && income > startIncome && income < endIncome)
                || (startIncome == 0 && endIncome == 0);
    }

    public boolean needRegisterTimeValid(){
        return registerTimeOrientation != null && registerTimeOrientation.getNoLimit() != AdConstants.AD_REGISTER_TIME_NO_LIMIT;
    }

    /**
     * 校验是否满足区域策略条件
     *
     * @param filterRegion 当前用户是否是受限地区用户，1-受限区／2-非受限区／0-不限制
     * @return true - 满足，false - 不满足
     */
    public boolean isRegionValid(Boolean filterRegion) {
        if (filterRegion == null || this.filterRegion == null) {
            return true;
        }

        if (Integer.valueOf(AdConstants.DEFAULT_NO_LIMIT_CODE).equals(this.filterRegion)) {
            return true;
        }

        Boolean res = this.filterRegion == 1;
        return filterRegion.equals(res);
    }

    public boolean isLockedArea(Boolean isLockedArea) {
        if (isLockedArea == null || this.lockArea == null) {
            return true;
        }

        if (Integer.valueOf(AdConstants.DEFAULT_NO_LIMIT_CODE).equals(this.lockArea)) {
            return true;
        }

        Boolean res = this.lockArea == 1;
        return isLockedArea.equals(res);
    }

    /**
     * 是否满足注册时间投放条件
     *
     * @param registerTime 用户注册时间
     * @return true - 满足，false - 不满足
     */
    public boolean isRegisterTimeValid(Date registerTime) {
        if (registerTimeOrientation == null || registerTime == null || registerTimeOrientation.getNoLimit() == AdConstants.AD_REGISTER_TIME_NO_LIMIT) {
            return true;
        }

        return (StringUtils.isNotEmpty(registerTimeOrientation.getStartTime()) && StringUtils.isEmpty(registerTimeOrientation.getEndTime())
                && registerTime.getTime() >= DateUtils.parseDate(registerTimeOrientation.getStartTime(), "yyyy-MM-dd").getTime()) ||
                (StringUtils.isEmpty(registerTimeOrientation.getStartTime()) && StringUtils.isNotEmpty(registerTimeOrientation.getEndTime())
                        && registerTime.getTime() <= DateUtils.parseDate(registerTimeOrientation.getEndTime(), "yyyy-MM-dd").getTime()) ||
                (StringUtils.isNotEmpty(registerTimeOrientation.getStartTime()) && StringUtils.isNotEmpty(registerTimeOrientation.getEndTime())
                        && registerTime.getTime() >= DateUtils.parseDate(registerTimeOrientation.getStartTime(), "yyyy-MM-dd").getTime()
                        && registerTime.getTime() <= DateUtils.parseDate(registerTimeOrientation.getEndTime(), "yyyy-MM-dd").getTime()) ||
                (StringUtils.isEmpty(registerTimeOrientation.getStartTime()) && StringUtils.isEmpty(registerTimeOrientation.getEndTime()));
    }

    /**
     * 是否满足包含包名投放条件
     *
     * @param pkgNames 用户手机当前安装所有包名
     * @return true - 满足，false - 不满足
     */
    public boolean isContainsPackageNameValid(Set<String> pkgNames) {
        if (CollectionUtils.isEmpty(containsPackages)) {
            return true;
        }

        // 广告有 包含包名定向条件，并且用户包名信息是空的时候，不投放该广告。
        if (CollectionUtils.isEmpty(pkgNames)) {
            return false;
        }

        int count = 0;
        for (String pkg : containsPackages) {
            if (pkgNames.contains(pkg)) {
                if (++count == containsPackages.size()) {
                    break;
                }
            }
        }

        return count == containsPackages.size();
    }

    /**
     * 是否满足不包含包名投放条件
     *
     * @param pkgNames 用户手机当前安装所有包名
     * @return true - 满足，false - 不满足
     */
    public boolean isNotContainsPackageNameValid(Set<String> pkgNames) {
        if (CollectionUtils.isEmpty(pkgNames) || CollectionUtils.isEmpty(notContainsPackages)) {
            return true;
        }

        // 当前时间复杂度为O(n)，可优化为O(logN)，方式：缓存用户已安装包名时，按自然顺序排列，饭后这里的查询用二分查找。
        boolean find = false;
        for (String pkg : notContainsPackages) {
            if (pkgNames.contains(pkg)) {
                find = true;
                break;
            }
        }

        return !find;
    }

    public boolean needUserPackageValid(){
        return !CollectionUtils.isEmpty(userPkg);
    }

    public boolean needLockArea(){
        return this.lockArea != null && this.lockArea != 0;
    }

    /**
     * 是否满足用户客户端包名投放条件
     *
     * @param userPackage 用户客户端包名
     * @return true - 满足，false - 不满足
     */
    public boolean isUserPackageValid(String userPackage) {
        if (CollectionUtils.isEmpty(userPkg)) {
            return true;
        }

        return StringUtils.isNotEmpty(userPackage) && this.userPkg.contains(userPackage);
    }

    /**
     * 是否满足用户渠道投放条件
     *
     * @param userChannel 用户所属渠道
     * @return true - 满足，false - 不满足
     */
    public boolean isUserChannelValid(String userChannel) {
        if (CollectionUtils.isEmpty(this.userChannels)) {
            return true;
        }

        if (StringUtils.isEmpty(userChannel)) {
            return false;
        }

        for (String channel : this.userChannels) {
            if (userChannel.startsWith(channel)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否满足用户日点击控量投放条件
     *
     * @param allExpend 有效广告在该用户下今日已出的点击量，Key：广告ID，Value：该广告当前用户当日的点击量（0表示没有点击量）
     * @return true - 满足，false - 不满足
     */
    public boolean isDailyClickLimitValid(Map<Long, Integer> allExpend) {
        if (dayClickLimit == AdConstants.AD_PUT_NO_DAY_CLICK_LIMIT) {
            return true;
        }

        Integer userDayClick = allExpend.get(adId);
        if (userDayClick == null || userDayClick == 0) {
            return true;
        }

        return userDayClick < dayClickLimit;
    }

    /**
     * 是否满足用户日曝光控量投放条件
     *
     * @param allExpend 有效广告在该用户下今日已出的曝光量，Key：广告ID，Value：该广告当前用户当日的曝光量（0表示没有曝光量）
     * @return true - 满足，false - 不满足
     */
    public boolean isDailyExposureLimitValid(Map<Long, Integer> allExpend) {
        if (dayExposureLimit == AdConstants.AD_PUT_NO_DAY_EXPOSURE_LIMIT) {
            return true;
        }

        Integer userDayExposure = allExpend.get(adId);
        if (userDayExposure == null || userDayExposure == 0) {
            return true;
        }

        return userDayExposure < dayExposureLimit;
    }

    /**
     * 是否满足用户点击控频投放条件
     *
     * @param allAdInterval 有效广告在该用户下的上一次点击时间，Key：广告ID，Value：该广告当前用户上一次的点击时间（0表示没有点击）
     * @return true - 满足，false - 不满足
     */
    public boolean isClickInvervalValid(Map<Long, Long> allAdInterval) {
        if (clickInterval == AdConstants.AD_PUT_NO_CLICK_INTERVAL_LIMIT) {
            return true;
        }

        Long userClickTimestamp = allAdInterval.get(adId);
        if (userClickTimestamp == null || userClickTimestamp == 0L) {
            return true;
        }

        return System.currentTimeMillis() - userClickTimestamp > clickInterval * 60 * 1000;
    }

    /**
     * 是否满足用户曝光控频投放条件
     *
     * @param allAdInterval 有效广告在该用户下的上一次曝光时间，Key：广告ID，Value：该广告当前用户上一次的曝光时间（0表示没有曝光）
     * @return true - 满足，false - 不满足
     */
    public boolean isExposureInvervalValid(Map<Long, Long> allAdInterval) {
        if (exposureInterval == AdConstants.AD_PUT_NO_EXPOSURE_INTERVAL_LIMIT) {
            return true;
        }

        Long userExposureTimestamp = allAdInterval.get(adId);
        if (userExposureTimestamp == null || userExposureTimestamp == 0L) {
            return true;
        }

        return System.currentTimeMillis() - userExposureTimestamp > exposureInterval * 60 * 1000;
    }

    public boolean needsRegisterValid(){
        return registLongerOrientation != null && registLongerOrientation.getNoLimit() != AdConstants.AD_REGISTER_LONGER_NO_LIMIT
                && (registLongerOrientation.getNoLimit() != 0
                || !StringUtils.isEmpty(registLongerOrientation.getStartRegistLonger())
                || !StringUtils.isEmpty(registLongerOrientation.getEndRegistLonger()));
    }

    /**
     * 是否满足用户注册时长投放条件
     *
     * @param registerTime 用户注册时间
     * @return true - 满足，false - 不满足
     */
    public boolean isRegisterLongerValid(Date registerTime) {
        if (registerTime == null) {
            return false;
        }

        if (registLongerOrientation == null || registLongerOrientation.getNoLimit() == AdConstants.AD_REGISTER_LONGER_NO_LIMIT
                || (registLongerOrientation.getNoLimit() == 0
                && StringUtils.isEmpty(registLongerOrientation.getStartRegistLonger())
                && StringUtils.isEmpty(registLongerOrientation.getEndRegistLonger()))) {
            return true;
        }

        long dayMills = 1000 * 60 * 60 * 24; // 一天的毫秒值

        if (StringUtils.isNotEmpty(registLongerOrientation.getStartRegistLonger())
                && StringUtils.isEmpty(registLongerOrientation.getEndRegistLonger())) {
            return System.currentTimeMillis() - registerTime.getTime() >= Integer.parseInt(registLongerOrientation.getStartRegistLonger()) * dayMills;
        }
        // <= 某一时间
        else if (StringUtils.isEmpty(registLongerOrientation.getStartRegistLonger())
                && StringUtils.isNotEmpty(registLongerOrientation.getEndRegistLonger())) {
            return System.currentTimeMillis() - registerTime.getTime() <= Integer.parseInt(registLongerOrientation.getEndRegistLonger()) * dayMills;
        }
        // 在一个区间
        else if (StringUtils.isNotEmpty(registLongerOrientation.getStartRegistLonger())
                && StringUtils.isNotEmpty(registLongerOrientation.getEndRegistLonger())) {
            long registTime = System.currentTimeMillis() - registerTime.getTime(); // 注册时间
            return registTime >= Integer.parseInt(registLongerOrientation.getStartRegistLonger()) * dayMills
                    && registTime <= Integer.parseInt(registLongerOrientation.getEndRegistLonger()) * dayMills;
        }

        return false;
    }

    /**
     * 判断是否快手广告
     * @return true - 是快手平台的广告
     */
    public boolean isKuaiShouAd(){
        ThirdPlatformType thirdPlatformType = AdTypeSub.getPlatform(this.type);
        return ThirdPlatformType.KS.equals(thirdPlatformType);
    }

    /**
     * 判断是否是需跳过的平台
     * @param needSkip 需要跳过的平台
     * @return TRUE 跳了得了
     */
    public boolean isSkipPlatform(Integer needSkip){
        ThirdPlatformType thirdPlatformType = AdTypeSub.getPlatform(this.type);
        return thirdPlatformType.getCode().equals(needSkip);
    }

    public boolean skipAdSpecial(Integer source,boolean contains,Integer viewPlat){
        if (source != null && contains){
            return !source.equals(viewPlat);
        }
        return false;
    }

    private static final List<Integer> tfTgGdtAdTypeList = Arrays.asList(1008100,
            1008101,1008102,1008103,1008104,1008105,
            1008106,1008107,1008108,1008109,1008110,
            1008111,1008112,1008113,1008114,1008115,
            1008116,1008117,1008118,1008119,1008120,
            1008121);

    private static final List<Integer> ntfTgGdtAdTypeList = Arrays.asList(1008,
            10081,10082,10083,10084,10085,10086,10087,10088,10089,100810,
            100811,100812,100813,100814,100815,100816,100817,100818,100819,100820,
            100821,100822,100823,100824,100825,100826,100827,100827,100829,100830,
            100831,100832,100833,100834,100836
            );

    public boolean skipGdtFilterSpecial(Integer source){
        if (source != null && type.toString().startsWith("1008")){
            // 若是广点通买量用户
            if (source == 3){
                // 清理非买量用户可看的广告
                return !tfTgGdtAdTypeList.contains(type);
            }else {
                // 清理买量用户可见的广告
                return !ntfTgGdtAdTypeList.contains(type);
            }
        }
        return false;
    }

    public boolean skipModelXiaoMi(String model){
        if (169821L == this.adId && StringUtils.isNotEmpty(model)){
            // 小米机型不看
            return "xiaomi".equalsIgnoreCase(model);
        }
        return false;
    }

    public boolean skipModelMiAndHua(String model,Boolean contains){
        if (StringUtils.isNotEmpty(model) && contains){
            // 小米华为机型不看
            return "xiaomi".equalsIgnoreCase(model) || "huawei".equalsIgnoreCase(model);
        }
        return false;
    }

    /**
     * 获取当前广告的点击量预算值，如果budget为空，默认预算值为0。
     *
     * @return 点击量预算值
     */
    public long getBudgetValue() {
        return this.budget == null ? 0L : this.budget.getValue();
    }

    /**
     * 获取排序后的应用投放列表
     */
    public List<Integer> getSortedApps() {
        List<Integer> ret = new ArrayList<>(this.apps);
        Collections.sort(ret);
        return ret;
    }

    /**
     * 获取投放平台
     */
    public Integer getPlatform() {
        return this.platformVersion.getPlatform();
    }

    /**
     * 获取投放的广告位
     */
    public List<Integer> getPosId() {
        return new ArrayList<>(this.adPos);
    }

    /**
     * 获取投放的Android起始版本号
     */
    public String getAndroidStartVersion() {
        return this.platformVersion.getAndroidStartVersion();
    }

    /**
     * 获取投放的Android结束版本号
     */
    public String getAndroidEndVersion() {
        return this.platformVersion.getAndRoidEndVersion();
    }

    /**
     * 获取投放的iOS起始版本号
     */
    public String getIosStartVersion() {
        return this.platformVersion.getIosStartVersion();
    }

    /**
     * 获取投放的iOS结束版本号
     */
    public String getIosEndVersion() {
        return this.platformVersion.getIosEndVersion();
    }

    /**
     * 获取排序后的尾号投放条件
     */
    public List<String> getSortedTailNumber() {
        List<String> ret = new ArrayList<>(this.tailNumber);
        Collections.sort(ret);
        return ret;
    }

    /**
     * 获取广告是否限制用户点击控频
     *
     * @return true - 限制，false - 不限制
     */
    public boolean isClickIntervalLimit() {
        return this.clickInterval != AdConstants.AD_PUT_NO_CLICK_INTERVAL_LIMIT && this.clickInterval > 0;
    }

    /**
     * 获取广告是否限制用户日点击控量
     *
     * @return true - 限制，false - 不限制
     */
    public boolean isDayClickLimit() {
        return this.dayClickLimit != AdConstants.AD_PUT_NO_DAY_CLICK_LIMIT && this.dayClickLimit > 0;
    }

    /**
     * 获取广告是否限制用户曝光控频
     *
     * @return true - 限制，false - 不限制
     */
    public boolean isExposureIntervalLimit() {
        return this.exposureInterval != AdConstants.AD_PUT_NO_EXPOSURE_INTERVAL_LIMIT && this.exposureInterval > 0;
    }

    /**
     * 获取广告是否限制用户日曝光控量
     *
     * @return true - 限制，false - 不限制
     */
    public boolean isDayExposureLimit() {
        return this.dayExposureLimit != AdConstants.AD_PUT_NO_DAY_EXPOSURE_LIMIT && this.dayExposureLimit > 0;
    }

    /**
     * 是否满足机型投放条件
     *
     * @param brand 用户机型
     * @return true - 满足，false - 不满足
     */
    public boolean isBrandValid(String brand) {
        if (this.brandLimit.getBrandLimit() == AdConstants.NO_BRAND_LIMIT) {
            return true;
        }

        // 广告有机型定向， 但定向列表为空，被过滤
        if (CollectionUtils.isEmpty(this.brandLimit.getBrandLimitList()) || StringUtils.isEmpty(brand)) {
            return false;
        }

        return this.brandLimit.getBrandLimitList().contains(brand);
    }

    /**
     * 是否满足广告曝光类型配置投放条件
     *
     * @param cacheConfig 内存数据
     * @return true - 满足，false - 不满足
     */
    public boolean isExposureActionValid(Map<String, String> cacheConfig, App app) {
        if (MapUtils.isEmpty(cacheConfig)) {
            return true;
        }

        Map<String, String> configMap = ContainerHolder.getGlobalConfigContainer().getConfig(app.appId(), GlobalConfigKey.AD_ACTION_TYPE_EXPOSURE, new TypeToken<Map<String, String>>() {
        }.getType());
        if (MapUtils.isEmpty(configMap)) {
            return true;
        }
        if (cacheConfig.get(type.toString()) != null && configMap.get(type.toString()) != null) {
            int finishNum = Integer.parseInt(cacheConfig.get(type.toString()));
            int configNum = Integer.parseInt(configMap.get(type.toString()));
            return finishNum < configNum;
        }
        return true;
    }

    /**
     * 是否满足广告请求类型配置投放条件
     *
     * @param cacheConfig 内存数据
     * @return true - 满足，false - 不满足
     */
    public boolean isRequestActionValid(Map<String, String> cacheConfig, App app) {
        if (MapUtils.isEmpty(cacheConfig)) {
            return true;
        }

        Map<String, String> configMap = ContainerHolder.getGlobalConfigContainer().getConfig(app.appId(), GlobalConfigKey.AD_ACTION_TYPE_REQUEST, new TypeToken<Map<String, String>>() {
        }.getType());
        if (MapUtils.isEmpty(configMap)) {
            return true;
        }

        if (cacheConfig.get(type.toString()) != null && configMap.get(type.toString()) != null) {
            int finishNum = Integer.parseInt(cacheConfig.get(type.toString()));
            int configNum = Integer.parseInt(configMap.get(type.toString()));
            return finishNum < configNum;
        }
        return true;
    }
}
