package com.coohua.ap.support.core.factory;

import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.NewEcpAdType;
import com.coohua.ap.support.core.components.AdVideoSource;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.mapper.newcustom.EcpMapper;
import com.coohua.ap.support.core.spring.model.NewCustomAdModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9
 */
public class NewCustomAdFactory implements AdFactory{

    private EcpMapper ecpMapper;

    private static class NewCustomAdFactoryHolder {
        private static final NewCustomAdFactory INSTANCE = new NewCustomAdFactory();
    }

    private NewCustomAdFactory() {
        this.ecpMapper = SpringIoCUtils.getEcpMapper();
    }

    public static NewCustomAdFactory getInstance() {
        return NewCustomAdFactory.NewCustomAdFactoryHolder.INSTANCE;
    }

    public NewCustomAdModel getNewestAd(Long adId) {
        if (adId == null) {
            return null;
        }
        return ecpMapper.getById(adId);
    }

    public List<NewCustomAdModel> getNewestAdByType(Integer adType) {
        if (adType == null) {
            return null;
        }
        adType = NewEcpAdType.convertToEcpAdType(adType);
        return ecpMapper.getByType(adType);
    }


    public List<NewCustomAdModel> getAllNewestAd() {
        return ecpMapper.getAll();
    }

    public List<AdVideoSource> queryDeliveryTime(List<Integer> videoIds) {
        if (videoIds == null || videoIds.size() == 0){
            return new ArrayList<>();
        }
        return ecpMapper.queryDeliveryTime(videoIds);
    }
}
