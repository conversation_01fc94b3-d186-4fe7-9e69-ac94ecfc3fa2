package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.GlobalConfigKey;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * 直客广告物料信息
 * PS : 暂时忽略了分享广告和直客热搜广告，有需要的话后面再支持
 * </pre>
 *
 * <AUTHOR>
 */
@Getter
@ToString
public class CustomAdExt {
    /**
     * 广告标签
     */
    private String adTag;
    /**
     * 是否显示广告标签，默认为true：显示
     */
    private Boolean showAdLabel;
    /**
     * 广告来源描述
     */
    private String adCreativeSource;
    /**
     * 是否展示激励视频卡片    0-否 1-是
     */
    private Integer showVideoCard;
    /**
     * endCardHtml点击引导显示及金币数（0为不显示引导）
     */
    private Integer endCardReward;
    /**
     * 广告样式id，101-小图，201-大图，301-组图，401-视频
     */
    private Integer style;
    /**
     * 广告标题
     */
    private String title;
    /**
     * 广告内容
     */
    private String content;
    /**
     * 广告落地页链接
     */
    private String clkUrl;
    /**
     * 广告图片物料地址
     */
    private List<String> imgUrl;
    /**
     * 广告曝光回调地址
     */
    private List<String> impTrackUrl;
    /**
     * 广告点击回调地址 或 落地页下载按钮点击上报
     */
    private List<String> clkTrackUrl;
    /**
     * 视频资源链接
     */
    private String adm;
    /**
     * 视频播放开始监控汇报地址
     */
    private List<String> videoStartTrackUrl;
    /**
     * 视频播放结束监控汇报地址
     */
    private List<String> videoFinishTrackUrl;
    /**
     * 视频播放暂停监控汇报地址
     */
    private List<String> videoPauseTrackUrl;
    /**
     * 安卓安装包的下载地址 或 iOS的App Store地址
     */
    private String downloadUrl;
    /**
     * iOS广告的iTunes id，用于内部打开。
     */
    private String itid;
    /**
     * 视频宽度，单位：像素
     */
    private Integer width;
    /**
     * 视频高度，单位：像素
     */
    private Integer height;
    /**
     * 视频时长，单位：毫秒
     */
    private Integer duration;
    /**
     * 对于视频下载类广告，倒计时此字段指定的毫秒数后，可以显示下载获取礼盒按钮。
     */
    private Long countDownGiftTime;
    /**
     * 下载广告安装包包名
     */
    private String appPkgName;
    /**
     * 下载激活延迟时间，过了此延迟时间后，才能加礼盒(单位：秒)
     */
    private Integer activationTime;

    // ======== CPO广告相关 begin ========
    /**
     * 原生应用落地页地址
     */
    private String deeplinkUrl;
    /**
     * 原生 or 网页
     */
    private Integer deeplinkOpenType;
    /**
     * 需要唤起的原生应用包名
     */
    private String deeplinkPkgName;
    // ======== CPO广告相关 end ========

    // ======== 小程序广告相关 begin ========
    /**
     * 是否微信小程序 0-否 1-是
     */
    private Integer isMiniProgram;
    /**
     * 微信小程序id
     */
    private String miniProgramId;
    /**
     * 微信小程序跳转路径
     */
    private String miniProgramPath;
    /**
     * 小程序关联微信ID
     */
    private String miniProgramWxId;
    /**
     * 中转小程序id
     */
    private String transferMiniProgramId;
    /**
     * 中转小程序路径
     */
    private String transferMiniProgramPath;
    /**
     * 中转小程序关联微信id
     */
    private String transferMiniProgramWxId;
    /**
     * 小程序名称
     */
    private String miniProgramName;
    /**
     * 小程序icon
     */
    private String miniIcon;
    /**
     * 分享标题
     */
    private String miniShareTitle;
    /**
     * 分享图片
     */
    private String miniShareImage;
    /**
     * 小程序中间页按钮文案
     */
    private String miniMiddleBtnText;
    /**
     * 小程序中间页全屏图
     */
    private String miniMiddleImage;
    /**
     * 小程序打开方式：1-FEED流直接打开，2-中间引导页打开
     */
    private Integer miniOpenStyle;
    /**
     * 小程序中间引导页LOGO图片地址
     */
    private String miniLogoImgUrl;
    /**
     * 小程序中间引导页名称
     */
    private String miniName;
    /**
     * 小程序中间引导页产品描述
     */
    private String miniProductDesc;
    /**
     * 小程序中间引导页产品配图
     */
    private String miniProductImgUrl;
    /**
     * 小程序中间引导页 任务描述，格式：item1\nitem2\nitem3...
     */
    private String miniMissionDesc;
    /**
     * 小程序中间引导页 引导图片
     */
    private String miniGuideImgUrl;
    /**
     * 小程序中间引导页 留存任务描述 20字符以内
     */
    private String miniRemainMissionDesc;
    /**
     * 热搜广告-3-是否外部浏览器打开
     */
    private Integer otherBrowser;
    /**
     * 关联小程序包名
     */
    private String miniProgramPkg;
    /**
     * 小程序引导图：信息流激励
     */
    private String miniFeedCreditImg;
    /**
     * 小程序引导图：信息流普通
     */
    private String miniFeedNormalImg;
    /**
     * 小程序引导图：任务大厅
     */
    private String miniTaskImg;
    /**
     * 是否已绑定中转小程序
     */
    private Boolean miniBindMiddlePage;
    /**
     * 小程序排重周期，0-长期，1-短期
     */
    private Integer miniNonRepeatPeriod;
    /**
     * CPC小程是否按照时长奖励，默认为false表示不按照时长奖励
     */
    private Boolean timeReward;
    // ======== 小程序广告相关 end ========

    // ======== 点点赚广告相关 begin ========
    /**
     * 阅读篇数
     */
    private Integer readNum;
    /**
     * 每篇停留时长,单位秒
     */
    private Integer readTime;
    /**
     * 奖励金币数
     */
    private Long rewardGold;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 点点赚类型 1-新闻站，2-互动
     */
    private Integer clickEarnType;
    /**
     * 点点赚投放限额，单位分
     */
    private Long clickEarnBudget;
    /**
     * 阅读篇数范围
     */
    private Integer readNumRange;
    /**
     * 阅读时长范围
     */
    private Integer readTimeRange;
    /**
     * 引导选项，0-仅底部，1-仅悬浮，2-悬浮+底部，默认为0
     */
    private Integer guidType;
    /**
     * 模板提示类型，0-新闻站，1-互动，2-刷广点通弹窗
     */
    private Integer templateType;
    /**
     * 首页提示
     */
    private String indexTip;
    /**
     * 阅读引导
     */
    private String readTip;
    /**
     * 翻页引导
     */
    private String pageTip;
    /**
     * 连续翻页引导
     */
    private String continuousPageTip;
    // ======== 点点赚广告相关 end ========

    // ======== 应用推荐广告相关 begin ========
    /**
     * 下载描述
     */
    private String downloadDesc;
    /**
     * 签到描述
     */
    private String signDesc;
    /**
     * 广告类型，0:CPA类型，1:唤醒类
     */
    private Integer appAdType;
    /**
     * 签到地址
     */
    private String signUrl;
    /**
     * 唤醒类型 0:deepLink ,1:className
     */
    private Integer awakeType;
    /**
     * 图标
     */
    private String icon;
    /**
     * 是否有深度任务
     */
    private Boolean hasDeeperTask;
    /**
     * 深度任务ID
     */
    private Integer deeperTaskID;
    /**
     * 深度任务奖励
     */
    private Integer deeperTaskGold;
    /**
     * 深度任务描述
     */
    private String deeperTaskDesc;
    // ======== 应用推荐广告相关 end ========

    // ======== CPA改版 begin ========
    /**
     * 安装包大小
     */
    private Float apkSize;
    /**
     * 下载人数
     */
    private Long downloadNum;
    // ======== CPA改版 end ========

    // ======== 阅读60秒广告相关 begin ========
    /**
    * qq号，可配置多个，用逗号隔开
    */
    private String qqText;
    /**
     * 微信号，可配置多个，用`,`隔开
     */
    private String weChatIdText;
    /**
     * 视频地址，可配置多个，用`,`隔开
     */
    private String videoUrlText;
    /**
     * 打开类型类型，0:内开，1:外开,2:时长+多跳
     */
    private Integer openType;
    /**
     * 打开类型，0-内开，1-表示外开，2-表示时长+跳转（由于之前的openType为boolean类型，无法满足现在新增的类型，但还需要兼容老版本的问题，所以新加一个字段）
     */
    private Integer newOpenType;
    /**
     * 详情文案
     */
    private String detail;
    /**
     * 按钮文案
     */
    private String buttonDesc;
    /**
     * 是否是首推任务，true:是首推任务，false:不是
     */
    private Boolean recommend;
    /**
     * 是否是二跳，true:二跳，false:不是，默认为false
     */
    private Boolean twiceJump;
    /**
     * 二跳时间
     */
    private Integer twiceJumpTime;
    /**
     * 跳转次数
     */
    private Integer jumpTimes;
    // ======== 阅读60秒广告相关 end ========

    // ======== 直客视频广告相关 begin ========
    // 复用字段：width:视频宽度， height:视频高度，duration:视频时长，单位毫秒， content:宣传文案
    // clkUrl:广告落地页地址, impTrackUrl:广告曝光回调链接, clkTrackUrl: 广告点击回调链接
    /**
     * logo图片地址
     */
    private String logoUrl;
    /**
     * 视频地址
     */
    private String videoUrl;
    /**
     * 封面图地址
     */
    private String coverImgUrl;
    /**
     * 视频大小
     */
    private String videoSize;
    // ======== 直客视频广告相关 end ========

    CustomAdExt(String extStr) {
        transfer(extStr);
    }

    private void transfer(String extStr) {
        JSONObject data = JSONObject.parseObject(extStr);
        this.showVideoCard = data.getInteger("showVideoCard");
        this.endCardReward = data.getInteger("endCardReward");
        this.style = data.getInteger("style");
        this.title = data.getString("title");
        this.content = data.getString("content");
        this.clkUrl = data.getString("clkUrl");
        String imgUrl = data.getString("imgUrls");
        this.imgUrl = StringUtils.isEmpty(imgUrl) ? new ArrayList<>() : JSONArray.parseArray(imgUrl, String.class);
        this.imgUrl.remove("");
        String impTrackUrl = data.getString("impTrackUrl");
        this.impTrackUrl = StringUtils.isEmpty(impTrackUrl) ? new ArrayList<>() : Lists.newArrayList(impTrackUrl);
        String clkTrackUrl = data.getString("clkTrackUrl");
        this.clkTrackUrl = StringUtils.isEmpty(clkTrackUrl) ? new ArrayList<>() : Lists.newArrayList(clkTrackUrl);
        this.adm = data.getString("adm");
        String videoStartTrackUrl = data.getString("videoStartTrackUrl");
        this.videoStartTrackUrl = StringUtils.isEmpty(videoStartTrackUrl) ? new ArrayList<>() : Lists.newArrayList(videoStartTrackUrl);
        String videoFinishTrackUrl = data.getString("videoFinishTrackUrl");
        this.videoFinishTrackUrl = StringUtils.isEmpty(videoFinishTrackUrl) ? new ArrayList<>() : Lists.newArrayList(videoFinishTrackUrl);
        String videoPauseTrackUrl = data.getString("videoPauseTrackUrl");
        this.videoPauseTrackUrl = StringUtils.isEmpty(videoPauseTrackUrl) ? new ArrayList<>() : Lists.newArrayList(videoPauseTrackUrl);
        this.itid = data.getString("itid");
        this.width = data.getInteger("width");
        this.height = data.getInteger("height");
        this.duration = data.getInteger("duration");
        this.downloadUrl = data.getString("downloadUrl");
        this.appPkgName = data.getString("appPkgName");
        this.activationTime = ContainerHolder.getGlobalConfigContainer().getConfig(App.MASTER.appId(), GlobalConfigKey.DOWNLOAD_ACTIVATE_TIME, Integer.class);
        this.adTag = data.getString("adTag");
        this.showAdLabel = data.getBoolean("showAdLabel");
        this.adCreativeSource = data.getString("adSource");

        this.deeplinkUrl = data.getString("deeplinkUrl");
        this.deeplinkOpenType = data.getInteger("deeplinkOpenType");
        this.deeplinkPkgName = data.getString("deeplinkPkgName");

        this.isMiniProgram = data.getInteger("isMiniProgram");
        this.miniProgramId = data.getString("miniProgramId");
        this.miniProgramPath = data.getString("miniProgramPath");
        this.miniProgramWxId = data.getString("miniProgramWxId");
        this.transferMiniProgramId = data.getString("transferMiniProgramId");
        this.transferMiniProgramPath = data.getString("transferMiniProgramPath");
        this.transferMiniProgramWxId = data.getString("transferMiniProgramWxId");
        this.miniProgramName = data.getString("miniProgramName");
        this.miniIcon = data.getString("miniIcon");
        this.miniShareTitle = data.getString("miniShareTitle");
        this.miniShareImage = data.getString("miniShareImage");
        this.miniMiddleBtnText = data.getString("miniMiddleBtnText");
        this.miniMiddleImage = data.getString("miniMiddleImage");
        this.miniOpenStyle = data.getInteger("miniOpenStyle");
        this.miniLogoImgUrl = data.getString("miniLogoImgUrl");
        this.miniName = data.getString("miniName");
        this.miniProductDesc = data.getString("miniProductDesc");
        this.miniProductImgUrl = data.getString("miniProductImgUrl");
        this.miniMissionDesc = data.getString("miniMissionDesc");
        this.miniGuideImgUrl = data.getString("miniGuideImgUrl");
        this.miniRemainMissionDesc = data.getString("miniRemainMissionDesc");
        this.otherBrowser = data.getInteger("otherBrowser");
        this.miniProgramPkg = data.getString("miniProgramPkg");
        this.miniFeedCreditImg = data.getString("miniFeedCreditImg");
        this.miniFeedNormalImg = data.getString("miniFeedNormalImg");
        this.miniTaskImg = data.getString("miniTaskImg");
        this.miniBindMiddlePage = data.getBoolean("miniBindMiddlePage");
        this.miniNonRepeatPeriod = data.getInteger("miniNonRepeatPeriod") == null ? 0 : data.getInteger("miniNonRepeatPeriod");
        this.timeReward = data.getBoolean("timeReward");

        this.downloadDesc = data.getString("downloadDesc");
        this.signDesc = data.getString("signDesc");
        this.appAdType = data.getInteger("appAdType");
        this.signUrl = data.getString("signUrl");
        this.awakeType = data.getInteger("awakeType");
        this.icon = data.getString("icon");
        this.hasDeeperTask = data.getBoolean("hasDeeperTask");
        this.deeperTaskID = data.getInteger("deeperTaskID");
        this.deeperTaskGold = data.getInteger("deeperTaskGold");
        this.deeperTaskDesc = data.getString("deeperTaskDesc");

        this.openType = data.getInteger("openType");
        this.newOpenType = data.getInteger("newOpenType");
        this.detail = data.getString("detail");
        this.buttonDesc = data.getString("buttonDesc");

        this.apkSize = data.getFloat("apkSize");
        this.downloadNum = data.getLong("downloadNum");

        this.readNum = data.getInteger("readNum");
        this.readTime = data.getInteger("readTime");
        this.rewardGold = data.getLong("rewardGold");
        this.priority = data.getInteger("priority");
        this.clickEarnType = data.getInteger("clickEarnType");
        this.clickEarnBudget = data.getLong("clickEarnBudget");
        this.readNumRange = data.getInteger("readNumRange");
        this.readTimeRange = data.getInteger("readTimeRange");
        this.guidType = data.getInteger("guidType");
        this.templateType = data.getInteger("templateType");
        this.indexTip = data.getString("indexTip");
        this.readTip = data.getString("readTip");
        this.pageTip = data.getString("pageTip");
        this.continuousPageTip = data.getString("continuousPageTip");

        this.recommend = data.getBoolean("recommend");
        this.twiceJump = data.getBoolean("twiceJump");
        this.twiceJumpTime = data.getInteger("twiceJumpTime");
        this.jumpTimes = data.getInteger("jumpTimes");

        this.logoUrl = data.getString("logoUrl");
        this.videoUrl = data.getString("videoUrl");
        this.coverImgUrl = data.getString("coverImgUrl");
        this.videoSize = data.getString("videoSize");
        this.qqText = data.getString("qqText");
        this.weChatIdText = data.getString("weChatIdText");
        this.videoUrlText = data.getString("videoUrlText");
    }
}
