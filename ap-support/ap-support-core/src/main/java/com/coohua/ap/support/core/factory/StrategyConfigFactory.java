package com.coohua.ap.support.core.factory;

import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.mapper.config.StrategyConfigMapper;
import com.coohua.ap.support.core.spring.model.StrategyBaseConfigModel;
import com.coohua.ap.support.core.spring.model.StrategyOrientationConfigModel;

import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/10
 */
public class StrategyConfigFactory implements ConfigFactory {

    private StrategyConfigMapper strategyConfigMapper;

    public List<StrategyBaseConfigModel> queryBaseConfigByPage(long lastId, int pageSize) {
        return strategyConfigMapper.queryBaseConfigByPage(lastId, pageSize);
    }

    public List<StrategyOrientationConfigModel> queryOrientationConfigByPage(long lastId, int pageSize) {
        return strategyConfigMapper.queryOrientationConfigByPage(lastId, pageSize);
    }

    private static class StrategyConfigFactoryHolder {
        private static final StrategyConfigFactory INSTANCE = new StrategyConfigFactory();
    }

    private StrategyConfigFactory() {
        this.strategyConfigMapper = SpringIoCUtils.getStrategyConfigMapper();
    }

    public static StrategyConfigFactory getInstance() {
        return StrategyConfigFactory.StrategyConfigFactoryHolder.INSTANCE;
    }

    public List<StrategyBaseConfigModel> getAllBaseConfig() {
        return strategyConfigMapper.queryAllBaseConfig();
    }

    public List<StrategyOrientationConfigModel> getAllOrientationConfig() {
        return strategyConfigMapper.queryAllOrientationConfig();
    }
}
