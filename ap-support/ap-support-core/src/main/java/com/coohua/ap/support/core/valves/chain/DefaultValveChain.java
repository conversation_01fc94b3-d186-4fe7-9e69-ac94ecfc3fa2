package com.coohua.ap.support.core.valves.chain;


import com.coohua.ap.support.core.valves.Valve;

/**
 * 广告过滤责任链构建器 默认实现
 */
public class DefaultValveChain implements ValveChain {

    private Valve first;

    private Valve last;

    @Override
    public ValveChain addValve(Valve valve) {
        this.last.setNext(valve);
        this.last = valve;
        return this;
    }

    @Override
    public Valve getFirst() {
        return first;
    }

    @Override
    public ValveChain setFirst(Valve valve) {
        this.first = valve;
        this.last = valve;
        return this;
    }
}
