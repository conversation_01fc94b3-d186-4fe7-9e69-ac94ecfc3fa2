package com.coohua.ap.support.core.factory;

import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.spring.mapper.config.GlobalConfigMapper;
import com.coohua.ap.support.core.spring.model.GlobalConfigModel;

import java.util.List;

/**
 * 全局配置工厂
 * <AUTHOR>
 */
public class GlobalConfigFactory implements ConfigFactory {

    private GlobalConfigMapper globalConfigMapper;

    public List<GlobalConfigModel> queryByPage(long lastId, int pageSize) {
        return globalConfigMapper.queryByPage(lastId, pageSize);
    }

    private static class GlobalConfigFactoryHolder {
        private static final GlobalConfigFactory INSTANCE = new GlobalConfigFactory();
    }

    private GlobalConfigFactory() {
        this.globalConfigMapper = SpringIoCUtils.getGlobalConfigMapper();
    }

    public static GlobalConfigFactory getInstance() {
        return GlobalConfigFactory.GlobalConfigFactoryHolder.INSTANCE;
    }

    public List<GlobalConfigModel> getAll() {
        return globalConfigMapper.getAllConfig();
    }

    public GlobalConfigModel getById(Integer id) {
        if (id == null) {
            return null;
        }
        return globalConfigMapper.getById(id);
    }
}
