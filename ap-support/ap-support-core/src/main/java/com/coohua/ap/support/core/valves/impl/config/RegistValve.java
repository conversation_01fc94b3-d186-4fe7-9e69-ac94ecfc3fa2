package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-11-11
 */
@Slf4j
public class RegistValve extends DefaultValve {

    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            Date userRegistDate = userInfo.getUserRegistDate() == null ? new Date() : userInfo.getUserRegistDate();
            long registSeconds = (System.currentTimeMillis() - userRegistDate.getTime()) / 1000; // 计算用户注册到现在的秒数
            candidates.removeIf(configId -> !ContainerHolder.getStrategyConfigContainer().getStrategyOrientationConfig(configId).isRegistValid(registSeconds));
        }
    }
}
