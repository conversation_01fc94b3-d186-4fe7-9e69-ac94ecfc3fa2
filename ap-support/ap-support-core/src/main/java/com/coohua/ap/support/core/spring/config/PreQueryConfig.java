package com.coohua.ap.support.core.spring.config;

import com.coohua.ap.support.core.components.StrategyOrientationConfig;
import com.coohua.ap.support.core.components.ThirdAd;
import com.coohua.ap.support.core.container.DefaultStrategyConfigContainer;
import com.coohua.ap.support.core.container.ThirdAdContainer;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.model.AdPosModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/24
 */
@Slf4j
@Service
public class PreQueryConfig {

    @Value("${app.id}")
    private String appId;

    private Map<Integer,Boolean> needCheckRegisterMapPrivate = new HashMap<>();
    private Map<Integer,Boolean> needCheckInstallPackagePrivate = new HashMap<>();
    private Map<Integer,Boolean> needCheckIsLockAreaPrivate = new HashMap<>();
    private Map<Integer,Boolean> needCheckIsFilterRegionPrivate = new HashMap<>();

    @PostConstruct
    @Scheduled(cron = "0 0/5 * ? * *")
    public void refreshPosNeedInfos(){
        log.info("Current AppId:{}",appId);
        if (!"ap.gateway".equalsIgnoreCase(appId)){
            return;
        }
        log.info(">>> 开始刷新POS_ID需要过滤...");
        Map<Integer,Boolean> needCheckRegisterMap = new HashMap<>();
        Map<Integer,Boolean> needCheckInstallPackage = new HashMap<>();
        Map<Integer,Boolean> needCheckIsLockArea = new HashMap<>();
        Map<Integer,Boolean> needCheckIsFilterRegion = new HashMap<>();
        DefaultStrategyConfigContainer strategyConfigContainer = ContainerHolder.getStrategyConfigContainer();
        ThirdAdContainer thirdAdContainer = ContainerHolder.getThirdAdContainer();

        List<StrategyOrientationConfig> strategyList  = strategyConfigContainer.getAllStrategy();
        Map<Integer,List<StrategyOrientationConfig>> strateMap = strategyList.stream().collect(Collectors.groupingBy(StrategyOrientationConfig::getAdPos));

        strateMap.forEach((posId,styList) ->{
            needCheckRegisterMap.put(posId, styList.stream().anyMatch(StrategyOrientationConfig::needRegisterValid));
            needCheckInstallPackage.put(posId, styList.stream().anyMatch(StrategyOrientationConfig::needUserPkgValid));
            needCheckIsLockArea.put(posId, styList.stream().anyMatch(StrategyOrientationConfig::needLockArea));
            needCheckIsFilterRegion.put(posId, styList.stream().anyMatch(StrategyOrientationConfig::needFilterRegion));
        });

        if (thirdAdContainer != null) {
            List<ThirdAd> thirdAdList = thirdAdContainer.getAllAd();
            if (CollectionUtils.isEmpty(thirdAdList)){
                log.error("thirdAdList is null");
                return;
            }
            thirdAdList.forEach(thirdAd -> thirdAd.getPosId().forEach(posId -> {
                if (thirdAd.needsRegisterValid()) {
                    needCheckRegisterMap.put(posId, thirdAd.needsRegisterValid());

                }
                if (thirdAd.needRegisterTimeValid()) {
                    needCheckRegisterMap.put(posId, thirdAd.needRegisterTimeValid());
                }
                if (thirdAd.needUserPackageValid()) {
                    needCheckInstallPackage.put(posId, thirdAd.needUserPackageValid());
                }
                if (thirdAd.needLockArea()) {
                    needCheckIsLockArea.put(posId, thirdAd.needLockArea());
                }
            }));
        }
        needCheckRegisterMapPrivate = needCheckRegisterMap;
        needCheckInstallPackagePrivate = needCheckInstallPackage;
        needCheckIsLockAreaPrivate = needCheckIsLockArea;
        needCheckIsFilterRegionPrivate = needCheckIsFilterRegion;
        log.info(">>> 完成刷新过滤....");
    }

    public boolean needQueryCrt(List<AdPosModel> adPosModelList){
        return adPosModelList.stream().anyMatch(adPosModel -> needQueryCrt(adPosModel.getId()));
    }

    public boolean needQueryInstallPkg(List<AdPosModel> adPosModelList){
        return adPosModelList.stream().anyMatch(adPosModel -> needQueryInstallPkg(adPosModel.getId()));
    }

    public boolean needQueryLockArea(List<AdPosModel> adPosModelList){
        return adPosModelList.stream().anyMatch(adPosModel -> needQueryLockArea(adPosModel.getId()));
    }

    public boolean needQueryFilterRegion(List<AdPosModel> adPosModelList){
        return adPosModelList.stream().anyMatch(adPosModel -> needQueryFilterRegion(adPosModel.getId()));
    }

    public boolean needQueryCrt(Integer posId){
        return needCheckRegisterMapPrivate.getOrDefault(posId,true);
    }

    public boolean needQueryInstallPkg(Integer posId){
        return needCheckInstallPackagePrivate.getOrDefault(posId,true);
    }

    public boolean needQueryLockArea(Integer posId){
        return needCheckIsLockAreaPrivate.getOrDefault(posId,true);
    }

    public boolean needQueryFilterRegion(Integer posId){
        return needCheckIsFilterRegionPrivate.getOrDefault(posId,true);
    }

}
