package com.coohua.ap.support.core.spring.mapper.third;

import com.coohua.ap.support.core.spring.model.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 第三方广告DB操作
 * <AUTHOR>
 */
public interface ThirdAdMapper {

    @Select(" SELECT a.id as 'id', "
            + " a.name as 'name', "
            + " a.type as 'type', "
            + " a.put_date_period as 'putDatePeriod', "
            + " a.time_bucket as 'timeBucket', "
            + " a.state as 'state', "
            + " a.ad_pos as 'adPos', "
            + " a.product as 'product', "
            + " a.create_time as 'createTime', "
            + " a.update_time as 'updateTime', "
            + " a.state_update_time as 'stateUpdateTime', "
            + " a.ecp_ad_id as 'ecpAdId', "
            + " b.region as 'region', "
            + " b.sex as 'sex', "
            + " b.platform_version as 'platformVersion', "
            + " b.app as 'app', "
            + " b.tail_number as 'tailNumber', "
            + " b.`position` as 'position', "
            + " b.`income` as 'income', "
            + " b.`register_time` as 'registerTime', "
            + " b.`regist_longer` as 'registLonger', "
            + " b.`contains_pkg` as 'containsPkg', "
            + " b.`not_contains_pkg` as 'notContainsPkg', "
            + " b.`user_pkg` as 'userPkg', "
            + " b.`channel_id` as 'channelId', "
            + " b.`ab_point` as 'abPoint', "
            + " b.`filter_region` as 'filterRegion', "
            + " b.`lock_area` as 'lockArea', "
            + " b.brand as 'brand', "
            + " c.budget as 'budget', "
            + " c.click_interval as 'clickInterval', "
            + " c.day_click_limit as 'dayClickLimit', "
            + " c.exposure_interval as 'exposureInterval', "
            + " c.day_exposure_limit as 'dayExposureLimit', "
            + " c.ecpm as 'ecpm', "
            + " d.ext as 'ext' "
            + " FROM tb_ap_ad_information a LEFT JOIN tb_ap_ad_orientation b ON a.id = b.id "
            + "	LEFT JOIN tb_ap_ad_budget c ON b.id = c.id "
            + " LEFT JOIN tb_ap_ad_ext d on c.id = d.id "
            + " WHERE a.id = #{adId} ")
    ThirdAdModel getById(@Param("adId") long adId);

    @Select({"<script>",
            " SELECT a.id as 'id', "
                    + " a.name as 'name', "
                    + " a.type as 'type', "
                    + " a.put_date_period as 'putDatePeriod', "
                    + " a.time_bucket as 'timeBucket', "
                    + " a.state as 'state', "
                    + " a.ad_pos as 'adPos', "
                    + " a.product as 'product', "
                    + " a.create_time as 'createTime', "
                    + " a.update_time as 'updateTime', "
                    + " a.state_update_time as 'stateUpdateTime', "
                    + " a.ecp_ad_id as 'ecpAdId', "
                    + " b.region as 'region', "
                    + " b.sex as 'sex', "
                    + " b.platform_version as 'platformVersion', "
                    + " b.app as 'app', "
                    + " b.tail_number as 'tailNumber', "
                    + " b.`position` as 'position', "
                    + " b.`income` as 'income', "
                    + " b.`register_time` as 'registerTime', "
                    + " b.`regist_longer` as 'registLonger', "
                    + " b.`contains_pkg` as 'containsPkg', "
                    + " b.`not_contains_pkg` as 'notContainsPkg', "
                    + " b.`user_pkg` as 'userPkg', "
                    + " b.`channel_id` as 'channelId', "
                    + " b.`ab_point` as 'abPoint', "
                    + " b.`filter_region` as 'filterRegion', "
                    + " b.`lock_area` as 'lockArea', "
                    + " b.brand as 'brand', "
                    + " c.budget as 'budget', "
                    + " c.click_interval as 'clickInterval', "
                    + " c.day_click_limit as 'dayClickLimit', "
                    + " c.exposure_interval as 'exposureInterval', "
                    + " c.day_exposure_limit as 'dayExposureLimit', "
                    + " c.ecpm as 'ecpm', "
                    + " d.ext as 'ext' "
                    + " FROM tb_ap_ad_information a LEFT JOIN tb_ap_ad_orientation b ON a.id = b.id "
                    + "	LEFT JOIN tb_ap_ad_budget c ON b.id = c.id "
                    + " LEFT JOIN tb_ap_ad_ext d on c.id = d.id "
                    + " WHERE a.id in ",
            "<foreach collection='adIds' index='index' item='item' open='(' separator=',' close=')'> #{item} </foreach>",
            "</script>",
    })
    List<ThirdAdModel> getByIds(@Param("adIds") List<Long> adIds);

    @Select("select id from tb_ap_ad_information where product = #{appId}")
    List<Long> queryAllIds(@Param("appId")Integer appId);

    @Select(" SELECT a.id as 'id', "
            + " a.name as 'name', "
            + " a.type as 'type', "
            + " a.put_date_period as 'putDatePeriod', "
            + " a.time_bucket as 'timeBucket', "
            + " a.state as 'state', "
            + " a.ad_pos as 'adPos', "
            + " a.product as 'product', "
            + " a.create_time as 'createTime', "
            + " a.update_time as 'updateTime', "
            + " a.state_update_time as 'stateUpdateTime', "
            + " a.ecp_ad_id as 'ecpAdId', "
            + " b.region as 'region', "
            + " b.sex as 'sex', "
            + " b.platform_version as 'platformVersion', "
            + " b.app as 'app', "
            + " b.tail_number as 'tailNumber', "
            + " b.`position` as 'position', "
            + " b.`income` as 'income', "
            + " b.`register_time` as 'registerTime', "
            + " b.`regist_longer` as 'registLonger', "
            + " b.`contains_pkg` as 'containsPkg', "
            + " b.`not_contains_pkg` as 'notContainsPkg', "
            + " b.`user_pkg` as 'userPkg', "
            + " b.`channel_id` as 'channelId', "
            + " b.`ab_point` as 'abPoint', "
            + " b.`filter_region` as 'filterRegion', "
            + " b.`lock_area` as 'lockArea', "
            + " b.brand as 'brand', "
            + " c.budget as 'budget', "
            + " c.click_interval as 'clickInterval', "
            + " c.day_click_limit as 'dayClickLimit', "
            + " c.exposure_interval as 'exposureInterval', "
            + " c.day_exposure_limit as 'dayExposureLimit', "
            + " c.ecpm as 'ecpm', "
            + " d.ext as 'ext' "
            + " FROM tb_ap_ad_information a LEFT JOIN tb_ap_ad_orientation b ON a.id = b.id "
            + "	LEFT JOIN tb_ap_ad_budget c ON b.id = c.id "
            + " LEFT JOIN tb_ap_ad_ext d on c.id = d.id "
    )
    List<ThirdAdModel> getAll();

    @Select(" SELECT a.id as 'id', "
            + " a.name as 'name', "
            + " a.type as 'type', "
            + " a.put_date_period as 'putDatePeriod', "
            + " a.time_bucket as 'timeBucket', "
            + " a.state as 'state', "
            + " a.ad_pos as 'adPos', "
            + " a.product as 'product', "
            + " a.create_time as 'createTime', "
            + " a.update_time as 'updateTime', "
            + " a.state_update_time as 'stateUpdateTime', "
            + " a.ecp_ad_id as 'ecpAdId', "
            + " b.region as 'region', "
            + " b.sex as 'sex', "
            + " b.platform_version as 'platformVersion', "
            + " b.app as 'app', "
            + " b.tail_number as 'tailNumber', "
            + " b.`position` as 'position', "
            + " b.`income` as 'income', "
            + " b.`register_time` as 'registerTime', "
            + " b.`regist_longer` as 'registLonger', "
            + " b.`contains_pkg` as 'containsPkg', "
            + " b.`not_contains_pkg` as 'notContainsPkg', "
            + " b.`user_pkg` as 'userPkg', "
            + " b.`channel_id` as 'channelId', "
            + " b.`ab_point` as 'ab_point', "
            + " b.`filter_region` as 'filter_region', "
            + " b.`lock_area` as 'lock_area', "
            + " b.brand as 'brand', "
            + " c.budget as 'budget', "
            + " c.click_interval as 'clickInterval', "
            + " c.day_click_limit as 'dayClickLimit', "
            + " c.exposure_interval as 'exposureInterval', "
            + " c.day_exposure_limit as 'dayExposureLimit', "
            + " c.ecpm as 'ecpm', "
            + " d.ext as 'ext' "
            + " FROM tb_ap_ad_information a LEFT JOIN tb_ap_ad_orientation b ON a.id = b.id "
            + "	LEFT JOIN tb_ap_ad_budget c ON b.id = c.id "
            + " LEFT JOIN tb_ap_ad_ext d on c.id = d.id "
            + " WHERE a.type = #{adType}")
    List<ThirdAdModel> getByType(@Param("adType") int adType);

    @Select({"select id,name,app_id from tb_ap_adpos"})
    List<AdPosModel> selectAllPos();

    @Select({"SELECT " +
            " a.id AS id, " +
            " a.product AS product, " +
            " a.ad_pos_type AS ad_pos_type, " +
            " a.switch_flag AS switch_flag, " +
            " b.ext AS ext, " +
            " a.ad_id AS ad_id, " +
            " c.name AS name, " +
            " d.ab_point AS strategy_id, " +
            " d.platform_version AS platform_version, " +
            " c.type AS ad_type," +
            " c.state AS state," +
            " d.tail_number AS tail_number," +
            " d.channel_id AS channel_id," +
            " d.filter_region AS filter_region," +
            " d.lock_area AS lock_area," +
            " d.brand AS brand," +
            " d.dsp as dsp," +
            " d.lock_action_point as lock_action_point," +
            " a.start_ecpm as start_ecpm," +
            " a.end_ecpm as end_ecpm," +
            " a.priority as priority," +
            " a.play_type as play_type," +
            " a.bidding_type as bidding_type" +
            " FROM " +
            " ( " +
            "  SELECT " +
            "   * " +
            "  FROM " +
            "   tb_ap_bidding " +
            "  WHERE " +
            "   del_flag = 1 and switch_flag = 1" +
            " ) AS a " +
            " LEFT JOIN tb_ap_ad_ext AS b ON a.ad_id = b.id " +
            " LEFT JOIN tb_ap_ad_information AS c on a.ad_id = c.id " +
            " LEFT JOIN tb_ap_ad_orientation AS d on a.ad_id = d.id"
    })
    List<BiddingModel> selectAllBiddingPos();

    @Select("select ad_id from tb_ap_bidding where del_flag = 1")
    List<Long> biddingIdList();

    @Select({"SELECT " +
            " a.product AS product, " +
            " c.`name` AS app_name, " +
            " b.value AS config " +
            " FROM " +
            " ( " +
            "  SELECT " +
            "   product " +
            "  FROM " +
            "   tb_ap_bidding where del_flag = 1 and switch_flag = 1" +
            "  GROUP BY " +
            "   product " +
            " ) AS a " +
            "LEFT JOIN ( " +
            " SELECT " +
            "  * " +
            " FROM " +
            "  tb_ap_config " +
            " WHERE " +
            "  `name` = 'ad.third.cache' " +
            ") AS b ON a.product = b.product " +
            " LEFT JOIN tb_ap_app c ON a.product = c.app_id"})
    List<BiddingAppModel> selectAllBiddingApp();


    @Select({
            "<script>",
            " select id from tb_ap_ad_information where type in ",
            " <foreach collection='typeList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            " and product in ",
            " <foreach collection='appList' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"
    })
    List<Long> selectAllGdtAd(@Param("typeList") List<Integer> typeList,@Param("appList") List<Integer> appList);
}
