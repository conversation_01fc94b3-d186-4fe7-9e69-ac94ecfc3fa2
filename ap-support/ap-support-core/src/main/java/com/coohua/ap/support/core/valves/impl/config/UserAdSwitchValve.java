package com.coohua.ap.support.core.valves.impl.config;

import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.support.core.components.StrategyOrientationConfig;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.spring.SpringIoCUtils;
import com.coohua.ap.support.core.valves.DefaultValve;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/10
 */
public class UserAdSwitchValve extends DefaultValve {
    @Override
    protected void doInvoke(List<Long> candidates, Object userInformation) {
        if (userInformation instanceof UserMetaForStrategyConfig) {
            UserMetaForStrategyConfig userInfo = (UserMetaForStrategyConfig) userInformation;
            String res = SpringIoCUtils.getJedisClusterClient().get(
                    RedisBuilder.buildUserReaderAd(
                            userInfo.getAppId().toString(),
                            userInfo.getUserId().toString()
                    )
            );
            if (StringUtils.isNotEmpty(res)) {
                if (!Boolean.parseBoolean(res)){
                    throw new BusinessException(405, "当前用户无法获取广告");
                }
            }
        }
    }
}
