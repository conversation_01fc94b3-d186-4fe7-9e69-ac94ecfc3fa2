package com.coohua.ap.support.core.container.event;

import com.coohua.ap.support.core.components.Config;

import java.util.EventObject;

/**
 * 配置事件
 * <AUTHOR>
 */
public class ConfigEvent extends EventObject {
    private final Object data;
    private final ConfigEventType type;

    public ConfigEvent(Config source, Object data, ConfigEventType type) {
        super(source);
        this.data = data;
        this.type = type;
    }

    public Object getData() {
        return this.data;
    }

    public Config getAd() {
        return (Config) getSource();
    }

    public ConfigEventType getType() {
        return this.type;
    }
}
