package com.coohua.ap.support.core.spring.mapper.config;

import com.coohua.ap.support.core.spring.model.StrategyBaseConfigModel;
import com.coohua.ap.support.core.spring.model.StrategyOrientationConfigModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/10
 */
public interface StrategyConfigMapper {

    /**
     * 查询所有配置信息
     *
     * @return 配置列表
     */
    @Select(" SELECT "
            + " id as 'id', "
            + " name as 'name', "
            + " product as 'product', "
            + " type as 'type', "
            + " config as 'config', "
            + " comment as 'comment', "
            + " state as 'state', "
            + " create_time as 'createTime', "
            + " update_time as 'updateTime' "
            + " FROM tb_ap_config_base WHERE `state` = 1")
    List<StrategyBaseConfigModel> queryAllBaseConfig();

    @Select(" SELECT "
            + " id as 'id', "
            + " name as 'name', "
            + " product as 'product', "
            + " type as 'type', "
            + " config as 'config', "
            + " comment as 'comment', "
            + " state as 'state', "
            + " create_time as 'createTime', "
            + " update_time as 'updateTime' "
            + " FROM tb_ap_config_base WHERE `state` = 1 AND id > #{lastId} order by id LIMIT #{pageSize}")
    List<StrategyBaseConfigModel> queryBaseConfigByPage(@Param("lastId") long lastId, @Param("pageSize") int pageSize);

    /**
     * 根据id查询配置信息列表
     * @param id
     * @return
     */
    @Select(" SELECT "
            + " id as 'id', "
            + " name as 'name', "
            + " product as 'product', "
            + " type as 'type', "
            + " config as 'config', "
            + " comment as 'comment', "
            + " state as 'state', "
            + " create_time as 'createTime', "
            + " update_time as 'updateTime' "
            + " FROM tb_ap_config_base WHERE `state` = 1 AND id = #{id}")
    StrategyBaseConfigModel queryBaseConfigById(@Param("id") Integer id);

    /**
     * 查询所有的配置信息
     *
     * @return 配置信息列表
     */
    @Select(" SELECT "
            + " id as 'id', "
            + " name as 'name', "
            + " ad_pos as 'adPos', "
            + " product as 'product', "
            + " os as 'os', "
            + " region as 'region', "
            + " region_side as 'regionSide', "
            + " anonymous as 'anonymous', "
            + " regist as 'regist', "
            + " income as 'income', "
            + " version as 'version', "
            + " tail_number as 'tailNumber', "
            + " user_pkg as 'userPkg', "
            + " channel_id as 'channelId', "
            + " manufacturer as 'manufacturer', "
            + " sdk_version as 'sdkVersion', "
            + " config as 'config', "
            + " state as 'state', "
            + " ab_test as 'abTest', "
            + " tf_platform as 'tfPlatform', "
            + " priority as 'priority', "
            + " create_time as 'createTime', "
            + " update_time as 'updateTime' "
            + " FROM tb_ap_config_orientation WHERE `state` = 1")
    List<StrategyOrientationConfigModel> queryAllOrientationConfig();

    @Select(" SELECT "
            + " id as 'id', "
            + " name as 'name', "
            + " ad_pos as 'adPos', "
            + " product as 'product', "
            + " os as 'os', "
            + " region as 'region', "
            + " region_side as 'regionSide', "
            + " anonymous as 'anonymous', "
            + " regist as 'regist', "
            + " income as 'income', "
            + " version as 'version', "
            + " tail_number as 'tailNumber', "
            + " user_pkg as 'userPkg', "
            + " channel_id as 'channelId', "
            + " manufacturer as 'manufacturer', "
            + " sdk_version as 'sdkVersion', "
            + " config as 'config', "
            + " state as 'state', "
            + " ab_test as 'abTest', "
            + " tf_platform as 'tfPlatform', "
            + " priority as 'priority', "
            + " dsp as 'dsp', "
            + " lock_action_point as 'lockActionPoint', "
            + " create_time as 'createTime', "
            + " update_time as 'updateTime' "
            + " FROM tb_ap_config_orientation WHERE `state` = 1 AND id > #{lastId} order by id LIMIT #{pageSize}")
    List<StrategyOrientationConfigModel> queryOrientationConfigByPage(@Param("lastId") long lastId, @Param("pageSize") int pageSize);

    /**
     * 根据id查询配置信息列表
     * @param id
     * @return
     */
    @Select(" SELECT "
            + " id as 'id', "
            + " name as 'name', "
            + " type as 'type', "
            + " product as 'product', "
            + " os as 'os', "
            + " region as 'region', "
            + " anonymous as 'anonymous', "
            + " regist as 'regist', "
            + " income as 'income', "
            + " version as 'version', "
            + " tail_number as 'tailNumber', "
            + " user_pkg as 'userPkg', "
            + " channel_id as 'channelId', "
            + " manufacturer as 'manufacturer', "
            + " sdk_version as 'sdkVersion', "
            + " config as 'config', "
            + " state as 'state', "
            + " abTest as 'abTest', "
            + " tf_platform as 'tfPlatform', "
            + " priority as 'priority', "
            + " create_time as 'createTime', "
            + " update_time as 'updateTime' "
            + " FROM tb_ap_config_orientation WHERE `state` = 1 AND id = #{id}")
    StrategyOrientationConfigModel queryOrientationConfigById(@Param("id") Integer id);

}
