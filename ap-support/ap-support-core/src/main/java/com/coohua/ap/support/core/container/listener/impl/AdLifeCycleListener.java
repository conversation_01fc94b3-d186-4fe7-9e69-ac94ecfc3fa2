package com.coohua.ap.support.core.container.listener.impl;

import com.coohua.ap.support.core.container.event.AdEvent;
import com.coohua.ap.support.core.container.event.AdEventType;
import com.coohua.ap.support.core.container.listener.AdListener;
import com.coohua.ap.support.core.spring.ContainerHolder;
import lombok.extern.slf4j.Slf4j;

/**
 * 广告生命周期事件监听器
 * <AUTHOR>
 */
@Slf4j
public class AdLifeCycleListener implements AdListener {
    @Override
    public void onAdEvent(AdEvent event) {
        if (event.getType() == AdEventType.CREATE) {
//            log.info("Event name is {}, adId={}", event.getType().name(), event.getData().toString());
        } else if (event.getType() == AdEventType.UPDATE) {
            log.info("Event name is {}, adId={}", event.getType().name(), event.getData().toString());
            if (event.getData() instanceof  Long){
                Long adId = (Long) event.getData();
                ContainerHolder.getThirdAdContainer().refreshAd(adId);
            }
        } else if (event.getType() == AdEventType.DESTROY) {
//            log.info("Event name is {}, adId={}", event.getType().name(), event.getData().toString());
        }
    }
}
