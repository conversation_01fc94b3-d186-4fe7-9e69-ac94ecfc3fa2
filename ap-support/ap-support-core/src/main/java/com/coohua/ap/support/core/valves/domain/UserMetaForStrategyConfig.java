package com.coohua.ap.support.core.valves.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/10
 */
@Data
public class UserMetaForStrategyConfig {
    private Long userId;

    private Integer appId;

    private Integer posId;

    // 筛选条件，1-匿名用户，2-非匿名用户
    private Integer anonymous;

    // 筛选条件：渠道ID
    private String channelId;

    // 筛选条件：用户总收入
    private Integer userIncome;

    // 条件
    private Integer os;

    // 条件，1-受限区，2-非受限区
    private Integer filterRegion;

    // 筛选条件。用户注册时间
    private Date userRegistDate;

    // 筛选条件：用户客户端包名
    private String userPkgName;

    // 筛选条件：用户客户端段本号
    private String userVersion;

    // 筛选条件: AB测试系统分组
    private Integer strategyId;
    // 筛选条件: 新AB测试系统分组
    private Integer categoryId;
    //制造商
    private String manufacturer;

    private String userSdkVersion;

    private Boolean isLockedArea;

    private Boolean isFirstLockActionPoint;

    private List<Integer> enableLockActionPointList;

    private Boolean isOCPC;

    private Date registerDate;

    private Map<Integer,Map<Integer,Map<Integer,Map<Integer,List<String>>>>> appABIsBiddingTypeMap;

    private String dsp;

    /**
     * bidding / 瀑布流实验配置
     */
    public Map<Integer, Map<Integer, Map<String, Object>>> adABTestStrategyMap;
}
