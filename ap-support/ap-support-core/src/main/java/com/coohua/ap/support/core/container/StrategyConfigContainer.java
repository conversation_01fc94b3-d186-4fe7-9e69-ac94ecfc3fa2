package com.coohua.ap.support.core.container;

import com.coohua.ap.support.core.domain.FilteredStrategyConfig;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;

/**
 * 策略配置容器
 * <AUTHOR>
 */
public interface StrategyConfigContainer {

    /**
     * 为用户筛选最优的广告投放策略配置
     * @param posId 广告位ID
     * @param userMeta 用户信息
     * @return 最优策略配置
     */
    FilteredStrategyConfig chooseOptimalStrategy(Integer posId, UserMetaForStrategyConfig userMeta);
}
