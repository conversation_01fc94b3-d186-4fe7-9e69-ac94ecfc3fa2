package com.coohua.ap.support.core.discount.handler;

import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;

import java.util.List;
import java.util.Map;

/**
 * sdk 版本过滤
 */
public class SdkVersionConfigDiscountHandler extends AbstractConfigDiscountHandler {

    @Override
    protected void doFilter(List<Long> candidates, Map<Long, ApConfigDiscountVo> discountMap, UserMetaForStrategyConfig userInformation) {
        candidates.removeIf(configId -> !discountMap.get(configId).isSdkVersionValid(userInformation.getUserSdkVersion()));
    }
}
