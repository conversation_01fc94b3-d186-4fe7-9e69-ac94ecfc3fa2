package com.coohua.ap.support.core.components;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.base.constants.ThirdPlatformType;
import com.coohua.ap.base.domain.BrandLimitDomain;
import com.coohua.ap.base.domain.PlatformVersion;
import com.coohua.ap.base.utils.Version;
import com.coohua.ap.support.core.spring.model.BiddingModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
@Data
public class BiddingAd {
    private Long id;
    // 内部产品Id
    private Integer product;
    // 内部广告Id
    private Long adId;
    private String name;
    // 广告类型
    private Integer adPosType;
    // 应用id 三方
    private String appId;
    // 广告位id 三方
    private String posId;
    // 我方广告类型
    private Integer adType;

    private Integer startEcpm;

    private Integer endEcpm;

    private Integer switchFlag;

    private Integer state;

    private List<String> channel;

    private List<String> userIds;

    private List<String> strategyId;

    private PlatformVersion platformVersion;

    /**
     * @see com.coohua.ap.base.constants.ThirdPlatformType
     */
    private Integer platform;

    private Integer priority;

    private Integer biddingType;

    private Integer lowReq;

    private Integer filterRegion;

    private Integer lockArea;

    private BrandLimitDomain brandLimit;

    private String dsp;

    private Integer lockActionPoint;

    public BiddingAd() {}

    public BiddingAd(BiddingModel biddingModel) {
        ThirdAdExt thirdAdExt = new ThirdAdExt(biddingModel.getExt());
        this.id = Long.valueOf(biddingModel.getId());
        this.product = biddingModel.getProduct();
        this.adType =  biddingModel.getAdType();
        this.adPosType = biddingModel.getAdPosType();
        this.adId = biddingModel.getAdId();
        this.name = biddingModel.getName();
        this.switchFlag = biddingModel.getSwitchFlag();
        this.state = biddingModel.getState();
        this.startEcpm = Optional.ofNullable(biddingModel.getStartEcpm()).orElse(0);
        this.endEcpm =  Optional.ofNullable(biddingModel.getEndEcmp()).orElse(3000);
        this.strategyId = StringUtils.isEmpty(biddingModel.getStrategyId()) ? new ArrayList<>() :
                Arrays.asList(biddingModel.getStrategyId().split(","));
        this.channel = StringUtils.isEmpty(biddingModel.getChannelId()) ? new ArrayList<>() :
                Arrays.asList(biddingModel.getChannelId().split(","));
        this.userIds = StringUtils.isEmpty(biddingModel.getTailNumber()) ? new ArrayList<>() :
                Arrays.asList(biddingModel.getTailNumber().split(","));
        this.appId = StringUtils.isNotEmpty(thirdAdExt.getAndroidAppId())? thirdAdExt.getAndroidAppId():thirdAdExt.getIosAppId();
        this.posId = StringUtils.isNotEmpty(thirdAdExt.getAndroidPosId())?thirdAdExt.getAndroidPosId():thirdAdExt.getIosPosId();
        ThirdPlatformType thirdPlatformType = AdTypeSub.getPlatform(biddingModel.getAdType());
        this.platform = thirdPlatformType.getCode();
        this.platformVersion = JSON.parseObject(biddingModel.getPlatformVersion(),PlatformVersion.class);
        this.priority = biddingModel.getPriority() == null ? 0 : biddingModel.getPriority();
        this.biddingType = biddingModel.getBiddingType() == null ? 2 : biddingModel.getBiddingType();
        this.lowReq = biddingModel.getPlayType() == null ? 1 : biddingModel.getPlayType();
        this.filterRegion = biddingModel.getFilterRegion() == null ? 0 : biddingModel.getFilterRegion();
        this.lockArea = biddingModel.getLockArea() == null ? 0 : biddingModel.getLockArea();
        this.lockActionPoint = biddingModel.getLockActionPoint() == null ? 0 : biddingModel.getLockActionPoint();
        String brand = biddingModel.getBrand();
        if (StringUtils.isNotEmpty(brand)) {
            this.brandLimit = JSONObject.parseObject(brand, BrandLimitDomain.class);
        } else {
            BrandLimitDomain brandLimitDomain = new BrandLimitDomain();
            brandLimitDomain.setBrandLimit(0);
            brandLimitDomain.setBrandLimitList(new ArrayList<>());
            this.brandLimit = brandLimitDomain;
        }
    }

    public boolean isUserChannelValid(String userChannel) {
        if (CollectionUtils.isEmpty(this.channel)) {
            return true;
        }

        if (StringUtils.isEmpty(userChannel)) {
            return false;
        }

        for (String channel : this.channel) {
            if (userChannel.startsWith(channel)) {
                return true;
            }
        }
        return false;
    }

    public boolean userDspValid(String dsp) {
        if (StringUtils.isBlank(this.dsp)) {
            return false;
        }

        if (StringUtils.isBlank(dsp)) {
            return true;
        }

        if (this.dsp.equals(dsp)) {
            return false;
        }

        return true;
    }

    public boolean isTailNumberValid(Long userId) {
        if (CollectionUtils.isEmpty(this.userIds)) {
            return true;
        }

        for (String tn : this.userIds) {
            if (StringUtils.isEmpty(tn.trim())) {
                continue;
            }
            if (userId.toString().endsWith(tn)) {
                return true;
            }
        }

        return false;
    }

    public boolean isPlatformVersionValid(OSType osType, String appVersion) {
        if (platformVersion == null) {
            return true;
        }

        if (platformVersion.getPlatform() == AdConstants.AD_PUT_PLATFORM_ALL) {
            return (osType == OSType.ANDROID && versionCompare(platformVersion.getAndroidStartVersion(), platformVersion.getAndRoidEndVersion(), appVersion))
                    || (osType == OSType.IOS && versionCompare(platformVersion.getIosStartVersion(), platformVersion.getIosEndVersion(), appVersion));
        } else if (platformVersion.getPlatform() == AdConstants.AD_PUT_PLATFORM_ANDROID) {// 限制平台，则在指定平台下，限制版本
            return osType == OSType.ANDROID && versionCompare(platformVersion.getAndroidStartVersion(), platformVersion.getAndRoidEndVersion(), appVersion);
        } else if (platformVersion.getPlatform() == AdConstants.AD_PUT_PLATFORM_IOS) {
            return osType == OSType.IOS && versionCompare(platformVersion.getIosStartVersion(), platformVersion.getIosEndVersion(), appVersion);
        }
        return false;
    }

    public boolean isSuitStrategyId(Integer categoryId){
        if (this.strategyId.size() == 0){
            return true;
        }
        return this.strategyId.contains(categoryId.toString());
    }

    private boolean versionCompare(String startVersion, String endVersion, String version) {
        if (StringUtils.isEmpty(startVersion) && StringUtils.isEmpty(endVersion)) { // 开始版本和结束版本都是空的时候，不限制
            return true;
        } else if (StringUtils.isNotEmpty(startVersion) && StringUtils.isEmpty(endVersion)) {
            return Version.compare(version, startVersion) >= 0;
        } else if (StringUtils.isEmpty(startVersion) && StringUtils.isNotEmpty(endVersion)) {
            return Version.compare(version, endVersion) <= 0;
        } else if (StringUtils.isNotEmpty(startVersion) && StringUtils.isNotEmpty(endVersion)) {
            return Version.compare(version, startVersion) >= 0 && Version.compare(version, endVersion) <= 0;
        }
        return false;
    }

    /**
     * 校验是否满足区域策略条件
     *
     * @param filterRegion 当前用户是否是受限地区用户，1-受限区／2-非受限区／0-不限制
     * @return true - 满足，false - 不满足
     */
    public boolean isRegionValid(Boolean filterRegion) {
        if (filterRegion == null || this.filterRegion == null) {
            return true;
        }

        if (Integer.valueOf(AdConstants.DEFAULT_NO_LIMIT_CODE).equals(this.filterRegion)) {
            return true;
        }

        Boolean res = this.filterRegion == 1;
        return filterRegion.equals(res);
    }

    public boolean isLockedArea(Boolean isLockedArea) {
        if (this.lockArea == null || this.lockArea.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
            return true;
        }

        if (isLockedArea == null) {
            return true;
        }

        Boolean limit = this.lockArea == 1;
        return isLockedArea.equals(limit);
    }

    /**
     * 锁区判断，增加锁区作用点逻辑
     * @param isLockedArea
     * @param isFirstLockActionPoint
     * @return
     */
    public boolean isLockedArea(Boolean isLockedArea, Boolean isFirstLockActionPoint) {
        if (this.lockArea == null || this.lockArea.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
            return true;
        }

        if (isLockedArea == null) {
            return true;
        }

        Boolean limit = this.lockArea == 1;

        boolean lockResult = isLockedArea.equals(limit);

        if (isLockedArea && lockResult) {
            Integer actionPoint = Optional.ofNullable(this.lockActionPoint).orElse(AdConstants.DEFAULT_NO_LIMIT_CODE);

            if (actionPoint.equals(AdConstants.DEFAULT_NO_LIMIT_CODE)) {
                return true;
            }

            if (isFirstLockActionPoint == null) {
                return false;
            }

            if (actionPoint == 1 && isFirstLockActionPoint) {
                return true;
            }

            return actionPoint == 2 && !isFirstLockActionPoint;
        }

        return lockResult;
    }

    private static List<Long> limitAdIdList = Arrays.asList(274213L,274214L,274215L,
            274216L,274217L,274218L,274219L,274220L,274221L,
            274222L,274223L,274224L,274225L,274226L,274227L,
            274228L,274229L,274230L,274231L,274232L,273658L,
            273659L,273660L,273661L,273662L,273663L,273664L,
            273665L,273666L,273667L,273668L,273669L,273670L,
            273671L,273672L,273673L,273674L,273675L,273676L,
            273677L,301808L,301809L,301810L,301811L,301812L,
            301813L,301814L,301815L,301816L,301817L,301862L,
            301863L,301864L,301865L,301866L,301867L,301868L,
            301869L,301870L,301871L);

    public boolean isFilterRegion(Boolean isFilterRegion){
        // 若空 不处理
        if (!limitAdIdList.contains(this.adId)){
            return true;
        }

        if (isFilterRegion == null){
            return false;
        }

        // 受限区删除
        return !isFilterRegion;
    }

    /**
     * 是否跳过某平台
     * @param skipList 需要跳过的平台
     * @return TRUE-啥都不跳 FALSE-跳过
     */
    public boolean isSkipPlatform(List<Integer> skipList){
        if (skipList == null || skipList.size() == 0){
            return true;
        }

        ThirdPlatformType thirdPlatformType = AdTypeSub.getPlatform(this.adType);
        if (thirdPlatformType == null){
            return true;
        }
        return !skipList.contains(thirdPlatformType.getCode());
    }

    public boolean skipAdSpecial(Integer source,List<Long> adIdList,Integer view){
        if (adIdList == null || this.adId == null){
            return false;
        }

        if (source != null && adIdList.contains(this.adId)){
            return source != view;
        }
        return false;
    }

    public boolean skipSpAdSpecial(Integer source){
        if (adId == 363013L){
            return !Arrays.asList(1, 2, 3, 15).contains(source);
        }
        return false;
    }

    public boolean skipTTAdSpecialAppId(Integer source,Boolean contains){
        if (contains == null || this.product == null){
            return false;
        }

        if (source != null && contains){
            return source != 1;
        }
        return false;
    }

    /**
     * 是否满足机型投放条件 于瀑布流配置不太相同
     *
     * @param brand 用户机型
     * @return true - 满足，false - 不满足
     */
    public boolean isBrandValid(String brand) {
        if (this.brandLimit.getBrandLimit() == AdConstants.NO_BRAND_LIMIT) {
            return true;
        }

        // 异常配置不过滤
        if (CollectionUtils.isEmpty(this.brandLimit.getBrandLimitList()) || StringUtils.isEmpty(brand)) {
            return true;
        }

        return this.brandLimit.getBrandLimitList().contains(brand.toLowerCase());
    }
}
