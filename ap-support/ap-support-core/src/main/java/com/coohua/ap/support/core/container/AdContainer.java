package com.coohua.ap.support.core.container;

import com.coohua.ap.base.extension.Scope;
import com.coohua.ap.base.extension.Spi;
import com.coohua.ap.support.core.components.Ad;
import com.coohua.ap.support.core.container.listener.AdListener;
import com.coohua.ap.support.core.container.listener.ContainerListener;

import java.util.List;

/**
 * 广告容器
 * <AUTHOR>
 */
@Spi(scope = Scope.SINGLETON)
public interface AdContainer extends Container {

    /**
     * 获取广告信息
     * @param adId 广告ID
     * @return 广告信息
     */
    Ad getAd(Long adId);

    /**
     * 获取指定广告类型下的所有广告信息
     * @param adType 广告类型
     * @return 指定广告类型下的所有广告
     */
    List<Long> getAdCollectionByAdType(Integer adType,Integer appId);

    /**
     * 添加广告容器事件监听器
     * @param containerListener 广告容器事件监听器
     */
    void addAdContainerListener(ContainerListener containerListener);

    /**
     * 添加广告事件监听器
     * @param adListener 广告事件监听器
     */
    void addAdListener(AdListener adListener);

    /**
     * 初始化广告工厂
     */
    void initAdFactory();

    /**
     * 设置广告MQ消息处理器
     */
    void setAdMessageHandler();
}

