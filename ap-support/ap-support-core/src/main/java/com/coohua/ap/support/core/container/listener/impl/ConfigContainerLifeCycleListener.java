package com.coohua.ap.support.core.container.listener.impl;

import com.coohua.ap.support.core.container.ConfigContainer;
import com.coohua.ap.support.core.container.ContainerState;
import com.coohua.ap.support.core.container.event.ContainerEvent;
import com.coohua.ap.support.core.container.event.ContainerEventType;
import com.coohua.ap.support.core.container.listener.ContainerListener;
import lombok.extern.slf4j.Slf4j;

/**
 * 配置容器生命周期事件监听器
 * <AUTHOR>
 */
@Slf4j
public class ConfigContainerLifeCycleListener implements ContainerListener {

    private ConfigContainer configContainer;

    public ConfigContainerLifeCycleListener(ConfigContainer configContainer) {
        this.configContainer = configContainer;
    }

    @Override
    public void onContainerEvent(ContainerEvent event) {
        if (event.getType() == ContainerEventType.STATE_CHANGE) {
            if (event.getData() instanceof ContainerState) {
                ContainerState changeState = (ContainerState) event.getData();
                if (ContainerState.INIT == changeState) {
                    log.info(configContainer.getName() + " init ... ");
                } else if (ContainerState.STARTED == changeState) {
                    log.info(configContainer.getName() + " started ... ");
                } else if (ContainerState.REFRESH == changeState) {
                    log.info(configContainer.getName() + " refresh ... mem={}", configContainer.toString());
                } else if (ContainerState.STOP == changeState) {
                    log.info(configContainer.getName() + " stop ... ");
                } else if (ContainerState.DESTROY == changeState) {
                    log.info(configContainer.getName() + " destroy ... ");
                }
            }
        }
    }
}
