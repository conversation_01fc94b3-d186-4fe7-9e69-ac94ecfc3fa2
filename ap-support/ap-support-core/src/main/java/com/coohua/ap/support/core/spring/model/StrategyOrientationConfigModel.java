package com.coohua.ap.support.core.spring.model;

import lombok.Data;

import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-11-08
 */
@Data
public class StrategyOrientationConfigModel {

    private Integer id;

    private String name;

    private Integer adPos;

    private Integer product;

    private Integer os;

    private Integer region;

    private Integer regionSide;

    private Integer anonymous;

    private String regist;

    private String income;

    private String version;

    private String tfPlatform;

    private String tailNumber;

    private String config;

    private Integer state;

    private Integer priority;

    private Date createTime;

    private Date updateTime;

    private String userPkg;

    private String channelId;

    private String manufacturer;

    private String sdkVersion;

    private String abTest;

    private String dsp;

    private Integer lockActionPoint;
}
