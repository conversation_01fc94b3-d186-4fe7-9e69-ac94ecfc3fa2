package com.coohua.ap.support.core.components;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/11/9
 */
@Data
public class NewCustomAdExt {

    private String logoUrl;
    /**
     * 视频地址，可配置多个，用`,`隔开
     */
    private String videoUrlText;
    /**
     * 原生应用落地页地址
     */
    private String deeplinkUrl;

    /**
     * 安卓安装包的下载地址 或 iOS的App Store地址
     */
    private String downloadUrl;
    /**
     * 下载广告安装包包名
     */
    private String appPkgName;

    /**
     *  视频地址
     **/
    private String videoUrl;

    private String detail;

    private Integer videoId;

    protected Integer style = 101;  //广告样式id，101-小图，201-大图，301-组图，401-视频
    protected String title = "";   //广告标题
    protected String content = ""; //广告摘要
    protected String clkUrl = "";  //广告落地页地址 or CPO广告的浏览器落地页地址
    protected List<String> imgUrl = new ArrayList<String>();  //广告图片物料地址
    protected List<String> impTrackUrl = new ArrayList<String>(); //广告曝光回调地址
    protected List<String> clkTrackUrl = new ArrayList<String>(); //广告点击回调地址 或 落地页下载按钮点击上报

}
