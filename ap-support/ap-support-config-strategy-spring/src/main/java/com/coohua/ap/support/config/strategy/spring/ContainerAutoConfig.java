package com.coohua.ap.support.config.strategy.spring;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

/**
 * 由spring.factories管理，用于从Apollo加载配置，并通过caf自动初始化相关Spring组件
 * <AUTHOR>
 */
@Configuration
@ComponentScan({"com.coohua.ap.support.core.spring"})
@EnableApolloConfig(value = {"ap.datasource"}) // "ad.nap.datasource"
@EnableAutoChangeApolloConfig
//@EnableDataSource(namespace = "napdb", mapperPackages = "com.coohua.ap.support.core.spring.mapper.config")
@EnableDataSource(namespace = "apdatasource", mapperPackages = "com.coohua.ap.support.core.spring.mapper.config")
public class ContainerAutoConfig {

}
