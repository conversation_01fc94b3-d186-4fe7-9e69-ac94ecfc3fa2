package com.coohua.ap.support.config.strategy.spring;

import com.coohua.ap.support.core.container.DefaultStrategyConfigContainer;
import com.coohua.ap.support.core.spring.ContainerHolder;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;

/**
 * Spring容器Ready事件监听器，用于Spring容器Ready后，初始化和启动广告相关容器。由spring.factories管理
 * <AUTHOR>
 */
public class ContainerBootListener implements ApplicationListener<ApplicationEvent> {
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ApplicationReadyEvent) {
            DefaultStrategyConfigContainer strategyConfigContainer = ContainerHolder.getStrategyConfigContainer();
            // 启动策略配置容器
            strategyConfigContainer.init();
            strategyConfigContainer.started();
        }
    }
}
