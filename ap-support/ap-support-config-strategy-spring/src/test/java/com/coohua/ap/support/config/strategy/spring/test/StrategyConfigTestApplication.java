package com.coohua.ap.support.config.strategy.spring.test;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.support.core.container.DefaultStrategyConfigContainer;
import com.coohua.ap.support.core.domain.FilteredStrategyConfig;
import com.coohua.ap.support.core.spring.ContainerHolder;
import com.coohua.ap.support.core.valves.domain.UserMetaForStrategyConfig;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/11
 */
@SpringBootApplication(scanBasePackages = "com.coohua.ap.support.core.spring")
@EnableApolloConfig(value = {"ap.redis.cluster", "ad.nap.datasource"}) // "ap.redis.single",
@EnableAutoChangeApolloConfig
//@EnableJedisClient(namespace = "ap-redis-single")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableDataSource(namespace = "napdb", mapperPackages = "com.coohua.ap.support.core.spring.mapper.config")
public class StrategyConfigTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(StrategyConfigTestApplication.class, args);
        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
        executorService.scheduleAtFixedRate(() -> {
            DefaultStrategyConfigContainer strategyConfigContainer = ContainerHolder.getStrategyConfigContainer();
            UserMetaForStrategyConfig userInfo = new UserMetaForStrategyConfig();
            userInfo.setUserId(12345L);
            userInfo.setAppId(5);
            userInfo.setPosId(1000022);
            userInfo.setAnonymous(2);
            userInfo.setChannelId("ks");
            userInfo.setUserIncome(100000);
            userInfo.setOs(1);
            userInfo.setFilterRegion(2);
            userInfo.setUserRegistDate(new Date(System.currentTimeMillis() - 1000L * 3600 * 24 * 30));
            userInfo.setUserPkgName("com.boda.cvideo");
            userInfo.setUserVersion("1.0.1.5");

            FilteredStrategyConfig result = strategyConfigContainer.chooseOptimalStrategy(1000022, userInfo);
            System.out.println(JSONObject.toJSONString(result));
        }, 3, 3, TimeUnit.SECONDS);
    }
}
