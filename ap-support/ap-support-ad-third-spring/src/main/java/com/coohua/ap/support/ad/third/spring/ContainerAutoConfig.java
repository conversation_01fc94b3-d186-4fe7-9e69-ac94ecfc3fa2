package com.coohua.ap.support.ad.third.spring;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

/**
 * 由spring.factories管理，用于从Apollo加载配置，并通过caf自动初始化相关Spring组件
 * <AUTHOR>
 */
@Configuration
@ComponentScan({"com.coohua.ap.support.core.spring"})
@EnableApolloConfig(value = {"ap.redis.cluster", "ap.datasource","ap.datasource.readonly","ad.rocket.mq"}) // ad.nap.datasource "ap.redis.single",
@EnableAutoChangeApolloConfig
//@EnableJedisClient(namespace = "ap-redis-single")
@EnableJedisClusterClient(namespace = "ap-cluster")
// 第三方广告数据源 & 全局配置数据源
//@EnableDataSource(namespace = "napdb", mapperPackages = {"com.coohua.ap.support.core.spring.mapper.third", "com.coohua.ap.support.core.spring.mapper.config"})
@EnableDataSource(namespace = "apdatasourcereadonly", mapperPackages = {"com.coohua.ap.support.core.spring.mapper.third", "com.coohua.ap.support.core.spring.mapper.config"})
public class ContainerAutoConfig {

}
