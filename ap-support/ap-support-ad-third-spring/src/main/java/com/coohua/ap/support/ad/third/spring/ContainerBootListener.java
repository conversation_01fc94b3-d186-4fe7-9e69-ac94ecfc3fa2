package com.coohua.ap.support.ad.third.spring;

import com.coohua.ap.support.core.container.ThirdAdContainer;
import com.coohua.ap.support.core.spring.ContainerHolder;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;

/**
 * Spring容器Ready事件监听器，用于Spring容器Ready后，初始化和启动广告相关容器。由spring.factories管理
 * <AUTHOR>
 */
public class ContainerBootListener implements ApplicationListener<ApplicationEvent> {
    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ApplicationReadyEvent) {
            ThirdAdContainer thirdAdContainer = ContainerHolder.getThirdAdContainer();
            // 启动第三方广告容器
            thirdAdContainer.init();
            thirdAdContainer.started();
        }
    }
}
