package com.coohua.ap.data.manager;


import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.support.core.components.CustomAd;
import com.coohua.ap.support.core.components.DefaultAd;
import com.coohua.ap.support.core.container.AdMetaInfoFactory;
import com.coohuadata.analytics.javasdk.CoohuaAnalytics;
import com.coohuadata.analytics.javasdk.exceptions.InvalidArgumentException;
import com.coohuadata.analytics.javasdk.util.PROJECT;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 * Comments:
 *  广告日志打点组件
 *
 * <pre/>
 * <hr/>
 * Created by <PERSON><PERSON> fan on 2020/2/26.
 */

@Component
@Slf4j
public class AdLogTrackManager {
    @Setter
    @Getter
    @Value("${kafka.broker}")
    private String kafkaBroker;

    @Setter
    @Getter
    @Value("${kafka.pool}")
    private int kafkaPool;

    private CoohuaAnalytics ecpAnalytics;

    @PostConstruct
    public void init() {

        ecpAnalytics = new CoohuaAnalytics(new CoohuaAnalytics.KafkaConsumer(PROJECT.ECP, kafkaBroker, kafkaPool, true));
        Runtime.getRuntime().addShutdownHook(new ShutdownHook(this));
    }

    public void ecpAdLogTrack(Long userId, String os, long adId, String adAction, String cpwTop2KeyPrice, int product) {
        DefaultAd ad = AdMetaInfoFactory.getAd(adId);
        if (ad == null
                ||(ad.getType()!= AdType.ZHIKE_READ60.type
                &&ad.getType()!=AdType.ZHIKE_CPC.type
                &&ad.getType()!=AdType.ZHIKE_VIDEO.type
                &&ad.getType()!=AdType.ENCOURAGE_CLICK.type)) {
            return;
        }
        CustomAd adInfo = (CustomAd)ad;

        Map<String, Object> properties = new HashMap<>();
        properties.put("$os", StringUtils.isNotEmpty(os) ? os.toLowerCase() : "");
        properties.put("ad_region", 1); // 0-受限地区，1-非受限地区
        properties.put("ad_name", adInfo.getName());
        properties.put("ad_type", adInfo.getType()); // 广告类型
        properties.put("ad_id", adId); // 广告ID
        properties.put("ad_position", "-10"); // feed流中的pos值
        properties.put("ad_action", adAction); // 用户行为
        properties.put("ad_source", "feed"); // 事件来源 feed or uc
        properties.put("ad_plan_id", adInfo.getPlanId() == null ? -1 : adInfo.getPlanId()); // 广告计划id
        properties.put("ad_group_id", adInfo.getGroupId() == null ? -1 : adInfo.getGroupId()); // 广告组id
        properties.put("ad_originality_id", adInfo.getAdId() == null ? -1 : adInfo.getAdId()); // 广告创意id
        properties.put("price", Integer.parseInt(cpwTop2KeyPrice)); // 单价
        properties.put("product", product);
        properties.put("advertiser_id", adInfo.getAdvertiserId() == null ? -1 : adInfo.getAdvertiserId()); // 广告主ID
        try {
            ecpAnalytics.track(userId.toString(), true, "AdDataServer", properties);
            log.info("ecpAnalytics success, userid = {} , os = {} , adId = {} , adAction = {} , cpwTop2KeyPrice = {} , product = {}",
                    userId,os,adId,adAction,cpwTop2KeyPrice,product);
        } catch (InvalidArgumentException e) {
            log.error(e.getMessage());
        }
    }

    protected void shutdown() {
        ecpAnalytics.shutdown();
    }


    protected class ShutdownHook extends Thread {
        private AdLogTrackManager adLogTrack;

        public ShutdownHook(AdLogTrackManager adLogTrack) {
            this.adLogTrack = adLogTrack;
        }

        @Override
        public void run() {
            log.info("Running shutdown sync");
            adLogTrack.shutdown();
        }
    }
}
