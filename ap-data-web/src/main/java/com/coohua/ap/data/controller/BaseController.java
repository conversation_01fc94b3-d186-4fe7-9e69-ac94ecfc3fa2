package com.coohua.ap.data.controller;

import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.base.vo.WebMessage;
import com.coohua.ap.data.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description:
 *  统一异常处理
 * <AUTHOR>
 * @package com.coohua.ad.credit.controller
 * @create_time 2019-08-24
 */
@Slf4j
public class BaseController {

    @ExceptionHandler
    @ResponseBody
    public WebMessage exception(HttpServletRequest request, HttpServletResponse response, Exception ex) {
        if (ex instanceof BusinessException) {
            BusinessException bex = (BusinessException) ex;

            int errCode = bex.code;
            log.error("Business error. code:{},message:{},ip:{}.", errCode, bex.message, IpUtils.getReomteIp(request));

            return WebMessage.build(errCode, bex.message);
        } else if (ex instanceof MissingServletRequestParameterException) {//Controller 请求参数异常
            return WebMessage.build(BusinessException.REQUEST_PARAM_ERR.code, BusinessException.REQUEST_PARAM_ERR.message);
        } else {
            ex.printStackTrace();
            log.error("Server error 500. {}", ex.getMessage());
            return WebMessage.build(BusinessException.SERVER_ERR.code, BusinessException.SERVER_ERR.message);
        }
    }
}
