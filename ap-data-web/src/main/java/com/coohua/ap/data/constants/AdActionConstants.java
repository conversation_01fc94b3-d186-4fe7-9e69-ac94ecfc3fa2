package com.coohua.ap.data.constants;

import org.apache.commons.lang3.StringUtils;

/**
 * 广告打点事件
 *
 * <AUTHOR>
 */
public enum AdActionConstants {

    CLICK("click", "点击"),
    EXPOSURE("exposure", "曝光");

    private String code;

    private String desc;

    AdActionConstants(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String code() {
        return this.code;
    }

    public String desc() {
        return this.desc;
    }

    public static AdActionConstants find(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }

        for (AdActionConstants constants : AdActionConstants.values()) {
            if (code.equals(constants.code)) {
                return constants;
            }
        }

        return null;
    }

}


