package com.coohua.ap.data.domain;

import lombok.Data;

/**
 * 打点数据模型
 * 从客户端传入数据映射
 */
@Data
public class ApDataDomain {

    // -------- 用户相关字段 begin --------
    // 用户ID
    private Long userId;
    // 平台：iOS、Android
    private String os;
    // 客户端版本号
    private String appVersion;
    // 产品标识：1-锁屏、2-淘新闻、3-浏览器
    private Integer appId;
    // 是否匿名用户：1-是，0-否
    private Integer anonymous;
    // 用户是否受限区：1-是，0-否
    private Integer filterRegion;
    // -------- 用户相关字段 end --------

    // -------- 广告相关字段 begin --------
    // 广告ID
    private Long adId;
    // 广告类型
    private Integer adType;
    // 行为："click"、"exposure"、"download"等，可以定义一个枚举，方便查阅对应关系
    private String adAction;
    // 是否是激励位置：1-是，0-否
    private Integer hasCredit;
    // 是否是打底广告：1-是，0-否
    private Integer defaultAd;
    // 投放系统的广告位，对应AdPos枚举类，由客户端传过来
    private Integer adPos;
    // 广告所在APP的页面，如：Feed流、开屏、详情页等
    private String adPage;
    // 广告在信息流中的位置，从0开始计数，理论上值等于 adGroup * group_size + adGroupPos
    private Integer adPosition;
    // 是否是下载广告：0-不是，1-是（目前只有广点通广告用这个字段，其他广告打0）
    private Integer downloadAd;

    // -------- 系统相关字段 begin --------
    // 客户端时间戳
    private Long timestampClient;
    // 服务端时间戳
    private Long timestampServer;
    // -------- 系统相关字段 end --------

}
