package com.coohua.ap.data.manager;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.utils.DateUtils;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.exceptions.JedisMovedDataException;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.Date;

@Component
public class AdDataManager {

//    @JedisClientRefer(namespace = "ap-redis-single")
//    private JedisClient jedisClient;

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    private String getTag(Object userId) {
        return "{" + userId + "}";
    }

    public String get(String key) {
        return apClusterRedisService.get(key);
    }

    public String buildRedisAdChargeForAdvertiser(Integer advertiserId, String day) {
        return RedisConstants.ECP_ADVERTISER_CHARGE_PRE + advertiserId + AdConstants.SYMBOL_COLON + day;
    }

    public String buildRedisAdCharge(long adPlanId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.ECP_AD_CHARGE_PRE + adPlanId + AdConstants.SYMBOL_COLON + today;
    }

    public String buildRedisAdChargeForGroup(long adGroupId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.ECP_ADGROUP_CHARGE_PRE + adGroupId + AdConstants.SYMBOL_COLON + today;
    }

    public String buildRedisAdChargeForCreative(long adId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.ECP_ADCREATIVE_CHARGE_PRE + adId + AdConstants.SYMBOL_COLON + today;
    }

    public void adChargeForPlan(Integer planId, String cpwTop2KeyPrice) {
        incrRedis(buildRedisAdCharge(planId), Long.parseLong(cpwTop2KeyPrice), RedisConstants.TIME_DAY_2);
    }

    public void adChargeForGroup(Integer groupId, String cpwTop2KeyPrice) {
        incrRedis(buildRedisAdChargeForGroup(groupId), Long.parseLong(cpwTop2KeyPrice), RedisConstants.TIME_DAY_2);
    }

    public void adChargeForCreative(Integer creativeId, String cpwTop2KeyPrice) {
        incrRedis(buildRedisAdChargeForCreative(creativeId), Long.parseLong(cpwTop2KeyPrice), RedisConstants.TIME_DAY_2);
    }

    public void adChargeForAdvertiser(Integer advertiserId, String cpwTop2KeyPrice) {
        incrRedis(buildRedisAdChargeForAdvertiser(advertiserId, DateUtils.formatDateForyyyyMMdd(new Date())), Long.parseLong(cpwTop2KeyPrice), RedisConstants.TIME_DAY_2);
    }

    public String getRedisAdChargeForAdvertiser(Integer advertiserId, String day) {
        return getFromSlot(buildRedisAdChargeForAdvertiser(advertiserId, day));
    }

    public void incrRedis(String key, Long value, int expire) {
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            Pipeline pipeline = jedis.pipelined();
            // redis中累加，
            pipeline.incrBy(key, value);
            // 设置超时时间，2天。
            pipeline.expire(key, expire);
            pipeline.sync();
        } catch (JedisMovedDataException jmde) {
            apClusterRedisService.get("EmptyKey");
        }
    }

    public String getFromSlot(String key) {
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            return jedis.get(key);
        } catch (JedisMovedDataException jmde) {
            apClusterRedisService.get("EmptyKey");
        }
        return null;
    }

    public String buildRedisUserAdClickIntervalTimeStampKey(long adId, long userId, int appId) {
        return RedisConstants.USER_AD_CLICK_INTERVAL_TIMESTAMP_KEY_PRE + adId + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + getTag(userId);
    }

    private String buildMarkPriceTopToKey(Long userId, Long adId, Integer appId) {
        return RedisConstants.USER_CPW_PRICE_TOP2_KEY_PRE + buildKey(appId.toString(), getTag(userId), adId.toString());
    }

    public String getUserCpwPriceTop2(long userId, long adId,int appId) {
        return apClusterRedisService.get(buildMarkPriceTopToKey(userId, adId,appId));
    }

    public String buildRedisAdClickHourCounter(long adId) {
        String hourTime = DateUtils.formatDate(new Date(), "yyyyMMddHH");
        return RedisConstants.NAP_AD_CLICK_HOUR_COUNTER_PRE + adId + AdConstants.SYMBOL_COLON + hourTime;
    }

    private String buildKey(String... items) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < items.length; i++) {
            if (i == items.length - 1) {
                sb.append(items[i]);
            } else {
                sb.append(items[i]).append(AdConstants.SYMBOL_COLON);
            }
        }
        return sb.toString();
    }

    public Long incrUserDayClickLimit(long adId, Long userId, int appId) {
        String userDayClickLimitKey = buildRedisUserDayClickLimitKey(adId, userId, appId);
        Long ret = apClusterRedisService.incrBy(userDayClickLimitKey, 1L);
        apClusterRedisService.expire(userDayClickLimitKey, RedisConstants.TIME_DAY_1); // 按日 设置超时时间
        return ret;
    }

    public String buildRedisUserDayClickLimitKey(long adId, Long userId, int appId) {
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        return RedisConstants.USER_AD_DAY_CLICK_LIMIT_KEY_PRE + adId + AdConstants.SYMBOL_COLON + getTag(userId) + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + today;
    }
//    public void incrRedisAdClickCounter(Long adId) {
//        jedisClient.doExecute(jedis -> {
//            Pipeline pipeline = jedis.pipelined();
//            String redisKey = buildRedisAdClickCounter(adId);
//            // redis中累加，
//            pipeline.incrBy(redisKey, 1L);
//            // 设置超时时间，1天。
//            pipeline.expire(redisKey, RedisConstants.TIME_DAY_1);
//            pipeline.sync();
//        });
//    }

//    public String buildRedisUserDayClickLimitKey(long adId, Long userId, int appId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.USER_AD_DAY_CLICK_LIMIT_KEY_PRE + adId + AdConstants.SYMBOL_COLON + getTag(userId) + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + today;
//    }

//    public Long incrUserDayClickLimit(long adId, Long userId, int appId) {
//        String userDayClickLimitKey = buildRedisUserDayClickLimitKey(adId, userId, appId);
//        Long ret = apClusterRedisService.incrBy(userDayClickLimitKey, 1L);
//        apClusterRedisService.expire(userDayClickLimitKey, RedisConstants.TIME_DAY_1); // 按日 设置超时时间
//        return ret;
//    }

//    public String buildRedisUserShareAdFrequencyKey(long userId, int code, int appId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.USER_SHARE_AD_FREQUENCY_KEY_PRE + getTag(userId) + AdConstants.SYMBOL_COLON
//                + appId + AdConstants.SYMBOL_COLON
//                + code + AdConstants.SYMBOL_COLON
//                + today;
//    }

//    public void incrUserShareAdFrequency(long userId, int code, int appId) {
//        String userCpwIndustryKey = buildRedisUserShareAdFrequencyKey(userId, code, appId);
//        apClusterRedisService.incrBy(userCpwIndustryKey, 1);
//        apClusterRedisService.expire(userCpwIndustryKey, RedisConstants.TIME_DAY_1);
//    }
//
//    public void setCpaDoingTask(long adId, long userId, int appId) {
//        Map<String, String> map = new HashMap<>();
//        map.put("adId", adId + "");
//        map.put("timestamp", System.currentTimeMillis() + "");
//        apClusterRedisService.setex(buildCpaDoingTaskKey(userId, appId), (int) AdConstants.DAY_SECONDS, JSONObject.toJSONString(map));
//    }

//    public String buildCpaDoingTaskKey(long userId, int appId) {
//        return RedisConstants.CPA_DOING_TASK + appId + AdConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    public String getUserAdClickIntervalTimeStampForCpa(long adId, long userId, int appId) {
//        return apClusterRedisService.get(buildRedisUserAdClickIntervalTimeStampKeyForCpa(adId, userId, appId));
//    }

//    private String buildRedisUserAdClickIntervalTimeStampKeyForCpa(long adId, long userId, int appId) {
//        return RedisConstants.CPA_CLICK + appId + AdConstants.SYMBOL_COLON + adId + AdConstants.SYMBOL_COLON + getTag(userId);
//    }

//    public void setUserAdClickIntervalTimeStampForCpa(long adId, Long userId, int appId) {
//        apClusterRedisService.setex(buildRedisUserAdClickIntervalTimeStampKeyForCpa(adId, userId, appId), (int) AdConstants.DAY_SECONDS, String.valueOf(System.currentTimeMillis()));
//    }
//
//    public void setUserAdClickIntervalTimeStampForMiniProgramCPA(String miniProgramWxId, Long userId) {
//        apClusterRedisService.setex(buildRedisUserAdClickIntervalTimeStampKeyForMiniProgramCPA(miniProgramWxId, userId), (int) AdConstants.DAY_SECONDS, String.valueOf(System.currentTimeMillis()));
//    }

//    private String buildRedisUserAdClickIntervalTimeStampKeyForMiniProgramCPA(String miniProgramWxId, Long userId) {
//        return RedisConstants.MINI_PROGRAM_TASK_CLICK + miniProgramWxId + AdConstants.SYMBOL_COLON + getTag(userId);
//    }

//    public String getUserAdClickIntervalTimeStampForMiniProgramCPA(String miniProgramWxId, Long userId) {
//        return apClusterRedisService.get(buildRedisUserAdClickIntervalTimeStampKeyForMiniProgramCPA(miniProgramWxId, userId));
//    }
//
//    public void incrByHash(String key,String field){
//        if(StringUtils.isBlank(key)){
//            return;
//        }
//        apClusterRedisService.hincrBy(key,field,1);
//        apClusterRedisService.expire(key,RedisConstants.TIME_DAY_1);
//    }

//    public String hget(String key,String field){
//        return apClusterRedisService.hget(key,field);
//    }
//
//    public void setex(String key,Integer time, String value){
//        apClusterRedisService.setex(key,time,value);
//    }

    //    public String getUserAdClickIntervalTimeStamp(long adId, long userId, int appId) {
//        return apClusterRedisService.get(buildRedisUserAdClickIntervalTimeStampKey(adId, userId, appId));
//    }
//
//    public void setUserAdClickIntervalTimeStamp(long adId, long userId, int appId, int time) {
//        apClusterRedisService.setex(buildRedisUserAdClickIntervalTimeStampKey(adId, userId, appId), time, String.valueOf(System.currentTimeMillis()));
//    }

//    public String buildAwakeKey(long userId, String appPkgName, int appId) {
//        return RedisConstants.AWAKE_FOR_AWAKE_KEY_PRE + getTag(userId) + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + appPkgName;
//    }

//    public void setAwake(long userId, String appPkgName, int appId, int time) {
//        apClusterRedisService.setex(buildAwakeKey(userId, appPkgName, appId), time, String.valueOf(System.currentTimeMillis()));
//    }

    //    public void adChargeForPlanClick(Integer planId) {
//        String redisKey = buildRedisAdChargeForClick(planId);
//        jedisClient.incrBy(redisKey, 1);
//        jedisClient.expire(redisKey, RedisConstants.EXPIRED_SECONDS_FOR_USER_PKGS);
//    }

//    public void adChargeForPlanExposure(Integer planId) {
//        String redisKey = buildRedisAdChargeForExposure(planId);
//        jedisClient.incrBy(redisKey, 1);
//        jedisClient.expire(redisKey, RedisConstants.EXPIRED_SECONDS_FOR_USER_PKGS);
//    }
//
//    public String buildMiniProgramRemainClickKey(long userId, long adId, int appId) {
//        return RedisConstants.MINI_PROGRAM_REMAIN_CLICK_KEY + getTag(userId) + AdConstants.SYMBOL_COLON + appId + AdConstants.SYMBOL_COLON + adId;
//    }

//    public void setMiniProgramRemainClick(long userId, long adId, int appId, String value) {
//        apClusterRedisService.setex(buildMiniProgramRemainClickKey(userId, adId, appId), RedisConstants.TIME_DAY_1, value);
//    }
//
//    public String buildMiniProgramClickLimit(long userId, String miniProgramId) {
//        return RedisConstants.MINI_PROGRAM_CLICK_KEY + getTag(userId) + AdConstants.SYMBOL_COLON + miniProgramId;
//    }
//
//    public String getMiniProgramClickLimit(String key) {
//        return apClusterRedisService.get(key);
//    }

//    public String buildMiniProgramIdPre(String wxId, long userId) {
//        return RedisConstants.MINI_PROGRAM_ID_KEY + getTag(userId) + AdConstants.SYMBOL_COLON + wxId;
//    }

//    public void setMiniProgramId(String miniProgramId, long userId) {
//        apClusterRedisService.setex(buildMiniProgramIdPre(miniProgramId, userId), RedisConstants.TIME_DAY_30, String.valueOf(System.currentTimeMillis()));
//    }

//    public void incrMiniProgramClickLimit(String key) {
//        String clickGap = overallConfigManager.getConfig().getMiniprogramCpwClickGap();
//        napClusterRedisService.incrBy(key, 1L);
//        napClusterRedisService.expire(key, Integer.parseInt(clickGap) * 3600);
//    }

    //    public String buildRedisAdChargeForClick(Integer adPlanId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_AD_CLICK_CHARGE_PRE + adPlanId + AdConstants.SYMBOL_COLON + today;
//    }
//
//    public String buildRedisAdChargeForExposure(Integer adPlanId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_AD_EXPOSURE_CHARGE_PRE + adPlanId + AdConstants.SYMBOL_COLON + today;
//    }

//    public String buildRedisAdClickCounter(long adId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.NAP_AD_CLICK_COUNTER_PRE + adId + AdConstants.SYMBOL_COLON + today;
//    }

    //    public Map<String, Response<String>> jedisClusterPipeline(long userId, List<String> keys) {
//        if (!keys.isEmpty()) {
//            try (Jedis jedis = apClusterRedisService.getResource(getTag(userId))) {
//                Pipeline pipeline = jedis.pipelined();
//                Map<String, Response<String>> ret = new HashMap<>();
//                for (String key : keys) {
//                    ret.put(key, pipeline.get(key));
//                }
//                pipeline.sync();
//                return ret;
//            } catch (JedisMovedDataException jmde) {
//                if (!CollectionUtils.isEmpty(keys)) {
//                    apClusterRedisService.get(keys.get(0));
//                } else {
//                    apClusterRedisService.get("EmptyKey");
//                }
//            }
//        }
//        return null;
//    }

//    public Map<String, Response<String>> redisPipeline(List<String> keys) {
//        Map<String, Response<String>> ret = new HashMap<>();
//        jedisClient.doExecute(jedis -> {
//            Pipeline pipeline = jedis.pipelined();
//            for (String key : keys) {
//                ret.put(key, pipeline.get(key));
//            }
//            pipeline.sync();
//        });
//        return ret;
//    }

    //    public Jedis getResource(long userId) {
//        return apClusterRedisService.getResource(getTag(userId));
//    }
}
