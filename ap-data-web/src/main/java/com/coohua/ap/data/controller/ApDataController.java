package com.coohua.ap.data.controller;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.base.vo.WebMessage;
import com.coohua.ap.data.constants.AdActionConstants;
import com.coohua.ap.data.domain.ApDataDomain;
import com.coohua.ap.data.service.ApDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ap-data/ap/data")
@Slf4j
public class ApDataController extends BaseController {

    @Autowired
    private ApDataService apDataService;

    /**
     * 数据打点统一处理
     *
     * @param data  广告打点数据
     * @param appId 产品标识
     */
    @PostMapping("/process")
    public WebMessage<String> process(Long userId,
                                      @RequestParam("data") String data,
                                      @RequestParam("appId") int appId) {

        /**
         * 处理数据混淆(后续可撤销)
         * 混淆之后的数据
         * {
         * 	"a": "android",
         * 	"b": "1.0.5",
         * 	"c": 0,
         * 	"d": 0,
         * 	"e": 1026543,
         * 	"f": "exposure",
         * 	"g": 0,
         * 	"h": 0,
         * 	"i": 0,
         * 	"j": "水滴不足视频",
         * 	"k": 0,
         * 	"l": 0,
         * 	"m": 1594460152165
         * }
         */
        ApDataDomain adDataDomain = new ApDataDomain();

        JSONObject jsonObject = JSONObject.parseObject(data);
        if(jsonObject.containsKey("e")&&jsonObject.containsKey("f")){
           adDataDomain.setAdId(jsonObject.getLong("e"));
           adDataDomain.setAdAction(jsonObject.getString("f"));

        }else {
            adDataDomain = JSONObject.parseObject(data, ApDataDomain.class);
        }
        if (adDataDomain.getAdId() == null) {
            throw new BusinessException(400, "adId can not be null.appId = "+appId+" , data = "+data);
        }
        log.info("ad action report, userId = {} , appId = {} , data = {} ", userId,appId,data);
        // 处理点击和曝光事件
        if (!AdActionConstants.CLICK.code().equalsIgnoreCase(adDataDomain.getAdAction())&&!AdActionConstants.EXPOSURE.code().equalsIgnoreCase(adDataDomain.getAdAction())) {
            return WebMessage.DEFAULT;
        }

        // 填充服务端字段
        adDataDomain.setUserId(userId);
        adDataDomain.setAppId(appId);
        adDataDomain.setTimestampServer(System.currentTimeMillis());

        apDataService.dataProcess(adDataDomain);
        return WebMessage.DEFAULT;
    }

}
