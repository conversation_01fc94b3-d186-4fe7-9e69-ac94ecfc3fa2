package com.coohua.ap.data.interceptor;

import com.aliyun.oss.HttpMethod;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.data.utils.ServletUtils;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.bp.user.remote.api.UserRPC;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.dispatcher.web.interceptor
 * @create_time 2019-10-30
 */
@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @MotanReferer(basicReferer = "bp-userBasicRefererConfigBean", application = "ap-data")
    private UserRPC userRPC;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {

//        String uri = request.getRequestURI();
//        if (uri.contains("test")) {
//            return true;
//        }
//
        try {

            //处理options
            if(HttpMethod.OPTIONS.toString().equalsIgnoreCase(request.getMethod())){
                response.setStatus(HttpStatus.NO_CONTENT.value());
                return false;
            }

            String accessKey = request.getHeader("accessKey");
            String appId = request.getParameter("appId");

            if (StringUtils.isEmpty(appId) || AppBuilder.getById(Integer.parseInt(appId)) == null || Integer.parseInt(appId) == 0) {
                throw new BusinessException(403, "Check appId fail, appId is empty or invalid. [appId] is " + appId);
            }

            // 用户有效性校验
            Boolean authResult = userRPC.checkAuth(accessKey, Long.parseLong(appId));
            if (authResult) {
                Long userId = Long.parseLong(accessKey.split("_")[1]);
                request.setAttribute(AdConstants.USER_ID, userId);
                return true;
            } else {
                log.info("User accessKey is invalid. appId={}, accessKey={}", appId, accessKey);
            }
            return false;
        } catch (Exception e) {
            log.error("Controller Exception! path: {} \n headers: {} \n param: {} \n message: {}",
                    request.getRequestURI(),
                    ServletUtils.getHeaderInfo(request),
                    ServletUtils.getParameters(request),
                    e.getMessage(),
                    e);
        }
        return false;
    }
}
