package com.coohua.ap.data.service.impl;

import com.coohua.ap.data.constants.AdActionConstants;
import com.coohua.ap.data.domain.ApDataDomain;
import com.coohua.ap.data.service.ApDataService;
import com.coohua.ap.support.core.components.DefaultAd;
import com.coohua.ap.support.core.container.AdMetaInfoFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ApDataServiceImpl implements ApDataService {

    @Override
    public void dataProcess(ApDataDomain adDataDomain) {
        DefaultAd defaultAd = AdMetaInfoFactory.getAd(adDataDomain.getAdId());
        if (defaultAd == null) {
            if (adDataDomain.getAdId() == -1) {
                log.error("客户端传入的广告ID为空，adDataDomain={}", adDataDomain);
            } else {
                log.error("无法获取广告，adId={}, adDataDomain={}", adDataDomain.getAdId(), adDataDomain);
            }
            return;
        }
        // 补充广告类型字段
        adDataDomain.setAdType(defaultAd.getType());
        if (adDataDomain.getAdAction().equalsIgnoreCase(AdActionConstants.CLICK.code())){
            defaultAd.onClick(adDataDomain);
        } else if(adDataDomain.getAdAction().equalsIgnoreCase(AdActionConstants.EXPOSURE.code())){
            defaultAd.onExposure(adDataDomain);
        }
    }

}
