package com.coohua.ap.data.config;

import com.coohua.ap.data.controller.UserIdMethodArgumentResolver;
import com.coohua.ap.data.interceptor.AuthInterceptor;
import com.coohua.ap.data.interceptor.CORSInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class MvcConfig {

    @Autowired
    private AuthInterceptor authInterceptor;

    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            // 拦截器配置
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                // 跨域配置
                registry.addInterceptor(new CORSInterceptor()).addPathPatterns(
                        "/**/ap-data/ap/data/process"
                );
                registry.addInterceptor(authInterceptor).addPathPatterns("/**");

            }

            // 参数解析配置
            @Override
            public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
                resolvers.add(new UserIdMethodArgumentResolver());
            }
        };
    }
}
