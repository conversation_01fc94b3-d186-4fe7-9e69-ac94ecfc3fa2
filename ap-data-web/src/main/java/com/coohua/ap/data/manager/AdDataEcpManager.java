package com.coohua.ap.data.manager;

import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.utils.DateUtils;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.exceptions.JedisMovedDataException;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020/11/10
 */
@Service
public class AdDataEcpManager {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    private static final Integer DAY1_INDEX = 1;
    private static final Integer DAY3_INDEX = 3;
    private static final Integer ZERO = 0;

    private static int getLeftSecondFromNow(Integer index){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, index);
        cal.set(Calendar.HOUR_OF_DAY, ZERO);
        cal.set(Calendar.SECOND, ZERO);
        cal.set(Calendar.MINUTE, ZERO);
        cal.set(Calendar.MILLISECOND, ZERO);
        return Math.toIntExact((cal.getTimeInMillis() - System.currentTimeMillis()) / 1000);
    }

    public static void main(String[] args) {
        System.out.println(getLeftSecondFromNow(DAY1_INDEX));
    }

    public void userClickAd(Long userId,Long adId){
        String key = RedisConstants.ECP_USER_AD_CLICK + buildKey(userId.toString(),adId.toString());
        this.incrRedisAndExpire(key,getLeftSecondFromNow(DAY1_INDEX));
    }

    public void adHourClick(Long adId){
        String now = DateUtils.formatDateForYMDH(new Date());
        String key = RedisConstants.ECP_USER_AD_CLICK_HOUR + buildKey(adId.toString(),now);
        this.incrRedisAndExpire(key,getLeftSecondFromNow(DAY1_INDEX));
    }

    public void adHourExposure(Long adId){
        String now = DateUtils.formatDateForYMDH(new Date());
        String key = RedisConstants.ECP_USER_AD_EXPOSURE_HOUR + buildKey(adId.toString(),now);
        this.incrRedisAndExpire(key,getLeftSecondFromNow(DAY1_INDEX));
    }

    public void userClickVideo(Long userId,Integer videoId){
        String key = RedisConstants.ECP_USER_AD_VIDEO_CLICK + buildKey(userId.toString(),videoId.toString());
        this.incrRedisAndExpire(key,getLeftSecondFromNow(DAY3_INDEX));
    }

    public void userProductVideoAd(Integer requestAppId,Long userId){
        String key = RedisConstants.ECP_USER_READ_PRODUCT_VIDEO_TIME + buildKey(requestAppId.toString(),userId.toString());
        this.incrRedisAndExpire(key,getLeftSecondFromNow(DAY1_INDEX));
    }

    public void userViewVideo(Long userId){
        String key = RedisConstants.ECP_USER_READ_VIDEO_TIME + buildKey(userId.toString());
        this.incrRedisAndExpire(key,getLeftSecondFromNow(DAY1_INDEX));
    }

    public void advertiserAmountChange(Integer advertiserId,Long amountChange){
        String now = DateUtils.formatDateForYMD(new Date());
        String key = RedisConstants.ECP_USER_ADVERTISER_DAILY + buildKey(advertiserId.toString(),now);
        // 账户日消耗保留三日
        this.incrRedisAndExpire(key,amountChange,getLeftSecondFromNow(DAY3_INDEX));
    }

    public void planAmountChange(Long planId,Long amountChange){
        String key = RedisConstants.ECP_USER_PLAN + buildKey(planId.toString());
        this.incrRedis(key,amountChange);
    }

    private String buildKey(String... items) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < items.length; i++) {
            if (i == items.length - 1) {
                sb.append(items[i]);
            } else {
                sb.append(items[i]).append(AdConstants.SYMBOL_COLON);
            }
        }
        return sb.toString();
    }

    public String getFromSlot(String key) {
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            return jedis.get(key);
        } catch (JedisMovedDataException jmde) {
            apClusterRedisService.get("EmptyKey");
        }
        return null;
    }

    private void incrRedis(String key, Long value){
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            Pipeline pipeline = jedis.pipelined();
            // redis中累加，
            pipeline.incrBy(key, value);
            pipeline.sync();
        } catch (JedisMovedDataException jmde) {
            apClusterRedisService.get("EmptyKey");
        }
    }

    private void incrRedisAndExpire(String key, int expire) {
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            Pipeline pipeline = jedis.pipelined();
            // redis中累加
            pipeline.incr(key);
            // 设置超时时间
            pipeline.expire(key, expire);
            pipeline.sync();
        } catch (JedisMovedDataException jmde) {
            apClusterRedisService.get("EmptyKey");
        }
    }

    private void incrRedisAndExpire(String key,Long value, int expire) {
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            Pipeline pipeline = jedis.pipelined();
            // redis中累加
            pipeline.incrBy(key,value);
            // 设置超时时间
            pipeline.expire(key, expire);
            pipeline.sync();
        } catch (JedisMovedDataException jmde) {
            apClusterRedisService.get("EmptyKey");
        }
    }
}
