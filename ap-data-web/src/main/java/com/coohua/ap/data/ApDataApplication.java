package com.coohua.ap.data;

import com.coohua.ap.data.manager.AdCommonManager;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import top.zrbcool.pepper.boot.jedis.EnableJedisClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import top.zrbcool.pepper.boot.motan.EnableMotan;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.data
 * @create_time 2019-10-29
 */
@SpringBootApplication(scanBasePackages = "com.coohua.ap")
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
        "bp.user.rpc.referer",
        // redis, redis-cluster配置
        "ap.redis.cluster"})  // ,"ad.nap.datasource" "ap.redis.single",
@EnableAutoChangeApolloConfig

//@EnableJedisClient(namespace = "ap-redis-single")
@EnableJedisClusterClient(namespace = "ap-cluster")

@EnableMotan(namespace = "bp-user")

//@EnableDataSource(namespace = "napdb", mapperPackages = "com.coohua.ap.data.mapper")

@Slf4j
public class ApDataApplication {
    public static void main(String[] args) {
        long start = System.currentTimeMillis();
        ApplicationContext application = SpringApplication.run(ApDataApplication.class, args);
        AdCommonManager.setApplicationContext(application);
        log.info("Ap data application started in {} ms", (System.currentTimeMillis() - start));
        System.out.println("Ap data application started ... ");
    }
}
