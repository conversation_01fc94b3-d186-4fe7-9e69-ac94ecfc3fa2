package com.coohua.ap.data.listener;

import com.coohua.ap.base.constants.*;
import com.coohua.ap.base.extension.SpiMeta;
import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.data.domain.ApDataDomain;
import com.coohua.ap.data.manager.AdCommonManager;
import com.coohua.ap.data.manager.AdDataEcpManager;
import com.coohua.ap.data.manager.AdDataManager;
import com.coohua.ap.data.manager.AdLogTrackManager;
import com.coohua.ap.support.core.components.CustomAd;
import com.coohua.ap.support.core.components.DefaultAd;
import com.coohua.ap.support.core.components.NewCustomAd;
import com.coohua.ap.support.core.container.AdMetaInfoFactory;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.container.event.AdEvent;
import com.coohua.ap.support.core.container.event.AdEventType;
import com.coohua.ap.support.core.container.listener.AdListener;
import com.coohua.ap.support.core.domain.App;
import com.pepper.metrics.core.AlertProfiler;
import com.pepper.metrics.core.AlertStats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * <pre>
 *  广告行为处理
 *  目前只处理点击事件，后面可根据具体需要进行扩展
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/26
 */
@Slf4j
@SpiMeta(name = "AdActionHandleListener")
@Component
public class AdActionHandleListener implements AdListener {

    private AlertStats alertStats;

    public AdActionHandleListener() {
        alertStats = AlertProfiler.Builder.builder().name("AdCustomerEvent").rule("rule_001").create();
    }

    /**
     * 直客广告打点上报
     * @param ad 直客广告vo
     * @param domain 广告Domain
     * @param adEvent 广告Event
     */
    private void point(CustomAd ad,ApDataDomain domain,AdEvent adEvent){
        CompletableFuture.runAsync(() ->{
            try {
                AdEventType adEventType = adEvent.getType();
                App app = AppBuilder.getById(domain.getAppId());
                String appName = app == null ? "未知" : app.desc();
                alertStats.incrementAndGet("BIZ_APP", appName, "AD_ACTION",adEventType.getDesc(), "AD_TYPE", AdTypeSub.getTypeDescCr(ad.getType()));
            }catch (Exception e){
                log.error("处理事件上报异常..:",e);
            }
        });
    }

    @Override
    public void onAdEvent(AdEvent event) {
        if (event.getType() == AdEventType.ON_CLICK || event.getType() == AdEventType.ON_EXPOSURE) {
            handleClickAdAction(event);
        }
    }

    private void handleClickAdAction(AdEvent event) {
        if (event.getData() instanceof ApDataDomain) {
            ApDataDomain adDataDomain = (ApDataDomain) event.getData();
            DefaultAd ad = AdMetaInfoFactory.getAd(adDataDomain.getAdId());
            if(ad instanceof CustomAd){
                CustomAd customAd = (CustomAd)ad;
                point(customAd,adDataDomain,event);
                if (event.getType() == AdEventType.ON_CLICK){
                    handleEcpAdClick(customAd,adDataDomain);
                }else if (event.getType() == AdEventType.ON_EXPOSURE){
                    handleEcpAdExposure(customAd,adDataDomain);
                }
            }
            if (ad instanceof NewCustomAd){
                NewCustomAd newCustomAd = (NewCustomAd) ad;
                if (event.getType() == AdEventType.ON_CLICK){
                    clickNewAd(newCustomAd,adDataDomain);
                }else if (event.getType() == AdEventType.ON_EXPOSURE){
                    exposureNewAd(newCustomAd,adDataDomain);
                }
            }
        }
    }

    private void clickNewAd(NewCustomAd newCustomAd,ApDataDomain apDataDomain){
        if (newCustomAd == null){
            return;
        }

        AdDataEcpManager adDataEcpManager = AdCommonManager.getApplicationContext().getBean(AdDataEcpManager.class);

        // 用户点击广告上报
        adDataEcpManager.userClickAd(apDataDomain.getUserId(),newCustomAd.getAdId());

        // 广告小时点击上报
        adDataEcpManager.adHourClick(newCustomAd.getAdId());

    }

    private void exposureNewAd(NewCustomAd newCustomAd,ApDataDomain apDataDomain){
        if (newCustomAd == null){
            return;
        }

        AdDataEcpManager adDataEcpManager = AdCommonManager.getApplicationContext().getBean(AdDataEcpManager.class);

        if (AdType.NEW_ZHIKE_VIDEO.type == newCustomAd.getType()) {
            // 广告主消耗维护
            adDataEcpManager.advertiserAmountChange(newCustomAd.getAdvertiserId(),newCustomAd.getUnitPrice().longValue());
            // 计划消耗维护
            adDataEcpManager.planAmountChange(newCustomAd.getPlanId(),newCustomAd.getUnitPrice().longValue());

            // 用户点击视频上报
            adDataEcpManager.userClickVideo(apDataDomain.getUserId(), newCustomAd.getVideoId());

            // 用户点击视频单日上报
            adDataEcpManager.userProductVideoAd(newCustomAd.getAppId(), apDataDomain.getUserId());

            // 用户阅读视频
            adDataEcpManager.userViewVideo(apDataDomain.getUserId());
        }

        // 曝光上报
        adDataEcpManager.adHourExposure(newCustomAd.getAdId());

    }

    private void handleEcpAdClick(CustomAd customAd,ApDataDomain apDataDomain){
        if(customAd==null){
            return;
        }

        AdDataManager adDataManager = AdCommonManager.getApplicationContext().getBean(AdDataManager.class);
        AdLogTrackManager adLogTrackManager = AdCommonManager.getApplicationContext().getBean(AdLogTrackManager.class);

        // 判断广告当日是否可投放，并且是开启的。如果不符合条件，不打点
        if (!customAd.isPutDatePeriodValid()) {
            return;
        }

        // 判断ECP广告日消耗是否超过广告主余额
        if (!checkAdvertiserBalance(customAd)) {
            log.info("广告主余额不足，不计费。userId={}, advertiserId={}, adId={}", apDataDomain.getUserId(), customAd.getAdvertiserId(), customAd.getAdId());
            return;
        }

        String cpwTop2KeyPrice = adDataManager.getUserCpwPriceTop2(apDataDomain.getUserId(), apDataDomain.getAdId(),apDataDomain.getAppId());

        //阅读60秒单价计费
        if(customAd.getType()==AdType.ZHIKE_READ60.type){
            cpwTop2KeyPrice = customAd.getUnitPrice().toString();
        }

        if (StringUtils.isBlank(cpwTop2KeyPrice)) {
            cpwTop2KeyPrice = "0";
        }

        if (customAd.getPlanId() != null) {
            adDataManager.adChargeForPlan(customAd.getPlanId().intValue(), cpwTop2KeyPrice);
        }

        if (customAd.getGroupId() != null) {
            adDataManager.adChargeForGroup(customAd.getGroupId().intValue(), cpwTop2KeyPrice);
        }

        if (customAd.getAdId() != null) {
            adDataManager.adChargeForCreative(customAd.getAdId().intValue(), cpwTop2KeyPrice);
        }

        if (customAd.getAdvertiserId() != null) {
            adDataManager.adChargeForAdvertiser(customAd.getAdvertiserId(), cpwTop2KeyPrice);
        }

        // 点击控量计费
        if (customAd.getType()==AdType.ZHIKE_READ60.type){
            read60Handler(adDataManager, apDataDomain);
        }else if(customAd.getType()==AdType.ENCOURAGE_CLICK.type){
            // 记录用户点击某广告的次数，用于单用户的广告控量。不限点击次数的广告，不计数
            if (customAd.isDayClickLimit()) {
                Long count = adDataManager.incrUserDayClickLimit(customAd.getAdId(), apDataDomain.getUserId(), apDataDomain.getAppId());
            }
        }

        adLogTrackManager.ecpAdLogTrack(apDataDomain.getUserId(),apDataDomain.getOs(),apDataDomain.getAdId(),apDataDomain.getAdAction(),cpwTop2KeyPrice,apDataDomain.getAppId());
    }

    private void read60Handler(AdDataManager adDataManager, ApDataDomain apDataDomain) {
        // 小时控量
        String key = adDataManager.buildRedisAdClickHourCounter(apDataDomain.getAdId());
        adDataManager.incrRedis(key, 1L, RedisConstants.TIME_MINS_60);
        // 点击间隔控频
        String time = adDataManager.get(adDataManager.buildRedisUserAdClickIntervalTimeStampKey(apDataDomain.getAdId(),apDataDomain.getUserId(),apDataDomain.getAppId()));
        if (time != null) {
            long now = new Date().getTime();
            if (now - Long.parseLong(time) < 1000 * 60 * 5) {
                return;
            }
        }
    }



    private void handleEcpAdExposure(CustomAd customAd,ApDataDomain apDataDomain){

        if(customAd==null){
            return;
        }

        AdLogTrackManager adLogTrackManager = AdCommonManager.getApplicationContext().getBean(AdLogTrackManager.class);

        // 判断广告当日是否可投放，并且是开启的。如果不符合条件，不打点
        if (!customAd.isPutDatePeriodValid()) {
            return;
        }

        // 判断ECP广告日消耗是否超过广告主余额
        if (!checkAdvertiserBalance(customAd)) {
            log.info("广告主余额不足，不计费。userId={}, advertiserId={}, adId={}", apDataDomain.getUserId(), customAd.getAdvertiserId(), customAd.getAdId());
            return;
        }


        adLogTrackManager.ecpAdLogTrack(apDataDomain.getUserId(),apDataDomain.getOs(),apDataDomain.getAdId(),apDataDomain.getAdAction(),"0",apDataDomain.getAppId());

    }


    private boolean checkAdvertiserBalance(CustomAd customAd) {
        // 获取redis中广告主日消耗
        AdDataManager adDataManager = AdCommonManager.getApplicationContext().getBean(AdDataManager.class);
        String today = DateUtils.formatDateForyyyyMMdd(new Date());
        String yesterday = DateUtils.formatDateForyyyyMMdd(new Date(System.currentTimeMillis() - 1000 * 60 * 60 * 24));
        String cost = adDataManager.getRedisAdChargeForAdvertiser(customAd.getAdvertiserId(), today);
        String yesterdayCost = adDataManager.getRedisAdChargeForAdvertiser(customAd.getAdvertiserId(), yesterday);
        long dayCost = StringUtils.isEmpty(cost) ? 0L : Long.parseLong(cost);
        long yesterdayCost1 = StringUtils.isEmpty(yesterdayCost) ? 0L : Long.parseLong(yesterdayCost);

        long zeroTimestamp = DateUtils.parseDate(DateUtils.formatDateForYMD(new Date())).getTime();
        if (customAd.getBalanceAccountTime() != null && customAd.getBalanceAccountTime().getTime() > zeroTimestamp) {
            return dayCost + customAd.getUnitPrice() < customAd.getAdvertiserBalance();
        } else {
            return dayCost + yesterdayCost1 + customAd.getUnitPrice() < customAd.getAdvertiserBalance();
        }
    }

}
