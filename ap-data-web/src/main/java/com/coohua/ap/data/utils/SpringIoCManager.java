package com.coohua.ap.data.utils;

import com.coohua.ap.data.manager.AdDataManager;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/16
 */
@Component
public class SpringIoCManager implements ApplicationContextAware {
    private static ApplicationContext cpx = null;

    // 获取已经创建的spring容器
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringIoCManager.cpx = applicationContext;
    }

    private static <T>T getBean(Class<T> beanType) {
        return cpx.getBean(beanType);
    }

    public static AdDataManager getAdDataManager() {
        return getBean(AdDataManager.class);
    }
}
