<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>bp-ap</artifactId>
        <groupId>com.coohua</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ap-data-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.coohua.caf</groupId>
            <artifactId>caf-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <dependency>
            <groupId>com.coohua</groupId>
            <artifactId>ap-base</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.coohua</groupId>
            <artifactId>bp-user-api</artifactId>
            <version>1.3.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.coohua.game</groupId>
            <artifactId>bp-account-api</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.coohua.platform</groupId>
            <artifactId>tools</artifactId>
            <version>0.0.14</version>
        </dependency>

        <!-- 数据平台接入 -->
        <dependency>
            <groupId>com.coohua.analytics.javasdk</groupId>
            <artifactId>CoohuaAnalyticsSDK</artifactId>
            <version>1.0.8</version>
        </dependency>

        <dependency>
            <groupId>com.coohua</groupId>
            <artifactId>ap-support-ad-spring</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.codehaus.mojo</groupId>
                                        <artifactId>flatten-maven-plugin</artifactId>
                                        <versionRange>[1.0.0,)</versionRange>
                                        <goals>
                                            <goal>flatten</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                        <mainClass>com.coohua.ap.data.ApDataApplication</mainClass>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <mainClass>com.coohua.ap.data.ApDataApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>