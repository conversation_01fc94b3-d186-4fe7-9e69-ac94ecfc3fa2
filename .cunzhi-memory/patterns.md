# 常用模式和最佳实践

- ap-support项目广告系统架构优化方案：

核心问题：gateway服务启动慢，需要加载所有support模块数据到内存

服务拆分方案：
1. ap-config-service：app容器+全局配置容器（配置相关数据）
2. ap-filter-service：融合third容器的瀑布流广告服务  
3. ap-bidding-service：独立bidding服务（复杂匹配逻辑）
4. ap-strategy-service：策略配置服务（AB测试相关）
5. ap-gateway：轻量化业务编排层

新getConfig流程：构建用户画像(AB等) → 获取策略配置 → 并行调用filter/bidding/config服务 → 业务处理和响应组装

存储优化：各服务独立缓存策略，Strategy按应用+AB缓存，Config分层缓存，Filter缓存广告位信息，Bidding只缓存热点应用

预期收益：启动时间优化85-90%，内存使用减少70-80%，getConfig响应时间优化50-60%

实施路径：Strategy → Config → Filter → Bidding，渐进式拆分，每阶段2-3周
