package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.aop.OperateLog;
import com.coohua.ap.admin.aop.OperateType;
import com.coohua.ap.admin.constants.OperateLogType;
import com.coohua.ap.admin.controller.vo.AdPosView;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.AdPosMapper;
import com.coohua.ap.admin.model.AdPosModel;
import com.coohua.ap.admin.service.AdPosService;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.service.impl
 * @create_time 2019-11-04
 */
@Service
public class AdPosServiceImpl implements AdPosService {

    @Autowired
    private AdPosMapper adPosMapper;

    @Override
    public void pageListByProduct(String name, String id, int product, Page<AdPosView> page) {
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<AdPosModel> retList = adPosMapper.queryByProduct(product, from, page.getPageSize(), name, id);
        long count = adPosMapper.queryCountByProduct(product, name, id);

        List<AdPosView> ret = new ArrayList<>();
        for (AdPosModel model : retList) {
            ret.add(convertModel2VO(model));
        }

        page.setCount((int) count);
        page.setItems(ret);
    }

    @Override
    public List<AdPosView> queryByProduct(int product) {
        List<AdPosModel> models = adPosMapper.queryAllByProduct(product);

        List<AdPosView> ret = new ArrayList<>();
        for (AdPosModel model : models) {
            ret.add(convertModel2VO(model));
        }

        return ret;
    }

    @Override
    @OperateLog(type = OperateType.INSERT,logType = OperateLogType.AD_POS_CONFIG)
    public AdPosModel insertAdPos(AdPosView view) {

        AdPosModel model = convertVO2Model(view);
        adPosMapper.insertOne(model);
        return model;

    }

    @Override
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.AD_POS_CONFIG)
    public AdPosModel updateAdPos(AdPosModel model) {
        adPosMapper.updateById(model);
        return model;
    }

    private AdPosView convertModel2VO(AdPosModel model) {
        FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
        AdPosView view = new AdPosView();
        view.setId(model.getId());
        view.setName(model.getName());
        view.setExtendInfo(model.getExtendInfo());
        view.setCreateTime(dateFormat.format(model.getCreateTime()));
        view.setUpdateTime(dateFormat.format(model.getUpdateTime()));
        return view;
    }

    private AdPosModel convertVO2Model(AdPosView adPosView) {
        AdPosModel model = new AdPosModel();
        model.setAppId(adPosView.getProduct());
        model.setName(adPosView.getName());
        model.setExtendInfo(adPosView.getExtendInfo());
        return model;
    }


}
