package com.coohua.ap.admin.constants;

/**
 * 通用枚举
 */

public enum CommonStatusEnum {
    DISABLED(0, "未启用"),
    ENABLED(1, "启用");

    private final int code;
    private final String description;

    CommonStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static CommonStatusEnum fromCode(int code) {
        for (CommonStatusEnum status : CommonStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }
}

