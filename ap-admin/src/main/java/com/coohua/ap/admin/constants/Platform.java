package com.coohua.ap.admin.constants;

/**
 * <AUTHOR>
 * @since 2021/8/13
 */
public enum Platform {
    CSJ(1,"穿山甲"),
    GDT(2,"广点通"),
    KS(3,"快手"),
    BD(4,"百度"),
    OPPO(5,"OPPO"),
    ;

    private Integer code;
    private String desc;

    Platform(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static Platform getPlatform(Integer code){
        for(Platform platform: Platform.values()){
            if (platform.code.equals(code)){
                return platform;
            }
        }
        throw new RuntimeException("不支持的平台");
    }

    public static Platform getPlatform(String code){
        for(Platform platform: Platform.values()){
            if (platform.desc.equals(code)){
                return platform;
            }
        }
        throw new RuntimeException("不支持的平台");
    }
}
