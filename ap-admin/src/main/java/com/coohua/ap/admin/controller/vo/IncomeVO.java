package com.coohua.ap.admin.controller.vo;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.web.vo
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/15 上午11:36
 * </pre>
 */
public class IncomeVO {

    // 是否限制，0-不限制，1-限制
    private Integer limit;

    // 限制起始点
    private String start;

    // 限制终结点
    private String end;

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    @Override
    public String toString() {
        return "IncomeVO{" +
                "limit=" + limit +
                ", start=" + start +
                ", end=" + end +
                '}';
    }
}
