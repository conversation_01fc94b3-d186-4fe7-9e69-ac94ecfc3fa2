package com.coohua.ap.admin.constants;

/**
 * 广告位替换分层类型
 */
public enum TbApReplaceAdEnum {
    NEW_LAYER(1,"新增加层"),
    ORIGIN_LAYERS(2,"替换原分层"),
    BIDDING_LAYERS(3,"新增Bidding")
    ;
    private Integer code;
    private String desc;

    TbApReplaceAdEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TbApReplaceAdEnum getByCode(Integer code){
        for (TbApReplaceAdEnum type : TbApReplaceAdEnum.values()){
            if (type.code.equals(code)){
                return type;
            }
        }
        return TbApReplaceAdEnum.NEW_LAYER;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
