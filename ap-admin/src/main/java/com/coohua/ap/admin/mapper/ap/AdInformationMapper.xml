<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.AdInformationMapper">

    <resultMap type="com.coohua.ap.admin.model.AdInformationEntity" id="adInformationResult">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="put_date_period" property="putDatePeriod"/>
        <result column="time_bucket" property="timeBucket"/>
        <result column="state" property="state"/>
        <result column="ad_pos" property="adPos"/>
        <result column="product" property="product"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="state_update_time" property="stateUpdateTime" />
        <result column="ecp_ad_id" property="ecpAdId"/>
        <result column="region" property="region"/>
        <result column="sex" property="sex"/>
        <result column="platform_version" property="platformVersion"/>
        <result column="app" property="app"/>
        <result column="tail_number" property="tailNumber" />
        <result column="position" property="position" />
        <result column="income" property="income" />
        <result column="register_time" property="registerTime" />
        <result column="contains_pkg" property="containsPkg"/>
        <result column="not_contains_pkg" property="notContainsPkg"/>
        <result column="budget" property="budget"/>
        <result column="click_interval" property="clickInterval"/>
        <result column="day_click_limit" property="dayClickLimit"/>
        <result column="ext" property="ext"/>
    </resultMap>

    <select id="getAll" resultMap="adInformationResult">
		<![CDATA[
			SELECT a.id,
			       a.name,
			       a.type,
			       a.put_date_period,
			       a.time_bucket,
			       a.state,
			       a.ad_pos,
			       a.product,
			       a.create_time,
			       a.update_time,
			       a.state_update_time,
			       a.ecp_ad_id,
			       b.region,
			       b.sex,
			       b.platform_version,
			       b.app,
			       b.tail_number,
			       b.`position`,
			       b.`income`,
			       b.`register_time`,
			       b.`contains_pkg`,
			       b.`not_contains_pkg`,
			       c.budget,
			       c.click_interval,
			       c.day_click_limit,
			       d.ext
			 FROM tb_ap_ad_information a LEFT JOIN tb_ap_ad_orientation b ON a.id = b.id
		          LEFT JOIN tb_ap_ad_budget c ON b.id = c.id
		          LEFT JOIN tb_ap_ad_ext d on c.id = d.id
		    WHERE a.state = 1
        ]]>
	</select>

    <select id="getById" resultMap="adInformationResult">
		<![CDATA[
			SELECT a.id,
			       a.name,
			       a.type,
			       a.put_date_period,
			       a.time_bucket,
			       a.state,
			       a.ad_pos,
			       a.product,
			       a.create_time,
			       a.update_time,
			       a.state_update_time,
			       a.ecp_ad_id,
			       b.region,
			       b.sex,
			       b.platform_version,
			       b.app,
			       b.tail_number,
			       b.`position`,
			       b.`income`,
			       b.`register_time`,
			       b.`contains_pkg`,
			       b.`not_contains_pkg`,
			       c.budget,
			       c.click_interval,
			       c.day_click_limit,
			       d.ext
			 FROM tb_ap_ad_information a LEFT JOIN tb_ap_ad_orientation b ON a.id = b.id
		          LEFT JOIN tb_ap_ad_budget c ON b.id = c.id
		          LEFT JOIN tb_ap_ad_ext d on c.id = d.id
		    WHERE a.id = #{id}
        ]]>
	</select>

    <update id="updateStateChangeTime">
        <![CDATA[
          UPDATE tb_ap_ad_information
             SET `state_update_time` = #{updateStateTime}
           WHERE `id` = #{id}
        ]]>
    </update>
</mapper>