<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ConfigOrientationMapper">

	<resultMap type="com.coohua.ap.admin.model.ConfigOrientationEntity" id="configOrientationResult">
		<id column="id" property="id" />
		<result column="name" property="name" />
		<result column="ad_pos" property="adPos" />
		<result column="product" property="product" />
		<result column="os" property="os"/>
		<result column="region" property="region"/>
		<result column="anonymous" property="anonymous"/>
		<result column="regist" property="regist"/>
		<result column="income" property="income"/>
		<result column="version" property="version"/>
		<result column="tail_number" property="tailNumber"/>
		<result column="user_pkg" property="userPkg"/>
		<result column="channel_id" property="channelId"/>
		<result column="manufacturer" property="manufacturer"/>
		<result column="sdk_version" property="sdkVersion"/>
		<result column="region_side" property="regionSide"/>
		<result column="config" property="config"/>
		<result column="state" property="state"/>
		<result column="ab_test" property="abTest"/>
		<result column="tf_platform" property="tfPlatform"/>
		<result column="priority" property="priority"/>
		<result column="dsp" property="dsp"/>
		<result column="lock_action_point" property="lockActionPoint"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
	</resultMap>

	<sql id="base_column">
		`name`, `ad_pos`, `product`, `os`, `region`, `anonymous`, `regist`, `income`, `version`, `tail_number`, `user_pkg`,`channel_id`,`manufacturer`,`sdk_version`,`region_side`,`config`, `state`,`ab_test`,`tf_platform`, `priority`,`dsp`, `lock_action_point`, `create_time`, `update_time`
	</sql>

	<select id="queryAllByProduct" resultMap="configOrientationResult">
		<![CDATA[
			SELECT *
			  FROM tb_ap_config_orientation
			 WHERE `product` = #{configOrientation.product}
		]]>
		<if test="configOrientation.id!=null">
			and `id` = #{configOrientation.id}
		</if>
		<if test="configOrientation.adPos!=0">
			and `ad_pos` = #{configOrientation.adPos}
		</if>
		<if test="configOrientation.state!=-1">
			and `state` = #{configOrientation.state}
		</if>
		<if test="configOrientation.name!=null and configOrientation.name!=''">
			and `name` like concat('%',#{configOrientation.name},'%')
		</if>
		LIMIT #{from}, #{offset}
	</select>

	<select id="queryAll" resultMap="configOrientationResult">
		SELECT * FROM tb_ap_config_orientation WHERE `state` = 1
	</select>

	<select id="countAllByProduct" resultType="Long">
		<![CDATA[
			SELECT COUNT(*)
			  FROM tb_ap_config_orientation
			 WHERE `product`= #{configOrientation.product}
		]]>
		<if test="configOrientation.id!=null">
			and `id` = #{configOrientation.id}
		</if>
		<if test="configOrientation.adPos!=0">
			and `ad_pos` = #{configOrientation.adPos}
		</if>
		<if test="configOrientation.state!=-1">
			and `state` = #{configOrientation.state}
		</if>
		<if test="configOrientation.name!=null and configOrientation.name!=''">
			and `name` like concat('%',#{configOrientation.name},'%')
		</if>
	</select>

	<select id="queryOneById" resultMap="configOrientationResult">
		SELECT * FROM tb_ap_config_orientation WHERE `id` = #{id}
	</select>

	<insert id="insertConfigOrientation" parameterType="com.coohua.ap.admin.model.ConfigOrientationEntity" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_ap_config_orientation
		(
			<include refid="base_column"/>
		)
		VALUES
		(
			#{name}, #{adPos}, #{product}, #{os}, #{region}, #{anonymous}, #{regist}, #{income}, #{version}, #{tailNumber},#{userPkg},
			#{channelId},#{manufacturer},#{sdkVersion},#{regionSide}, #{config}, #{state},#{abTest},#{tfPlatform}, #{priority}, #{dsp}, #{lockActionPoint}, now(), now()
		)
	</insert>

	<update id="updateConfigOrientation" parameterType="com.coohua.ap.admin.model.ConfigOrientationEntity">
		UPDATE tb_ap_config_orientation
		   SET `name` = #{name},
		       `config` = #{config},
		       `ad_pos` = #{adPos},
		       `state` =  #{state},
		       `ab_test` = #{abTest},
		       `os` = #{os},
		       `region` = #{region},
		       `anonymous` = #{anonymous},
		       `regist` = #{regist},
		       `income` = #{income},
		       `version` = #{version},
		       `tf_platform` = #{tfPlatform},
		       `tail_number` = #{tailNumber},
		       `user_pkg` = #{userPkg},
		       `channel_id` = #{channelId},
		       `manufacturer` = #{manufacturer},
		       `sdk_version` = #{sdkVersion},
		       `region_side` = #{regionSide},
		       `priority` = #{priority},
		       `dsp` = #{dsp},
		       `lock_action_point` = #{lockActionPoint},
		       `update_time` = now()
		 WHERE `id` = #{id}
	</update>

	<update id="updateConfigOrientationState" parameterType="com.coohua.ap.admin.model.ConfigOrientationEntity">
		UPDATE tb_ap_config_orientation
		   SET `state` = #{state},
		       `update_time` = now()
		 WHERE `id` = #{id}
	</update>

</mapper>