package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/4/13
 */
@Data
public class AdFilterBean {
    // 分层楼塔
    private List<List<AdFilterSub>> filterSubList;
    // 分层头层
    private AdFilterSub linkFilter;
    // Repeat参数
    private Integer repeat;
    // 分层配置类型 1-头层 2-楼塔配置
    private Integer filterType;

    @Data
    public static class AdFilterSub{
        private Integer adType;
        private String adTypeName;
        private Integer rate;
        private List<Integer> abortAdList;
        private Integer ecpm;
    }
}
