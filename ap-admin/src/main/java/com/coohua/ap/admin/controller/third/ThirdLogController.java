package com.coohua.ap.admin.controller.third;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.ThirdLogView;
import com.coohua.ap.admin.controller.vo.ThirdTemplateView;
import com.coohua.ap.admin.model.ThirdLogEntity;
import com.coohua.ap.admin.service.third.ThirdLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/1/10
 */
@RequestMapping("third/log")
@RestController
public class ThirdLogController {

    @Autowired
    private ThirdLogService thirdLogService;

    @RequestMapping("list")
    @ResponseBody
    public BaseResponse list(@RequestParam(value = "subId",required = false) Integer id,
                             @RequestParam(value = "type",required = false) Integer type,
                             HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(thirdLogService.list(id,type));
        return baseResponse;
    }
}
