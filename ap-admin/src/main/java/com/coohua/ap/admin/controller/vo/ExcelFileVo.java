package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: ExcelFileVo
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/7/30
 * @Version: 1.0.0
 **/
@Data
public class ExcelFileVo implements Serializable {

    // 应用id
    private Integer appId;

    // 广告类型
    private Integer adType;

    // 曝光占比
    private Integer exposurePercent;

    // 千次曝光收入
    private Integer ecpm;

    public ExcelFileVo() {
    }

    public ExcelFileVo(int[] array) {
        // array = {appId,adType,exposure,exposureCount,income}
        this.appId = array[0];
        this.adType = array[1];
        this.exposurePercent = array[2]*100/array[3];
        this.ecpm = array[4]*1000/array[2];

    }
}
