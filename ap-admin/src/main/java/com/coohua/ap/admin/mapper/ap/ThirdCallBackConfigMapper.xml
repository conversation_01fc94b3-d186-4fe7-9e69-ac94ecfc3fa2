<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdCallBackConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdCallBackConfig" >
        <result column="id" property="id" />
        <result column="product" property="product" />
        <result column="ad_id" property="adId" />
        <result column="app_id" property="appId" />
        <result column="pos_id" property="posId" />
        <result column="is_set" property="isSet" />
        <result column="secret" property="secret" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                product,
                ad_id,
                app_id,
                pos_id,
                is_set,
                secret,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdCallBackConfig">
        INSERT INTO third_call_back_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != product ">
                product,
            </if>
            <if test="null != adId ">
                ad_id,
            </if>
            <if test="null != appId ">
                app_id,
            </if>
            <if test="null != posId ">
                pos_id,
            </if>
            <if test="null != isSet ">
                is_set,
            </if>
            <if test="null != secret and '' != secret">
                secret,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != product ">
                #{product},
            </if>
            <if test="null != adId ">
                #{adId},
            </if>
            <if test="null != appId ">
                #{appId},
            </if>
            <if test="null != posId ">
                #{posId},
            </if>
            <if test="null != isSet ">
                #{isSet},
            </if>
            <if test="null != secret and '' != secret">
                #{secret},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_call_back_config
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdCallBackConfig">
        UPDATE third_call_back_config
        <set>
            <if test="null != product ">product = #{product},</if>
            <if test="null != adId">ad_id = #{adId},</if>
            <if test="null != appId">app_id = #{appId},</if>
            <if test="null != posId">pos_id = #{posId},</if>
            <if test="null != isSet">is_set = #{isSet},</if>
            <if test="null != secret and '' != secret">secret = #{secret},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_call_back_config
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_call_back_config
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_call_back_config
    </select>

</mapper>