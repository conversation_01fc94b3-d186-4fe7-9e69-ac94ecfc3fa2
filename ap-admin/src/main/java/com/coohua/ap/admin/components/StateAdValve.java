package com.coohua.ap.admin.components;

import com.coohua.ap.admin.controller.vo.Ad;
import com.coohua.ap.admin.service.AdAdminService;
import com.coohua.ap.base.constants.AdConstants;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.components
 * @create_time 2019-11-04
 */
public class StateAdValve extends AbstractAdValve {

    // 设置条件
    private String status;

    public StateAdValve(String status) {
        this.status = status;
    }

    @Override
    public List<Ad> doFilter(List<Ad> adList) {
        if (StringUtils.isEmpty(status) || Integer.parseInt(status) == 0) {
            return adList;
        }

        int chooseState = Integer.parseInt(status); // 用户选择的广告状态（筛选条件） 0:不过滤，1：有效，2：无效

        List<Ad> result = new ArrayList<>();

        if (chooseState == 1) {
            for (Ad ad : adList) {
                int state = AdAdminService.getAdStatus(ad);
                if (state == AdConstants.ADMIN_AD_STATE_OPEN) {
                    result.add(ad);
                }
            }
        } else if (chooseState == 2) {
            for (Ad ad : adList) {
                int state = AdAdminService.getAdStatus(ad);
                if (state == AdConstants.ADMIN_AD_STATE_PAUSE || state == AdConstants.ADMIN_AD_STATE_CLOSE) {
                    result.add(ad);
                }
            }
        }

        return result;
    }
}
