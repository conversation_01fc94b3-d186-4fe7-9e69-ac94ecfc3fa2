package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdAdPosConfig;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/4
 */
@Mapper
@Repository
public interface ThirdAdPosConfigMapper {

    int insert(ThirdAdPosConfig thirdAdPosConfig);

    int delete(int id);

    int update(ThirdAdPosConfig thirdAdPosConfig);

    ThirdAdPosConfig load(int id);


    List<ThirdAdPosConfig> pageList(int offset, int pagesize);

    int pageListCount(int offset,int pagesize);

}