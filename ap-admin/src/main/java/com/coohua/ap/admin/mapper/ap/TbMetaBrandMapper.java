package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbMetaBrandModel;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <pre>
 *  机型管理
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/02
 */
public interface TbMetaBrandMapper {

    @Select("select distinct a.manufacturer as `code`, a.`name` from `tb_meta_brand` a")
    List<TbMetaBrandModel> queryAllBrandType();
}
