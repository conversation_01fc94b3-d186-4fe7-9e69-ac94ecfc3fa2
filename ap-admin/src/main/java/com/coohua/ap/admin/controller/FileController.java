package com.coohua.ap.admin.controller;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.constants.CommonField;
import com.coohua.ap.admin.controller.vo.BaseConfigHistoryVO;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.EmptyAdInfoVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.domain.EmptyAdCountDomain;
import com.coohua.ap.admin.mapper.ap.BaseConfigHistoryMapper;
import com.coohua.ap.admin.mapper.ap.TbApAppMapper;
import com.coohua.ap.admin.model.AppModel;
import com.coohua.ap.admin.model.BaseConfigHistoryEntity;
import com.coohua.ap.admin.service.ExcelConfigService;
import com.coohua.ap.admin.service.FileUploadService;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.vo.WebMessage;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: FileController
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/7/29
 * @Version: 1.0.0
 **/
@RestController
@RequestMapping("/file")
public class FileController {


    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private BaseConfigHistoryMapper baseConfigHistoryMapper;

    @Autowired
    private TbApAppMapper tbApAppMapper;

    @Autowired
    private ExcelConfigService excelConfigService;


    @RequestMapping("/upload")
    public WebMessage uploadFile(MultipartFile file,HttpServletRequest request) {
        if(file == null){
            return new WebMessage(-2,"文件上传失败",null);
        }
        String fileType = file.getOriginalFilename().split("\\.")[1];
        if(!(CommonField.XLS.equalsIgnoreCase(fileType)||CommonField.XLSX.equalsIgnoreCase(fileType))){
            return new WebMessage(-1,"文件格式不正确",null);
        }
        boolean b = fileUploadService.changeAdOutput(file,fileType);
        if(b){
            return WebMessage.build(0,"修改成功",null);
        }

        return WebMessage.build(-1,"修改失败",null);
    }

    @RequestMapping("/uploadAdPlanConfig")
    public WebMessage uploadAdPlanConfig(MultipartFile file,HttpServletRequest request) {
        if(file == null){
            return new WebMessage(-2,"文件上传失败",null);
        }
        String fileType = file.getOriginalFilename().split("\\.")[1];
        if(!(CommonField.XLS.equalsIgnoreCase(fileType)||CommonField.XLSX.equalsIgnoreCase(fileType))){
            return new WebMessage(-1,"文件格式不正确",null);
        }
        int product = SessionUtils.getUserProduct(request);
        return excelConfigService.initAdPlanConfig(file,fileType,product);

    }

    @RequestMapping("/getHistory")
    public WebMessage getHistory() {
        Map<Integer,String> appMap = new HashMap<>();
        List<BaseConfigHistoryEntity> baseConfigHistoryEntities = baseConfigHistoryMapper.queryAll();
        List<AppModel> appModels = tbApAppMapper.queryAllApp();
        for(AppModel appModel:appModels){
            appMap.put(appModel.getAppId(),appModel.getName());
        }
        List<BaseConfigHistoryVO> baseConfigHistoryVOS = new ArrayList<>();
        for(BaseConfigHistoryEntity baseConfigHistoryEntity:baseConfigHistoryEntities){
            baseConfigHistoryVOS.add(new BaseConfigHistoryVO(baseConfigHistoryEntity,appMap));
        }
//        return WebMessage.build(0,"", JSON.toJSONString(baseConfigHistoryVOS));
        return WebMessage.build(0,"", baseConfigHistoryVOS);
    }

    @RequestMapping("/getHistoryCount")
    public WebMessage getHistoryCount(){
        int count = baseConfigHistoryMapper.queryCount();
        return  WebMessage.build(0,"",count);
    }

    @RequestMapping("/getHistoryInfo")
    public WebMessage getHistoryInfo(Page<BaseConfigHistoryVO> page){
        Map<Integer,String> appMap = new HashMap<>();
        List<BaseConfigHistoryEntity> baseConfigHistoryEntities = baseConfigHistoryMapper.queryByPage((page.getPageNo()-1)*page.getPageSize(),page.getPageSize());
        List<AppModel> appModels = tbApAppMapper.queryAllApp();
        for(AppModel appModel:appModels){
            appMap.put(appModel.getAppId(),appModel.getName());
        }
        List<BaseConfigHistoryVO> baseConfigHistoryVOS = new ArrayList<>();
        for(BaseConfigHistoryEntity baseConfigHistoryEntity:baseConfigHistoryEntities){
            baseConfigHistoryVOS.add(new BaseConfigHistoryVO(baseConfigHistoryEntity,appMap));
        }
        return WebMessage.build(0,"",baseConfigHistoryVOS);
    }


}
