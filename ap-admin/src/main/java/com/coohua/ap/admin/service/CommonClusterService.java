package com.coohua.ap.admin.service;


/**
 * Created by <PERSON><PERSON>ian<PERSON> on 2018/1/12.
 */
public interface CommonClusterService {
//    /**
//     * 获取用户积分策略Key
//     * @return
//     */
//    String buidlRedisCreditCostCurrKey();
//
//    /**
//     * 构建用户积分策略刷新时间的redis key
//     * @param userId
//     * @return
//     */
//    String buildRedisUserRefreshTimeKey(long userId);
//
//    String buildDeeperTaskKey(int product, long userId);
//
//    String buildNewUserStrategyKey(Long userId, int product, int adPos);
//
//    String buildUserMiniVedioAdStrategyKey(Long userId, int product);
//
//    String buildUserMobileKeyPre(long userId);
//
//    String buildCoohuaUserInfoKeyPre(long userId);
//
//    String buildBroswerUserInfoKeyPre(long userId);
//
//    String buildNewsearnUserRegion(long userId);
//
//    String buildMiniProgramIdPre(String wxId, long userId);
//
//    /**
//     * 构建key : 记录用户点击广告的时间戳，用于投放时过滤用户对某一广告点击的时间间隔。
//     * @param adId 广告ID
//     * @param userId 用户ID
//     * @param product
//     * @return key
//     */
//    String buildRedisUserAdClickIntervalTimeStampKey(long adId, long userId, ProductType product);
//
//    /**
//     * 构建Key：记录用户分享广告分行业的日分享次数
//     * @param userId 用户ID
//     * @param code 行业标识
//     * @param product 产品标识
//     * @return Key
//     */
//    String buildRedisUserShareAdFrequencyKey(long userId, int code, ProductType product);
//
//    /**
//     * 构建Key：CPW广告，第二高价
//     * @param userId 用户ID
//     * @param adId 广告ID
//     * @return Key
//     */
//    String buildRedisUserCpwPriceTop2Key(long userId, long adId);
//
//    /**
//     * 构建key : 记录用户点击某广告的次数，用于单用户的广告控量。
//     * @param adId
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildRedisUserDayClickLimitKey(long adId, Long userId, ProductType product);
//
//
//    /**
//     * 构建用户获取用户投放策略缓存信息的key
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildUserStrategyKey(Long userId, int product);
//
//    /**
//     * 构建判断作弊用户缓存Key
//     * @param userId 用户ID
//     * @return Key
//     */
//    String buildCheatUserKey(Long userId, int product);
//
//    /**
//     * 构建获取用户上传包名的key
//     * @param userId
//     * @return
//     */
//    String buildRedisUserPkgNamesKey(Long userId);
//
//    /**
//     * 构建获取用户上传包名的key
//     * @param userId
//     * @param product 产品类型，3：浏览器
//     * @return
//     */
//    public String buildRedisUserPkgNamesKey(Long userId, int product);
//
//    String buildUserNativeDownloadGiftCostFlagKey(long userId, int product, String pkgName);
//
//    String buildUserShareAdListKey(long userId, int product);
//
//    String buildNewuserShareAdId(long userId, int product);
//
//    String buildRedisUserAdPutLockKey(long userId, int product);
//
//    String buildRedisTotalGoldCoinKey(long userId);
//
//    /**
//     * 小程序策略 用户key
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildMiniUserStrategyKey(long userId, int product);
//
//    String buildRedisUserAdMiniPutLockKey(long userId, int product);
//
//    String buildPersonasKeyPre(long userId);
//
//    String buildMiniProgramClickLimit(long userId, String miniProgramId);
//
//    /**
//     * 构建点点赚用户阅读信息key
//     * @param adId 广告id
//     * @param userId 用户id
//     * @param date 日期
//     * @param product 产品类型，2：淘新闻，3：浏览器
//     * @return
//     */
//    String buildDdzUserInfoKey(Long adId, Long userId, String date, int product);
//
//    /**
//     * 点点赚用户金币信息key
//     * @param userId 用户id
//     * @param date 日期
//     * @param product 产品类型，2：淘新闻，3：浏览器
//     * @return
//     */
//    String buildDdzGoldCoinInfoKey(Long userId, String date, int product);
//
//    /**
//     * 拼接大转盘返回信息key
//     * @param userId 用户id
//     * @param product 产品类型，2：淘新闻，3：浏览器
//     * @return
//     */
//    String buildDialResponseInfo(Long userId, int product);
//
//    /**
//     * 下载类控频前缀
//     * @param userId 用户id
//     * @param appPkgName app包名
//     * @param product
//     * @return
//     */
//    String getCpaDownloadKey(long userId, String appPkgName, int product);
//
//    /**
//     * 应用推荐广告留存key
//     * @param adId 广告id
//     * @param timestamp 时间戳
//     * @param product
//     * @return
//     */
//    String buildAppRemainKey(Long adId, String timestamp, int product);
//
//    /**
//     * 构建应用推荐-现在类广告第一次投放的key
//     * @param userId 用户id
//     * @param adId 广告id
//     * @param product
//     * @return
//     */
//    String buildAppFirstPutKey(long userId, Long adId, int product);
//
//    /**
//     * 下载类广告金币信息缓存key
//     * @param product  产品表示
//     * @param userId 用户id
//     * @return
//     */
//    String buildDownloadAdGoldKey(int product, long userId);
//
//    /**
//     * 唤醒类列表中签到类任务key
//     * @param userId
//     * @param timestamp
//     * @param product
//     * @return
//     */
//    String buildAppAwakeForSignKey(long userId, String timestamp, int product);
//
//    /**
//     * 唤醒列表中唤醒任务key
//     * @param userId 用户id
//     * @param product 产品表示，2-淘新闻，3-浏览器
//     * @param adId 广告id
//     * @return
//     */
//    String buildAppAwakeForAwakeKey(long userId, int product, Long adId);
//
//    /**
//     * 唤起key
//     * @param userId
//     * @param appPkgName
//     * @param product
//     * @return
//     */
//    String buildAwakeKey(long userId, String appPkgName, int product);
//
//    /**
//     * 小程序留存key
//     * @param userId 用户id
//     * @param timestamp 时间戳，yyyyMMdd
//     * @param product 产品类型，2-淘新闻
//     * @return
//     */
//    String buildMiniProgramRemainKey(long userId, String timestamp, int product);
//
//    /**
//     * 小程序留存点击key
//     * @param userId
//     * @param adId
//     * @param product
//     * @return
//     */
//    String buildMiniProgramRemainClickKey(long userId, long adId, int product);
//
//    /**
//     * CPA广告，用户正在进行的任务
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildCpaDoingTaskKey(long userId, int product);
//
//    /**
//     * CPA下载任务策略key
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildCpaDownloadStrategy(long userId, int product);
//
//    /**
//     * CPA留存任务策略key
//     * @param userId 用户id
//     * @param product 产品类型
//     * @return
//     */
//    String buildCpaRemainStrategy(long userId, int product);
//
//    /**
//     * CPA点击时间key
//     * @param adId
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildUserAdClickIntervalTimeStampForCpa(long adId, long userId, int product);
//
//    /**
//     * 小程序任务是否完成key
//     * @param imei
//     * @param miniProgramWxId
//     * @return
//     */
//    String getMiniProgramTaskDoneKey(String imei, String miniProgramWxId);
//
//    /**
//     * 小程序任务新增策略key
//     * @param imei
//     * @return
//     */
//    String buildMiniProgramTaskNewStrategyKey(String imei);
//
//    /**
//     * 小程序任务留存策略key
//     * @param imei
//     * @return
//     */
//    String buildMiniProgramTaskRemainUserStrategyKey(String imei);
}
