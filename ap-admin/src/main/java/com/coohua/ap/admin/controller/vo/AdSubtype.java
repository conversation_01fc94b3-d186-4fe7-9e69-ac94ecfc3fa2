package com.coohua.ap.admin.controller.vo;

/**
 * <pre>
 * Comments:
 *
 *
 * <pre/>
 * <hr/>
 * Created by <PERSON><PERSON><PERSON> on 2017/6/14.
 */
public class AdSubtype implements Comparable<AdSubtype> {
    private int type; // 广告子类型
    private String name; // 广告子类型名称

    public AdSubtype() {}

    public AdSubtype(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "AdType{" +
                "type=" + type +
                ", name='" + name + '\'' +
                '}';
    }

    @Override
    public int compareTo(AdSubtype other) {
        return this.type > other.type ? 1 : this.type == other.type ? 0 : -1;
    }
}
