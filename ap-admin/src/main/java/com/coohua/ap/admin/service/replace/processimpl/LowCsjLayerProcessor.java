package com.coohua.ap.admin.service.replace.processimpl;

import com.coohua.ap.admin.mapper.ap.TbApDiminishBiddingMonitorMapper;
import com.coohua.ap.admin.model.TbApDiminishBiddingMonitor;
import com.coohua.ap.admin.model.TbApSubsidiesConfig;
import com.coohua.ap.admin.service.TbApSubsidiesConfigService;
import com.coohua.ap.admin.service.replace.BiddingProcessor;
import com.coohua.ap.admin.service.TbApDiminishBiddingMonitorService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class LowCsjLayerProcessor extends BiddingProcessor {

    @Resource
    private TbApDiminishBiddingMonitorMapper tbApDiminishBiddingMonitorMapper;
    @Autowired
    private TbApDiminishBiddingMonitorService tbApDiminishBiddingMonitorService;
    @Autowired
    private TbApSubsidiesConfigService tbApSubsidiesConfigService;

    @Override
    protected List<TbApDiminishBiddingMonitor> fetchData() {
        return tbApDiminishBiddingMonitorMapper.segementLowLayersBidding();
    }

    @Override
    protected void handle(List<TbApDiminishBiddingMonitor> tbApDiminishBiddingMonitors) {
        if (CollectionUtils.isNotEmpty(tbApDiminishBiddingMonitors)) {
            tbApDiminishBiddingMonitors.forEach(item -> {
                List<TbApSubsidiesConfig> tbApSubsidiesConfigs = tbApSubsidiesConfigService.queryValidByProduct(item.getOs(), item.getProduct(), item.getConfigName());
                if (CollectionUtils.isNotEmpty(tbApDiminishBiddingMonitors)){
                    tbApSubsidiesConfigs.forEach(config -> {
                        tbApDiminishBiddingMonitorService.pauseBidding(config);
                    });
                }
            });
        }
    }
}
