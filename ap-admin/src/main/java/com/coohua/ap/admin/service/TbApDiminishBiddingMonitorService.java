package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.constants.AdPosCreateType;
import com.coohua.ap.admin.constants.CreateReason;
import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.constants.PriceType;
import com.coohua.ap.admin.domain.CreateExceptionAdPosDto;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.AdInfo;
import com.coohua.ap.admin.model.TbApBidding;
import com.coohua.ap.admin.model.TbApDiminishBiddingMonitor;
import com.coohua.ap.admin.model.TbApSubsidiesConfig;
import com.coohua.ap.admin.service.third.ThirdAdPosService;
import com.coohua.ap.admin.utils.StringUtils;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.constants.ThirdPlatformType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.coohua.ap.admin.constants.AdPosCreateType.JLSP_BIDDING;
import static com.coohua.ap.base.utils.DateUtils.PATTERN_YMD;

@Service
@Slf4j
public class TbApDiminishBiddingMonitorService  {

    @Autowired
    private TbApBiddingMapper tbApBiddingMapper;
    @Autowired
    private TbApSubsidiesConfigService tbApSubsidiesConfigService;
    @Autowired
    private TbApSubsidiesConfigMapper tbApSubsidiesConfigMapper;
    @Autowired
    private ThirdAdPosService thirdAdPosService;
    @Autowired
    private BiddingConfigService biddingConfigService;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private AdInfoNewMapper adInfoNewMapper;
    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;
    @Autowired
    private TbApDiminishBiddingMonitorMapper biddingMonitorMapper;

    /**
     * Bidding追加数量上限
     */
    public static final int createBiddingMax = 5;
    /**
     * Bidding每次创建数量
     */
    public static final int onceCreateBiddingCount = 5;

    public void appendBidding(TbApSubsidiesConfig config) {

        List<TbApBidding> validPerkBiddingList = biddingConfigService.findPerkBiddingList(config, 1, getAdType(config));
        List<TbApBidding> invalidPerkBiddingList = biddingConfigService.findPerkBiddingList(config, 0, getAdType(config));

        if (validPerkBiddingList.size() >= createBiddingMax) return;

        int createCount = Math.min(createBiddingMax - validPerkBiddingList.size(), onceCreateBiddingCount);
        int appendCount = createCount;

        for (TbApBidding tbApBidding : invalidPerkBiddingList) {
            biddingConfigService.switchFlag(tbApBidding.getId(), 1);
            adInfoMapper.updateState(tbApBidding.getAdId().intValue(), 1, new Date());
            createCount--;
        }
        thirdAdPosService.sendCreatePosMsg(config.getProductName(), config.getAdTypeName(), appendCount - createCount, appendCount - createCount, "Bidding重启", ThirdPlatformType.getByCode(config.getPlatform()).getDesc(), config.getOs());
        createBidding(config, createCount);
    }

    public void pauseBidding(TbApSubsidiesConfig config){
//        List<TbApBidding> perkBiddingList = biddingConfigService.findPerkBiddingList(config, 1, getAdType(config));
//
//        if (CollectionUtils.isNotEmpty(perkBiddingList)){
//            Integer size = 0;
//            for (TbApBidding tbApBidding : perkBiddingList) {
//                biddingConfigService.switchFlag(tbApBidding.getId(), 0);
//                adInfoMapper.updateState(tbApBidding.getAdId().intValue(), 0, new Date());
//                size++;
//            }
//            thirdAdPosService.sendCreatePosMsg(config.getProductName(), config.getAdTypeName(), perkBiddingList.size(), size, "Bidding暂停", ThirdPlatformType.getByCode(config.getPlatform()).getDesc(), config.getOs());
//        }
    }

    public void replaceBidding(TbApSubsidiesConfig config){
        List<AdInfo> adInfoList = biddingConfigService.findPerkAdInfoList(config, 1, getAdType(config));
        Date date = new Date();
        String today = new SimpleDateFormat(PATTERN_YMD).format(date);
        String yesterday = DateUtil.dateToString(DateUtil.dateIncreaseByDay(date, -1));
        String logday = date.getHours() != 0 ? today : yesterday;
        String key = RedisBuilder.buildSubsidesReplaceBiddingRecord(config.getProduct(), config.getOs(), logday);
        String value = apClusterRedisService.get(key);
        Integer count = new Integer(0);
        if (CollectionUtils.isNotEmpty(adInfoList) && StringUtils.isEmpty(value)) {
            AdPosCreateType createType = JLSP_BIDDING;
            List<CreateExceptionAdPosDto> exceptionList = new ArrayList<>();
            adInfoList.forEach(adInfo -> {
                CreateExceptionAdPosDto createExceptionAdPosDto = new CreateExceptionAdPosDto();
                createExceptionAdPosDto.setAdId(adInfo.getAdId());
                createExceptionAdPosDto.setAppId(adInfo.getProduct());
                createExceptionAdPosDto.setProduct(config.getProduct());
                createExceptionAdPosDto.setProductName(adInfo.getProductName());
                createExceptionAdPosDto.setPosId(Long.valueOf(adInfo.getPosIdThird()));
                createExceptionAdPosDto.setPosName(adInfo.getAdName());
                createExceptionAdPosDto.setAdType(getAdType(config));
                createExceptionAdPosDto.setTypeName(createType.getDesc());
                createExceptionAdPosDto.setOs(config.getOs());
                createExceptionAdPosDto.setSourceName(Platform.getPlatform(config.getPlatform()).getDesc());
                createExceptionAdPosDto.setCreateDesc(CreateReason.LAYER_INSERT.getDesc());
                createExceptionAdPosDto.setCreateType(CreateReason.LAYER_INSERT.getType());
                exceptionList.add(createExceptionAdPosDto);
            });

            if (exceptionList.size() <= 0){
                return;
            }

            count = tbApSubsidiesConfigService.createAndSendMsg(exceptionList, today, createType);

            apClusterRedisService.setex(key, RedisConstants.TIME_DAY_1, JSON.toJSONString(config));
            thirdAdPosService.sendCreatePosMsg(config.getProductName(), createType.getDesc(), exceptionList.size(), count, "Bidding自动替换", ThirdPlatformType.getByCode(config.getPlatform()).getDesc(), config.getOs());
            tbApSubsidiesConfigMapper.insertReplaceRecord(config, logday, date, PriceType.BIDDING.getCode());
        }
    }

    public void createBidding(TbApSubsidiesConfig config, Integer createCount){
        if (createCount == 0){
            return;
        }
        Map<Integer, Integer> adTypeSubPriceMap = new HashMap<>();
        int[] createAdType = new int[]{Objects.requireNonNull(AdPosCreateType.getByDesc(config.getAdTypeName())).code};
        List<AdTypeSub> validAdTypeSubList = tbApSubsidiesConfigService.findValidAdTypeSubList(config);
        int loopCount = Math.min(validAdTypeSubList.size(), createCount);
        for (int i = 0; i < loopCount; i++) {
            int type = validAdTypeSubList.get(i).getType();
            adTypeSubPriceMap.put(type, 0);
        }
        thirdAdPosService.autoCreateThirdAdPos(config, adTypeSubPriceMap, createAdType[0], "perk", PriceType.BIDDING.getCode());
        adTypeSubPriceMap.clear();
    }

    public Integer getAdType(TbApSubsidiesConfig config){
        int[] createAdType = new int[]{Objects.requireNonNull(AdPosCreateType.getByDesc(config.getAdTypeName())).code};
        return generateAdType(config.getPlatform(), createAdType);
    }


    /**
     * 获取所需广告位类型
     *
     * @param platform     平台
     * @param createAdType 商业中台统一广告位类别
     * @return 广告位Id-中台
     */
    private Integer generateAdType(Integer platform, int[] createAdType) {
        Integer platformCode = ThirdPlatformType.getByCode(platform).getCode();
        int adType = 0;
        if (ThirdPlatformType.CSJ.getCode().equals(platformCode)) {
            if (AdType.TOUTIAO_VIDEO_TEMPLATE.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.TOUTIAO_VIDEO_TEMPLATE.type;
            } else if (AdType.CSJ_NEW_CHAPIN.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.CSJ_NEW_CHAPIN.type;
                createAdType[0] = AdPosCreateType.NCP.code;
            }
        } else if (ThirdPlatformType.GDT.getCode().equals(platformCode)) {
            if (AdType.GDT_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.GDT_VIDEO.type;
            } else if (AdType.GDT_CHAPING.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.GDT_CHAPING.type;
            }
        } else if (ThirdPlatformType.KS.getCode().equals(platformCode)) {
            if (AdType.KS_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.KS_VIDEO.type;
            } else if (AdType.KS_NEW_CHAPIN.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.KS_NEW_CHAPIN.type;
                createAdType[0] = AdPosCreateType.NCP.code;
            }
        } else if (ThirdPlatformType.BD.getCode().equals(platformCode)) {
            if (AdType.BAIDU_SUB_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.BAIDU_SUB_VIDEO.type;
            } else if (AdType.BAIDU_CHAPIN_NEW.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.BAIDU_CHAPIN_NEW.type;
                createAdType[0] = AdPosCreateType.NCP.code;
            }
        } else if (ThirdPlatformType.SMG.getCode().equals(platformCode)) {
            if (AdType.SIG_MOB_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.SIG_MOB_VIDEO.type;
            }
        }
        return adType;
    }

    public void biddingMonitor(String param) {
        List<TbApDiminishBiddingMonitor> highMonitors = biddingMonitorMapper.segementHighLayersBidding();
        List<TbApDiminishBiddingMonitor> lowMonitors = biddingMonitorMapper.segementLowLayersBidding();
        List<TbApDiminishBiddingMonitor> noBiddingMonitors = biddingMonitorMapper.segementNoLayersBidding();
        List<TbApDiminishBiddingMonitor> intersections = new ArrayList<>(CollectionUtils.intersection(highMonitors, lowMonitors));
        highMonitors.removeAll(lowMonitors);
        if (CollectionUtils.isNotEmpty(intersections)){
            intersections.forEach(intersection -> {
                Set<TbApSubsidiesConfig> tbApReplacePosConfigList = tbApSubsidiesConfigService.findTbApSubsidesConfigList(intersection);
                tbApReplacePosConfigList.forEach(tbApReplacePosConfig -> pauseBidding(tbApReplacePosConfig));;
            });
        }

        if (CollectionUtils.isNotEmpty(highMonitors)){
            highMonitors.forEach(highMonitor -> {
                Set<TbApSubsidiesConfig> tbApReplacePosConfigList = tbApSubsidiesConfigService.findTbApSubsidesConfigList(highMonitor);
                tbApReplacePosConfigList.forEach(tbApSubsidiesConfig -> {
                    tbApSubsidiesConfig.setStrategyName(highMonitor.getProductName()+"新增Bidding-auto");
                    replaceBidding(tbApSubsidiesConfig);
                });
            });

        }

        if (CollectionUtils.isNotEmpty(noBiddingMonitors)){
            noBiddingMonitors.forEach(noBiddingMonitor -> {
                Set<TbApSubsidiesConfig> tbApReplacePosConfigList = tbApSubsidiesConfigService.findTbApSubsidesConfigList(noBiddingMonitor);
                tbApReplacePosConfigList.forEach(tbApSubsidiesConfig -> {
                    tbApSubsidiesConfig.setStrategyName(noBiddingMonitor.getProductName()+"新增Bidding-auto");
                    appendBidding(tbApSubsidiesConfig);
                    replaceBidding(tbApSubsidiesConfig);
                });

            });
        }

    }
}
