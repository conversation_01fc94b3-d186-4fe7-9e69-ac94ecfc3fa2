package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.AdPosView;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.model.AdPosModel;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.service
 * @create_time 2019-11-04
 */
public interface AdPosService {


    void pageListByProduct(String name, String id, int product, Page<AdPosView> page);

    List<AdPosView> queryByProduct(int product);

    AdPosModel insertAdPos(AdPosView view);

    AdPosModel updateAdPos(AdPosModel model);
}
