package com.coohua.ap.admin.service.third.dto.bd.req;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/30
 * https://baidu-ssp.gz.bcebos.com/mssp/api/百度联盟-媒体管理API-接入文档.pdf
 */
@Data
public class BdAdPosCreateRequest {
    private String app_sid;
    private Integer ad_type;
    private String ad_name;
    private String[] ad_tags;
    private Integer channel_id;
    private String protect_rule_id;
    private Double cpm;
    private AdInfo ad_info;
    private Integer price_type;

    @Data
    public static class AdInfo{
        // 信息流
        private Integer info_flow_style_control_type;
        private Integer info_flow_ad_num;

        // 插屏
        private Integer interstitial_ad_scene;
        private List<Integer> interstitial_style_types;
        private List<Integer> interstitial_material_types;

        // 激励视频
        //激励视频 回调控制 默认为 0，单选，可选值:0(无 需服务器判断)， 1(需要服务器 判断)
        private Integer reward_video_return_control;
        private String reward_video_return_url;
        //激励视频 多次奖励 回调 本字段有权限控制【如需开通权 限，请联系您的业务经理】; 无权限仅能设置默认值 1(是)， 开通后可设置为 0(否)或 1(是
        private Integer reward_video_multi_rewards;
        private Integer reward_video_voice_control;
        private Integer reward_video_ad_num;
        private Integer reward_video_update_return_token;
    }
}
