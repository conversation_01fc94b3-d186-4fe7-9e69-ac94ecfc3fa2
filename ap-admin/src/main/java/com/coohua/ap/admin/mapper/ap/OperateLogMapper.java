package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.OperateLogEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 */
public interface OperateLogMapper {

    @Select("<script> " +
            " select `id` AS 'id', " +
            " `item_id` AS 'itemId', " +
            " `operate_log_type` AS 'operateLogType', " +
            " `ip` AS 'ip', " +
            " `user_id` AS 'userId', " +
            " `user_name` AS 'userName', " +
            " `operate_type` AS 'operateType', " +
            " `old_value` AS 'oldValueJson', " +
            " `new_value` AS 'newValueJson', " +
            " `operate_time` AS 'operateTime', " +
            " `product` AS 'product' " +
            " from tb_bp_ap_operate_log " +
            " where `product` = #{appId} " +
            " <if test=\"itemId!=null\"> " +
            " and `item_id` = #{itemId} " +
            "</if>" +
            " <if test=\"userId!=null\"> " +
            " and `user_id` = #{userId} " +
            "</if>" +
            "<if test=\"userName!=null and userName!=''\">" +
            " and `user_name` like concat('%', #{userName}, '%') " +
            "</if>" +
            "<if test=\"operateType!=null and operateType!=0\">" +
            " and `operate_type` = #{operateType} " +
            "</if>" +
            "<if test=\"operateLogType!=null and operateLogType!=0\">" +
            " and `operate_log_type` = #{operateLogType} " +
            "</if>" +
            "order by id desc"+
            " limit #{from}, #{offset} " +
            " </script>")
    List<OperateLogEntity> queryByApp(@Param("appId") int appId,
                                      @Param("from") int from,
                                      @Param("offset") int offset,
                                      @Param("itemId") Integer itemId,
                                      @Param("userId") Integer userId,
                                      @Param("userName") String userName,
                                      @Param("operateType") Integer operateType,
                                      @Param("operateLogType") Integer operateLogType);


    @Select("<script> " +
            " select count(*) " +
            " from tb_bp_ap_operate_log " +
            " where `product` = #{appId} " +
            " <if test=\"itemId!=null\"> " +
            " and `item_id` = #{itemId} " +
            "</if>" +
            " <if test=\"userId!=null\"> " +
            " and `user_id` = #{userId} " +
            "</if>" +
            "<if test=\"userName!=null and userName!=''\">" +
            " and `user_name` like concat('%', #{userName}, '%') " +
            "</if>" +
            "<if test=\"operateType!=null and operateType!=0\">" +
            " and `operate_type` = #{operateType} " +
            "</if>" +
            "<if test=\"operateLogType!=null and operateLogType!=0\">" +
            " and `operate_log_type` = #{operateLogType} " +
            "</if>" +
            " </script>")
    long queryCount(@Param("appId") int appId,
                    @Param("itemId") Integer itemId,
                    @Param("userId") Integer userId,
                    @Param("userName") String userName,
                    @Param("operateType") Integer operateType,
                    @Param("operateLogType") Integer operateLogType);


    @Insert("insert into tb_bp_ap_operate_log ( " +
            " `item_id`, `operate_log_type`, `ip`, `user_id`, `user_name`, `operate_type`, `old_value`, `new_value`, `operate_time`, `product` " +
            " ) values ( " +
            " #{log.itemId}, #{log.operateLogType}, #{log.ip}, #{log.userId}, #{log.userName}, #{log.operateType}, #{log.oldValueJson}, #{log.newValueJson}, now(), #{log.product} " +
            " ) ")
    int addOperateLog(@Param("log") OperateLogEntity log);
}
