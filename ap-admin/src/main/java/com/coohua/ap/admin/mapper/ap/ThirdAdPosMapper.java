package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdAdPosEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/11
 */
@Mapper
@Repository
public interface ThirdAdPosMapper {
    /**
     * [新增]
     **/
    int insert(ThirdAdPosEntity thirdAdPos);

    /**
     * [刪除]
     **/
    int delete(int id);

    /**
     * [更新]
     **/
    int update(ThirdAdPosEntity thirdAdPos);

    /**
     * [查询] 根据主键 id 查询
     **/
    ThirdAdPosEntity load(int id);

    /**
     * [查询] 分页查询
     **/
    List<ThirdAdPosEntity> pageList(@Param("offset") int offset, @Param("pageSize")int pagesize,
                                    @Param("appName") String name,
                                    @Param("appId") String appId,
                                    @Param("posName") String posName,
                                    @Param("posId") Long posId,
                                    @Param("incomeStatus") Integer incomeStatus,
                                    @Param("adType") Integer adType,
                                    @Param("stTime") Date stTime,
                                    @Param("edTime") Date edTime,
                                    @Param("appIdList") List<String> appIdList);

    /**
     * [查询] 分页查询 count
     **/
    int pageListCount(@Param("offset") int offset, @Param("pageSize")int pagesize,
                      @Param("appName") String name,
                      @Param("appId") String appId,
                      @Param("posName") String posName,
                      @Param("posId") Long posId,
                      @Param("incomeStatus") Integer incomeStatus,
                      @Param("adType") Integer adType,
                      @Param("stTime") Date stTime,
                      @Param("edTime") Date edTime,
                      @Param("appIdList") List<String> appIdList);

    @Select({
            "<script>",
            " select * from third_ad_pos where id in ",
            " <foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"
    })
    List<ThirdAdPosEntity> queryList(@Param("ids") List<Integer> ids);


    @Select({
            "<script>",
            " select * from third_ad_pos where pos_id in ",
            " <foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"
    })
    List<ThirdAdPosEntity> queryListByPosId(@Param("ids") List<Long> posId);

    @Select({" select * from third_ad_pos where pos_id = #{posId}"})
    ThirdAdPosEntity queryByPosId(@Param("posId") Long posId);

    @Update({" update third_ad_pos set income_status = 1 where id =#{id}"})
    int updateStateToAlreadyLoadById(@Param("id")Integer id);

    @Update({
            "<script>",
            " update third_ad_pos set income_status = 1 where id in ",
            " <foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"
    })
    int updateStateToAlreadyLoad(@Param("ids") List<Integer> ids);

    @Select("select count(1) from third_ad_pos where pos_id = #{posId}")
    int existCount(@Param("posId")Long posId);

    @Select("select * from third_ad_pos where create_time > #{crTime} and pos_name like #{likeFlag}")
    List<ThirdAdPosEntity> queryCreateExPos(@Param("crTime") Date crTime,@Param("likeFlag") String likeFlag);

    @Select("select count(1) from third_ad_pos where pos_name = #{posName}")
    Integer queryCountAdPos(@Param("posName") String posName);

    @Select("select * from third_ad_pos where create_time < #{crTime} and income_status = 0  " +
            " and platform_code = #{platformCode} and app_id = #{appId}")
    List<ThirdAdPosEntity> queryNoLoadAds(@Param("crTime") Date crTime,@Param("platformCode") Integer platformCode,@Param("appId")Long appId);

    @Update({"update third_ad_pos set pos_status = 0 where pos_id = #{posId}"})
    int closeByPosId(@Param("posId")Long posId);
}
