package com.coohua.ap.admin.controller.third;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.constants.CreateReason;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.domain.CreateExceptionAdPosDto;
import com.coohua.ap.admin.utils.MergeAdUtils;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.admin.utils.third.Strings;
import com.coohua.ap.base.constants.AdPosType;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeConvertVo;
import com.coohua.user.event.api.dto.ExceptionAdPos;
import com.coohua.user.event.api.remote.rpc.AdPosRpc;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/28
 */
@Slf4j
@RestController
@RequestMapping("third/exception")
public class ExceptionAdPosController {

    @MotanReferer(basicReferer = "user-eventBasicRefererConfigBean", application = "user-event",version = "2.0.0")
    private AdPosRpc adPosRpc;

    @RequestMapping(value = "list")
    @ResponseBody
    public BaseResponse queryExceptionList(@RequestParam(value = "logday",required = false) String logday,
                                           @RequestParam(value = "type",required = false) String type){
        if (Strings.isEmpty(logday)){
            logday = DateUtil.dateToString(DateUtil.dateIncreaseByDay(new Date(),-1));
        }
        if (Strings.isEmpty(type)){
            type = "视频";
        }
        List<ExceptionAdPos> exceptionAdPosList = adPosRpc.queryExceptionAds(logday,type,50,-3);
        List<CreateExceptionAdPosDto> createExceptionAdIncomes = exceptionAdPosList.stream()
                .map(r -> CreateExceptionAdPosDto.build(r, CreateReason.GAP)).collect(Collectors.toList());
        BaseResponse response = new BaseResponse(0);
        response.setData(MergeAdUtils.mergeRepeatPos(createExceptionAdIncomes));
        return response;
    }
}
