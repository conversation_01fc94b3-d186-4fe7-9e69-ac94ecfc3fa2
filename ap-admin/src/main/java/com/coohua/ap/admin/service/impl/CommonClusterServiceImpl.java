package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.service.CommonClusterService;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON> <PERSON>iandong on 2018/1/10.
 */
//@Service
public class CommonClusterServiceImpl implements CommonClusterService {

//    private String getTag(Object userId) {
//        return "{" + userId + "}";
//    }
//
//    @Override
//    public String buidlRedisCreditCostCurrKey() {
//        return null;
//    }
//
//    @Override
//    public String buildRedisUserRefreshTimeKey(long userId) {
//        return null;
//    }
//
//    @Override
//    public String buildDeeperTaskKey(int product, long userId) {
//        return RedisConstants.DEEPER_TASK_KEY_PRE + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildUserMobileKeyPre(long userId) {
//        return RedisConstants.USER_MOBILE_KEY_PRE + getTag(userId);
//    }
//
//    @Override
//    public String buildCoohuaUserInfoKeyPre(long userId) {
//        return RedisConstants.COOHUA_USER_INFO_KEY + getTag(userId);
//    }
//
//    @Override
//    public String buildBroswerUserInfoKeyPre(long userId) {
//        return RedisConstants.BROWER_USER_INFO_KEY + getTag(userId);
//    }
//
//    @Override
//    public String buildNewsearnUserRegion(long userId) {
//        return RedisConstants.NEWSEARN_USER_REGION_KEY + getTag(userId);
//    }
//
//    @Override
//    public String buildMiniProgramIdPre(String wxId, long userId) {
//        return RedisConstants.MINI_PROGRAM_ID_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + wxId;
//    }
//
//    @Override
//    public String buildRedisUserAdClickIntervalTimeStampKey(long adId, long userId, ProductType product) {
//        String keyPre = product != null && product.code() == ProductType.COOHUA.code() ? RedisConstants.USER_AD_CLICK_INTERVAL_TIMESTAMP_SCREENLOCK_KEY_PRE : RedisConstants.USER_AD_CLICK_INTERVAL_TIMESTAMP_KEY_PRE;
//        return keyPre + adId + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    // 格式：cpw:frequency:用户ID:产品标识:行业Code:日期
//    @Override
//    public String buildRedisUserShareAdFrequencyKey(long userId, int code, ProductType product) {
//        String today = com.coohua.nap.utils.DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.USER_SHARE_AD_FREQUENCY_KEY_PRE + getTag(userId) + NewsConstants.SYMBOL_COLON
//                + product.code() + NewsConstants.SYMBOL_COLON
//                + code + NewsConstants.SYMBOL_COLON
//                + today;
//    }
//
//    @Override
//    public String buildRedisUserCpwPriceTop2Key(long userId, long adId) {
//        return RedisConstants.USER_CPW_PRICE_TOP2_KEY_PRE + getTag(userId) + NewsConstants.SYMBOL_COLON + adId;
//    }
//
//    @Override
//    public String buildRedisUserDayClickLimitKey(long adId, Long userId, ProductType product) {
//        String today = com.coohua.nap.utils.DateUtils.formatDateForyyyyMMdd(new Date());
//        String keyPre = product != null && product.code() == ProductType.COOHUA.code() ? RedisConstants.USER_AD_DAY_CLICK_LIMIT_SCREENLOCK_KEY_PRE : RedisConstants.USER_AD_DAY_CLICK_LIMIT_KEY_PRE;
//        return keyPre + adId + NewsConstants.SYMBOL_COLON + getTag(userId) + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public String buildUserStrategyKey(Long userId, int product) {
//        String userStrategyKey = null;
//        if(product == ProductType.NEWSEARN.code()){
//            userStrategyKey = RedisConstants.USER_STRATEGY_KEY_PRE + getTag(userId);
//        } else if (product == ProductType.COOHUA.code()){
//            userStrategyKey = RedisConstants.USER_STRATEGY_SCREENLOCK_KEY_PRE + getTag(userId);
//        } else if (product == ProductType.BROWER.code()){
//            userStrategyKey = RedisConstants.USER_STRATEGY_BROWER_KEY_PRE + getTag(userId);
//        } else if (product == ProductType.NEWMINI.code()){
//            userStrategyKey = RedisConstants.USER_STRATEGY_NEWSMINI_KEY_PRE + getTag(userId);
//        }
//        return userStrategyKey;
//    }
//
//    @Override
//    public String buildNewUserStrategyKey(Long userId, int product, int adPos) {
//        return RedisConstants.NEW_USER_STRATEGY_KEY_PRE + product + NewsConstants.SYMBOL_COLON + adPos + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildUserMiniVedioAdStrategyKey(Long userId, int product) {
//        return RedisConstants.USER_MINI_VEDIO_AD_STRATEGY_KEY_PRE + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildCheatUserKey(Long userId, int product) {
//        return RedisConstants.CHEAT_USER_KEY_PRE + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildRedisUserPkgNamesKey(Long userId) {
//        return RedisConstants.USER_PKG_NAMES_KEY_PRE + getTag(userId);
//    }
//
//    @Override
//    public String buildRedisUserPkgNamesKey(Long userId, int product) {
//        return RedisConstants.USER_PKG_NAMES_KEY_PRE + product + ":" + getTag(userId);
//    }
//
//    @Override
//    public String buildUserNativeDownloadGiftCostFlagKey(long userId, int product, String pkgName) {
//        return RedisConstants.USER_NATIVE_DOWNLOAD_GIFT_COST_FLAG_PRE + product + NewsConstants.SYMBOL_COLON + getTag(userId) + NewsConstants.SYMBOL_COLON + pkgName;
//    }
//
//    @Override
//    public String buildUserShareAdListKey(long userId, int product) {
//        return RedisConstants.USER_SHARE_ADLIST + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildNewuserShareAdId(long userId, int product) {
//        return RedisConstants.NEWUSER_SHARE_ADID + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildRedisUserAdPutLockKey(long userId, int product) {
//        return RedisConstants.USER_AD_PUT_STRATEGY_LOCK_PRE + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildRedisTotalGoldCoinKey(long userId) {
//        String fDate = DateFormatUtils.format(new Date(), "yyyyMMdd");
//        return RedisConstants.USER_DAY_TOTAL_CREDIT_KEY_PRE + getTag(userId) + ":" + fDate;
//    }
//
//    @Override
//    public String buildMiniUserStrategyKey(long userId, int product) {
//        String userStrategyKey = null;
//        if(product == ProductType.NEWSEARN.code()){
//            userStrategyKey = RedisConstants.USER_MINI_STRATEGY_KEY_PRE + getTag(userId);
//        } else if (product == ProductType.COOHUA.code()){
//            userStrategyKey = RedisConstants.USER_MINI_STRATEGY_SCREENLOCK_KEY_PRE + getTag(userId);
//        } else if (product == ProductType.BROWER.code()){
//            userStrategyKey = RedisConstants.USER_MINI_STRATEGY_BROWER_KEY_PRE + getTag(userId);
//        } else if (product == ProductType.NEWMINI.code()){
//            userStrategyKey = RedisConstants.USER_STRATEGY_NEWSMINI_KEY_PRE + getTag(userId);
//        }
//        return userStrategyKey;
//    }
//
//    @Override
//    public String buildRedisUserAdMiniPutLockKey(long userId, int product) {
//        return RedisConstants.USER_AD_MINI_PUT_STRATEGY_LOCK_PRE + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildPersonasKeyPre(long userId) {
//        return RedisConstants.PERSONAS_KEY_PRE + getTag(userId);
//    }
//
//    @Override
//    public String buildMiniProgramClickLimit(long userId, String miniProgramId) {
//        return RedisConstants.MINI_PROGRAM_CLICK_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + miniProgramId;
//    }
//
//    @Override
//    public String buildDdzUserInfoKey(Long adId, Long userId, String date, int product) {
//        if(ProductType.NEWSEARN.code() == product){
//            return RedisConstants.NEWS_DDZ_USER_INFO_KEY_PRE + adId + NewsConstants.SYMBOL_COLON + getTag(userId) + NewsConstants.SYMBOL_COLON + date;
//        }else if(ProductType.BROWER.code() == product){
//            return RedisConstants.BROWSER_DDZ_USER_INFO_KEY_PRE + adId + NewsConstants.SYMBOL_COLON + getTag(userId) + NewsConstants.SYMBOL_COLON + date;
//        }
//        return "";
//    }
//
//    @Override
//    public String buildDdzGoldCoinInfoKey(Long userId, String date, int product) {
//        if(ProductType.NEWSEARN.code() == product){
//            return RedisConstants.NEWS_DDZ_GOLD_COIN_INFO_KEY_PRE  + getTag(userId) + NewsConstants.SYMBOL_COLON + date;
//        }else {
//            return RedisConstants.BROWSER_DDZ_GOLD_COIN_INFO_KEY_PRE + getTag(userId) + NewsConstants.SYMBOL_COLON + date;
//        }
//    }
//
//    @Override
//    public String buildDialResponseInfo(Long userId, int product) {
//        if(ProductType.NEWSEARN.code() == product){
//            return RedisConstants.NEWS_DIAL_RESPONSE_KEY_PRE  + getTag(userId) ;
//        }else {
//            return RedisConstants.BROWSER_DIAL_RESPONSE_KEY_PRE + getTag(userId);
//        }
//    }
//
//    @Override
//    public String getCpaDownloadKey(long userId, String appPkgName, int product) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_CPA_DOWNLOAD_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + appPkgName;
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_CPA_DOWNLOAD_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + appPkgName;
//        }
//        return key;
//    }
//
//    @Override
//    public String buildAppRemainKey(Long adId, String timestamp, int product) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_APP_AD_REMAIN_PRE_KEY + getTag(adId) + NewsConstants.SYMBOL_COLON + timestamp;
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_APP_AD_REMAIN_PRE_KEY + getTag(adId) + NewsConstants.SYMBOL_COLON + timestamp;
//        }
//        return key;
//    }
//
//    @Override
//    public String buildAppFirstPutKey(long userId, Long adId, int product) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_APP_FIRST_PUT_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + adId;
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_APP_FIRST_PUT_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + adId;
//        }
//        return key;
//    }
//
//    @Override
//    public String buildDownloadAdGoldKey(int product, long userId) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_DOWNLOAD_AD_GOLD + getTag(userId);
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_DOWNLOAD_AD_GOLD + getTag(userId);
//        }
//        return key;
//    }
//
//    @Override
//    public String buildAppAwakeForSignKey(long userId, String timestamp, int product) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_AWAKE_FOR_SIGN_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + timestamp;
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_AWAKE_FOR_SIGN_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + timestamp;
//        }
//        return key;
//    }
//
//    @Override
//    public String buildAppAwakeForAwakeKey(long userId, int product, Long adId) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_AWAKE_FOR_AWAKE_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + adId;
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_AWAKE_FOR_AWAKE_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + adId;
//        }
//        return key;
//    }
//
//    @Override
//    public String buildAwakeKey(long userId, String appPkgName, int product) {
//        String key = "";
//        if(product == ProductType.NEWSEARN.code()){
//            key = RedisConstants.NEWS_AWAKE_FOR_AWAKE_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + appPkgName;
//        }else if(product == ProductType.BROWER.code()){
//            key = RedisConstants.BROWSER_AWAKE_FOR_AWAKE_PRE_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + appPkgName;
//        }
//        return key;
//    }
//
//    @Override
//    public String buildMiniProgramRemainKey(long userId, String timestamp, int product) {
//        return RedisConstants.MINI_PROGRAM_REMAIN_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + product +NewsConstants.SYMBOL_COLON + timestamp;
//    }
//
//    @Override
//    public String buildMiniProgramRemainClickKey(long userId, long adId, int product) {
//        return RedisConstants.MINI_PROGRAM_REMAIN_CLICK_KEY + getTag(userId) + NewsConstants.SYMBOL_COLON + product +NewsConstants.SYMBOL_COLON + adId;
//    }
//
//    @Override
//    public String buildCpaDoingTaskKey(long userId, int product) {
//        return RedisConstants.CPA_DOING_TASK + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildCpaDownloadStrategy(long userId, int product) {
//        return RedisConstants.CPA_DOWNLOAD_STRATEGY + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildCpaRemainStrategy(long userId, int product) {
//        return RedisConstants.CPA_REMAIN_STRATEGY + product + NewsConstants.SYMBOL_COLON + getTag(userId);
//    }
//
//    @Override
//    public String buildUserAdClickIntervalTimeStampForCpa(long adId, long userId, int product) {
//        return RedisConstants.CPA_CLICK + product+NewsConstants.SYMBOL_COLON+adId+NewsConstants.SYMBOL_COLON+getTag(userId);
//    }
//
//    @Override
//    public String getMiniProgramTaskDoneKey(String imei, String miniProgramWxId) {
//        return RedisConstants.MINI_PROGRAM_TASK_DONE + getTag(imei) + NewsConstants.SYMBOL_COLON + miniProgramWxId;
//    }
//
//    @Override
//    public String buildMiniProgramTaskNewStrategyKey(String imei) {
//        return RedisConstants.MINI_PROGRAM_TASK_NEW_STRATEGY + getTag(imei);
//    }
//
//    @Override
//    public String buildMiniProgramTaskRemainUserStrategyKey(String imei) {
//        return RedisConstants.MINI_PROGRAM_TASK_REMAIN_STRATEGY + getTag(imei);
//    }

}
