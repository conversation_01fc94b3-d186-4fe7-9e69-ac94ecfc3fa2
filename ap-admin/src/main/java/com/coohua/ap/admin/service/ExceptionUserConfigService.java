package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.BiddingConfigVo;
import com.coohua.ap.admin.controller.vo.ExUserConfigRequest;
import com.coohua.ap.admin.controller.vo.ExUserConfigVo;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.ExUserConfigMapper;
import com.coohua.ap.admin.model.ExUserConfig;
import com.coohua.ap.admin.model.TbApBidding;
import com.coohua.ap.admin.utils.third.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/10/28
 */
@Slf4j
@Service
public class ExceptionUserConfigService {

    @Resource
    private ExUserConfigMapper exUserConfigMapper;

    public void queryList(Page<ExUserConfigVo> page, Integer product){
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<ExUserConfig> exUserConfigList = exUserConfigMapper.pageList(from,page.getPageSize(),product);
        page.setCount(exUserConfigMapper.pageListCount(from,page.getPageSize(),product));
        page.setItems(exUserConfigList.stream().map(exUserConfig -> {
            ExUserConfigVo exUserConfigVo = new ExUserConfigVo();
            BeanUtils.copyProperties(exUserConfig,exUserConfigVo);
            exUserConfigVo.setCreateTime(DateUtil.dateToStringWithTime(exUserConfig.getCreateTime()));
            exUserConfigVo.setUpdateTime(DateUtil.dateToStringWithTime(exUserConfig.getUpdateTime()));
            return exUserConfigVo;
        }).collect(Collectors.toList()));
    }

    public void insert(ExUserConfigRequest request,Integer product){

        if (Strings.isEmpty(request.getChannels()) &&
                Strings.isEmpty(request.getIps()) &&
                Strings.isEmpty(request.getModels()) &&
                Strings.isEmpty(request.getPhones())){
            throw new RuntimeException("必须填写一个标签条件");
        }
        checkRate(request.getWithDrawRate());
        checkRate(request.getScoreRate());

        ExUserConfig exUserConfig = new ExUserConfig();
        exUserConfig.setChannel(request.getChannels());
        exUserConfig.setOcpcType(request.getOcpcType());
        exUserConfig.setProduct(product);
        exUserConfig.setIp(request.getIps());
        exUserConfig.setModel(request.getModels());
        exUserConfig.setPhone(request.getPhones());
        exUserConfig.setVideoLimit(request.getVideoLimit());
        exUserConfig.setState(1);
        exUserConfig.setScoreRate(request.getScoreRate());
        exUserConfig.setWithdrawRate(request.getWithDrawRate());
        exUserConfig.setOcpcChannel(request.getOcpcChannel());
        exUserConfig.setRuleLevel(request.getRuleLevel());

        Date now = new Date();
        exUserConfig.setCreateTime(now);
        exUserConfig.setUpdateTime(now);

        exUserConfigMapper.insert(exUserConfig);
    }

    private void checkRate(Integer rate){
        if (rate == null || rate > 100 || rate < 0){
            throw new RuntimeException("比率必须是0~100之间的整数");
        }
    }

    public void update(ExUserConfigRequest request,Integer product){

        if (Strings.isEmpty(request.getChannels()) &&
                Strings.isEmpty(request.getIps()) &&
                Strings.isEmpty(request.getModels()) &&
                Strings.isEmpty(request.getPhones())){
            throw new RuntimeException("必须填写一个标签条件");
        }
        checkRate(request.getWithDrawRate());
        checkRate(request.getScoreRate());

        ExUserConfig exUserConfig = exUserConfigMapper.load(request.getId());
        Optional.ofNullable(exUserConfig).orElseThrow(() -> new RuntimeException("要修改的记录不存在！"));

        ExUserConfig record = new ExUserConfig();
        record.setId(exUserConfig.getId());

        record.setChannel(request.getChannels());
        record.setIp(request.getIps());
        record.setModel(request.getModels());
        record.setPhone(request.getPhones());
        record.setOcpcType(request.getOcpcType());
        record.setOcpcChannel(request.getOcpcChannel());

        record.setVideoLimit(request.getVideoLimit());
        record.setScoreRate(request.getScoreRate());
        record.setWithdrawRate(request.getWithDrawRate());
        record.setRuleLevel(request.getRuleLevel());


        exUserConfigMapper.update(record);
    }

    public void switchFlag(Integer id,Integer switchFlag){
        ExUserConfig exUserConfig = exUserConfigMapper.load(id);
        Optional.ofNullable(exUserConfig).orElseThrow(() -> new RuntimeException("要修改的记录不存在！"));

        ExUserConfig record = new ExUserConfig();
        record.setId(exUserConfig.getId());

        record.setState(switchFlag);

        exUserConfigMapper.update(record);
    }
}
