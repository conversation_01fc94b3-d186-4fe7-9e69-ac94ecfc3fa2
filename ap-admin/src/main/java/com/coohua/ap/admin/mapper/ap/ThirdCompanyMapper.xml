<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdCompanyMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdCompanyEntity" >
        <result column="id" property="id" />
        <result column="platform_code" property="platformCode" />
        <result column="platform_desc" property="platformDesc" />
        <result column="main_body" property="mainBody" />
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="security_key" property="securityKey" />
        <result column="mask_rule_id" property="maskRuleId" />
        <result column="main_status" property="mainStatus" />
        <result column="app_count" property="appCount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                platform_code,
                platform_desc,
                main_body,
                user_id,
                role_id,
                security_key,
                mask_rule_id,
                main_status,
                app_count,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdCompanyEntity">
        INSERT INTO third_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != platformCode'>
                platform_code,
            </if>
            <if test ='null != platformDesc'>
                platform_desc,
            </if>
            <if test ='null != mainBody'>
                main_body,
            </if>
            <if test ='null != userId'>
                user_id,
            </if>
            <if test ='null != roleId'>
                role_id,
            </if>
            <if test ='null != securityKey'>
                security_key,
            </if>
            <if test ='null != maskRuleId'>
                mask_rule_id,
            </if>
            <if test ='null != mainStatus'>
                main_status,
            </if>
            <if test ='null != appCount'>
                app_count,
            </if>
            <if test ='null != createTime'>
                create_time,
            </if>
            <if test ='null != updateTime'>
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != platformCode'>
                #{platformCode},
            </if>
            <if test ='null != platformDesc'>
                #{platformDesc},
            </if>
            <if test ='null != mainBody'>
                #{mainBody},
            </if>
            <if test ='null != userId'>
                #{userId},
            </if>
            <if test ='null != roleId'>
                #{roleId},
            </if>
            <if test ='null != securityKey'>
                #{securityKey},
            </if>
            <if test ='null != maskRuleId'>
                #{maskRuleId},
            </if>
            <if test ='null != mainStatus'>
                #{mainStatus},
            </if>
            <if test ='null != appCount'>
                #{appCount},
            </if>
            <if test ='null != createTime'>
                #{createTime},
            </if>
            <if test ='null != updateTime'>
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_company
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdCompanyEntity">
        UPDATE third_company
        <set>
            <if test ='null != platformCode'>platform_code = #{platformCode},</if>
            <if test ='null != platformDesc'>platform_desc = #{platformDesc},</if>
            <if test ='null != mainBody'>main_body = #{mainBody},</if>
            <if test ='null != userId'>user_id = #{userId},</if>
            <if test ='null != roleId'>role_id = #{roleId},</if>
            <if test ='null != securityKey'>security_key = #{securityKey},</if>
            <if test ='null != maskRuleId'>mask_rule_id = #{maskRuleId},</if>
            <if test ='null != mainStatus'>main_status = #{mainStatus},</if>
            <if test ='null != appCount'>app_count = #{appCount},</if>
            <if test ='null != createTime'>create_time = #{createTime},</if>
            <if test ='null != updateTime'>update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM `bp-ap`.third_company
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_company
        <if test=' null != name and name != ""'> WHERE main_body like concat('%',#{name},'%')</if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_company
        <if test='null != name and name != ""'> WHERE main_body like concat('%',#{name},'%')</if>
    </select>

</mapper>