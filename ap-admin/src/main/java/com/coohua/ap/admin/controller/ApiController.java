package com.coohua.ap.admin.controller;

import com.alibaba.druid.util.Base64;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.model.AdminUseEntity;
import com.coohua.ap.admin.service.AdminUserService;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.utils.RSAUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * //                          _ooOoo_
 * //                           o8888888o
 * //                           88" . "88
 * //                           (| -_- |)
 * //                            O\ = /O
 * //                        ____/`---'\____
 * //                      .   ' \\| |// `.
 * //                       / \\||| : |||// \
 * //                     / _||||| -:- |||||- \
 * //                       | | \\\ - /// | |
 * //                     | \_| ''\---/'' | |
 * //                      \ .-\__ `-` ___/-. /
 * //                   ___`. .' /--.--\ `. . __
 * //                ."" '< `.___\_<|>_/___.' >'"".
 * //               | | : `- \`.;`\ _ /`;.`/ - ` : | |
 * //                 \ \ `-. \_ __\ /__ _/ .-` / /
 * //         ======`-.____`-.___\_____/___.-`____.-'======
 * //                            `=---='
 * //
 * //         .............................................
 * //                  佛祖镇楼                  BUG辟易
 * //          佛曰:
 * //                  写字楼里写字间，写字间里程序员；
 * //                  程序人员写程序，又拿程序换酒钱。
 * //                  酒醒只在网上坐，酒醉还来网下眠；
 * //                  酒醉酒醒日复日，网上网下年复年。
 * //                  但愿老死电脑间，不愿鞠躬老板前；
 * //                  奔驰宝马贵者趣，公交自行程序员。
 * //                  别人笑我忒疯癫，我笑自己命太贱；
 * //                  不见满街漂亮妹，哪个归得程序员？
 * <AUTHOR>
 */

@Controller
@RequestMapping("/api")
@Slf4j
public class ApiController extends BaseController {

    @Autowired
    private AdminUserService adminUserService;

    @ApolloJsonValue("${join.newConfig.app.list}")
    private List<Integer> joinAppList;


    @RequestMapping("/login")
    @ResponseBody
    public Object check(@RequestParam(value = "uname", required = false) String userName,
                        @RequestParam(value = "pwd", required = false) String password,
                        HttpServletRequest request, HttpServletResponse response) throws Exception {
        BaseResponse ret = new BaseResponse(0);
        AdminUseEntity user = adminUserService.check(userName, password);
        if (user != null) {
            ret.setRet(0);
            String uuid = UUID.randomUUID().toString();
            Map<String, String> data = new HashMap<String, String>();
            data.put("token", uuid);
            data.put("showName", user.getName());
            data.put("showProduct", user.getProductName());

            String cookieValue = user.getId() + "|" + user.getUserName() + "|" + user.getName() + "|" + user.getProduct() + "|" + uuid;
            // set session
            request.getSession().setAttribute(AdConstants.COOKIE_NAME, cookieValue);

            // set cookie
            String secret = Base64.byteArrayToBase64(RSAUtils.encryptByPublicKey(cookieValue.getBytes(), AdConstants.PUBLIC_KEY));

            Cookie cookie = new Cookie(AdConstants.COOKIE_NAME, secret);
            cookie.setPath("/");
            cookie.setMaxAge(Integer.MAX_VALUE); // Cookie存活最大值
            response.addCookie(cookie);

            Cookie cookieProduct = new Cookie(AdConstants.COOKIE_NAME_PRODUCT, String.valueOf(user.getProduct()));
            cookieProduct.setPath("/");
            cookieProduct.setMaxAge(Integer.MAX_VALUE);
            response.addCookie(cookieProduct);

            Cookie cookieEnable = new Cookie(AdConstants.COOKIE_NAME_SUPPORT, String.valueOf(joinAppList.contains(user.getProduct())));
            cookieEnable.setPath("/");
            cookieEnable.setMaxAge(Integer.MAX_VALUE);
            response.addCookie(cookieEnable);

            ret.setData(data);

        } else {
            ret.setRet(1);
        }
        return ret;
    }

}
