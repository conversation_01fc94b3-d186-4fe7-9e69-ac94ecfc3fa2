package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.mapper.ap.TbApAppReqClusterMapper;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.vo.TbApAppReqCluster;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TbApAppReqClusterServiceImpl {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    @Resource
    private TbApAppReqClusterMapper tbApAppReqClusterMapper;

    private static final String K8S_GATEWAY = "k8s-gateway";
    private static final String K8S_GATEWAY02 = "k8s-gateway02";
    private static final String TEAM_CITY = "team_city";


    public void saveReqClusterFromRedis() {
        Map<String, String> k8sGetewayMap = getAppReqByCluster(K8S_GATEWAY);
        XxlJobLogger.log("k8s-gateway map size:" + k8sGetewayMap.size());
        filterAndSaveBatch(k8sGetewayMap, K8S_GATEWAY);

        Map<String, String> K8sGeteway02Map = getAppReqByCluster(K8S_GATEWAY02);
        XxlJobLogger.log("k8s-gateway02 map size:" + K8sGeteway02Map.size());
        filterAndSaveBatch(K8sGeteway02Map, K8S_GATEWAY02);
        Map<String, String> teamCityMap = getAppReqByCluster(TEAM_CITY);
        XxlJobLogger.log("team_city map size:" + teamCityMap.size());
        filterAndSaveBatch(teamCityMap, TEAM_CITY);


    }

    private void filterAndSaveBatch(Map<String, String> map, String cluster) {
        if (MapUtils.isEmpty(map)) return;
        String table;
        switch (cluster) {
            case K8S_GATEWAY:
                table = "tb_ap_app_req_cluster_k8s_gateway";
                break;
            case K8S_GATEWAY02:
                table = "tb_ap_app_req_cluster_k8s_gateway02";
                break;
            case TEAM_CITY:
                table = "tb_ap_app_req_cluster_team_city";
                break;
            default:
                return;
        }

        List<String> hashKeys = tbApAppReqClusterMapper.selectAllHashKeysFromMysql(table);
        List<String> keys = map.keySet().stream().filter(hashKey -> !hashKeys.contains(hashKey)).collect(Collectors.toList());
        List<TbApAppReqCluster> tbApAppReqClusterList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(keys)) {
            for (String key : keys) {
                String s = map.get(key);

                if (s == null) continue;

                TbApAppReqCluster tbApAppReqCluster = JSON.parseObject(s, TbApAppReqCluster.class);
                tbApAppReqCluster.setRedisHashKey(key);
                tbApAppReqCluster.setCreateTime(LocalDateTime.now());
                tbApAppReqCluster.setUpdateTime(LocalDateTime.now());
                tbApAppReqClusterList.add(tbApAppReqCluster);
            }
            XxlJobLogger.log("save to mysql size:" + tbApAppReqClusterList.size());
            tbApAppReqClusterMapper.saveBatch(table, tbApAppReqClusterList);
        }
    }

    public Map<String, String> getAppReqByCluster(String cluster) {

        Map<String, String> map = new HashMap<>();

        String key = String.format(RedisConstants.STATISTIC_APP_CLUSTER_KEY, cluster);
        // 初始化游标
        String cursor = "0";
        ScanParams scanParams = new ScanParams().count(100); // 每次读取 100 个字段

        do {
            // 使用 hscan 命令分页读取 Hash 的数据
            ScanResult<Map.Entry<String, String>> scanResult = apClusterRedisService.hscan(key, cursor, scanParams);
            List<Map.Entry<String, String>> entries = scanResult.getResult();

            // 处理当前页的数据
            for (Map.Entry<String, String> entry : entries) {
                map.put(entry.getKey(), entry.getValue());
            }

            // 更新游标
            cursor = String.valueOf(scanResult.getCursor());
        } while (!cursor.equals("0"));

        return map;
    }
}