package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.coohua.ap.admin.controller.vo.Ad;
import com.coohua.ap.admin.controller.vo.AdPosView;
import com.coohua.ap.admin.controller.vo.ExcelFileAdConfVo;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.*;
import com.coohua.ap.admin.mq.MqConfigAdmin;
import com.coohua.ap.base.constants.MessageTag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/10/27
 */
@Slf4j
@Service
public class DefaultConfigService {
    
    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor executor;

    @Autowired
    private AdPosService adPosService;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private ConfigBaseMapper configBaseMapper;
    @Resource
    private ConfigOrientationMapper configOrientationMapper;
    @Resource
    private StrategyDefaultConfigMapper strategyDefaultConfigMapper;
    @Autowired
    private AdInfoSubService adInfoSubService;

    @Value("${env}")
    private String env;

    @Resource(name = "produceMqApp")
    private ProducerBean producerBean;
    @Autowired
    private MqConfigAdmin mqConfig;


    public final static String EMPTY = "";

    public final static Integer SATE_ON = 1;
    public final static Integer SATE_OFF = 0;

    public final static String OPEN_SCREEN = "开屏";
    public final static String POP_UPS = "弹窗";
    public final static String FIXED_POSITION = "固定位";
    public final static String REWARDED_VIDEO = "激励视频";
    public final static String TABLE_PLAQUE = "插屏";
    public final static String BANNER = "banner";
    public final static String Full_screen_video = "全屏视频";

    public final static String TENCENT = "广点通";
    public final static String TOU_TIAO = "穿山甲";
    public final static String IOS = "ios";
    public final static String ANDROID = "android";
    public final static String KUAI_SHOU = "快手";
    public final static String SPLIT_CARD = "分层";
    public final static String OTHER = "其他";

    private final static String OPEN_SCREEN_ANDROID= "开屏";
    private final static String POP_UPS_ANDROID = "静态图";
    private final static String FIXED_POSITION_ANDROID= "分层";
    private final static String REWARDED_VIDEO_ANDROID= "激励视频";
    private final static String TABLE_PLAQUE_IOS= "IOS插屏";
    private final static String TABLE_PLAQUE_FENG= "分层插屏";
    private final static String POP_UPS_ANDROID_R= "弹窗";

    private final static String DEFAULT_CONFIG = "系统自动生成配置";

    private boolean isTest(){
        return "fat".equalsIgnoreCase(env);
    }


    public void test(){
        init(1392, null);
    }

    public void init(Integer appId,String appPkgName){
        CompletableFuture.runAsync(() ->{
            try {
                log.info("STEP1 >>>> INIT POS_ID");
                setDefaultAdPosId(appId);
                log.info("STEP2 >>>> INIT PLAN");
                setAdPlanDefault(appId);
                log.info("STEP3 >>>> INIT CACHE_CONFIG");
                setCacheConfigDefault(appId,appPkgName);
                log.info("STEP4 >>>> INIT POLICY_CONFIG");
                setPolicyConfig(appId);
                if (isTest()){
                    log.info("STEP5 >>>> INIT ADINFO");
                    createDefaultAd(appId);
                }else {
                    // Fa♂ MQ
                    producerBean.send(new Message(mqConfig.getTopic(), MessageTag.APP.getTag(), appId.toString().getBytes(StandardCharsets.UTF_8)));
                }
            } catch (Exception e) {
                log.error("INIT APP ERROR", e);
            }
        },executor);
    }

    private void setDefaultAdPosId(Integer appId){
        List<AdPosView> defaultViewList = new ArrayList<>();
        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(OPEN_SCREEN);
            setExtendInfo(DEFAULT_CONFIG);
        }});

        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(POP_UPS);
            setExtendInfo(DEFAULT_CONFIG);
        }});

        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(FIXED_POSITION);
            setExtendInfo(DEFAULT_CONFIG);
        }});

        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(REWARDED_VIDEO);
            setExtendInfo(DEFAULT_CONFIG);
        }});

        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(TABLE_PLAQUE);
            setExtendInfo(DEFAULT_CONFIG);
        }});

        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(BANNER);
            setExtendInfo(DEFAULT_CONFIG);
        }});

        defaultViewList.add(new AdPosView(){{
            setProduct(appId);
            setName(Full_screen_video);
            setExtendInfo(DEFAULT_CONFIG);
        }});
        defaultViewList.forEach( adPosService::insertAdPos);
    }

    private void setAdPlanDefault(Integer appId){
        List<AdPlanEntity> adPlanEntityList = new ArrayList<>();
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(TENCENT);
        }});
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(TOU_TIAO);
        }});
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(KUAI_SHOU);
        }});
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(SPLIT_CARD);
        }});
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(OTHER);
        }});
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(ANDROID);
        }});
        adPlanEntityList.add(new AdPlanEntity(){{
            setProduct(appId);
            setState(SATE_ON);
            setName(IOS);
        }});

        adPlanEntityList.forEach(adInfoMapper::insertPlan);
    }

    private void setCacheConfigDefault(Integer appId,String appPkgName){
        configMapper.insertOne(new Config(){{
            setProduct(appId);
            setName("ad.third.cache");
            setValue("{\""+appPkgName+"\":{}}");
            setComment("第三方广告缓存配置");
        }});
        configMapper.insertOne(new Config(){{
            setProduct(appId);
            setName("ad.third.cache.ios");
            setValue("{\""+appPkgName+"\":{}}");
            setComment("第三方广告缓存配置IOS");
        }});
        configMapper.insertOne(new Config(){{
            setProduct(appId);
            setName("ad.third.open.cache");
            setValue("{\""+appPkgName+"\":{}}");
            setComment("开屏广告缓存配置");
        }});

        configMapper.insertOne(new Config(){{
            setProduct(appId);
            setName("ad.third.static.cache");
            setValue("{\""+appPkgName+"\":{}}");
            setComment("静态图广告缓存配置");
        }});

        configMapper.insertOne(new Config(){{
            setProduct(appId);
            setName("ad.third.banner.cache");
            setValue("{\""+appPkgName+"\":{}}");
            setComment("Banner广告缓存配置");
        }});
    }

    private ConfigBaseEntity convertToConfig(StrategyDefaultConfig strategyDefaultConfig,Integer appId){
        return new ConfigBaseEntity(){{
            setName(strategyDefaultConfig.getStrategyName());
            setProduct(appId);
            setType(strategyDefaultConfig.getStrategyType());
            setConfig(strategyDefaultConfig.getStrategyConfig());
            setComment(strategyDefaultConfig.getStrategyComment());
            setState(strategyDefaultConfig.getStrategyState());
            setDelFlag(SATE_ON);
            setPosType(strategyDefaultConfig.getPosType());
        }};
    }

    private void setConfigOrientationDefault(ConfigOrientationEntity configOrientationDefault){
        configOrientationDefault.setRegionSide(0);
        configOrientationDefault.setAnonymous(0);
        configOrientationDefault.setRegist("{\"end\":\"\",\"limit\":0,\"start\":\"\"}");
        configOrientationDefault.setIncome("{\"end\":\"\",\"limit\":0,\"start\":\"\"}");
        configOrientationDefault.setVersion("{\"end\":\"\",\"limit\":0,\"start\":\"\"}");
        configOrientationDefault.setTailNumber(EMPTY);
        configOrientationDefault.setUserPkg(EMPTY);
        configOrientationDefault.setChannelId(EMPTY);
    }

    private void setPolicyConfig(Integer appId){
        log.info("STEP4-1 >>>> INIT POLICY_CONFIG_BASE..");
        // 获取配置
        List<StrategyDefaultConfig> strategyDefaultConfigs = strategyDefaultConfigMapper.queryDefaultConfig();
        List<ConfigBaseEntity> configBaseEntityList = strategyDefaultConfigs.stream().map(r -> convertToConfig(r,appId)).collect(Collectors.toList());
        configBaseEntityList.forEach(configBaseMapper::insertConfig);
        log.info("STEP4-2 >>>> INIT POLICY_CONFIG_DETAIL:..");
        // 查询之前的广告位
        List<AdPosView> adPosViewList = adPosService.queryByProduct(appId);
        // 查询之前的投放配置
        List<ConfigBaseEntity>  configBaseEntities = configBaseMapper.queryIdAndNameList(appId);
        Map<String,List<ConfigBaseEntity>> configMap = configBaseEntities.stream()
                .collect(Collectors.groupingBy(ConfigBaseEntity::getName));

        if (adPosViewList.size() == 0){
            log.error("初始化{}配置异常，重新初始化",appId);
            setDefaultAdPosId(appId);
        }
        Map<String,AdPosView> adPosViewMap = adPosViewList.stream()
                .collect(Collectors.toMap(AdPosView::getName,k->k,(k1,k2)->k1));

        List<ConfigOrientationEntity> configOrientationEntityList = new ArrayList<>();
        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(OPEN_SCREEN_ANDROID);
            setAdPos(adPosViewMap.getOrDefault(OPEN_SCREEN,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(OPEN_SCREEN_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(SATE_ON);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(POP_UPS_ANDROID_R);
            setAdPos(adPosViewMap.getOrDefault(POP_UPS,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(POP_UPS_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(SATE_ON);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(REWARDED_VIDEO_ANDROID);
            setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(REWARDED_VIDEO_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(SATE_ON);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(FIXED_POSITION);
            setAdPos(adPosViewMap.getOrDefault(FIXED_POSITION,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(POP_UPS_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(SATE_ON);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(FIXED_POSITION_ANDROID);
            setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(FIXED_POSITION_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(3);
            setState(SATE_ON);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(TABLE_PLAQUE_FENG);
            setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(TABLE_PLAQUE_FENG);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});


        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(OPEN_SCREEN_ANDROID);
            setAdPos(adPosViewMap.getOrDefault(OPEN_SCREEN,new AdPosView()).getId());
            setProduct(appId);
            setOs(2);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("开屏IOS");
            List<ConfigBaseEntity> baseEntityListR = configMap.get(OPEN_SCREEN_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            baseEntityListR.stream().skip(1).forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(POP_UPS_ANDROID_R);
            setAdPos(adPosViewMap.getOrDefault(POP_UPS,new AdPosView()).getId());
            setProduct(appId);
            setOs(2);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("静态图IOS");
            List<ConfigBaseEntity> baseEntityListR = configMap.get(POP_UPS_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            baseEntityListR.stream().skip(1).forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(REWARDED_VIDEO_ANDROID);
            setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
            setProduct(appId);
            setOs(2);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("激励视频IOS");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});


        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName("激励+百度");
            setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("激励+百度");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(4);
            setState(0);
        }});


        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName("插屏+百度");
            setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("插屏+百度");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(4);
            setState(0);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(FIXED_POSITION);
            setAdPos(adPosViewMap.getOrDefault(FIXED_POSITION,new AdPosView()).getId());
            setProduct(appId);
            setOs(2);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("静态图IOS");
            List<ConfigBaseEntity> baseEntityListR = configMap.get(POP_UPS_ANDROID);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            baseEntityListR.stream().skip(1).forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName(TABLE_PLAQUE_IOS);
            setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
            setProduct(appId);
            setOs(2);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get(TABLE_PLAQUE_IOS);
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName("BANNER_ANDROID");
            setAdPos(adPosViewMap.getOrDefault(BANNER,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("BANNER");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName("BANNER_IOS");
            setAdPos(adPosViewMap.getOrDefault(BANNER,new AdPosView()).getId());
            setProduct(appId);
            setOs(2);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("BANNER");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(1);
            setState(1);
        }});

        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName("锁区-审核仅穿山甲");
            setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(1);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("锁区-审核仅穿山甲");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(15);
            setState(0);
        }});



        configOrientationEntityList.add(new ConfigOrientationEntity(){{
            setName("激励+百度+oppo");
            setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
            setProduct(appId);
            setOs(1);
            setRegion(0);
            setConfigOrientationDefault(this);
            List<ConfigBaseEntity> baseEntityList = configMap.get("激励+百度+oppo");
            Map<Integer,Integer> resMap = new HashMap<>();
            baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
            setConfig(JSON.toJSONString(resMap));
            setPriority(6);
            setState(0);
        }});

        // 若线上环境 继续增加
        if (!isTest()){
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("锁区-插屏");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(1);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("锁区-插屏无广告");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(15);
                setState(0);
            }});

            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("全屏视频");
                setAdPos(adPosViewMap.getOrDefault(Full_screen_video,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("全屏视频");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(1);
                setState(1);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("全屏视频");
                setAdPos(adPosViewMap.getOrDefault(Full_screen_video,new AdPosView()).getId());
                setProduct(appId);
                setOs(2);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("全屏视频");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(1);
                setState(1);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("受限地区-激励视频5s");
                setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("受限地区-激励视频5s");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(13);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("激励+百度+sig");
                setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("激励+百度+sig");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(7);
                setState(SATE_OFF);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("分层插屏-ks新插屏");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("分层插屏-ks新插屏");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(2);
                setState(1);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("插屏+百度-ks新插屏");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("插屏+百度-ks新插屏");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(5);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity() {{
                setName("激励视频+百度iOS");
                setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO, new AdPosView()).getId());
                setProduct(appId);
                setOs(2);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("激励视频+百度iOS");
                Map<Integer, Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(), configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(3);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity() {{
                setName("插屏+百度iOS");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE, new AdPosView()).getId());
                setProduct(appId);
                setOs(2);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("插屏+百度iOS");
                Map<Integer, Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(), configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(3);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity() {{
                setName("锁区-审核仅快手");
                setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO, new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(1);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("锁区-审核仅快手");
                Map<Integer, Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(), configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(16);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity() {{
                setName("锁区-审核仅穿山甲(3s跳过");
                setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO, new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(1);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("锁区-审核仅穿山甲(3s跳过)");
                Map<Integer, Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(), configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(17);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity() {{
                setName("锁区自定义激励视频");
                setAdPos(adPosViewMap.getOrDefault(REWARDED_VIDEO, new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(1);
                setLockActionPoint(1);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("锁区自定义激励视频");
                Map<Integer, Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(), configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(19);
                setState(0);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity() {{
                setName("锁区自定义插屏");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE, new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(1);
                setLockActionPoint(1);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("锁区自定义插屏");
                Map<Integer, Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(), configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(19);
                setState(0);
            }});
        }else {
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("全屏视频");
                setAdPos(adPosViewMap.getOrDefault(Full_screen_video,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("全屏视频");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(1);
                setState(1);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("iOS全屏视频");
                setAdPos(adPosViewMap.getOrDefault(Full_screen_video,new AdPosView()).getId());
                setProduct(appId);
                setOs(2);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("iOS全屏视频");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(1);
                setState(1);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("分层插屏-ks新插屏");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("分层插屏-ks新插屏");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(2);
                setState(1);
            }});
            configOrientationEntityList.add(new ConfigOrientationEntity(){{
                setName("插屏+百度-ks新插屏");
                setAdPos(adPosViewMap.getOrDefault(TABLE_PLAQUE,new AdPosView()).getId());
                setProduct(appId);
                setOs(1);
                setRegion(0);
                setConfigOrientationDefault(this);
                List<ConfigBaseEntity> baseEntityList = configMap.get("插屏+百度-ks新插屏");
                Map<Integer,Integer> resMap = new HashMap<>();
                baseEntityList.forEach(configBaseEntity -> resMap.put(configBaseEntity.getType(),configBaseEntity.getId()));
                setConfig(JSON.toJSONString(resMap));
                setPriority(5);
                setState(0);
            }});
        }

        configOrientationEntityList.forEach(cr ->{
            configOrientationMapper.insertConfigOrientation(cr);
            adInfoSubService.saveOrUpdateConfigInfo(cr.getId());
        });
    }

    @Autowired
    private ExcelConfigService excelConfigService;
    @Autowired
    private AdAdminService adAdminService;

    private void createDefaultAd(Integer product){

        List<ExcelFileAdConfVo> excelFileAdConfVoList = new ArrayList<>();
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"android","开屏","开屏","穿山甲","头条开屏","887402737","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"android","固定位","固定位","穿山甲","头条静态图模板广告","945610472","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"android","弹窗","弹窗","穿山甲","头条静态图模板广告","945610470","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"android","插屏","插屏","穿山甲","穿山甲插屏","945684970","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"android","激励","激励视频","穿山甲","头条激励视频模板广告","945610474","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"android","banner","banner","穿山甲","穿山甲banner","945800683","5119641","","0",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"ios","ios开屏","开屏","穿山甲","头条开屏","887402737","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"ios","ios固定位","固定位","穿山甲","头条静态图模板广告","945610472","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"ios","ios弹窗","弹窗","穿山甲","头条静态图模板广告","945610470","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"ios","ios插屏","插屏","穿山甲","穿山甲插屏","945684970","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"ios","ios激励","激励视频","穿山甲","头条激励视频模板广告","945610474","5119641","","10",""}));
        excelFileAdConfVoList.add(new ExcelFileAdConfVo(new String[]{"ios","banner","banner","穿山甲","穿山甲banner","945800683","5119641","","0",""}));
        List<Ad> adList = excelConfigService.convertToAd(excelFileAdConfVoList,product);
        adList.forEach(adAdminService::insertAd);
    }
}
