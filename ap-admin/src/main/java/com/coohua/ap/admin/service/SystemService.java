package com.coohua.ap.admin.service;

/**
 * <pre>
 *  系统服务
 * <hr/>
 * Package Name : com.coohua.nap.service
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/5/30 下午1:07
 * </pre>
 */
public interface SystemService {
//    /**
//     * 删除用户缓存
//     * @param userId 用户ID
//     * @param product 用户产品
//     */
//    void delUserStrategyByProduct(String userId, int product);
//
//    String forceUpdateUserStrategy(Long userId, int groupId, Long adId, int product);
//
//    Object doDebug(String userId, String version, int product, String os) throws Exception;
//
//    /**
//     * 查询是否是作弊用户
//     *  0 - 未知
//     *  1 - 是作弊用户
//     *  2 - 不是作弊用户
//     * @param userId 用户ID
//     * @param product 产品标识
//     * @return 作弊用户标识
//     */
//    int cheatUser(String userId, int product);
//
//    /**
//     * 获取用户的Redis缓存-策略缓存信息
//     * @param userId 用户ID
//     * @param product 产品标识
//     * @return 缓存信息
//     */
//    String getUserRedisInfo(String userId, int product);
//
//    /**
//     * 获取用户账户信息
//     * @param userId 用户ID
//     * @param product 用户产品标识
//     * @return 用户账户信息
//     */
//    String getUserInfo(String userId, int product);
//
//    /**
//     * 删除用户账户信息
//     * @param userId 用户ID
//     * @param product 用户产品标识
//     * @return 用户账户信息
//     */
//    long delUserInfo(String userId, int product);
//
//    String getBaseKey(String userId, String version);
}
