package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.controller.vo.TagInfoVO;
import com.coohua.ap.admin.controller.vo.UserTagVO;
import com.coohua.ap.admin.mapper.ap.UserTagMapper;
import com.coohua.ap.admin.model.UserTagEntity;
import com.coohua.ap.admin.service.UserTagService;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.base.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: UserTagServiceImpl
 * @Description: 用户标签服务
 * @Author: fan jin yang
 * @Date: 2020/5/6
 * @Version: V1.0
 **/
@Service
@Slf4j
public class UserTagServiceImpl implements UserTagService {

    @Autowired
    private UserTagMapper userTagMapper;

    @Override
    public List<UserTagVO> queryUserTag(int product) {
        List<UserTagEntity> entities = userTagMapper.queryByProduct(product);
        List<UserTagVO> ret = new ArrayList<>();
        for (UserTagEntity entity : entities) {
            ret.add(convertEntityToVO(entity));
        }
        return ret;
    }

    @Override
    public UserTagEntity addUserTag(UserTagVO userTagVO, int product) {
        UserTagEntity entity = convertVoToEntity(userTagVO, product);
        int state = userTagMapper.addUserTag(entity);
        if (state != 1 || entity.getId() == null) {
            throw new BusinessException(500, "insert user tag error.");
        }
        return userTagMapper.queryById(entity.getId());
    }

    @Override
    public UserTagEntity updateUserTag(UserTagVO userTagVO, int product) {
        UserTagEntity entity = convertVoToEntity(userTagVO, product);
        int state = userTagMapper.updateUserTag(entity);
        if (state != 1) {
            throw new BusinessException(500, "update user tag error.");
        }
        return userTagMapper.queryById(userTagVO.getId());
    }

    private UserTagVO convertEntityToVO(UserTagEntity entity) {
        UserTagVO userTagVO = new UserTagVO();
        userTagVO.setId(entity.getId());
        userTagVO.setName(entity.getName());
        userTagVO.setComment(entity.getComment());
        userTagVO.setUpdateTime(DateUtils.formatDateForYMDHMS(entity.getUpdateTime()));
        userTagVO.setTagInfo(JSONObject.parseObject(entity.getTag(), TagInfoVO.class));
        return userTagVO;
    }

    private UserTagEntity convertVoToEntity(UserTagVO userTagVO, int product) {
        UserTagEntity entity = new UserTagEntity();
        entity.setName(userTagVO.getName());
        entity.setComment(userTagVO.getComment());
        entity.setProduct(product);
        entity.setTag(JSONObject.toJSONString(userTagVO.getTagInfo()));
        if (userTagVO.getId() != null) {
            entity.setId(userTagVO.getId());
        }
        return entity;
    }
}
