package com.coohua.ap.admin.mapper.ap;


import com.coohua.ap.admin.model.AdInformationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <pre>
 * Comments:
 *  广告信息Mapper接口
 *  重构版
 *
 * <pre/>
 * <hr/>
 * Created by <PERSON><PERSON><PERSON> on 2017/2/27.
 */
public interface AdInformationMapper {

    /**
     * 获取广告相关信息，包括定向信息、预算信息、广告额外信息。
     * @return 广告信息列表
     */
    List<AdInformationEntity> getAll();

    AdInformationEntity getById(@Param("id") int id);

    int updateStateChangeTime(@Param("id") long id, @Param("updateStateTime") Date updateStateTime);
}
