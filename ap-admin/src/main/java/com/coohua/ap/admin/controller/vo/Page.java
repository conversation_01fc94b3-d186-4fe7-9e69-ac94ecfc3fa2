package com.coohua.ap.admin.controller.vo;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Joey
 * Desc: 分页类
 * Date: 2016/9/18
 * Version: 1.0
 */
public class Page<T> implements Serializable {
    private static final long serialVersionUID = -7160794849698245816L;

    /**
     * 查询的应用
     */
    private int appId = 0;

    /**
     * 页码从1开始
     */
    private int pageNo = 1;

    /**
     * 一页包含元素个数
     */
    private int pageSize = 10;

    /**
     * 总数
     */
    private int count;

    private List<T> items;

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<T> getItems() {
        return items;
    }

    public void setItems(List<T> items) {
        this.items = items;
    }

    public int getAppId() {
        return appId;
    }

    public void setAppId(int appId) {
        this.appId = appId;
    }

    @Override
    public String toString() {
        return "Page{" +
                "appId=" + appId +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", count=" + count +
                ", items=" + items +
                '}';
    }
}
