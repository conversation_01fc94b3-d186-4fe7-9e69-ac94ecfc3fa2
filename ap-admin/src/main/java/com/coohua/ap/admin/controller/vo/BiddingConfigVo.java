package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.TbApBidding;
import com.coohua.ap.admin.utils.third.DateUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
@Data
public class BiddingConfigVo {
    /**
     * id
     */
    private Integer id;

    /**
     * product
     */
    private Integer product;

    /**
     * ad_pos_type
     */
    private Integer adPosType;

    /**
     * 广告id
     */
    private Long adId;

    /**
     * 1-开 0-关
     */
    private Integer switchFlag;

    /**
     * 1-可用 0已删除
     */
    private Integer delFlag;

    /**
     * create_time
     */
    private String createTime;

    /**
     * update_time
     */
    private String updateTime;

    private Integer startEcpm;

    private Integer endEcpm;

    private Integer priority;

    private Integer biddingType;

    private Integer playType;

    private String adName;

    private String adTypeName;

    public BiddingConfigVo build(TbApBidding tbApBidding){
        BiddingConfigVo biddingConfigVo = new BiddingConfigVo();
        biddingConfigVo.setAdId(tbApBidding.getAdId());
        biddingConfigVo.setAdPosType(tbApBidding.getAdPosType());
        biddingConfigVo.setCreateTime(DateUtil.dateToStringWithTime(tbApBidding.getCreateTime()));
        biddingConfigVo.setUpdateTime(DateUtil.dateToStringWithTime(tbApBidding.getUpdateTime()));
        biddingConfigVo.setDelFlag(tbApBidding.getDelFlag());
        biddingConfigVo.setId(tbApBidding.getId());
        biddingConfigVo.setProduct(tbApBidding.getProduct());
        biddingConfigVo.setSwitchFlag(tbApBidding.getSwitchFlag());
        biddingConfigVo.setStartEcpm(tbApBidding.getStartEcpm());
        biddingConfigVo.setEndEcpm(tbApBidding.getEndEcpm());
        biddingConfigVo.setPriority(tbApBidding.getPriority());
        biddingConfigVo.setBiddingType(tbApBidding.getBiddingType()== null?2:tbApBidding.getBiddingType());
        biddingConfigVo.setPlayType(tbApBidding.getBiddingType()== null?1:tbApBidding.getPlayType());
        return biddingConfigVo;
    }
}
