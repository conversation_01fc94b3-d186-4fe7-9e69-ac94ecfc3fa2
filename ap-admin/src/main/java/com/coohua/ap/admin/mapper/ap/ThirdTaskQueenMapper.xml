<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdTaskQueenMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdTaskQueen" >
        <result column="task_id" property="taskId" />
        <result column="batch_no" property="batchNo" />
        <result column="task_param" property="taskParam" />
        <result column="task_status" property="taskStatus" />
        <result column="retry_times" property="retryTimes" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                task_id,
                batch_no,
                task_param,
                task_status,
                remark,
                retry_times,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdTaskQueen">
        INSERT INTO third_task_queen
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != taskId ">
                task_id,
            </if>
            <if test="null != batchNo and '' != batchNo">
                batch_no,
            </if>
            <if test="null != taskParam and '' != taskParam">
                task_param,
            </if>
            <if test="null != remark and '' != remark">
                remark,
            </if>
            <if test="null != taskStatus">
                task_status,
            </if>
            <if test="null != retryTimes">
                retry_times,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != taskId">
                #{taskId},
            </if>
            <if test="null != batchNo and '' != batchNo">
                #{batchNo},
            </if>
            <if test="null != taskParam and '' != taskParam">
                #{taskParam},
            </if>
            <if test="null != taskStatus">
                #{taskStatus},
            </if>
            <if test="null != remark and '' != remark">
                #{remark},
            </if>
            <if test="null != retryTimes">
                #{retryTimes},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_task_queen
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdTaskQueen">
        UPDATE third_task_queen
        <set>
            <if test="null != taskStatus">task_status = #{taskStatus},</if>
            <if test="null != remark and '' != remark">remark = #{remark},</if>
            <if test="null != retryTimes">retry_times = #{retryTimes},</if>
            <if test="null != updateTime ">update_time = #{updateTime}</if>
        </set>
        WHERE task_id = #{taskId}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_task_queen
        WHERE task_id = #{taskId}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_task_queen
        <if test="null != batchNo and '' != batchNo">where batch_no = #{batchNo}</if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_task_queen
        <if test="null != batchNo and '' != batchNo">where  batch_no = #{batchNo}</if>
    </select>

</mapper>