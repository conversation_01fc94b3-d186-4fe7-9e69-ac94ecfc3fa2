package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ChuanShanJiaTempReplace;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/28
 */
public interface ChuanShanJiaTempReplaceMapper {

    @Select("select * from chuanshanjia_temp_replace where product = #{product} and is_used = 0")
    List<ChuanShanJiaTempReplace> queryByProduct(@Param("product")Integer product);

    @Update("update chuanshanjia_temp_replace set is_used = 1 where id = #{id}")
    int updateToUsed(@Param("id") Integer id);
}
