package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.BiddingConfigVo;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mapper.ap.TbApBiddingMapper;
import com.coohua.ap.admin.model.AdInfo;
import com.coohua.ap.admin.model.AdInfoEntity;
import com.coohua.ap.admin.model.TbApBidding;
import com.coohua.ap.admin.model.TbApSubsidiesConfig;
import com.coohua.ap.base.constants.AdPosType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.OSType;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
@Slf4j
@Service
public class BiddingConfigService {

    @Resource
    private TbApBiddingMapper tbApBiddingMapper;
    @Resource
    private AdInfoMapper adInfoMapper;

    public Page<BiddingConfigVo> queryList(Page<BiddingConfigVo> page, Integer product,String adId){
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<TbApBidding> tbApBiddingList = tbApBiddingMapper.pageList(from,page.getPageSize(),product,adId);
        List<ThirdAdModel> thirdAdModelList = adInfoMapper.queryByAppJustNameAndType(product);
        Map<Long,ThirdAdModel> thirdAdModelMap = thirdAdModelList.stream().collect(Collectors.toMap(ThirdAdModel::getId, r->r,(r1, r2)->r1));
        page.setCount(tbApBiddingMapper.pageListCount(from,page.getPageSize(),product,adId));
        page.setItems(tbApBiddingList.stream().map(tbApBidding -> {
            BiddingConfigVo biddingConfigVo = new BiddingConfigVo().build(tbApBidding);
            if (tbApBidding.getPriority() == null){
                tbApBidding.setPriority(0);
            }
            ThirdAdModel thirdAdModel = thirdAdModelMap.get(tbApBidding.getAdId());
            biddingConfigVo.setAdName(thirdAdModel.getName());
            biddingConfigVo.setAdTypeName(AdTypeSub.get(thirdAdModel.getType()).getTypeDesc());
            return biddingConfigVo;
        }).collect(Collectors.toList()));
        return page;
    }

    public void insert(Integer product,Integer posType,Long adId,Integer startEcpm,Integer endEcpm,
                       Integer priority, Integer biddingType,Integer playType){
        checkBasic(posType,product,adId,startEcpm,endEcpm);
        TbApBidding bidding = new TbApBidding();
        bidding.setAdPosType(posType);
        bidding.setDelFlag(1);
        bidding.setSwitchFlag(1);
        bidding.setProduct(product);
        bidding.setAdId(adId);
        bidding.setStartEcpm(startEcpm);
        bidding.setEndEcpm(endEcpm);
        if (adInfoMapper.queryAdByProductIsGdt(product,adId) > 0){
            log.info("GDT 广告 可设置竞价模式..");
            bidding.setBiddingType(biddingType);
        }else {
            bidding.setBiddingType(2);
        }
        bidding.setPlayType(playType);
        bidding.setPriority(priority);
        Date now = new Date();
        bidding.setCreateTime(now);
        bidding.setUpdateTime(now);
        tbApBiddingMapper.insert(bidding);
    }

    private void checkBasic(Integer posType,Integer product,Long adId,Integer startEcpm,Integer endEcpm){
        AdPosType adPosType = AdPosType.get(posType);
        Optional.ofNullable(adPosType).orElseThrow(() -> new RuntimeException("不存在的广告位置类型！"));
        AdInfoEntity adInfoEntity = adInfoMapper.selectOneAd(adId,product);
        Optional.ofNullable(adInfoEntity).orElseThrow(() -> new RuntimeException("未获取到广告！"));
        Optional.ofNullable(startEcpm).orElseThrow(() -> new RuntimeException("Ecpm配置不能为空！"));
        Optional.ofNullable(endEcpm).orElseThrow(() -> new RuntimeException("Ecpm配置不能为空！"));
        if (startEcpm > endEcpm){
            throw new RuntimeException("Ecpm配置错误！");
        }
    }

    public void update(Integer id,Integer product,Integer posType,Long adId,Integer startEcpm,Integer endEcpm,
                       Integer priority,Integer biddingType,Integer playType){
        checkBasic(posType,product,adId,startEcpm,endEcpm);
        TbApBidding tbApBidding = tbApBiddingMapper.load(id);
        Optional.ofNullable(tbApBidding).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));

        TbApBidding tbApBiddingRecord = new TbApBidding();
        tbApBiddingRecord.setId(id);
        tbApBiddingRecord.setAdId(adId);
        tbApBiddingRecord.setAdPosType(posType);
        tbApBiddingRecord.setStartEcpm(startEcpm);
        tbApBiddingRecord.setEndEcpm(endEcpm);
        tbApBiddingRecord.setPriority(priority);
        if (adInfoMapper.queryAdByProductIsGdt(product,adId) > 0){
            log.info("GDT 广告 可设置竞价模式..");
            tbApBiddingRecord.setBiddingType(biddingType);
        }else {
            tbApBiddingRecord.setBiddingType(2);
        }
        tbApBiddingRecord.setPlayType(playType);
        tbApBiddingRecord.setUpdateTime(new Date());
        tbApBiddingMapper.update(tbApBiddingRecord);
    }

    public void switchFlag(Integer id,Integer switchFlag){
        TbApBidding tbApBidding = tbApBiddingMapper.load(id);
        Optional.ofNullable(tbApBidding).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));
        TbApBidding tbApBiddingRecord = new TbApBidding();
        tbApBiddingRecord.setId(id);
        tbApBiddingRecord.setSwitchFlag(switchFlag);
        tbApBiddingRecord.setUpdateTime(new Date());
        tbApBiddingMapper.update(tbApBiddingRecord);
    }

    public void delFlag(Integer id,Integer delFlag){
        TbApBidding tbApBidding = tbApBiddingMapper.load(id);
        Optional.ofNullable(tbApBidding).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));
        TbApBidding tbApBiddingRecord = new TbApBidding();
        tbApBiddingRecord.setId(id);
        tbApBiddingRecord.setDelFlag(delFlag);
        tbApBiddingRecord.setUpdateTime(new Date());
        tbApBiddingMapper.update(tbApBiddingRecord);
    }

    public List<TbApBidding> findPerkBiddingList(TbApSubsidiesConfig config, Integer switchFlag, Integer adType){
        return tbApBiddingMapper.queryReplaceBidding(config.getAppId(), OSType.find(config.getOs()).getCode(), adType, switchFlag);
    }

    public List<AdInfo> findPerkAdInfoList(TbApSubsidiesConfig config, Integer switchFlag, Integer adType){
        return tbApBiddingMapper.queryAdInfo(config.getAppId(), OSType.find(config.getOs()).getCode(), adType, switchFlag);
    }
}
