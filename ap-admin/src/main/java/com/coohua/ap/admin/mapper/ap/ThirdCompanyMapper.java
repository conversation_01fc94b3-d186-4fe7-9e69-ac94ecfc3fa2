package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.model.ThirdLogEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 广告主体
 */
@Mapper
@Repository
public interface ThirdCompanyMapper {

    /**
     * [新增]
     **/
    int insert(ThirdCompanyEntity thirdCompany);

    /**
     * [刪除]
     **/
    int delete(int id);

    /**
     * [更新]
     **/
    int update(ThirdCompanyEntity thirdCompany);

    /**
     * [查询] 根据主键 id 查询
     **/
    ThirdCompanyEntity load(int id);

    /**
     * [查询] 分页查询
     **/
    List<ThirdCompanyEntity> pageList(@Param("offset") int offset,@Param("pageSize")int pagesize,@Param("name") String name);

    /**
     * [查询] 分页查询 count
     **/
    int pageListCount(@Param("offset") int offset,@Param("pageSize")int pagesize,@Param("name") String name);


    @Select({"select id from third_company"})
    List<Integer> selectAll();

    @Select({"select * from third_company"})
    List<ThirdCompanyEntity> queryAll();

    @Select({"select count(1) from third_company where main_body = #{mainBody}"})
    int countExist(String mainBody);
}
