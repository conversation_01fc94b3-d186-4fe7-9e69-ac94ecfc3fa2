package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.TbApTaskModel;
import com.coohua.ap.base.utils.DateUtils;
import lombok.Data;

import java.util.Date;

/**
 * <pre>
 *  任务
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/20
 */
@Data
public class TaskVO {

    /**
     * 任务ID
     */
    private Integer id;
    /**
     * 应用ID
     */
    private Integer appId;
    /**
     * 任务类型
     */
    private Integer taskType;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务类型名称
     */
    private String taskTypeName;
    /**
     * 广告位ID
     */
    private int posId;
    /**
     * 任务配置
     */
    private String taskConfig;
    /**
     * 优先级
     */
    private int priority;
    /**
     * 用户标签配置id
     */
    private int userTagId;
    /**
     * 任务配置状态false-未启用，true-启用
     */
    private boolean state;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 任务创建时间
     */
    private String createTime;
    /**
     * 任务最后更改时间
     */
    private String updateTime;

    public TaskVO() {
    }

    public TaskVO(TbApTaskModel model) {
        this.id = model.getId();
        this.appId = model.getAppId();
        this.taskType = model.getTaskType();
        this.taskName = model.getTaskName();
        this.taskConfig = model.getTaskConfig();
        this.createTime = DateUtils.formatDateForYMDHMS(model.getCreateTime());
        this.updateTime = DateUtils.formatDateForYMDHMS(model.getUpdateTime());
        this.description = model.getDescription();
        this.priority = model.getPriority();
        this.userTagId = model.getUserTagId();
        this.state = model.getState()==0?false:true;
        this.posId = model.getPosId();
    }
}
