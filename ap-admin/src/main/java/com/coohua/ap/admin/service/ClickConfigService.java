package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.ClickConfigVo;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.ClickConfigMapper;
import com.coohua.ap.admin.model.ClickConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@Slf4j
@Service
public class ClickConfigService {

    @Resource
    private ClickConfigMapper clickConfigMapper;

    public void list(Page<ClickConfigVo> page,int appId){
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<ClickConfig> clickConfigList = clickConfigMapper.pageList(from,page.getPageSize(),appId);
        page.setItems(clickConfigList.stream().map(clickConfig -> {
            ClickConfigVo vo = new ClickConfigVo();
            vo.setId(clickConfig.getId());
            vo.setExposure(clickConfig.getExposure());
            vo.setClickA(clickConfig.getClickA());
            vo.setClickB(clickConfig.getClickB());
            vo.setRateA(clickConfig.getRateA());
            vo.setRateB(clickConfig.getRateB());
            return vo;
        }).collect(Collectors.toList()));
        page.setCount(clickConfigMapper.pageListCount(from,page.getPageSize(),appId));
    }

    public void insert(ClickConfigVo vo,int appId){
        ClickConfig clickConfig = new ClickConfig();

        if (clickConfigMapper.countThisProductExposureCount(appId,vo.getExposure()) > 0){
            throw new RuntimeException("当前曝光配置已存在...");
        }
        clickConfig.setAppId(appId);
        Date now = new Date();
        clickConfig.setExposure(vo.getExposure());
        clickConfig.setClickA(vo.getClickA());
        clickConfig.setClickB(vo.getClickB());
        clickConfig.setRateA((double)vo.getClickA()/vo.getExposure() * 100);
        clickConfig.setRateB((double)vo.getClickB()/vo.getExposure() * 100);
        clickConfig.setDelFlag(1);
        clickConfig.setCreateTime(now);
        clickConfig.setUpdateTime(now);

        clickConfigMapper.insert(clickConfig);
    }

    public void edit(ClickConfigVo vo){
        ClickConfig clickConfig = clickConfigMapper.load(vo.getId());
        Optional.ofNullable(clickConfig).orElseThrow(() -> new RuntimeException("未查询到相关配置"));

        clickConfig.setExposure(vo.getExposure());
        clickConfig.setClickA(vo.getClickA());
        clickConfig.setClickB(vo.getClickB());
        clickConfig.setRateA((double)vo.getClickA()/vo.getExposure() * 100);
        clickConfig.setRateB((double)vo.getClickB()/vo.getExposure() * 100);
        clickConfig.setUpdateTime(new Date());
        clickConfigMapper.update(clickConfig);
    }

    public void delete(Integer id){
        ClickConfig clickConfig = clickConfigMapper.load(id);
        Optional.ofNullable(clickConfig).orElseThrow(() -> new RuntimeException("未查询到相关配置"));
        clickConfig.setDelFlag(0);
        clickConfig.setUpdateTime(new Date());
        clickConfigMapper.update(clickConfig);
    }
}
