package com.coohua.ap.admin.service.third;

import com.coohua.ap.admin.mapper.ap.TbApThirdConfigMapper;
import com.coohua.ap.admin.mapper.ap.ThirdAdPosMapper;
import com.coohua.ap.admin.mapper.ap.ThirdAppMapper;
import com.coohua.ap.admin.model.TbApThirdConfig;
import com.coohua.ap.admin.model.ThirdAdPosEntity;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ThirdPlatformType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/12/2
 */
@Slf4j
@Service
public class ThirdConfigService {

    @Resource
    private ThirdAppMapper thirdAppMapper;
    @Resource
    private ThirdAdPosMapper thirdAdPosMapper;
    @Autowired
    private ThirdCompanyService thirdCompanyService;
    @Resource
    private TbApThirdConfigMapper tbApThirdConfigMapper;

    /**
     * 根据POS_ID 查询APP_ID
     * @param posId POS_ID
     * @return 映射关系
     */
    public Map<Long, ThirdAppEntity> queryThirdApp(List<Long> posId){
        List<TbApThirdConfig> tbApThirdConfigs = tbApThirdConfigMapper.queryByPosId(posId);

        Map<Long, ThirdAppEntity> queryResult = tbApThirdConfigs.stream().collect(Collectors.toMap(TbApThirdConfig::getPosId,
                tbApThirdConfig -> {
                    ThirdPlatformType platform = AdTypeSub.getPlatform(tbApThirdConfig.getAdType());
                    ThirdAppEntity entity = thirdAppMapper.queryByProductAndOsAndAppId(tbApThirdConfig.getProduct(),tbApThirdConfig.getOs(),
                            tbApThirdConfig.getAppId(),platform.getCode());
                    if (entity == null){
                        return new ThirdAppEntity();
                    }
                    return entity;
                },(r1,r2)->r1));
        List<Long> notFund = posId.stream().filter(r -> !queryResult.containsKey(r)).collect(Collectors.toList());

        if (notFund.size() == 0){
            return queryResult;
        }
        List<ThirdAdPosEntity> entities = thirdAdPosMapper.queryListByPosId(notFund);
        if (entities.size() > 0){
            Date now = new Date();
            String dates = DateUtil.dateToString(now)+ " 00:00:00";
            List<ThirdAdPosEntity> rs = entities.stream()
                    .filter(r -> r.getCreateTime().before(DateUtil.stringToDate(dates)))
                    .collect(Collectors.toList());
            if (rs.size() > 0){
                List<Integer> ids = rs.stream().map(ThirdAdPosEntity::getApplicationId).collect(Collectors.toList());
                List<ThirdAppEntity> thirdAppEntities = thirdAppMapper.queryList(ids);
                Map<Integer,ThirdAppEntity> thirdAppEntityMap = thirdAppEntities.stream()
                        .collect(Collectors.toMap(ThirdAppEntity::getId,r->r,(r1,r2)->r1));

                for (ThirdAdPosEntity posEntity : rs){
                    ThirdAppEntity entity = thirdAppEntityMap.getOrDefault(posEntity.getApplicationId(),new ThirdAppEntity());
                    queryResult.put(posEntity.getPosId(),entity);
                }
            }
        }
        return queryResult;
    }

    /**
     * 根据POS_ID 查询COMPANY
     * @return 映射关系
     */
    public Map<Long, ThirdCompanyEntity> queryThirdCompany(Map<Long,ThirdAppEntity> queryMap){
        List<ThirdCompanyEntity> companyEntities = thirdCompanyService.queryAll();
        Map<Integer,ThirdCompanyEntity> thirdCompanyEntityMap = companyEntities.stream()
                .collect(Collectors.toMap(ThirdCompanyEntity::getId,r->r,(r1,r2) -> r1));
        return queryMap.keySet().stream().collect(Collectors.toMap(r->r,r->{
            ThirdAppEntity entity = queryMap.get(r);
            return thirdCompanyEntityMap.getOrDefault(entity.getCompanyId(),new ThirdCompanyEntity());
        },(r1,r2) -> r1));
    }

    public List<TbApThirdConfig> queryAdId(Long posId){
        return tbApThirdConfigMapper.queryByPosIdSingle(posId);
    }
}
