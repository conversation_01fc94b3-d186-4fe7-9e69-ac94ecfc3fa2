package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdTemplateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/11
 */
@Mapper
@Repository
public interface ThirdTemplateMapper {
    /**
     * [新增]
     **/
    int insert(ThirdTemplateEntity thirdTemplate);

    /**
     * [刪除]
     **/
    int delete(int id);

    /**
     * [更新]
     **/
    int update(ThirdTemplateEntity thirdTemplate);

    /**
     * [查询] 根据主键 id 查询
     **/
    ThirdTemplateEntity load(int id);

    /**
     * [查询] 分页查询
     **/
    List<ThirdTemplateEntity> pageList(@Param("offset") int offset, @Param("pageSize")int pagesize, @Param("name") String name);

    /**
     * [查询] 分页查询 count
     **/
    int pageListCount(@Param("offset") int offset,@Param("pageSize")int pagesize,@Param("name") String name);

    @Select({"select count(1) from third_template where model_name = #{modelName}"})
    int countExist(String modelName);
}
