package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mq.MqConfigAdmin;
import com.coohua.ap.admin.utils.Env;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.MessageTag;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/11
 */
@Slf4j
@Service
public class RefreshRedisService {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    @Resource(name = "produceMq")
    private ProducerBean producerBean;
    @Autowired
    private MqConfigAdmin mqConfig;

    @Resource
    private AdInfoMapper adInfoMapper;
    @Autowired
    private Env env;

    public void refreshRedis(Long id){
        ThirdAdModel thirdAdModel = adInfoMapper.getById(id);
        if (thirdAdModel == null){
            return;
        }
        String key = RedisBuilder.buildAdInfo(id);
        apClusterRedisService.set(key, JSON.toJSONString(thirdAdModel));
//        apClusterRedisService.expire(key, RedisConstants.TIME_DAY_1);
        if (!env.isTest()) {
            producerBean.send(new Message(mqConfig.getTopic(), MessageTag.AD_CONFIG.getTag(), JSON.toJSONBytes(thirdAdModel)));
        }
    }

    public void removeFormRedis(Long id){
        String key = RedisBuilder.buildAdInfo(id);
        apClusterRedisService.del(key);
    }

    public void addAdId(Integer appId){
        List<Long> ids = adInfoMapper.queryAllIds(appId);
        if (ids == null){
            return;
        }
        String key = RedisBuilder.buildAppAdList(appId);
        apClusterRedisService.set(key, JSON.toJSONString(ids));
    }
}
