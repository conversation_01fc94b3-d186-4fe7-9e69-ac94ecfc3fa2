package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.aop.OperateLog;
import com.coohua.ap.admin.aop.OperateType;
import com.coohua.ap.admin.constants.OperateLogType;
import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mapper.ap.ConfigBaseMapper;
import com.coohua.ap.admin.mapper.ap.ConfigOrientationMapper;
import com.coohua.ap.admin.model.ConfigBaseEntity;
import com.coohua.ap.admin.model.ConfigOrientationEntity;
import com.coohua.ap.admin.service.ConfigBaseService;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.base.utils.ListUtils;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 *      配置管理服务实现
 * <hr/>
 * Package Name : com.coohua.nap.service.impl
 * Project Name : nap
 * Created by Zhimin Xu on 2018/3/13 上午10:56
 * </pre>
 */
@Service
public class ConfigBaseServiceImpl implements ConfigBaseService, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Resource
    private ConfigBaseMapper configBaseMapper;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private ConfigOrientationMapper configOrientationMapper;

    @Override
    public void listConfig(Page<ConfigBaseVO> page, int userProduct, int type, String name, int state) {
        ConfigBaseEntity entity = new ConfigBaseEntity();
        entity.setProduct(userProduct);
        entity.setType(type);
        entity.setName(name);
        entity.setState(state);
        entity.setId(0);
        entity.setPosType(0);
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<ConfigBaseEntity> entityList = configBaseMapper.queryAllByProduct(entity, from, page.getPageSize(),1);
        Long count = configBaseMapper.countAllByProduct(entity,1);
        List<ConfigBaseVO> vos = entityList.parallelStream().map(this::convertConfigBaseEntity2VO).collect(Collectors.toList());

        page.setItems(vos);
        page.setCount(count.intValue());
    }

    @Override
    @OperateLog(type = OperateType.INSERT,logType = OperateLogType.STRATEGY_CONFIG_CONFIGS)
    public ConfigBaseVO saveConfig(ConfigBaseVO configBaseVO) {
        ConfigBaseEntity entity = convertConfigBaseVO2Entity(configBaseVO);
        configBaseMapper.insertConfig(entity);
        return convertConfigBaseEntity2VO(entity);
    }

    @Override
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.STRATEGY_CONFIG_CONFIGS)
    public ConfigBaseVO updateConfig(ConfigBaseVO configBaseVO) {
        ConfigBaseEntity entity = convertConfigBaseVO2Entity(configBaseVO);
        entity.setId(configBaseVO.getId());
        configBaseMapper.updateConfig(entity);
        return convertConfigBaseEntity2VO(entity);
    }

    @Override
    @OperateLog(type = OperateType.UPDATE_STATE,logType = OperateLogType.STRATEGY_CONFIG_CONFIGS)
    public ConfigBaseVO updateConfigState(ConfigBaseVO configBaseVO) {
        ConfigBaseEntity entity = new ConfigBaseEntity();
        entity.setId(configBaseVO.getId());
        entity.setState(configBaseVO.isState() ? 1 : 0);
        int i = configBaseMapper.updateConfigState(entity);
        if(i==0){
            return null;
        }
        return configBaseVO;
    }

    @Override
    public ConfigBaseVO queryById(Integer id) {
        ConfigBaseEntity entity = configBaseMapper.queryOneById(id);
        ConfigOrientationEntity configOrientationEntity = configOrientationMapper.queryByConfigBase(entity.getProduct(), id);
        ConfigBaseVO baseVO = convertConfigBaseEntity2VO(entity);
        baseVO.setConfigPre(convertPre(baseVO.getType(),baseVO.getConfig(),baseVO.getProduct(),configOrientationEntity));
        return baseVO;
    }

    private ConfigBaseEntity convertConfigBaseVO2Entity(ConfigBaseVO configBaseVO) {
        ConfigBaseEntity entity = new ConfigBaseEntity();
        entity.setProduct(configBaseVO.getProduct());
        entity.setComment(configBaseVO.getComment());
        entity.setConfig(configBaseVO.getConfig());
        entity.setName(configBaseVO.getName());
        entity.setState(configBaseVO.isState() ? 1 : 0);
        entity.setDelFlag(1);
        entity.setPosType(configBaseVO.getPosType());
        entity.setType(configBaseVO.getType());
        return entity;
    }

    private ConfigBaseVO convertConfigBaseEntity2VO(ConfigBaseEntity ent) {
        ConfigBaseVO vo = new ConfigBaseVO();
        vo.setId(ent.getId());
        vo.setProduct(ent.getProduct());
        vo.setComment(ent.getComment());
        vo.setConfig(ent.getConfig());
        vo.setCreateTime(ent.getCreateTime());
        vo.setUpdateTime(ent.getUpdateTime());
        vo.setName(ent.getName());
        vo.setState(ent.getState() == 1);
        vo.setDelFlag(ent.getDelFlag());
        vo.setPosType(ent.getPosType());
        vo.setType(ent.getType());
//        vo.setConfigPre(convertPre(ent.getType(),ent.getConfig(),ent.getProduct()));
        return vo;
    }

    private String convertPre(Integer configType, String config, Integer product, ConfigOrientationEntity configOrientationEntity){
        try {
            List<Integer> adTypes = getAllAdType(configType,config);
            List<ThirdAdModel> models = adInfoMapper.getByType(adTypes,product,configOrientationEntity.getOs());
            Map<Integer,Integer> ecpmMap = models.stream()
                    .filter(rx -> rx.getState() == 1)
                    .collect(Collectors.toMap(ThirdAdModel::getType, r-> r.getEcpm() == null?0:r.getEcpm(),(r1, r2)->r1));
            return rebuildEcpm(configType,config,ecpmMap);
        }catch (Exception e){
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 获取锁区和受限的AdType
     * @return 产品-AdTypeSet Map
     */
    public Map<Integer,Set<Integer>> queryLockOrLimitAdType(){
        List<ConfigBaseEntity> queryConfigBaseList = configBaseMapper.queryLimitOrLockedConfig();
        Map<Integer,Set<Integer>> baseMap = new HashMap<>();
        queryConfigBaseList.forEach(configBaseEntity -> {
           Set<Integer> rs = baseMap.get(configBaseEntity.getProduct());
           if (rs == null){
               rs = new HashSet<>();
           }
           List<Integer> adTypes = getAllAdType(configBaseEntity.getType(),configBaseEntity.getConfig());
           rs.addAll(adTypes);
           baseMap.put(configBaseEntity.getProduct(),rs);
        });
        return baseMap;
    }


    private List<Integer> getAllAdType(Integer configType,String config){
        Set<Integer> adTypeSet = new HashSet<>();
        if (configType == ConfigType.AD_CONFIG.code()) {
            JSONArray array = JSONArray.parseArray(config);
            for (int index = 0; index < array.size(); index++) {
                JSONObject item = array.getJSONObject(index);
                String adType = item.getString("adType");
                adTypeSet.addAll(ListUtils.parseRateStringToList(adType));
            }
        } else if (configType == ConfigType.AD_DEFAULT_CONFIG.code()
                || configType == ConfigType.APP_DEFAULT_CONFIG.code()
                || configType == ConfigType.APP_DEFAULT_GOLD_CONFIG.code()) {
            JSONArray array = JSONArray.parseArray(config);
            for (int index = 0; index < array.size(); index++) {
                JSONObject item = array.getJSONObject(index);
                Integer targetType = item.getInteger("targetType");
                adTypeSet.add(targetType);
                JSONArray innerArray = item.getJSONArray("layer");

                for (int j = 0; j < innerArray.size(); j++) {
                    String types = innerArray.getString(j);
                    adTypeSet.addAll(ListUtils.parseRateStringToList(types));
                }
            }
        }
        return new ArrayList<>(adTypeSet);
    }

    private String rebuildEcpm(Integer configType,String config,Map<Integer,Integer> ecpmMap){
        if (configType == ConfigType.AD_CONFIG.code()) {
            JSONArray array = JSONArray.parseArray(config);
            for (int index = 0; index < array.size(); index++) {
                JSONObject item = array.getJSONObject(index);
                String adType = item.getString("adType");
                item.put("adType",filterConvert(adType,ecpmMap));
            }
            return array.toJSONString();
        } else if (configType == ConfigType.AD_DEFAULT_CONFIG.code()
                || configType == ConfigType.APP_DEFAULT_CONFIG.code()
                || configType == ConfigType.APP_DEFAULT_GOLD_CONFIG.code()) {
            JSONArray array = JSONArray.parseArray(config);
            for (int index = 0; index < array.size(); index++) {
                JSONObject item = array.getJSONObject(index);
                JSONArray innerArray = item.getJSONArray("layer");
                JSONArray newInnerArray = new JSONArray();
                for (int j = 0; j < innerArray.size(); j++) {
                    String types = innerArray.getString(j);
                    newInnerArray.add(filterConvert(types,ecpmMap));
                }
                item.put("layer",newInnerArray);
            }
            return array.toJSONString();
        }
        return "";
    }

    private String filterConvert(String filter,Map<Integer,Integer> ecpmMap){
        return Arrays.stream(filter.split("[|]")).map(t -> t.split("_")).map(tp -> {
            Integer price = ecpmMap.getOrDefault(Integer.valueOf(tp[0]), 0);
            StringBuilder result = new StringBuilder();
            return result.append(tp[0]).append("_").append(tp[1]).append("_price:").append(price);
        }).collect(Collectors.joining("|"));
    }
}
