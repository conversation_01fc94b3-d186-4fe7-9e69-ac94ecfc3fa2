<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.CreateRecordMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.CreateRecord" >
        <result column="id" property="id" />
        <result column="logday" property="logday" />
        <result column="product" property="product" />
        <result column="product_name" property="productName" />
        <result column="os" property="os" />
        <result column="old_ad_id" property="oldAdId" />
        <result column="old_pos_id" property="oldPosId" />
        <result column="old_pos_name" property="oldPosName" />
        <result column="new_pos_id" property="newPosId" />
        <result column="new_pos_name" property="newPosName" />
        <result column="pv_rate" property="pvRate" />
        <result column="create_type" property="createType" />
        <result column="create_type_name" property="createTypeName" />
        <result column="ad_source" property="adSource" />
        <result column="sum_income" property="sumIncome" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="replace_status" property="replaceStatus" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                logday,
                product,
                product_name,
                os,
                old_ad_id,
                old_pos_id,
                old_pos_name,
                new_pos_id,
                new_pos_name,
                pv_rate,
                create_type,
                create_type_name,
                ad_source,
                sum_income,
                create_time,
                update_time,
                replace_status
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.CreateRecord">
        INSERT INTO create_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != logday">
                logday,
            </if>
            <if test="null != product and '' != product">
                product,
            </if>
            <if test="null != productName and '' != productName">
                product_name,
            </if>
            <if test="null != os and '' != os">
                os,
            </if>
            <if test="null != oldAdId">
                old_ad_id,
            </if>
            <if test="null != oldPosId">
                old_pos_id,
            </if>
            <if test="null != oldPosName and '' != oldPosName">
                old_pos_name,
            </if>
            <if test="null != newPosId">
                new_pos_id,
            </if>
            <if test="null != newPosName and '' != newPosName">
                new_pos_name,
            </if>
            <if test="null != pvRate">
                pv_rate,
            </if>
            <if test="null != createType">
                create_type,
            </if>
            <if test="null != createTypeName and '' != createTypeName">
                create_type_name,
            </if>
            <if test="null != adSource">
                ad_source,
            </if>
            <if test="null != sumIncome">
                sum_income,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != replaceStatus">
                replace_status
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != logday">
                #{logday},
            </if>
            <if test="null != product and '' != product">
                #{product},
            </if>
            <if test="null != productName and '' != productName">
                #{productName},
            </if>
            <if test="null != os and '' != os">
                #{os},
            </if>
            <if test="null != oldAdId">
                #{oldAdId},
            </if>
            <if test="null != oldPosId">
                #{oldPosId},
            </if>
            <if test="null != oldPosName and '' != oldPosName">
                #{oldPosName},
            </if>
            <if test="null != newPosId">
                #{newPosId},
            </if>
            <if test="null != newPosName and '' != newPosName">
                #{newPosName},
            </if>
            <if test="null != pvRate">
                #{pvRate},
            </if>
            <if test="null != createType">
                #{createType},
            </if>
            <if test="null != createTypeName and '' != createTypeName">
                #{createTypeName},
            </if>
            <if test="null != adSource and '' != adSource">
                #{adSource},
            </if>
            <if test="null != sumIncome">
                #{sumIncome},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != replaceStatus">
                #{replaceStatus}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM create_record
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.CreateRecord">
        UPDATE create_record
        <set>
            <if test="null != logday">logday = #{logday},</if>
            <if test="null != product and '' != product">product = #{product},</if>
            <if test="null != productName and '' != productName">product_name = #{productName},</if>
            <if test="null != os and '' != os">os = #{os},</if>
            <if test="null != oldAdId">old_ad_id = #{oldAdId},</if>
            <if test="null != oldPosId">old_pos_id = #{oldPosId},</if>
            <if test="null != oldPosName and '' != oldPosName">old_pos_name = #{oldPosName},</if>
            <if test="null != newPosId">new_pos_id = #{newPosId},</if>
            <if test="null != newPosName and '' != newPosName">new_pos_name = #{newPosName},</if>
            <if test="null != pvRate">pv_rate = #{pvRate},</if>
            <if test="null != createType">create_type = #{createType},</if>
            <if test="null != createTypeName and '' != createTypeName">create_type_name = #{createTypeName},</if>
            <if test="null != adSource and '' != adSource">ad_source = #{adSource},</if>
            <if test="null != sumIncome">sum_income = #{sumIncome},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != replaceStatus">replace_status = #{replaceStatus}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM create_record
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM create_record
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM create_record
    </select>

</mapper>