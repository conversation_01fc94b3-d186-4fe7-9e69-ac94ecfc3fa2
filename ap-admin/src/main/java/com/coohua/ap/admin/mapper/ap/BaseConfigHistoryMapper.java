package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.BaseConfigHistoryEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface BaseConfigHistoryMapper {

    @Insert("insert into tb_ap_config_base_history (config_id,app_id,config_old,config_new,create_time,update_time)" +
            " values " +
            "(#{configId},#{appId},#{configOld},#{configNew},now(),now())")
    int addBaseConfigHistory(BaseConfigHistoryEntity baseConfigHistoryEntity);

    @Select("select id as id,config_id as configId,app_id as appId,config_old as configOld,config_new as configNew,create_time as createTime,update_time as updateTime" +
            " from tb_ap_config_base_history order by update_time desc")
    List<BaseConfigHistoryEntity> queryAll();

    @Select("select id as id,config_id as configId,app_id as appId,config_old as configOld,config_new as configNew,create_time as createTime,update_time as updateTime" +
            " from tb_ap_config_base_history order by update_time desc limit #{pageNo},#{pageSize}")
    List<BaseConfigHistoryEntity> queryByPage(@Param("pageNo") int pageNo,@Param("pageSize") int pageSize);

    @Select("select count(*) from tb_ap_config_base_history")
    int queryCount();
}
