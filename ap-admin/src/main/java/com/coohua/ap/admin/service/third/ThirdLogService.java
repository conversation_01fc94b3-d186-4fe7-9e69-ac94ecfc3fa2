package com.coohua.ap.admin.service.third;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.OpType;
import com.coohua.ap.admin.controller.vo.ThirdLogView;
import com.coohua.ap.admin.mapper.ap.ThirdLogMapper;
import com.coohua.ap.admin.model.ThirdLogEntity;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.admin.utils.third.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@Service
public class ThirdLogService {


    @Resource
    private ThirdLogMapper thirdLogMapper;


    public void saveLog(String account, String opName, OpType opType, Integer id, String before, String after ){
        ThirdLogEntity entity = new ThirdLogEntity();
        Date now = new Date();

        entity.setOpId(account);
        entity.setOpName(opName);
        entity.setOpType(opType.type);
        entity.setOpTypeDesc(opType.desc);
        entity.setContent(String.format(opType.content,opName, DateUtil.dateToString(now,DateUtil.DATETIME_PATTERN),id));

        entity.setSubId(id);
        entity.setBefore(before);
        entity.setAfter(after);

        entity.setCreateTime(now);
        entity.setUpdateTime(now);

        thirdLogMapper.insert(entity);
    }


    public List<ThirdLogView> list(Integer subId, Integer type){
        String types = convertTypes(type);
        List<ThirdLogEntity> entityList = thirdLogMapper.queryThirdLog(subId,types);


        return entityList.stream().map(thirdLogEntity -> {
            ThirdLogView view = new ThirdLogView();

            view.setId(thirdLogEntity.getId());
            view.setAfter(thirdLogEntity.getAfter());
            view.setBefore(thirdLogEntity.getBefore());
            view.setContent(thirdLogEntity.getContent());
            view.setDesc(convertDesc(thirdLogEntity));
            view.setCreateTime(DateUtil.dateToString(thirdLogEntity.getCreateTime(),DateUtil.DATETIME_PATTERN));
            view.setOpName(thirdLogEntity.getOpName());

            return view;
        }).collect(Collectors.toList());
    }

    private String convertTypes(Integer type){
        switch (type){
            case 1: return OpType.ADD_COMPANY.type + "," + OpType.UPDATE_COMPANY.type;
            case 2: return OpType.ADD_APPLICATION.type + "," + OpType.UPDATE_APPLICATION.type;
            case 3: return OpType.ADD_ADPOS.type + "," + OpType.UPDATE_ADPOS.type + "," + OpType.LOAD_ADPOS.type;
            case 4: return OpType.ADD_TEMPLATE.type + "," + OpType.UPDATE_TEMPLATE.type;
            default:throw new RuntimeException("Type错误！");
        }
    }

    private static String convertDesc(ThirdLogEntity entity){
        if (Strings.isEmpty(entity.getBefore())){
            return entity.getBefore();
        }
        List<String> desc = new ArrayList<>();
        // 比对两组json 只保留不同的值
        JSONObject jsonObjectAfter = JSONObject.parseObject(entity.getAfter());
        JSONObject jsonObjectBefore = JSONObject.parseObject(entity.getBefore());

        for (String key : jsonObjectAfter.keySet()){
            Object oB = jsonObjectBefore.get(key);
            if (Objects.isNull(oB)){
                continue;
            }
            Object oA = jsonObjectAfter.get(key);
            if (oA.equals(oB)){
                continue;
            }
            desc.add(key + ":" + oB.toString() + " ---> " + oA.toString());
        }
        return StringUtils.join(desc,"\n");
    }
}
