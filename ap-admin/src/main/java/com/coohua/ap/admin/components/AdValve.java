package com.coohua.ap.admin.components;

import com.coohua.ap.admin.controller.vo.Ad;

import java.util.List;

/**
 * Description:
 *  过滤器，责任链模式
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.components
 * @create_time 2019-11-04
 */
public interface AdValve {

    List<Ad> filter(List<Ad> adList);

    AdValve getPrev();

    void setPrev(AdValve prev);

    AdValve getNext();

    void setNext(AdValve next);
}
