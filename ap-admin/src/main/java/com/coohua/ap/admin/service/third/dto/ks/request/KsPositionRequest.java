package com.coohua.ap.admin.service.third.dto.ks.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KsPositionRequest extends KsBasicSignRequest{
    private String appId;
    private String name;
    private Long positionId;
    private Integer adStyle;
    private Integer skipAdMode;
    private Integer renderType;
    private List<Integer> materialTypeList;
    private Integer templateId;
    private Long shieldId;
    private Integer rewardedType;
    private Integer rewardedNum;
    private Integer callbackStatus;
    private String callbackUrl;

    private Integer countdownShow;
    private Integer voice;

    private Integer priceStrategy;
    private Integer biddingStrategy;
    //广告铺开大小
    private Integer adRolloutSize;
}
