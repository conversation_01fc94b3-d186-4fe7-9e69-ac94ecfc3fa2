package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.base.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriComponentsBuilder;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: GDTReportVo
 * @Description: 广点通数据请求模板
 * @Author: fan jin yang
 * @Date: 2020/5/8
 * @Version: V1.0
 **/
@Slf4j
public class GDTReportVo extends DefaultReportVO {

    private Map<String,String> header = new HashMap<>();

    public GDTReportVo(String memberId,String secretId) {
        this.url = "https://api.adnet.qq.com/open/v1.0/report/get";
        this.memberId = memberId;
        this.secretId = secretId;
    }

    @Override
    void buildRequestTime(String ...date) {
        //日期格式转换默认传入格式为yyyy-MM-dd
        if(date!=null&&date.length==2){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            this.startTime = simpleDateFormat.format(DateUtils.parseDate(date[0],"yyyy-MM-dd"));
            this.endTime = simpleDateFormat.format(DateUtils.parseDate(date[1],"yyyy-MM-dd"));
        }else {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            Date yesterday = new Date(System.currentTimeMillis()-24*3600*1000);
            this.startTime = simpleDateFormat.format(yesterday);
            this.endTime = simpleDateFormat.format(yesterday);
        }

    }

    @Override
    void generateSign(){
        try {
            String time = String.valueOf(System.currentTimeMillis()/1000);
            String sign = getSha1((memberId+secretId+time).getBytes());
            this.sign = getBase64(memberId,time,sign);
            header.put("Content-Type","multipart/form-data");
            header.put("token",this.sign);
        } catch (NoSuchAlgorithmException e) {
            log.error("guang dian tong generate sign error, e = {} ",e);
        }
    }

    @Override
    void generateUrl() {
        UriComponentsBuilder uriComponentsBuilder =  UriComponentsBuilder.fromUriString(this.url)
                .queryParam("member_id",memberId)
                .queryParam("start_date",startTime)
                .queryParam("end_date",endTime)
                ;
        this.requestUrl = uriComponentsBuilder.build().toUri();
    }

    private static String getSha1(byte[] input) throws NoSuchAlgorithmException {
        MessageDigest mDigest = MessageDigest.getInstance("SHA1");
        byte[] result = mDigest.digest(input);
        StringBuilder sb = new StringBuilder();
        for (byte b : result) {
            sb.append(Integer.toString((b & 0xff) + 0x100, 16).substring(1));
        }
        return sb.toString();
    }

    private static String getBase64(String memberId, String time, String sign) {
        String combine = memberId + "," + time + "," + sign;
        byte[] res = combine.getBytes();
        Base64.Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(res);
    }

    public Map<String, String> getHeader() {
        return header;
    }
}
