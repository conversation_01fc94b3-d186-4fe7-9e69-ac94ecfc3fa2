package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.controller.vo.TbApSubsidiesConfigVO;
import com.coohua.ap.admin.model.TbApDiminishMonitor;
import com.coohua.ap.admin.model.TbApSubsidiesConfig;
import com.coohua.ap.admin.model.TbApSubsidiesReplaceRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

public interface TbApSubsidiesConfigMapper {

    List<TbApSubsidiesConfig> pageList(@Param("offset") int offset, @Param("pageSize") int pagesize);

    @Select("select * from tb_ap_subsidies_config")
    List<TbApSubsidiesConfigVO> queryAll();

    int pageListCount(@Param("offset") int offset,@Param("pageSize") int pagesize);

    TbApSubsidiesConfig load(Long id);


    @Select("select * from tb_ap_subsidies_config where is_enabled = 1 and status = #{status} ")
    List<TbApSubsidiesConfig> queryAllValidAndInsert(@Param("status") Integer status);

    @Select("select * from tb_ap_subsidies_config where id = #{id} ")
    TbApSubsidiesConfig queryById(@Param("id") Integer id);

    @Select("select * from tb_ap_subsidies_config where os = #{os} and app_id = #{appId} and is_ab_test = 1 and ab_test = #{categoryId} and strategy_name like concat('%',#{strategyName},'%') order by id desc limit 1")
    TbApSubsidiesConfig queryReplaceByAbTest(@Param("os") String os, @Param("appId") Integer appId, @Param("categoryId") Integer categoryId, @Param("strategyName") String strategyName);

    @Select("select * from tb_ap_subsidies_config where os = #{os} and app_id = #{appId} and is_ab_test = 1 and has_bidding = 1 order by id desc limit 1")
    TbApSubsidiesConfig queryReplaceBiddingConfig(@Param("os") String os, @Param("appId") Integer appId);


    @Update("update tb_ap_subsidies_config set status = 1 where id = #{id} ")
    void updateStatus(@Param("id") Long id);

    @Select("select * from tb_ap_subsidies_config where is_enabled = 1 and is_ab_test = 1 and status = 1 and os = #{os} and product = #{product} and strategy_name like concat('%',#{strategyName},'%') order by id desc limit 1")
    List<TbApSubsidiesConfig> queryValidByProduct(@Param("os") String os, @Param("product") String product, @Param("strategyName") String strategyName);

    @Update("update tb_ap_subsidies_config set is_enabled = #{isEnabled} where id = #{id}")
    void queryByProduct(@Param("id") Long id, @Param("isEnabled") Integer isEnabled);

    @Update("update tb_ap_subsidies_config set is_enabled = #{isEnabled} where id = #{id}")
    void updateEnable(@Param("id") Long id, @Param("isEnabled") Integer isEnabled);

    @Insert("insert into tb_ap_subsidies_config (strategy_name,os,app_id,product,product_name,ad_type_name,platform,ab_test,is_ab_test,config_priority,layer_ecpm,auto_generate_ecpm,priority,is_enabled,status)" +
            " values " +
            "(#{strategyName},#{os},#{appId},#{product},#{productName},#{adTypeName},#{platform},#{abTest},#{isAbTest},#{configPriority},#{layerEcpm},#{autoGenerateEcpm},#{priority},#{isEnabled},#{status})")
    void insert(TbApSubsidiesConfig tbApSubsidiesConfig);



    @Insert(" insert into tb_ap_subsidies_replace_record(`logday`,`app_id`, `product`, `product_name`, `os`,`platform`,`strategy_name`,`ab_test`,`create_type`,`create_time`,`update_time`) " +
            " values (#{logday}, #{config.appId}, #{config.product}, #{config.productName}, #{config.os}, #{config.platform}, #{config.strategyName}, #{config.abTest}, #{createType}, #{date}, #{date})")
    void insertReplaceRecord(@Param("config") TbApSubsidiesConfig config, @Param("logday") String logday, @Param("date")  Date date, @Param("createType") Integer createType);

    @Select("select * from tb_ap_subsidies_replace_record where app_id = #{monitor.appId} and os = #{monitor.os} and ab_test = #{monitor.categoryId} " +
            "and create_time >= DATE_SUB(NOW(), INTERVAL '6:20' HOUR_MINUTE)  ")
    List<TbApSubsidiesReplaceRecord> queryReplaceRecord(@Param("monitor") TbApDiminishMonitor monitor);

    @Select("select * from tb_ap_subsidies_config where id = #{id} and has_bidding = 1")
    TbApSubsidiesConfig queryByIdForBidding(@Param("id") Integer id);
}
