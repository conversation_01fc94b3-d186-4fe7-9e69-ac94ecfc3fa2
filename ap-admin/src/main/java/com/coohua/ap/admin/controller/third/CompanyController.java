package com.coohua.ap.admin.controller.third;

import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.service.third.ThirdCompanyService;
import com.coohua.ap.admin.utils.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

import static com.coohua.ap.admin.controller.vo.BaseResponse.CODE_SYS_ERROR;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@RestController
@RequestMapping("third/company")
public class CompanyController {

    @Autowired
    private ThirdCompanyService thirdCompanyService;

    @RequestMapping("list")
    @ResponseBody
    public BaseResponse list(@RequestParam(value = "name",required = false) String name, Page<ThirdCompanyEntity> page, HttpServletRequest request) {
        int from = (page.getPageNo() - 1) * page.getPageSize();
        thirdCompanyService.queryList(from,page.getPageSize(),name,page);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping("addOne")
    public BaseResponse addOne(  @RequestParam("platformCode") Integer platformCode,
                                 @RequestParam("mainBody") String mainBody,
                                 @RequestParam("userId") String userId,
                                 @RequestParam(value = "roleId",required = false) Integer roleId,
                                 @RequestParam("securityKey") String securityKey,
                                 @RequestParam(value = "maskRuleId",required = false) Integer maskRuleId,
                               HttpServletRequest request) {
        try {
            ThirdCompanyEntity thirdCompanyEntity = new ThirdCompanyEntity();
            thirdCompanyEntity.setPlatformCode(platformCode);
            Platform platform = Platform.getPlatform(platformCode);
            thirdCompanyEntity.setPlatformDesc(platform.getDesc());
            thirdCompanyEntity.setMainBody(mainBody);
            thirdCompanyEntity.setUserId(userId);
            thirdCompanyEntity.setRoleId(roleId);
            thirdCompanyEntity.setSecurityKey(securityKey);
            thirdCompanyEntity.setMaskRuleId(maskRuleId);
            Date now = new Date();
            thirdCompanyEntity.setMainStatus(1);
            thirdCompanyEntity.setCreateTime(now);
            thirdCompanyEntity.setUpdateTime(now);
            thirdCompanyService.insert(thirdCompanyEntity,SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }

        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("updateOne")
    public BaseResponse updateOne(@RequestParam("id") Integer id,
                                  @RequestParam("platformCode") Integer platformCode,
                                  @RequestParam("mainBody") String mainBody,
                                  @RequestParam("userId") String userId,
                                  @RequestParam(value = "roleId",required = false) Integer roleId,
                                  @RequestParam("securityKey") String securityKey,
                                  @RequestParam(value = "maskRuleId",required = false) Integer maskRuleId,
                                  HttpServletRequest request) {
        ThirdCompanyEntity entity = new ThirdCompanyEntity();
        entity.setId(id);
        entity.setPlatformCode(platformCode);
        entity.setMainBody(mainBody);
        entity.setUserId(userId);
        entity.setRoleId(roleId);
        entity.setSecurityKey(securityKey);
        entity.setMaskRuleId(maskRuleId);
        thirdCompanyService.update(entity,SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("delete")
    public BaseResponse deleteCompany(@RequestParam("id") Integer id,HttpServletRequest request){
        thirdCompanyService.delete(id ,SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }
}
