package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.web.vo
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/14 下午5:57
 * </pre>
 */
@Data
public class ConfigOrientationVO {

    private Integer id;

    private String name;

    private Integer adPos;

    private Integer product;

    private Integer os;

    private Integer region;

    private Integer regionSide;

    private Integer anonymous;

    private RegistVO regist;

    private IncomeVO income;

    private VersionVO version;

    private TfPlatformVo tfPlatform;

    private String tailNumber;

    private String userPkg;

    private String channelId;

    private String manufacturer;

    private VersionVO sdkVersion;

    private Map<String, Integer> config;

    private boolean state;

    private String abTest;

    private Integer priority;

    private String dsp;

    private Integer lockActionPoint;

    private Date createTime;

    private Date updateTime;

}
