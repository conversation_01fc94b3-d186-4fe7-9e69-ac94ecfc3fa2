package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.TbApReplacePosConfigRequest;
import com.coohua.ap.admin.controller.vo.TbApReplacePosConfigVO;
import com.coohua.ap.admin.controller.vo.TbApReplacePosPlatformConfigVO;
import com.coohua.ap.admin.mapper.ap.TbApReplacePosConfigMapper;
import com.coohua.ap.admin.model.TbApReplacePosConfig;
import com.coohua.ap.base.constants.AdPosType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class TbApReplacePosConfigService {

    @Autowired
    private TbApReplacePosConfigMapper tbApReplacePosConfigMapper;

    private static final Integer rewardedVideo = AdPosType.Rewarded_video.getCode();
    private static final Integer tablePlaque = AdPosType.Table_plaque.getCode();



    public Page<TbApReplacePosConfigVO> queryList(Page<TbApReplacePosConfigVO> page) throws IOException {
        int from = (page.getPageNo() - 1) * page.getPageSize();

        List<TbApReplacePosConfig> tbApReplacePosConfigList = tbApReplacePosConfigMapper.pageList(from,page.getPageSize());
        page.setCount(tbApReplacePosConfigMapper.pageListCount(from,page.getPageSize()));
        List<TbApReplacePosConfigVO> tbApReplacePosConfigVOList = new ArrayList<>();
        for (TbApReplacePosConfig tbApReplacePosConfig : tbApReplacePosConfigList){
            TbApReplacePosConfigVO tbApReplacePosConfigVO = new TbApReplacePosConfigVO();
            tbApReplacePosConfigVO = new TbApReplacePosConfigVO().build(tbApReplacePosConfig);
            tbApReplacePosConfigVOList.add(tbApReplacePosConfigVO);
            page.setItems(tbApReplacePosConfigVOList);
        }
        return page;
    }

    public List<TbApReplacePosConfigVO> queryAllValid(){
        List<TbApReplacePosConfig> tbApReplacePosConfigList = tbApReplacePosConfigMapper.getAllValid();
        List<TbApReplacePosConfigVO> tbApReplacePosConfigVOList = new ArrayList<>();
        try {
            for (TbApReplacePosConfig tbApReplacePosConfig : tbApReplacePosConfigList) {
                TbApReplacePosConfigVO tbApReplacePosConfigVO = new TbApReplacePosConfigVO();
                BeanUtils.copyProperties(tbApReplacePosConfig, tbApReplacePosConfigVO);
                ObjectMapper objectMapper = new ObjectMapper();
                List<TbApReplacePosPlatformConfigVO> platFormList = null;

                platFormList = objectMapper.readValue(tbApReplacePosConfig.getPlatformConfig(), new TypeReference<List<TbApReplacePosPlatformConfigVO>>() {
                });

                tbApReplacePosConfigVO.setPlatformConfig(platFormList);
                tbApReplacePosConfigVOList.add(tbApReplacePosConfigVO);
            }
        } catch (IOException e) {
            log.error("替换广告位配置数据bean转换失败", e);
            throw new RuntimeException("替换广告位配置数据bean转换失败！");
        }
        return tbApReplacePosConfigVOList;
    }

    public void update(TbApReplacePosConfigRequest tbApReplacePosConfigRequest) {

        TbApReplacePosConfig record = tbApReplacePosConfigMapper.load(tbApReplacePosConfigRequest.getId());
        Optional.ofNullable(record).orElseThrow(() -> new RuntimeException("要修改的记录不存在！"));
        try {
            TbApReplacePosConfig tbApReplacePosConfig = new TbApReplacePosConfig();
            tbApReplacePosConfig.setId(tbApReplacePosConfigRequest.getId());
            tbApReplacePosConfig.setStrategyName(tbApReplacePosConfigRequest.getStrategyName());
            tbApReplacePosConfig.setOs(tbApReplacePosConfigRequest.getOs());
            tbApReplacePosConfig.setProduct(tbApReplacePosConfigRequest.getProduct());
            tbApReplacePosConfig.setAdTypeName(tbApReplacePosConfigRequest.getAdTypeName());
            tbApReplacePosConfig.setDsp(tbApReplacePosConfigRequest.getDsp());
            ObjectMapper objectMapper = new ObjectMapper();
            String platFormConfig = objectMapper.writeValueAsString(tbApReplacePosConfigRequest.getPlatformConfig());
            tbApReplacePosConfig.setPlatformConfig(platFormConfig);
            tbApReplacePosConfig.setExtend1(tbApReplacePosConfigRequest.getExtend1());
            tbApReplacePosConfig.setExtend2(tbApReplacePosConfigRequest.getExtend2());
            tbApReplacePosConfig.setIsEnabled(tbApReplacePosConfigRequest.getIsEnabled());
            tbApReplacePosConfig.setIsDeleted(tbApReplacePosConfigRequest.getIsDeleted());

            tbApReplacePosConfigMapper.update(tbApReplacePosConfig);
        } catch (Exception e) {
            log.error("修改广告替换数据失败", e);
            throw new RuntimeException("修改广告替换数据失败！");
        }

    }

    @Transactional
    public void insert(TbApReplacePosConfigRequest tbApReplacePosConfigRequest) {
        TbApReplacePosConfig tbApReplacePosConfig = new TbApReplacePosConfig();
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            tbApReplacePosConfig.setStrategyName(tbApReplacePosConfigRequest.getStrategyName());
            tbApReplacePosConfig.setOs(tbApReplacePosConfigRequest.getOs());
            tbApReplacePosConfig.setProduct(tbApReplacePosConfigRequest.getProduct());
            tbApReplacePosConfig.setAdTypeName(tbApReplacePosConfigRequest.getAdTypeName());
            tbApReplacePosConfig.setDsp(tbApReplacePosConfigRequest.getDsp());
            String platFormConfig = objectMapper.writeValueAsString(tbApReplacePosConfigRequest.getPlatformConfig());
            tbApReplacePosConfig.setPlatformConfig(platFormConfig);
            tbApReplacePosConfig.setExtend1(tbApReplacePosConfigRequest.getExtend1());
            tbApReplacePosConfig.setExtend2(tbApReplacePosConfigRequest.getExtend2());
            tbApReplacePosConfig.setIsEnabled(tbApReplacePosConfigRequest.getIsEnabled());
            tbApReplacePosConfig.setIsDeleted(tbApReplacePosConfigRequest.getIsDeleted());

            Date now = new Date();
            tbApReplacePosConfig.setCreateTime(now);
            tbApReplacePosConfig.setUpdateTime(now);

            tbApReplacePosConfigMapper.insert(tbApReplacePosConfig);

        } catch (Exception e) {
            log.error("插入广告替换数据失败", e);
            throw new RuntimeException("插入广告替换数据失败！");
        }
    }

    public void deleteOne(Integer id) {
        try {
            TbApReplacePosConfig tbApReplacePosConfig = tbApReplacePosConfigMapper.load(id);
            Optional.ofNullable(tbApReplacePosConfig).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));
            tbApReplacePosConfigMapper.deleteOne(id);
        } catch (Exception e) {
            log.error("删除广告替换数据失败", e);
            throw new RuntimeException("删除广告替换数据失败！");
        }
    }

    public void updateEnable(Integer id, Integer isEnabled) {
        TbApReplacePosConfig tbApReplacePosConfig = tbApReplacePosConfigMapper.load(id);
        Optional.ofNullable(tbApReplacePosConfig).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));
        try {
            tbApReplacePosConfigMapper.updateEnable(id, isEnabled);
        } catch (Exception e) {
            log.error("修改广告替换状态失败", e);
            new RuntimeException("修改广告替换状态失败！");
        }
    }
}
