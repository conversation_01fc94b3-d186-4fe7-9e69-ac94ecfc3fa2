package com.coohua.ap.admin.constants;

import com.coohua.ap.admin.controller.vo.ExcelFileVo;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: CommonField
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/7/30
 * @Version: 1.0.0
 **/
public class CommonField {

    public static final String XLS = "xls";
    public static final String XLSX = "xlsx";

    private static Map<Integer,Map<Integer, ExcelFileVo>> excelContainer;


    public static Map<Integer, Map<Integer, ExcelFileVo>> getExcelContainer() {
        return excelContainer;
    }

    public static void setExcelContainer(Map<Integer, Map<Integer, ExcelFileVo>> excelContainer) {
        CommonField.excelContainer = excelContainer;
    }
}
