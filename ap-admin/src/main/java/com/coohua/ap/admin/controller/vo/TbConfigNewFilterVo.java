package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/24
 */
@Data
public class TbConfigNewFilterVo {
    private Integer id;

    private Integer indexIx;

    /**
     * config_id
     */
    private Integer configId;

    /**
     * product
     */
    private Integer product;

    /**
     * switch_flag
     */
    private Integer switchFlag;

    /**
     * del_flag
     */
    private Integer delFlag;

    /**
     * ad_type_name
     */
    private List<SubFilterVo> adTypeList;

    /**
     * price
     */
    private Integer price;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * update_time
     */
    private Date updateTime;

    /**
     * 是否可排序
     */
    private boolean disableSort;


    @Data
    public static class SubFilterVo{
        // 广告类型
        private Integer adType;
        // 广告类型名称
        private String adTypeName;
        // 比率e
        private Integer ret;
    }
}
