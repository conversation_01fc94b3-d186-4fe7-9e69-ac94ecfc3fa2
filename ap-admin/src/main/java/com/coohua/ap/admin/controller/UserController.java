package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.UserVO;
import com.coohua.ap.admin.service.UserService;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/03/19
 */
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    @RequestMapping("/list")
    public BaseResponse list(@RequestParam("username") String username) {
        BaseResponse response = new BaseResponse(0);
        List<UserVO> ret = userService.listUser(username);
        response.setData(ret);
        return response;
    }

    @RequestMapping("/addUser")
    public BaseResponse addUser(@RequestBody UserVO userVO) {
        BaseResponse response = new BaseResponse(0);
        if (userVO.getAppId() == null || StringUtils.isEmpty(userVO.getUsername()) || StringUtils.isEmpty(userVO.getPassword()) || StringUtils.isEmpty(userVO.getName())) {
            throw new BusinessException(400, "参数缺失");
        }
        App app = AppBuilder.getById(userVO.getAppId());
        if (app == null) {
            throw new BusinessException(400, "非法应用ID");
        }
        userVO.setAppName(app.getProductName());
        int state = userService.addUser(userVO);
        if (state != 1) {
            throw new BusinessException(500, "添加用户异常");
        }
        response.setData("ok");
        return response;
    }

    @RequestMapping("/updateUser")
    public BaseResponse updateUser(@RequestBody UserVO userVO) {
        BaseResponse response = new BaseResponse(0);
        if (userVO.getId() == null || StringUtils.isEmpty(userVO.getUsername()) || StringUtils.isEmpty(userVO.getPassword()) || StringUtils.isEmpty(userVO.getName())) {
            throw new BusinessException(400, "参数缺失");
        }
        int state = userService.updateUser(userVO);
        if (state != 1) {
            throw new BusinessException(500, "更新用户异常");
        }
        response.setData("ok");
        return response;
    }
}
