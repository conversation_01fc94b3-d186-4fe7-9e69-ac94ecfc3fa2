package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ClickConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description click_config
 */
@Mapper
@Repository
public interface ClickConfigMapper {

    /**
     * 新增
     **/
    int insert(ClickConfig clickConfig);

    /**
     * 刪除
     **/
    int delete(int id);

    /**
     * 更新
     **/
    int update(ClickConfig clickConfig);

    /**
     * 查询 根据主键 id 查询
     **/
    ClickConfig load(int id);

    /**
     * 查询 分页查询
     **/
    List<ClickConfig> pageList(@Param("offset") int offset, @Param("pageSize") int pagesize, @Param("appId") Integer appId);

    /**
     * 查询 分页查询 count
     **/
    int pageListCount(@Param("offset") int offset,@Param("pageSize") int pagesize, @Param("appId") Integer appId);

    @Select({"select count(1) from click_config where del_flag = 1 and app_id = #{appId} and exposure=#{exposure}"})
    int countThisProductExposureCount(@Param("appId") Integer appId,@Param("exposure") Integer exposure);
}