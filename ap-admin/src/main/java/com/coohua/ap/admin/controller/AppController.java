package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.AppShortNameVO;
import com.coohua.ap.admin.controller.vo.AppVO;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.service.AppService;
import com.coohua.ap.admin.service.BasicAppService;
import com.coohua.ap.admin.service.DefaultConfigService;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/03/19
 */
@RestController
@RequestMapping("/app")
public class AppController extends BaseController {

    @Autowired
    private AppService appService;
    @Autowired
    private BasicAppService basicAppService;
    @Autowired
    private DefaultConfigService defaultConfigService;

    @RequestMapping("/list")
    public BaseResponse list(@RequestParam("name") String name) {
        List<AppVO> appList = appService.list(name);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(appList);
        return baseResponse;
    }

    @Data
    private static class AppDict{
        private String appId;
        private String product;
        private String productName;
    }

    @RequestMapping("/dist")
    public BaseResponse dist() {
        List<AppDict> appList = basicAppService.queryBasicList().stream()
                .map(r -> {
                    AppDict dict = new AppDict();
                    dict.setAppId(String.valueOf(r.getAppId()));
                    dict.setProduct(r.getProduct());
                    dict.setProductName(r.getProductName());
                    return dict;
                }).collect(Collectors.toList());
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(appList);
        return baseResponse;
    }

    @RequestMapping("/addApp")
    public BaseResponse add(@RequestBody AppVO appVO) {
        App app = AppBuilder.getById(Integer.valueOf(appVO.getAppId()));
        if (app == null){
            throw new BusinessException(400, "参数缺失");
        }
        appVO.setAppName(app.getProductName());
        if (StringUtils.isEmpty(appVO.getAppId()) || StringUtils.isEmpty(appVO.getAppName())) {
            throw new BusinessException(400, "参数缺失");
        }
        BaseResponse response = new BaseResponse(0);
        int state = appService.addApp(appVO);
        if (state != 1) {
            throw new BusinessException(500, "添加应用异常");
        }
        // 添加产品时初始化广告位、计划、缓存等配置目录
        defaultConfigService.init(Integer.valueOf(appVO.getAppId()),appVO.getAppPkgName());
        response.setData("OK");
        return response;
    }

    @RequestMapping("/updateApp")
    public BaseResponse update(@RequestBody AppVO appVO) {
        if (StringUtils.isEmpty(appVO.getAppId()) || StringUtils.isEmpty(appVO.getAppName())) {
            throw new BusinessException(400, "参数缺失");
        }
        BaseResponse response = new BaseResponse(0);
        int state = appService.updateApp(appVO);
        if (state != 1) {
            throw new BusinessException(500, "更新应用异常");
        }
        response.setData("OK");
        return response;
    }

    @RequestMapping("/shortNameList")
    public BaseResponse shortNameList() {
        List<AppVO> appList = appService.list(AdConstants.EMPTY_STRING);
        BaseResponse response = new BaseResponse(0);
        List<AppShortNameVO> ret = new ArrayList<>();
        for (AppVO vo : appList) {
            AppShortNameVO shortNameVO = new AppShortNameVO();
            shortNameVO.setValue(Integer.parseInt(vo.getAppId()));
            shortNameVO.setLabel(vo.getAppName());
            ret.add(shortNameVO);
        }
        response.setData(ret);
        return response;
    }
}
