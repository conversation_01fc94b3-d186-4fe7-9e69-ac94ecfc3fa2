package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.StrategyDefaultConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/10
 */
public interface StrategyDefaultConfigMapper {

    @Select("select * from tb_ap_default_strategy_config")
    List<StrategyDefaultConfig> queryDefaultConfig();

    @Update("update tb_ap_default_strategy_config set strategy_config=#{comment},update_time=#{updateTime},strategy_comment=#{config} where id =#{id}")
    int updateDefaultConfig(@Param("id") Integer id,
                            @Param("config") String config,
                            @Param("updateTime") Date updateTime,
                            @Param("comment") String comment);
}
