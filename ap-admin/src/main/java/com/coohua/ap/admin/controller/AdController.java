package com.coohua.ap.admin.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.ap.admin.components.DateAdValve;
import com.coohua.ap.admin.components.DefaultFilterValve;
import com.coohua.ap.admin.components.FilterValve;
import com.coohua.ap.admin.components.StateAdValve;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.model.AdInfoEntity;
import com.coohua.ap.admin.service.AdAdminService;
import com.coohua.ap.admin.service.BrandService;
import com.coohua.ap.admin.service.ImageService;
import com.coohua.ap.admin.utils.ListUtils;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.WeekNo;
import com.coohua.ap.base.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by liu liandong on 2017/4/12.
 */
@Controller
@RequestMapping("/ad")
@Slf4j
public class AdController extends BaseController {

    @Autowired
    private AdAdminService adAdminService;

    @Resource
    private AdInfoMapper adInfoMapper;

    @Autowired
    private ImageService imageService;

    @Autowired
    private BrandService brandService;

    private static Map<Integer, String> AD_STATE_DESC = new HashMap<>();

    static {
        AD_STATE_DESC.put(1, "投放中");
        AD_STATE_DESC.put(2, "暂停中");
        AD_STATE_DESC.put(3, "已关闭");
    }

    @RequestMapping("/addPlan")
    @ResponseBody
    public Object addPlan(@RequestBody AdPlan plan, HttpServletRequest request) {
        /*
        * 记录状态和数据；0成功；1失败
        * */
        BaseResponse ret = new BaseResponse(0);
        /*模糊*/
        plan.setProduct(SessionUtils.getUserProduct(request));
        /*进行数据添加操作*/
        AdPlan retPlan = adAdminService.insertAdPlan(plan);
        if (retPlan != null) {
            ret.setRet(0);
            ret.setData(retPlan);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/listPlan")
    @ResponseBody
    public Object listPlan(@RequestParam("name") String name, Page page, HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        List<AdPlan> list = adAdminService.selectAllAdPlans(SessionUtils.getUserProduct(request), page, name);
        if (list != null) {
            page.setItems(list);
            AdPlanWrapper adPlanWrapper = new AdPlanWrapper();
            adPlanWrapper.setPage(page);
            adPlanWrapper.setPlanList(adAdminService.selectAllAdPlansNoPage(SessionUtils.getUserProduct(request), page));
            adPlanWrapper.setIndustryVOList(adAdminService.selectAllIndustryInfo());
            ret.setRet(0);
            ret.setData(adPlanWrapper);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/updatePlan")
    @ResponseBody
    public Object updatePlan(@RequestBody AdPlan plan, HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(request);
        plan.setProduct(product);
        // 获取广告计划的旧值
//        AdPlanEntity oldValue = adInfoMapper.selectOnePlan(plan.getId(), product);
        AdPlan retPlan = adAdminService.updateAdPlan(plan);
        if (retPlan != null) {
            ret.setRet(0);
            ret.setData(retPlan);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("batchUpdate")
    @ResponseBody
    public BaseResponse batchUpdate(@RequestBody BatchUpdateAdRequest request,HttpServletRequest servletRequest){
        BaseResponse ret = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(servletRequest);
        adAdminService.updateBatch(request, product);
        return ret;
    }

    @RequestMapping("/addAd")
    @ResponseBody
    public Object addAd(@RequestBody Ad ad, HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        if (ad.getStartDate().contains("NaN") || ad.getEndDate().contains("NaN")) {
            ret.setRet(1);
            ret.setData(ad.getStartDate().contains("NaN") ? "广告投放开始时间包含NaN" : "广告投放结束时间包含NaN");
            return ret;
        }
        if (!ad.isNoEndDate() && (StringUtils.isEmpty(ad.getStartDate()) || StringUtils.isEmpty(ad.getEndDate()))) {
            ret.setRet(1);
            ret.setData("广告有结束日期，但没设置。");
            return ret;
        }

        ad.setProduct(SessionUtils.getUserProduct(request));
        Ad retAd = adAdminService.insertAd(ad);
        if (ad != null) {
            ret.setRet(0);
            ret.setData(retAd);
//            adAdminService.shareAdUrlMonitor(null, retAd);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/listAd")
    @ResponseBody
    public Object listAd(@RequestParam(name = "adName", required = false) String adName, // 用like匹配 （输入框）
                         @RequestParam(name = "adPlan", required = false) String adPlan,  // 广告计划ID （下拉框）
                         @RequestParam(name = "adId", required = false) String adId,  // 广告ID （输入框）
                         @RequestParam(name = "adType", required = false) String adType, // 广告类型 （下拉框）
                         @RequestParam(name = "status", required = false) String status, // 广告状态筛选条件，0：不限制，1：有效广告，2：无效广告
                         @RequestParam(name = "startDate", required = false) String startDate, // 查询条件：投放日期起始时间
                         @RequestParam(name = "endDate", required = false) String endDate, // 查询条件：投放日期结束时间
                         @RequestParam(name = "adPosStr", required = false) String adPosStr,
                         @RequestParam(name = "userPkg", required = false) String userPkg,
                         @RequestParam(name = "appIdThird", required = false) String appId,
                         @RequestParam(name = "posIdThird", required = false) String posId,
                         Page page,
                         HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);

        int product = SessionUtils.getUserProduct(request);
        List<Integer> adPos = null;
        if (StringUtils.isNotEmpty(adPosStr)) {
            adPos = JSONArray.parseArray(adPosStr, Integer.class);
        }
        List<Ad> list = adAdminService.selectAllAds(adName, adPlan, adId, adType,
                page, null, product, adPos, userPkg,appId,posId,status);

        if (list != null) {
            page.setItems(list);
            ret.setRet(0);
            ret.setData(page);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    private List<Integer> getIdList(List<Ad> filteredAdIds) {
        List<Integer> idList = new ArrayList<>();
        for (Ad ad : filteredAdIds) {
            idList.add(ad.getId());
        }
        return idList;
    }

    @RequestMapping("/queryAdByProduct")
    @ResponseBody
    public BaseResponse queryProductActiveAd(HttpServletRequest httpServletRequest){
        return BaseResponse.build(adInfoMapper.queryAdByProduct(SessionUtils.getUserProduct(httpServletRequest)));
    }

    @RequestMapping("/queryAdByProductGdt")
    @ResponseBody
    public BaseResponse queryAdByProductGdt(HttpServletRequest httpServletRequest){
        return BaseResponse.build(adInfoMapper.queryAdByProductGdt(SessionUtils.getUserProduct(httpServletRequest)));
    }

    @RequestMapping("/updateAd")
    @ResponseBody
    public Object updateAd(@RequestBody Ad ad, HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        if (ad.getStartDate().contains("NaN") || ad.getEndDate().contains("NaN")) {
            ret.setRet(1);
            ret.setData(ad.getStartDate().contains("NaN") ? "广告投放开始时间包含NaN" : "广告投放结束时间包含NaN");
            return ret;
        }

        int product = SessionUtils.getUserProduct(request);
        ad.setProduct(product);
        // 获取旧值
        AdInfoEntity oldValue = adInfoMapper.selectOneAd(ad.getId(), product);
        Ad retAd = adAdminService.updateAd(ad, oldValue);
        if (retAd != null) {
            ret.setRet(0);
            ret.setData(ad);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/upload")
    @ResponseBody
    public String upload(HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        String filename = imageService.upload(request);
        if (filename != null) {
            ret.setRet(0);
            ret.setData(filename);
        } else {
            ret.setRet(1);
        }
        return JSON.toJSONString(ret);
    }

    private List<Ad> doFilter(String status, String startDate, String endDate, List<Ad> allAds) {
        FilterValve filterValve = new DefaultFilterValve();
        filterValve.setHead(new StateAdValve(status)).setNext(new DateAdValve(startDate, endDate));
        return filterValve.doChain(allAds);
    }

    private static String[] CSV_HEADER = {"日期", "广告ID", "广告名称", "广告状态",
            "广告类型", "所属计划名称", "所属计划ID", "时间设置", "投放时段",
            "人工&系统投放", "预算", "最小日预算", "优先权重", "积分", "访问地址", "性别",
            "年龄", "运营商", "机型", "地域", "投放版本", "尾号定向", "标签", "应用定向", "热词","三方posId","三方appId","ECPM"};

    @RequestMapping("/export")
    @ResponseBody
    public Object export(HttpServletResponse response,
                         @RequestParam(name = "adName", required = false) String adName, // 用like匹配 （输入框）
                         @RequestParam(name = "adPlan", required = false) String adPlan,  // 广告计划ID （下拉框）
                         @RequestParam(name = "adId", required = false) String adId,  // 广告ID （输入框）
                         @RequestParam(name = "adType", required = false) String adType, // 广告类型 （下拉框）
                         @RequestParam(name = "status", required = false) String status, // 广告状态筛选条件，0：不限制，1：有效广告，2：无效广告
                         @RequestParam(name = "startDate", required = false) String startDate, // 查询条件：投放日期起始时间
                         @RequestParam(name = "endDate", required = false) String endDate, // 查询条件：投放日期结束时间
                         @RequestParam(name = "adPos", required = false) List<Integer> adPos,
                         @RequestParam(name = "userPkg", required = false) String userPkg,
                         @RequestParam(name = "appIdThird", required = false) String appId,
                         @RequestParam(name = "posIdThird", required = false) String posId,
                         HttpServletRequest request
    ) { // 广告父类型（和广告类型是级联下拉框）

        int product = SessionUtils.getUserProduct(request);
        // select 出所有广告
        List<Ad> allAds = adAdminService.selectAllAds(adName, adPlan, adId, adType, null, null, product, adPos, userPkg,appId,posId,status);

        List<Ad> filteredAdIds = doFilter(status, startDate, endDate, allAds);

        List<Ad> list;
        if (ListUtils.isEmpty(filteredAdIds)) {
            list = new ArrayList<>();
        } else {
            list = adAdminService.selectAllAds(adName, adPlan, adId, adType, null, getIdList(filteredAdIds), product, adPos, userPkg,appId,posId,status);
        }

        try {
            ServletOutputStream os = response.getOutputStream();

            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            String today = df.format(new Date());
            response.setContentType("application/octet-stream; charset=UTF-8");
            String filename = java.net.URLEncoder.encode("广告数据", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename="
                    + filename + today + ".xlsx");

            SXSSFWorkbook wb = new SXSSFWorkbook(10000);
            SXSSFSheet sheet = (SXSSFSheet) wb.createSheet("广告数据");
            SXSSFRow row = (SXSSFRow) sheet.createRow(0);
            for (int i = 0; i < CSV_HEADER.length; i++) {
                row.createCell(i).setCellValue(CSV_HEADER[i]);
            }

            int rowNumber = 1;
            for (Ad ad : list) {
                row = (SXSSFRow) sheet.createRow(rowNumber);
                rowNumber++;
                row.createCell(0).setCellValue(today); // 导出时间
                row.createCell(1).setCellValue(ad.getId()); // 广告ID
                row.createCell(2).setCellValue(ad.getName()); // 广告名称
                row.createCell(3).setCellValue(AD_STATE_DESC.get(adAdminService.getAdState(ad))); // 广告状态
                row.createCell(4).setCellValue(AdTypeSub.getTypeDescCr(ad.getType())); // 广告类型
                row.createCell(6).setCellValue(ad.getPlan().getName()); // 广告计划名称
                row.createCell(7).setCellValue(ad.getPlan().getId()); // 广告计划ID
                row.createCell(8).setCellValue(ad.getStartDate() + " - " + ad.getEndDate()); // 广告投放时间
                row.createCell(9).setCellValue(parseTimeBucketList(startDate, ad.getTimeBucketVOList())); // 广告投放时段
                row.createCell(10).setCellValue(0); // 人工&系统投放
                row.createCell(11).setCellValue(ad.getBudget().getBudget()); // 预算
                row.createCell(12).setCellValue("--"); // 最小日预算
                row.createCell(13).setCellValue(0); // 优先权重
                row.createCell(14).setCellValue("--"); // 积分
                row.createCell(15).setCellValue(StringUtils.isEmpty(ad.getExt().getClkUrl()) ? AdConstants.EMPTY_STRING : ad.getExt().getClkUrl()); // 访问地址
                row.createCell(16).setCellValue(0); // 性别定向
                row.createCell(17).setCellValue("--"); // 年龄定向
                row.createCell(18).setCellValue("ALL"); // 运营商定向
                row.createCell(19).setCellValue("9999"); // 机型定向
                row.createCell(20).setCellValue(parseRegions(ad.getOrientation().getRegions())); // 地域定向
                row.createCell(21).setCellValue(parseVersion(ad.getOrientation())); // 投放版本定向
                row.createCell(22).setCellValue(StringUtils.isEmpty(ad.getOrientation().getTailNumber()) ? AdConstants.EMPTY_STRING : ad.getOrientation().getTailNumber().replaceAll(",", "\\|")); // 尾号定向
                row.createCell(23).setCellValue("--"); // 标签定向
                row.createCell(24).setCellValue("--"); // 应用定向
                String posIdStr = ad.getOrientation().isNoIos() ? ad.getExt().getAndroidPosId() : ad.getExt().getIosPosId();
                String appIdStr = ad.getOrientation().isNoIos() ? ad.getExt().getAndroidAppId() : ad.getExt().getIosAppId();
                row.createCell(25).setCellValue(posIdStr);
                row.createCell(26).setCellValue(appIdStr);
                row.createCell(27).setCellValue(Optional.ofNullable(ad.getBudget().getEcpm()).orElse(0));
            }
            wb.write(os);
            wb.close();
            os.flush();
            os.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return new BaseResponse(0);
    }

    private String parseTimeBucketList(String startDate, List<TimeBucketVO> list) {
        if (ListUtils.isEmpty(list)) {
            return AdConstants.EMPTY_STRING;
        }

        if (StringUtils.isEmpty(startDate)) {
            startDate = DateUtils.formatDate(new Date(), DateUtils.PATTERN_YMD);
        }

        // 计算startDate星期
        int weekNo = calculateWeekNo(startDate);
        List<Double> ret = new ArrayList<>();

        for (TimeBucketVO vo : list) {
            if (vo.getWeek() == weekNo) {
                for (Bucket bucket : vo.getTime()) {
                    if (bucket.isShow()) {
                        ret.add(bucket.getTime() / 2.0);
                    }
                }
            }
        }
        return JSON.toJSONString(ret);
    }

    public int calculateWeekNo(String startDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtils.parseDate(startDate, DateUtils.PATTERN_YMD));
        int week = calendar.get(Calendar.DAY_OF_WEEK); // 获取星期，从周日开始，1-星期日，2-星期一。。。7-星期六
        return WeekNo.WEEK_NO_MAP.get(week);
    }

    private String parseRegions(List<Integer> regions) {
        if (ListUtils.isEmpty(regions)) {
            return AdConstants.EMPTY_STRING;
        }

        StringBuilder sb = new StringBuilder();
        for (Integer region : regions) {
            sb.append(region).append("|");
        }
        return sb.toString();
    }

    private String parseVersion(AdOrientation adOrientation) {
        StringBuilder sb = new StringBuilder();
        if (adOrientation.isNoIos() && !adOrientation.isNoAndroid()) {
            sb.append("iOS：不投放");
            sb.append("| Android：").append(adOrientation.getMinAndroidVersion()).append(" - ").append(adOrientation.getMaxAndroidVersion());
        } else if (!adOrientation.isNoIos() && adOrientation.isNoAndroid()) {
            sb.append("iOS：").append(adOrientation.getMinIosVersion()).append(" - ").append(adOrientation.getMaxIosVersion());
            sb.append("| Android：不投放");
        } else if (!adOrientation.isNoIos() && !adOrientation.isNoAndroid()) {
            sb.append("iOS：").append(adOrientation.getMinIosVersion()).append(" - ").append(adOrientation.getMaxIosVersion());
            sb.append("| Android：").append(adOrientation.getMinAndroidVersion()).append(" - ").append(adOrientation.getMaxAndroidVersion());
        }
        return sb.toString();
    }

//    @RequestMapping("/adPosList")
//    @ResponseBody
//    public BaseResponse adPosList() {
//        BaseResponse ret = new BaseResponse(0);
//        try {
//            List<AdminAdPosVO> vos = new ArrayList<>();
//            for (AdPos pos : AdPos.values()) {
//                AdminAdPosVO vo = new AdminAdPosVO();
//                vo.setCode(pos.code());
//                vo.setName(pos.desc());
//                vos.add(vo);
//            }
//            ret.setData(vos);
//        } catch (Exception e) {
//            ret.setRet(500);
//            ret.setData("error");
//            e.printStackTrace();
//        }
//
//        return ret;
//    }

    @RequestMapping("/brandList")
    @ResponseBody
    public BaseResponse brandList() {
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(brandService.queryBrandType());
        return baseResponse;
    }
}
