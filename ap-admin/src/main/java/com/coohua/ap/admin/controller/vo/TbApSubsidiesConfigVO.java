package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TbApSubsidiesConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private long id;
    private String strategyName;
    private String os;
    private Integer appId;
    private String product;
    private String productName;
    private String adTypeName;
    private String platform;
    private String abTest;
    private Byte isAbTest;
    private String layerEcpm;
    private String autoGenerateEcpm;
    private Integer configPriority;
    private Integer priority;
    private Integer hasBidding;
    private Byte isEnabled;
    private Byte status;
    private Date createTime;
    private Date updateTime;


}
