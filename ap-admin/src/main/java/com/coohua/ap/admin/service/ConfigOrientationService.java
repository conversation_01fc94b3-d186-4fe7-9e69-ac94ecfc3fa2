package com.coohua.ap.admin.service;


import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.ConfigOrientationVO;
import com.coohua.ap.admin.controller.vo.Page;

import java.util.List;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.service
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/14 下午6:01
 * </pre>
 */
public interface ConfigOrientationService {

    /**
     * 分页查询配置
     * @param page 分页条件
     * @param product 产品标识
     * @param name
     * @param adPos
     * @param state
     * @param id
     */
    void listConfigOrientation(Page<ConfigOrientationVO> page, int product, String name, int adPos, int state, String id);

    /**
     * 根据产品查询  此产品下的所有的配置的 ID和名称的映射
     * @param product 产品
     * @return &lt;ID, Name&gt;
     */
    List<ConfigBaseVO> listConfigBaseIdAndName(int product);

    /**
     * 新增配置
     * @param configOrientationVO 配置信息
     * @return 新增后的配置信息
     */
    ConfigOrientationVO saveConfig(ConfigOrientationVO configOrientationVO);

    /**
     * 修改配置
     * @param configOrientationVO 配置信息
     * @return
     */
    ConfigOrientationVO updateConfig(ConfigOrientationVO configOrientationVO);

    /**
     * 修改状态
     * @param configOrientationVO 配置信息
     * @return
     */
    ConfigOrientationVO updateState(ConfigOrientationVO configOrientationVO);
}
