package com.coohua.ap.admin.controller.vo;

/**
 * Package Name : com.coohua.nap.web.vo
 * Project Name : nap
 * Created by <PERSON>_<PERSON>_ on 2017/5/15 下午5:33
 */
public class Bucket {

    private boolean show; // 是否投放广告，true-投放，false-不投放

    private int time; // 48个时段的时间槽标号，0-47

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Bucket bucket = (Bucket) o;

        if (show != bucket.show) return false;
        return time == bucket.time;
    }

    @Override
    public String toString() {
        return "Bucket{" +
                "show=" + show +
                ", time=" + time +
                '}';
    }
}
