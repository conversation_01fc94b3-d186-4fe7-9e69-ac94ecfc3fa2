package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.TbApReplacePosConfigRequest;
import com.coohua.ap.admin.controller.vo.TbApReplacePosConfigVO;
import com.coohua.ap.admin.service.TbApReplacePosConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/config/replace")
public class TbApReplacePosConfigController {

    @Autowired
    private TbApReplacePosConfigService tbApReplacePosConfigService;

    //status platform
    @RequestMapping("/list")
    @ResponseBody
    public Page<TbApReplacePosConfigVO> list(Page<TbApReplacePosConfigVO> page, HttpServletRequest request) throws IOException {
        tbApReplacePosConfigService.queryList(page);
        return page;
    }

    @RequestMapping("/listAllValid")
    @ResponseBody
    public List<TbApReplacePosConfigVO> listValid(HttpServletRequest request){
        List<TbApReplacePosConfigVO> tbApReplacePosConfigVOS = tbApReplacePosConfigService.queryAllValid();
        return tbApReplacePosConfigVOS;
    }

    @RequestMapping("/addOne")
    public BaseResponse addOne(@RequestBody TbApReplacePosConfigRequest tbApReplacePosConfigRequest,
                            HttpServletRequest request) {
        tbApReplacePosConfigService.insert(tbApReplacePosConfigRequest);
        return new BaseResponse(0);
    }

    @RequestMapping("/updateOne")
    public BaseResponse updateOne(@RequestBody TbApReplacePosConfigRequest tbApReplacePosConfigRequest,
                               HttpServletRequest request){
        tbApReplacePosConfigService.update(tbApReplacePosConfigRequest);
        return new BaseResponse(0);
    }

    @RequestMapping("/deleteOne")
    public BaseResponse deleteOne(@RequestParam("id")Integer id,
                                HttpServletRequest request){
        tbApReplacePosConfigService.deleteOne(id);
        return new BaseResponse(0);
    }

    @RequestMapping("/updateEnabled")
    public BaseResponse updateEnbaled(@RequestParam("id")Integer id, @RequestParam("isEnabled")Integer isEnabled,
                                HttpServletRequest request){
        tbApReplacePosConfigService.updateEnable(id, isEnabled);
        return new BaseResponse(0);
    }
}
