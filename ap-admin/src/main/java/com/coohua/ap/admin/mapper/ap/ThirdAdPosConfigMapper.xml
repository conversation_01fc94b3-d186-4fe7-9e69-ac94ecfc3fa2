<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdAdPosConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdAdPosConfig" >
        <result column="pos_id" property="posId" />
        <result column="pos_name" property="posName" />
        <result column="product" property="product" />
        <result column="ad_id" property="adId" />
        <result column="os" property="os" />
        <result column="ad_type" property="adType" />
        <result column="platform" property="platform" />
        <result column="ecpm" property="ecpm" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                pos_id,
                pos_name,
                product,
                ad_id,
                os,
                ad_type,
                platform,
                ecpm,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdAdPosConfig">
        INSERT INTO third_ad_pos_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != posId">
                pos_id,
            </if>
            <if test="null != posName and '' != posName">
                pos_name,
            </if>
            <if test="null != product">
                product,
            </if>
            <if test="null != adId ">
                ad_id,
            </if>
            <if test="null != os">
                os,
            </if>
            <if test="null != adType">
                ad_type,
            </if>
            <if test="null != platform">
                platform,
            </if>
            <if test="null != ecpm">
                ecpm,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime ">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != posId">
                #{posId},
            </if>
            <if test="null != posName and '' != posName">
                #{posName},
            </if>
            <if test="null != product ">
                #{product},
            </if>
            <if test="null != adId ">
                #{adId},
            </if>
            <if test="null != os ">
                #{os},
            </if>
            <if test="null != adType">
                #{adType},
            </if>
            <if test="null != platform ">
                #{platform},
            </if>
            <if test="null != ecpm ">
                #{ecpm},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime ">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_ad_pos_config
        WHERE pos_id = #{posId}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdAdPosConfig">
        UPDATE third_ad_pos_config
        <set>
            <if test="null != posId">pos_id = #{posId},</if>
            <if test="null != posName and '' != posName">pos_name = #{posName},</if>
            <if test="null != product ">product = #{product},</if>
            <if test="null != adId ">ad_id = #{adId},</if>
            <if test="null != os and ">os = #{os},</if>
            <if test="null != adType ">ad_type = #{adType},</if>
            <if test="null != platform ">platform = #{platform},</if>
            <if test="null != ecpm and">ecpm = #{ecpm},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime ">update_time = #{updateTime}</if>
        </set>
        WHERE pos_id = #{posId}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_ad_pos_config
        WHERE pos_id = #{posId}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_ad_pos_config
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_ad_pos_config
    </select>

</mapper>