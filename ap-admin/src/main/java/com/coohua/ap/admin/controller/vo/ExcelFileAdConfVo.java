package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.utils.StringUtils;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/10/28
 */
@Data
public class ExcelFileAdConfVo {
    private String os;
    private String name;
    private List<String> adPosName;
    private String adPlanName;
    private String adTypeName;
    private String adThirdPosId;
    private String appId;
    private boolean cache;
    private String ecpm;
    private String abPoint;

    public  ExcelFileAdConfVo(String[] array){
        this.os = array[0];
        this.name = array[1];
        this.adPosName = Arrays.asList(array[2].split(","));
        this.adPlanName = array[3];
        this.adTypeName = array[4];
        this.adThirdPosId = array[5];
        this.appId = array[6];
        this.cache = Integer.valueOf(1).equals(StringUtils.isEmpty(array[7]) ? 0 : Integer.parseInt(array[7]));
        this.ecpm = StringUtils.isEmpty(array[8]) ? "" : array[8].trim();
        this.abPoint = array[9].trim();
    }
}
