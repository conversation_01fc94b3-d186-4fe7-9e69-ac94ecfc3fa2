package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.service.CommonService;

/**
 * <pre>
 *      公共服务
 * <hr/>
 * Package Name : com.coohua.ad.strategy.api.service.common.impl
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/2/24 下午2:50
 * </pre>
 */
//@Service
public class CommonServiceImpl implements CommonService {

//    private Random random = new Random();
//
//    private Gson gson = new Gson();
//
//    @Autowired
//    @Qualifier("napredisJedisClient")
//    private JedisClient napRedisService;
//
//    @Override
//    public boolean checkTimeToRefreshStrategy(long targetRefreshTime, long nowTime) {
//        return nowTime >= targetRefreshTime;
//    }
//
//    /**
//     * 返回时间槽
//     *
//     * @return
//     */
//    @Override
//    public int getCurrCreditCostIndex() {
//        return NewsConstants.DEFAULT_TIME_SLOT;
//    }
//
//    @Override
//    public String getUserLastRefreshTime(long userId) {
//        return napRedisService.hget(buidlRedisCreditCostCurrKey(), buildRedisUserRefreshTime(userId));
//    }
//
//    @Override
//    public String buidlRedisCreditCostCurrKey() {
//        return RedisConstants.USER_CREDIT_COST_KEY_PRE + getCurrCreditCostIndex();
//    }
//
//    @Override
//    public String buildRedisUserRefreshTime(long userId) {
//        return userId + RedisConstants.USER_CREDIT_COST_REFRESH_TIME;
//    }
//
//    @Override
//    public String buildRedisUserAdClickIntervalTimeStampKey(long adId, long userId, ProductType product) {
//        String keyPre = product != null && product.code() == ProductType.COOHUA.code() ? RedisConstants.USER_AD_CLICK_INTERVAL_TIMESTAMP_SCREENLOCK_KEY_PRE : RedisConstants.USER_AD_CLICK_INTERVAL_TIMESTAMP_KEY_PRE;
//        return keyPre + adId + NewsConstants.SYMBOL_COLON + userId;
//    }
//
//    @Override
//    public String buildRedisUserDayClickLimitKey(long adId, Long userId, ProductType product) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        String keyPre = product != null && product.code() == ProductType.COOHUA.code() ? RedisConstants.USER_AD_DAY_CLICK_LIMIT_SCREENLOCK_KEY_PRE : RedisConstants.USER_AD_DAY_CLICK_LIMIT_KEY_PRE;
//        return keyPre + adId + NewsConstants.SYMBOL_COLON + userId + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public String chooseValueViaPercent(List<ValuePercent> valuePercentList) {
//        List<String> percentList = convertValuePercent(valuePercentList);
//        return percentList.get(random.nextInt(percentList.size()));
//    }
//
//    @Override
//    public List<String> convertValuePercent(List<ValuePercent> valuePercents) {
//        List<String> percentList = new ArrayList<>();
//        for (ValuePercent valuePercent : valuePercents) {
//            for (int i = 0; i < valuePercent.getPercent(); i++) {
//                percentList.add(valuePercent.getValue());
//            }
//        }
//
//        Collections.shuffle(percentList);
//        return percentList;
//    }
//
//    @Override
//    public String getUserStrategyFromRedis(Long userId, int product) {
//        return napRedisService.get(buildUserStrategyKey(userId, product));
//    }
//
//    @Override
//    public String buildUserStrategyKey(Long userId, int product) {
//        return product == ProductType.NEWSEARN.code() ? RedisConstants.USER_STRATEGY_KEY_PRE + userId : RedisConstants.USER_STRATEGY_SCREENLOCK_KEY_PRE + userId;
//    }
//
//    @Override
//    public String buildRedisUserPkgNamesKey(Long userId) {
//        return RedisConstants.USER_PKG_NAMES_KEY_PRE + userId;
//    }
//
//    @Override
//    public String buildRedisAdCharge(long adPlanId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_AD_CHARGE_PRE + String.valueOf(adPlanId) + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public String buildRedisAdChargeForGroup(long adGroupId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_ADGROUP_CHARGE_PRE + String.valueOf(adGroupId) + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public String buildRedisAdChargeForCreative(long adId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_ADCREATIVE_CHARGE_PRE + String.valueOf(adId) + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public String buildRedisAdClickCounter(long adId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.NAP_AD_CLICK_COUNTER_PRE + String.valueOf(adId) + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public UserReqParams buildUserReqParams(String version, boolean newUc, long userId, UserInfo userInfo, UserRedisDataDomain userRedisDataDomain) {
//        UserReqParams userReqParams = new UserReqParams();
//        userReqParams.setAppVersion(version);
//        userReqParams.setNewUc(newUc);
//        userReqParams.setUserId(userId);
//        userReqParams.setUserInfo(userInfo);
//        userReqParams.setUserRedisDataDomain(userRedisDataDomain);
//        userReqParams.setPkgNames(userRedisDataDomain.getPkgNames());
//        return userReqParams;
//    }
//
//    @Override
//    public String buildRedisAdClickHourCounter(long adId) {
//        String hourTime = DateUtils.formatDate(new Date(), "yyyyMMddHH");
//        return RedisConstants.NAP_AD_CLICK_HOUR_COUNTER_PRE + adId + NewsConstants.SYMBOL_COLON + hourTime;
//    }
//
//    @Override
//    public String buildRedisAdChargeForAdvertiser(Integer advertiserId, String day) {
//        return RedisConstants.ECP_ADVERTISER_CHARGE_PRE + String.valueOf(advertiserId) + NewsConstants.SYMBOL_COLON + day;
//    }
//
//    @Override
//    public CreativeIndustryTop getAdIndustryTop(Integer firstCategoryId, Integer secondCategoryId, int type) {
//        if (type != NewsPutType.AD_NATIVE.subType) {
//            return CreativeIndustryTop.ALL;
//        }
//        if (firstCategoryId == null || secondCategoryId == null || firstCategoryId == 0 || secondCategoryId == 0) { // 没有行业信息的情况 => 网赚
//            return CreativeIndustryTop.WANG_ZHUAN;
//        }
//        if (secondCategoryId == AdConstants.INDUSTRY_TOP_WANG_ZHUAN) {
//            return CreativeIndustryTop.WANG_ZHUAN;
//        }
//        if (firstCategoryId == AdConstants.INDUSTRY_TOP_FEI_ZHENG_QI) {
//            return CreativeIndustryTop.FEI_ZHENG_QI;
//        }
//
//        return CreativeIndustryTop.ZHENG_QI;
//    }
//
//    @Override
//    public void chooseAppId(UserAdPutStrategyDomain userAdPutStrategyDomain, FeedStrategyConfig feedStrategyConfig, UserReqParams userReqParams) {
//        Map<String, Map<Integer, List<String>>> adAppidConfig = feedStrategyConfig.getAdAppidConfig();
//        String bundleId = userReqParams.getUserInfo().getBundleid();
//        Map<Integer, List<String>> adTypeSid = adAppidConfig.get(bundleId);
//        if (adTypeSid == null || adTypeSid.isEmpty()) {
//            return;
//        }
//
//        for (Map.Entry<Integer, List<String>> entry : adTypeSid.entrySet()) {
//            int adType = entry.getKey();
//            List<String> sids = entry.getValue();
//            if (adType == NewsPutType.AD_QQ.subType) {
//                userAdPutStrategyDomain.setGdtAppId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_BAIDU.subType || adType == NewsPutType.AD_BAIDU_VIDEO.subType) {
//                userAdPutStrategyDomain.setBaiduAppId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_TOUTIAO.subType || adType == NewsPutType.AD_TOUTIAO_VEDIO.subType) {
//                userAdPutStrategyDomain.setToutiaoAppId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_INMOBI.subType) { // InMobi视频
//                userAdPutStrategyDomain.setInmVideoActId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_INMOBI_IMG.subType) { // InMobi静态图
//                userAdPutStrategyDomain.setInmPicActId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_360.subType) {
//                userAdPutStrategyDomain.setAppId360(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_MINTEGRAL.subType) {
//                userAdPutStrategyDomain.setMintegralAppId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_OPPO.subType) {
//                userAdPutStrategyDomain.setOppoAppId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            } else if (adType == NewsPutType.AD_SOUGOU.subType) {
//                userAdPutStrategyDomain.setSougouAppId(sids.size() <= 0 ? NewsConstants.EMPTY_STRING : sids.get(random.nextInt(sids.size())));
//            }
//        }
//    }
//
//    @Override
//    public long calculateNextRefreshTimestamp(long userId, long nowTime) {
//        int strategyRefreshRandomSecond = AdConstants.STRATEGY_REFRESH_RANDOM_SECOND;
//        long secondOffset = 0L;
//        if (strategyRefreshRandomSecond > 0) {
//            secondOffset = (userId % strategyRefreshRandomSecond) * 1000; // 计算用户1分钟内的毫秒数偏移量
//        }
//
//        long refreshTime;
//
//        // 计算当前时间整点的时间戳，比如：14:40 =>14:00的时间戳
//        long curHour = DateUtils.parseDate(DateUtils.formatDate(new Date(nowTime), "yyyy-MM-dd HH"), "yyyy-MM-dd HH").getTime();
//        // 整点时间戳+用户需要偏移的时间=下次刷新的具体时间点。
//        long detailTime = curHour + secondOffset;
//        if (detailTime < nowTime) {
//            // 返回下个小时的偏移时间
//            refreshTime = detailTime + NewsConstants.HOUR_MILLIS;
//        } else {
//            refreshTime = detailTime;
//        }
//
//        return refreshTime;
//    }
//
//    @Override
//    public String buildRedisAdChargeForClick(Integer adPlanId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_AD_CLICK_CHARGE_PRE + String.valueOf(adPlanId) + NewsConstants.SYMBOL_COLON + today;
//    }
//
//    @Override
//    public String buildRedisAdChargeForExposure(Integer adPlanId) {
//        String today = DateUtils.formatDateForyyyyMMdd(new Date());
//        return RedisConstants.ECP_AD_EXPOSURE_CHARGE_PRE + String.valueOf(adPlanId) + NewsConstants.SYMBOL_COLON + today;
//    }
}
