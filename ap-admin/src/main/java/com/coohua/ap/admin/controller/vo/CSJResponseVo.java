package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.constants.AdReportEnum;
import com.coohua.ap.admin.model.AdDataReportEntity;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: CSJResponseVo
 * @Description: 穿山甲数据报表返回
 * @Author: fan jin yang
 * @Date: 2020/4/24
 * @Version: V1.0
 **/
@Data
public class CSJResponseVo extends DefaultReportResponseVo{

    private Long code;

    private String message;

    private List<ReportData> data = new ArrayList<>();

    @Override
    public List<AdDataReportEntity> convertReportEntity() throws ParseException {
        List<AdDataReportEntity> list = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(!CollectionUtils.isEmpty(data)){
            for(ReportData reportData:data){
                AdDataReportEntity adDataReportEntity = new AdDataReportEntity();
                adDataReportEntity.setMediaName(reportData.getMedia_name());
                adDataReportEntity.setAppId(reportData.getAppid());
                if("步步多康_android".equalsIgnoreCase(reportData.getSite_name())){
                    adDataReportEntity.setAppName("好运天气_android");
                }else if ("如意天气_ios".equalsIgnoreCase(reportData.getSite_name())){
                    adDataReportEntity.setAppName("好运天气_ios");
                }else {
                    adDataReportEntity.setAppName(reportData.getSite_name());
                }
                adDataReportEntity.setPosId(reportData.getAd_slot_id());
                adDataReportEntity.setPosName(reportData.getCode_name());
                adDataReportEntity.setPv(reportData.getShow());
                adDataReportEntity.setClick(reportData.getClick());
                adDataReportEntity.setClickRate(reportData.getClick_rate());
                adDataReportEntity.setEcpm(reportData.getEcpm());
                adDataReportEntity.setTypeName(reportData.getAd_slot_type());
                adDataReportEntity.setRevenue(reportData.getCost());
                adDataReportEntity.setDateTime(simpleDateFormat.parse(reportData.getStat_datetime()));
                list.add(adDataReportEntity);
            }
        }
        return list;
    }

    class ReportData{
        /**
         * 本条数据的时间
         */
        private String stat_datetime;
        /**
         * 国家或地区代码
         */
        private String region;
        /**
         * 应用id
         */
        private Long appid;
        /**
         * 广告位id
         */
        private Long ad_slot_id;

        /**
         * 广告类型
         */
        private String ad_slot_type;
        /**
         * 账号名称
         */
        private String media_name;
        /**
         * 应用名称
         */
        private String site_name;
        /**
         * 广告位名称
         */
        private String code_name;
        /**
         * 曝光量
         */
        private Long show;
        /**
         * 点击量
         */
        private Long click;
        /**
         * 点击率
         */
        private Float click_rate;
        /**
         * 千人收入
         */
        private Float ecpm;
        /**
         * 预估收入
         */
        private Float cost;

        public String getStat_datetime() {
            return stat_datetime;
        }

        public void setStat_datetime(String stat_datetime) {
            this.stat_datetime = stat_datetime;
        }

        public String getRegion() {
            return region;
        }

        public void setRegion(String region) {
            this.region = region;
        }

        public Long getAppid() {
            return appid;
        }

        public void setAppid(Long appid) {
            this.appid = appid;
        }

        public Long getAd_slot_id() {
            return ad_slot_id;
        }

        public void setAd_slot_id(Long ad_slot_id) {
            this.ad_slot_id = ad_slot_id;
        }

        public String getMedia_name() {
            return media_name;
        }

        public void setMedia_name(String media_name) {
            this.media_name = media_name;
        }

        public String getSite_name() {
            return site_name;
        }

        public void setSite_name(String site_name) {
            this.site_name = site_name;
        }

        public String getCode_name() {
            return code_name;
        }

        public void setCode_name(String code_name) {
            this.code_name = code_name;
        }

        public Long getShow() {
            return show;
        }

        public void setShow(Long show) {
            this.show = show;
        }

        public Long getClick() {
            return click;
        }

        public void setClick(Long click) {
            this.click = click;
        }

        public Float getClick_rate() {
            return click_rate;
        }

        public void setClick_rate(Float click_rate) {
            this.click_rate = click_rate;
        }

        public Float getEcpm() {
            return ecpm;
        }

        public void setEcpm(Float ecpm) {
            this.ecpm = ecpm;
        }

        public Float getCost() {
            return cost;
        }

        public void setCost(Float cost) {
            this.cost = cost;
        }

        public String getAd_slot_type() {
            return ad_slot_type;
        }

        public void setAd_slot_type(String ad_slot_type) {
            this.ad_slot_type = ad_slot_type;
        }
    }
}
