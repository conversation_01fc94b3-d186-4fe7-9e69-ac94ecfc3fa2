package com.coohua.ap.admin.constants;

/**
 * <AUTHOR>
 * @since 2022/6/29
 */
public enum PriceType {
    NORMAL(0,"目标底价"),
    BIDDING(1,"实时BIDDING"),
    ;
    private Integer code;
    private String desc;

    PriceType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PriceType getByCode(Integer code){
        for (PriceType type : PriceType.values()){
            if (type.code.equals(code)){
                return type;
            }
        }
        return PriceType.NORMAL;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
