package com.coohua.ap.admin.controller.vo;

import java.util.Objects;

/**
 * Created by Administrator on 2017/4/12.
 */
public class AdBudget {
    private boolean enableBudget;
    private int budget;
    private boolean noIdleTime;
    private int idleTime;
    private boolean noPerMaxTimes;
    private int perMaxTimes;
    private boolean noExposureIdleTime;
    private int exposureIdleTime;
    private boolean noExposureMaxTimes;
    private int exposureMaxTimes;
    private Integer ecpm;

    public boolean isEnableBudget() {
        return enableBudget;
    }

    public void setEnableBudget(boolean enableBudget) {
        this.enableBudget = enableBudget;
    }

    public int getBudget() {
        return budget;
    }

    public void setBudget(int budget) {
        this.budget = budget;
    }

    public int getIdleTime() {
        return idleTime;
    }

    public void setIdleTime(int idleTime) {
        this.idleTime = idleTime;
    }

    public int getPerMaxTimes() {
        return perMaxTimes;
    }

    public void setPerMaxTimes(int perMaxTimes) {
        this.perMaxTimes = perMaxTimes;
    }

    public boolean isNoIdleTime() {
        return noIdleTime;
    }

    public void setNoIdleTime(boolean noIdleTime) {
        this.noIdleTime = noIdleTime;
    }

    public boolean isNoPerMaxTimes() {
        return noPerMaxTimes;
    }

    public void setNoPerMaxTimes(boolean noPerMaxTimes) {
        this.noPerMaxTimes = noPerMaxTimes;
    }

    public boolean isNoExposureIdleTime() {
        return noExposureIdleTime;
    }

    public void setNoExposureIdleTime(boolean noExposureIdleTime) {
        this.noExposureIdleTime = noExposureIdleTime;
    }

    public int getExposureIdleTime() {
        return exposureIdleTime;
    }

    public void setExposureIdleTime(int exposureIdleTime) {
        this.exposureIdleTime = exposureIdleTime;
    }

    public boolean isNoExposureMaxTimes() {
        return noExposureMaxTimes;
    }

    public void setNoExposureMaxTimes(boolean noExposureMaxTimes) {
        this.noExposureMaxTimes = noExposureMaxTimes;
    }

    public int getExposureMaxTimes() {
        return exposureMaxTimes;
    }

    public void setExposureMaxTimes(int exposureMaxTimes) {
        this.exposureMaxTimes = exposureMaxTimes;
    }

    public Integer getEcpm() {
        return ecpm;
    }

    public void setEcpm(Integer ecpm) {
        this.ecpm = ecpm;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdBudget adBudget = (AdBudget) o;
        return enableBudget == adBudget.enableBudget &&
                budget == adBudget.budget &&
                noIdleTime == adBudget.noIdleTime &&
                idleTime == adBudget.idleTime &&
                noPerMaxTimes == adBudget.noPerMaxTimes &&
                perMaxTimes == adBudget.perMaxTimes &&
                noExposureIdleTime == adBudget.noExposureIdleTime &&
                exposureIdleTime == adBudget.exposureIdleTime &&
                noExposureMaxTimes == adBudget.noExposureMaxTimes &&
                exposureMaxTimes == adBudget.exposureMaxTimes;
    }

    @Override
    public int hashCode() {
        return Objects.hash(enableBudget, budget, noIdleTime, idleTime, noPerMaxTimes, perMaxTimes, noExposureIdleTime, exposureIdleTime, noExposureMaxTimes, exposureMaxTimes);
    }

    @Override
    public String toString() {
        return "AdBudget{" +
                "enableBudget=" + enableBudget +
                ", budget=" + budget +
                ", noIdleTime=" + noIdleTime +
                ", idleTime=" + idleTime +
                ", noPerMaxTimes=" + noPerMaxTimes +
                ", perMaxTimes=" + perMaxTimes +
                '}';
    }
}
