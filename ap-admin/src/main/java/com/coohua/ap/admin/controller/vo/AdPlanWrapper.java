package com.coohua.ap.admin.controller.vo;

import java.util.List;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.vo.admin
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/23 下午1:01
 * </pre>
 */
public class AdPlanWrapper {
    private Page page;
    /*
    * 广告信息
    * */
    private List<AdPlan> planList;
    /*
     *商业信息
     * */
    private List<IndustryVO> industryVOList;

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public List<AdPlan> getPlanList() {
        return planList;
    }

    public void setPlanList(List<AdPlan> planList) {
        this.planList = planList;
    }

    public List<IndustryVO> getIndustryVOList() {
        return industryVOList;
    }

    public void setIndustryVOList(List<IndustryVO> industryVOList) {
        this.industryVOList = industryVOList;
    }

    @Override
    public String toString() {
        return "AdPlanWrapper{" +
                "page=" + page +
                ", planList=" + planList +
                ", industryVOList=" + industryVOList +
                '}';
    }
}
