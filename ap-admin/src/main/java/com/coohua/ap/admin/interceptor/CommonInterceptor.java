package com.coohua.ap.admin.interceptor;

import com.alibaba.druid.util.Base64;
import com.coohua.ap.admin.domain.LoginUserInfo;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.utils.RSAUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * <pre>
 * Comments:
 *  拦截器
 *    对除登录接口以外的所有接口进行拦截，如果没有登录，则跳转到登录页面。
 *
 * <pre/>
 * <hr/>
 * Created by <PERSON><PERSON><PERSON> on 2017/6/14.
 */
public class CommonInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/api/login")||requestURI.contains("/report/")||requestURI.contains("/file/upload")) { // 不拦截登录接口
            return true;
        }

        String value = "";
        String productFromCookie = "";
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            if (AdConstants.COOKIE_NAME.equals(cookie.getName())) {
                value = cookie.getValue();
            } else if (AdConstants.COOKIE_NAME_PRODUCT.equals(cookie.getName())) {
                productFromCookie = cookie.getValue();
            }
        }

        if (StringUtils.isNotEmpty(value) && StringUtils.isNotEmpty(productFromCookie)) {
            String decryptValue = new String(RSAUtils.decryptByPrivateKey(Base64.base64ToByteArray(value), AdConstants.PRIVATE_KEY));
            //与session里的值比较。
            Object valueFromSession = request.getSession().getAttribute(AdConstants.COOKIE_NAME);
            int productFromSession = 0;
            try {
                productFromSession = SessionUtils.getUserProduct(request); // 校验session中存储的product与用户传过来的是否一致
            }catch (Exception e){
                throw new RuntimeException("当前账户未登录！");
            }


            if (valueFromSession == null) {
                return false;
            }

            if (!decryptValue.equals(valueFromSession)) {
                return false;
            }

            String[] userParams = decryptValue.split("\\|");
            LoginUserInfo loginUserInfo = new LoginUserInfo();
            loginUserInfo.setId(Integer.parseInt(userParams[0]));
            loginUserInfo.setUserName(userParams[2]);
            loginUserInfo.setProduct(Integer.parseInt(userParams[3]));
            request.setAttribute("LoginUserInfo", loginUserInfo);
            // 用户所属产品不匹配
            return productFromSession == Integer.parseInt(productFromCookie);

        } else {
            return false;
        }

    }

}
