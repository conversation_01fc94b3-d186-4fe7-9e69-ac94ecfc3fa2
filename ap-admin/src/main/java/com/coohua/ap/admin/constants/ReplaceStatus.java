package com.coohua.ap.admin.constants;

/**
 * <AUTHOR>
 * @since 2022/7/19
 */
public enum  ReplaceStatus {
    NO_REPLACE(0,"未替换"),
    ALREADY_REPLACE(1,"已替换")
    ;

    private Integer code;
    private String desc;

    ReplaceStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
