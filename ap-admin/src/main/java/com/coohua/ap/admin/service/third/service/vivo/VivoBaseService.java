package com.coohua.ap.admin.service.third.service.vivo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.openservices.shade.org.apache.commons.codec.binary.Base64;
import com.aliyun.openservices.shade.org.apache.commons.codec.digest.DigestUtils;
import com.coohua.ap.admin.service.third.dto.vivo.req.VivoCreatePosRequest;
import com.coohua.ap.admin.service.third.dto.vivo.req.VivoQueryListRequest;
import com.coohua.ap.admin.service.third.dto.vivo.rsp.VivoCreateAdRsp;
import com.coohua.ap.admin.service.third.dto.vivo.rsp.VivoPageRsp;
import com.coohua.ap.admin.service.third.dto.vivo.rsp.VivoResponse;
import com.coohua.ap.admin.utils.third.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/8
 * @link: https://coohua-toufang.oss-cn-beijing.aliyuncs.com/vivo_api.pdf
 * vivo对接
 */
@Slf4j
@Service
public class VivoBaseService {

    private final static String URL = "https://adnet.vivo.com.cn/api/open/";

    private static String sign(String accountName,String secretKey){
        long timestamp = System.currentTimeMillis();
        String sign = DigestUtils.sha256Hex(accountName + secretKey+ timestamp);
        return Base64.encodeBase64String((timestamp + "," + sign).getBytes());
    }


    public VivoResponse<VivoCreateAdRsp> createAdPos(VivoCreatePosRequest vivoCreatePosRequest, String skKey){
        Map<String,Object> headerMap = new HashMap<>();
        headerMap.put("token",sign(vivoCreatePosRequest.getAccountName(),skKey));
        String result = HttpClients.POST(URL + "position/add",headerMap, JSON.toJSONString(vivoCreatePosRequest));
        return JSON.parseObject(result,new TypeReference<VivoResponse<VivoCreateAdRsp>>(){});
    }

//    public VivoResponse<VivoPageRsp> queryAdPos(VivoQueryListRequest request, String skKey){
//        Map<String,Object> headerMap = new HashMap<>();
//        headerMap.put("token",sign(request.getAccountName(),skKey));
//        String result = HttpClients.GET_PARAM(URL + "position/list",request,headerMap);
//        return JSON.parseObject(result,new TypeReference<VivoResponse<VivoPageRsp>>(){});
//    }


    public static void main(String[] args) {
        System.out.println(sign("hainangaoyu", "cc2eda41e0bf498cba1cd450097da3e0"));
    }
}
