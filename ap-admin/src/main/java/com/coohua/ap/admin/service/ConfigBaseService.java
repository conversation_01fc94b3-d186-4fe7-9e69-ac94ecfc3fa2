package com.coohua.ap.admin.service;


import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.Page;

/**
 * <pre>
 *      配置管理服务
 * <hr/>
 * Package Name : com.coohua.nap.service
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/13 上午10:56
 * </pre>
 */
public interface ConfigBaseService {
    /**
     * 根据产品查询所有配置
     * @param page 分页条件
     * @param userProduct 用户产品
     * @param type
     * @param name
     * @param state
     */
    void listConfig(Page<ConfigBaseVO> page, int userProduct, int type, String name, int state);

    /**
     * 新增一条配置
     * @param configBaseVO 新增配置详情
     * @return 新增的配置
     */
    ConfigBaseVO saveConfig(ConfigBaseVO configBaseVO);

    /**
     * 更新一条配置
     * @param configBaseVO 修改详情
     * @return 更新后的配置
     */
    ConfigBaseVO updateConfig(ConfigBaseVO configBaseVO);

    /**
     * 更新配置状态
     * @param configBaseVO 待更新的配置状态
     * @return 更新的条数，等于1的时候表示成功
     */
    ConfigBaseVO updateConfigState(ConfigBaseVO configBaseVO);


    ConfigBaseVO queryById(Integer id);
}
