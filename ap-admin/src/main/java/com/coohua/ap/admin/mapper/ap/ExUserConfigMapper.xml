<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ExUserConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ExUserConfig" >
        <result column="id" property="id" />
        <result column="product" property="product" />
        <result column="state" property="state" />
        <result column="channel" property="channel" />
        <result column="ip" property="ip" />
        <result column="model" property="model" />
        <result column="phone" property="phone" />
        <result column="video_limit" property="videoLimit" />
        <result column="withdraw_rate" property="withdrawRate" />
        <result column="score_rate" property="scoreRate" />
        <result column="ocpc_type" property="ocpcType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="ocpc_channel" property="ocpcChannel" />
        <result column="rule_level" property="ruleLevel" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                product,
                state,
                channel,
                ip,
                model,
                phone,
                video_limit,
                withdraw_rate,
                score_rate,
                ocpc_type,
                create_time,
                update_time,
                ocpc_channel,
                rule_level
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.mapper.ap.ExUserConfigMapper">
        INSERT INTO ex_user_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != product  ">
                product,
            </if>
            <if test="null != state  ">
                state,
            </if>
            <if test="null != channel and '' != channel">
                channel,
            </if>
            <if test="null != ip and '' != ip">
                ip,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != phone and '' != phone">
                phone,
            </if>
            <if test="null != videoLimit ">
                video_limit,
            </if>
            <if test="null != withdrawRate  ">
                withdraw_rate,
            </if>
            <if test="null != scoreRate  ">
                score_rate,
            </if>
            <if test="null != ocpcType  ">
                ocpc_type,
            </if>
            <if test="null != createTime  ">
                create_time,
            </if>
            <if test="null != updateTime  ">
                update_time,
            </if>
            <if test="null != ocpcChannel  ">
                ocpc_channel,
            </if>
            <if test="null != ruleLevel  ">
                rule_level
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != product  ">
                #{product},
            </if>
            <if test="null != state  ">
                #{state},
            </if>
            <if test="null != channel and '' != channel">
                #{channel},
            </if>
            <if test="null != ip and '' != ip">
                #{ip},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != phone and '' != phone">
                #{phone},
            </if>
            <if test="null != videoLimit ">
                #{videoLimit},
            </if>
            <if test="null != withdrawRate  ">
                #{withdrawRate},
            </if>
            <if test="null != scoreRate  ">
                #{scoreRate},
            </if>
            <if test="null != ocpcType  ">
                #{ocpcType},
            </if>
            <if test="null != createTime  ">
                #{createTime},
            </if>
            <if test="null != updateTime  ">
                #{updateTime},
            </if>
            <if test="null != ocpcChannel  ">
                #{ocpcChannel},
            </if>
            <if test="null != ruleLevel  ">
                #{ruleLevel}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM ex_user_config
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.mapper.ap.ExUserConfigMapper">
        UPDATE ex_user_config
        <set>
            <if test="null != product ">product = #{product},</if>
            <if test="null != state ">state = #{state},</if>
            <if test="null != channel and '' != channel">channel = #{channel},</if>
            <if test="null != ip and '' != ip">ip = #{ip},</if>
            <if test="null != model and '' != model">model = #{model},</if>
            <if test="null != phone and '' != phone">phone = #{phone},</if>
            <if test="null != videoLimit ">video_limit = #{videoLimit},</if>
            <if test="null != withdrawRate  ">withdraw_rate = #{withdrawRate},</if>
            <if test="null != scoreRate  ">score_rate = #{scoreRate},</if>
            <if test="null != ocpcType  ">ocpc_type = #{ocpcType},</if>
            <if test="null != createTime  ">create_time = #{createTime},</if>
            <if test="null != updateTime  ">update_time = #{updateTime},</if>
            <if test="null != ocpcType  ">ocpc_channel = #{ocpcChannel},</if>
            <if test="null != ruleLevel  ">rule_level = #{ruleLevel}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ex_user_config
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ex_user_config
        where 1 = 1
        <if test=' null != product'> AND product =#{product} </if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM ex_user_config
        where 1 = 1
        <if test=' null != product'> AND product =#{product} </if>
    </select>

</mapper>