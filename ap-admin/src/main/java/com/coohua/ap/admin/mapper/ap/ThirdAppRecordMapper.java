package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdAppRecord;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/20
 */
@Mapper
@Repository
public interface ThirdAppRecordMapper {


    @Insert("insert into third_app_record(logday, third_id, third_status, main_body, create_time, update_time) " +
            " value(#{logday},#{thirdId},#{thirdStatus},#{mainBody},#{createTime},#{updateTime})")
    int insert(ThirdAppRecord thirdAppRecord);

    @Select("select * from third_app_record where logday=#{logday}")
    List<ThirdAppRecord> queryByLogday(@Param("logday")String logday);
}
