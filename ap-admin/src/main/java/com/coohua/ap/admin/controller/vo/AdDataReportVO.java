package com.coohua.ap.admin.controller.vo;

import lombok.Data;

/**
 * @ClassName: AdDataReportVO
 * @Description: 广告报表数据
 * @Author: fan jin yang
 * @Date: 2020/4/26
 * @Version: V1.0
 **/
@Data
public class AdDataReportVO {

    /**
     * 报表头：appName-dataSourceName-posName
     */
    private String title;

    /**
     * 报表日期
     */
    private String date;

    /**
     * 日活跃人数
     */
    private long dau;

    /**
     * 曝光数
     */
    private long pv;

    /**
     * 点击数
     */
    private long click;

    /**
     * 人均视频曝光
     */
    private float perVideoPv;

    /**
     * 人均视频点击
     */
    private float perVideoClk;

    /**
     * 非视频人均曝光
     */
    private float perOtherPv;

    /**
     * 非视频人均点击
     */
    private float perOtherClk;

    /**
     * 收入
     */
    private long revenue;

    /**
     * 均日活收入
     */
    private float arpu;

    /**
     * 点击率
     */
    private float clickRate;

    /**
     * 人均曝光
     */
    private float perPv;

    /**
     * 人均点击
     */
    private float perClk;

    /**
     * 千次曝光收入
     */
    private float ecpm;

    /**
     * 均点击收入
     */
    private float cpc;


}
