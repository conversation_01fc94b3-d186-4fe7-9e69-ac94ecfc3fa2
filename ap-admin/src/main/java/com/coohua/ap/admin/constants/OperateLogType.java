package com.coohua.ap.admin.constants;

/**
 * <pre>
 *  操作日志类型
 * </pre>
 *
 */
public enum OperateLogType {
    GLOBAL_CONFIG(1, "全局配置"),
    STRATEGY_CONFIG(2, "策略配置"),
    STRATEGY_CONFIG_CONFIGS(3, "策略配置项"),
    AD_PLAN_CONFIG(4, "广告计划"),
    AD_CONFIG(5,"广告"),
    AD_POS_CONFIG(6,"广告位");

    private int type;
    private String desc;

    OperateLogType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int type() {
        return this.type;
    }

    public String desc() {
        return this.desc;
    }

    public static OperateLogType find(int type) {
        for (OperateLogType logType : OperateLogType.values()) {
            if (type == logType.type) {
                return logType;
            }
        }
        return null;
    }
}
