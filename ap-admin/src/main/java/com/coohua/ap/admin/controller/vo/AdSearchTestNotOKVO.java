package com.coohua.ap.admin.controller.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class AdSearchTestNotOKVO {

    // 被过滤的原因
    private int filteredReasonType;

    private String filteredReasonDesc;

    // 被过滤的广告 类型：ID列表
    private List<AdSearchTestOKVO> notOkList = new ArrayList<>();


}
