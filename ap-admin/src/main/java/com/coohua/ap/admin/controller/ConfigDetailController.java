package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.service.ConfigNewBasicService;
import com.coohua.ap.admin.utils.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/23
 */
@Slf4j
@Controller
@RequestMapping("/config/detail")
public class ConfigDetailController {

    @Autowired
    private ConfigNewBasicService configNewBasicService;


    @RequestMapping("/queryById")
    @ResponseBody
    public BaseResponse queryFilterPosById(@RequestParam("configId")Integer configId){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(configNewBasicService.queryConfigList(configId));
        return baseResponse;
    }

    @RequestMapping("/addNew")
    @ResponseBody
    public BaseResponse saveNewFilterConfig(@RequestBody AdConfigFilterRequest request, HttpServletRequest servletRequest){
        int product = SessionUtils.getUserProduct(servletRequest);
        configNewBasicService.addAdTypeList(request,product);
        return new BaseResponse(0);
    }

    // 查看分层对应的广告
    @RequestMapping("/queryConfigNodeById")
    public BaseResponse queryConfigNodeById(HttpServletRequest servletRequest, @RequestParam("configId")Integer configId){
        BaseResponse baseResponse = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(servletRequest);
        baseResponse.setData(configNewBasicService.queryFilterList(configId,product));
        return baseResponse;
    }

    // 按照node调整分层
    @RequestMapping("/updateConfigNode")
    public BaseResponse queryConfigNode(@RequestBody List<AdFilterBean> adFilterBeanList){
        configNewBasicService.updateFilterList(adFilterBeanList);
        return new BaseResponse(0);
    }

    @RequestMapping("/sortNode")
    @ResponseBody
    public BaseResponse sortNode(@RequestBody AdConfigSortRequest request, HttpServletRequest servletRequest){
        int product = SessionUtils.getUserProduct(servletRequest);
        configNewBasicService.sortNode(request,product);
        return new BaseResponse(0);
    }

    @RequestMapping("/editConfig")
    @ResponseBody
    public BaseResponse editFilterConfig(@RequestBody AdConfigFilterRequest request, HttpServletRequest servletRequest){
        configNewBasicService.editAdTypeList(request);
        return new BaseResponse(0);
    }

    @RequestMapping("/editDefaultConfig")
    @ResponseBody
    public BaseResponse saveDefaultFilterConfig(@RequestBody AdConfigDefaultFilterRequest request){
        configNewBasicService.saveAdTypeDefaultList(request);
        return new BaseResponse(0);
    }

    @RequestMapping("/switchFlag")
    @ResponseBody
    public BaseResponse switchFilterConfig(@RequestParam("id") int id,
                                           @RequestParam("state") int state,
                                           HttpServletRequest request){
        configNewBasicService.switchAdTypeFilter(id,state);
        return new BaseResponse(0);
    }

    @RequestMapping("/delNode")
    @ResponseBody
    public BaseResponse delNode(@RequestParam("id") int id, HttpServletRequest request){
        configNewBasicService.delNode(id);
        return new BaseResponse(0);
    }
}
