package com.coohua.ap.admin.mapper.ap;


import com.coohua.ap.base.vo.TbApAppReqCluster;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_ap_app_req_cluster(应用请求所属集群)】的数据库操作Mapper
* @createDate 2025-01-03 09:18:49
* @Entity com.coohua.ap.admin.model.TbApAppReqCluster
*/
public interface TbApAppReqClusterMapper {

    List<String> selectAllHashKeysFromMysql(@Param("table") String table);

    void saveBatch(@Param("table") String table, @Param("list") List<TbApAppReqCluster> tbApAppReqClusterList);
}




