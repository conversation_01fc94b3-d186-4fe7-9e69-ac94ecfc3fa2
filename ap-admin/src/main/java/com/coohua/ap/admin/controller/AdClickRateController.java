package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.AdPosView;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.ClickConfigVo;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.model.AdPosModel;
import com.coohua.ap.admin.service.ClickConfigService;
import com.coohua.ap.admin.utils.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/9/3
 */
@RequestMapping("click")
@RestController
public class AdClickRateController {

    @Autowired
    private ClickConfigService clickConfigService;

    @RequestMapping("list")
    public BaseResponse list(Page<ClickConfigVo> page, HttpServletRequest request) {

        int product = SessionUtils.getUserProduct(request);
        clickConfigService.list(page,product);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }


    @RequestMapping("addOne")
    public BaseResponse addOne(@RequestBody ClickConfigVo clickConfigVo,
                               HttpServletRequest request) {
        int product = SessionUtils.getUserProduct(request);
        checkParam(clickConfigVo);
        clickConfigService.insert(clickConfigVo,product);
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    private void checkParam(ClickConfigVo clickConfigVo){
        if (clickConfigVo.getClickA() == null ||
                clickConfigVo.getClickB() == null ||
                clickConfigVo.getExposure() == null) {
            throw new RuntimeException("参数异常！不能为空..");
        }
    }

    @RequestMapping("updateOne")
    public BaseResponse updateOne(@RequestBody ClickConfigVo clickConfigVo,
                                  HttpServletRequest request) {
        checkParam(clickConfigVo);
        clickConfigService.edit(clickConfigVo);
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }


    @RequestMapping("delete")
    public BaseResponse delete(@RequestParam("id") int id,
                                  HttpServletRequest request) {
        clickConfigService.delete(id);
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }
}
