package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.service.SystemService;
import org.springframework.stereotype.Service;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.service.impl
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/5/30 下午1:07
 * </pre>
 */
//@Service
public class SystemServiceImpl implements SystemService {

//    @Value("${debug.url}")
//    private String debugUrl;
//
//    @Autowired
//    @Qualifier("napredisJedisClient")
//    private JedisClient napRedisService;
//
//    @Autowired
//    @Qualifier("adclusterJedisClusterClient")
//    private JedisClusterClient napClusterRedisService;
//
//    @Autowired
//    private CommonClusterService commonClusterService;
//
//    private Gson gson = new Gson();
//
//    @Override
//    public void delUserStrategyByProduct(String userId, int product) {
//        String key = commonClusterService.buildUserStrategyKey(Long.parseLong(userId), product);
//        napClusterRedisService.del(key);
//    }
//
//    @Override
//    public String forceUpdateUserStrategy(Long userId, int groupId, Long adId, int product) {
//        String userStrategyJsonStr = napClusterRedisService.get(commonClusterService.buildUserStrategyKey(userId, product));
//        if (StringUtils.isEmpty(userStrategyJsonStr)) {
//            return "请求一下推荐页先";
//        }
//        UserStrategy strategy = gson.fromJson(userStrategyJsonStr, new TypeToken<UserStrategy>() {}.getType());
//        if (strategy.getRefreshTimestamp() < System.currentTimeMillis()) {
//            return "请求一下推荐页先";
//        }
//
//        UserAdPutStrategyDomain domain = strategy.getUserAdPutStrategyDomain();
//        List<Group> groups = domain.getGroupList();
//        Group group = groups.get(groupId - 1);
//        List<Ad> ads = group.getAd();
//        Long replaceAdId = 0L;
//        for (Ad ad : ads) {
//            if (ad.getPos() == 2) {
//                replaceAdId = Long.parseLong(ad.getAdId() + "");
//                ad.setAdId(adId.intValue());
//            }
//        }
//
//        if (replaceAdId == 0) { // 没有在中间找到广告，直接设置在中间
//            Ad ad = new Ad();
//            ad.setAdId(adId.intValue());
//            ad.setPos(2);
//            ads.add(ad);
//            domain.getAdIds().add(adId);
//        }
//
//        domain.getAdIds().add(adId);
//
//        napClusterRedisService.setex(commonClusterService.buildUserStrategyKey(userId, product), AdConstants.USER_STRATEGY_TIMEOUT, gson.toJson(strategy));
//        return "OK";
//    }
//
//    @Override
//    public Object doDebug(String userId, String version, int product, String os) throws Exception {
//        String url = "http://" + debugUrl + "/ad/";
//        if (product == 1) { // 锁屏
//            url += "getAdInfoForScreenLock";
//        } else if (product == 2) { // 淘新闻
//            url += "getAdInfoV3";
//        } else if (product == 3) { // 浏览器
//            url += "getAdInfoForBrower";
//        }
//        String baseKey = getBaseKey(userId, version);
//        url += "?base-key=" + baseKey;
//        url += "&os=" + os;
//        url += "&product=" + product;
//        String res = HttpClient.getResponse(url);
//        return JSONObject.parse(res);
//    }
//
//    @Override
//    public int cheatUser(String userId, int product) {
//        String fromRedis = napClusterRedisService.get(commonClusterService.buildCheatUserKey(Long.parseLong(userId), product));
//        if (StringUtils.isNotEmpty(fromRedis)) {
//            return Integer.parseInt(fromRedis) == 1 ? 1 : 0;
//        }
//        return 0;
//    }
//
//    @Override
//    public String getUserRedisInfo(String userId, int product) {
//        String ret = napClusterRedisService.get(commonClusterService.buildUserStrategyKey(Long.parseLong(userId), product));
//        return StringUtils.isEmpty(ret) ? "未知" : ret;
//    }
//
//    @Override
//    public String getUserInfo(String userId, int product) {
//        if (ProductType.NEWSEARN.code() == product) {
//            Map<String, String> userInfo = napRedisService.hgetAll(RedisConstants.NEWSEARN_USERINFO_KEY_PRE + userId);
//            return userInfo != null && userInfo.size() > 0 ? JSONObject.toJSONString(userInfo) : "未知";
//        } else if (ProductType.COOHUA.code() == product) {
//            String key = commonClusterService.buildCoohuaUserInfoKeyPre(Long.parseLong(userId));
//            String value = napClusterRedisService.get(key);
//            return StringUtils.isNotEmpty(value) ? value : "未知";
//        } else if (ProductType.BROWER.code() == product) {
//            String key = commonClusterService.buildBroswerUserInfoKeyPre(Long.parseLong(userId));
//            String value = napClusterRedisService.get(key);
//            return StringUtils.isNotEmpty(value) ? value : "未知";
//        }
//        return "未知";
//    }
//
//    @Override
//    public long delUserInfo(String userId, int product) {
//        if (ProductType.NEWSEARN.code() == product) {
//            String key = buildNewsearnUserInfoRedisKey(Long.parseLong(userId));
//            return napClusterRedisService.del(key);
//        } else if (ProductType.COOHUA.code() == product) {
//            String key = commonClusterService.buildCoohuaUserInfoKeyPre(Long.parseLong(userId));
//            return napClusterRedisService.del(key);
//        } else if (ProductType.BROWER.code() == product) {
//            String key = commonClusterService.buildBroswerUserInfoKeyPre(Long.parseLong(userId));
//            return napClusterRedisService.del(key);
//        }
//        return 0;
//    }
//
//    private String buildNewsearnUserInfoRedisKey(long userId) {
//        return RedisConstants.NEWSEARN_USERINFO_REDIS_KEY_PRE + "{" + userId + "}";
//    }
//
//    public String getBaseKey(String userId, String version) {
//        String basedStr = null;
//        try {
//            StringBuilder sb = new StringBuilder();
//            sb.append(version).append("^")
//                    .append(userId).append("^")
//                    .append("ticket");
//            byte[] encryptData = AESCoder.encrypt(sb.toString().getBytes(), getCommonKey());
//            basedStr = new String(Base64.encode(encryptData, Base64.URL_SAFE | Base64.NO_WRAP));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return basedStr;
//    }
//
//    private byte[] getCommonKey() {
//        byte[] buff = "7WcNcg2P2YAruAO5Y1WoRw==".getBytes();
//        return Base64.decode(buff, Base64.URL_SAFE);
//    }
}
