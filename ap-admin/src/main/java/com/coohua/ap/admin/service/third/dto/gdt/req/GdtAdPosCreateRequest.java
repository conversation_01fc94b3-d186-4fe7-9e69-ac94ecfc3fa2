package com.coohua.ap.admin.service.third.dto.gdt.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Data
public class GdtAdPosCreateRequest {
    private Long member_id;
    private Long app_id;
    private String placement_name;
    private String scene;
    private String rewarded_video_scene;
    private String rewarded_video_description;
    private Integer placement_image_id;
    private String ad_pull_mode;
    private String render_type;
    private String[] ad_crt_type_list;
    private String[] ad_crt_template_type;
    private Integer ad_crt_normal_type;
    private String flash_crt_type;
    private String rewarded_video_crt_type;
    private String[] ad_feedback_element;

    private String price_strategy_type;
    private String real_time_bidding_type;
    private Integer ecpm_price;
    private FilterRule filter_rule;
    private RtbConfig rtb_config;
    private String custom_position_scene_level3;
    private String need_server_verify;
    private String transfer_url;
    private String secret;


    @Data
    public static class FilterRule{
        private String[] filter_pkg_names;
        private String[] filter_ituns_ids;
        private String[] filter_keywords;
        private String[] filter_categories;
    }

    @Data
    public static class RtbConfig{
        private Long rtb_relation_adx_id;
        private String rtb_relation_pos_adx_id;
        private String rtb_relation_os;
        private String rtb_relation_name;
        private String rtb_relation_bundle;
        private Integer rtb_relation_posw;
        private Integer rtb_relation_posh;
        private Integer rtb_relation_requestw;
        private Integer rtb_relation_requesth;
        private String rtb_relation_placement_type;
        private Long rtb_relation_template_id;
        private Boolean rtb_relation_is_paster;
        private String rtb_relation_native_layout;
        private String rtb_relation_extra;
    }
}
