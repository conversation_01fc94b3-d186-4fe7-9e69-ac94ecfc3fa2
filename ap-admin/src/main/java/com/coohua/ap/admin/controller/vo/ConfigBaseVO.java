package com.coohua.ap.admin.controller.vo;

import java.util.Date;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.web.vo
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/13 上午10:36
 * </pre>
 */
public class ConfigBaseVO {

    private Integer id;

    private String name;

    private Integer product;

    private Integer type;

    private String config;

    private String configPre;

    private String comment;

    private boolean state;

    private Integer delFlag;

    private Integer posType;

    private Date createTime;

    private Date updateTime;

    private Page page;

    public ConfigBaseVO() {
    }

    public ConfigBaseVO(Integer id, String name, Integer product, Integer type) {
        this.id = id;
        this.name = name;
        this.product = product;
        this.type = type;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public boolean isState() {
        return state;
    }

    public void setState(boolean state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getPosType() {
        return posType;
    }

    public void setPosType(Integer posType) {
        this.posType = posType;
    }

    public String getConfigPre() {
        return configPre;
    }

    public void setConfigPre(String configPre) {
        this.configPre = configPre;
    }

    @Override
    public String toString() {
        return "ConfigBaseVO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", product=" + product +
                ", type=" + type +
                ", config='" + config + '\'' +
                ", configPre='" + configPre + '\'' +
                ", comment='" + comment + '\'' +
                ", state=" + state +
                ", delFlag=" + delFlag +
                ", posType=" + posType +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", page=" + page +
                '}';
    }
}
