package com.coohua.ap.admin.service;


import java.util.List;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.ad.strategy.api.service.common
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/2/24 下午2:50
 * </pre>
 */
public interface CommonService {

//    /**
//     * 检查是否到了需要刷新用户策略的时间
//     * @param targetRefreshTime 目标刷新时间
//     * @param nowTime 当前系统时间
//     * @return true - 可以刷新，false - 不可刷新
//     */
//    boolean checkTimeToRefreshStrategy(long targetRefreshTime, long nowTime);
//
//    /**
//     * 返回时间槽，当前默认是0
//     * @return
//     */
//    int getCurrCreditCostIndex();
//
//    /**
//     * 获取用户积分策略最后一次刷新时间
//     * @param userId
//     * @return
//     */
//    String getUserLastRefreshTime(long userId);
//
//    /**
//     * 获取用户积分策略Key
//     * @return
//     */
//    String buidlRedisCreditCostCurrKey();
//
//    /**
//     * 构建用户积分策略刷新时间的redis key
//     * @param userId
//     * @return
//     */
//    String buildRedisUserRefreshTime(long userId);
//
//    /**
//     * 构建key : 记录用户点击广告的时间戳，用于投放时过滤用户对某一广告点击的时间间隔。
//     * @param adId 广告ID
//     * @param userId 用户ID
//     * @param product
//     * @return key
//     */
//    String buildRedisUserAdClickIntervalTimeStampKey(long adId, long userId, ProductType product);
//
//    /**
//     * 构建key : 记录用户点击某广告的次数，用于单用户的广告控量。
//     * @param adId
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildRedisUserDayClickLimitKey(long adId, Long userId, ProductType product);
//
//    /**
//     * 根据百分比，从列表中选取一个值
//     * @param valuePercentList
//     * @return
//     */
//    String chooseValueViaPercent(List<ValuePercent> valuePercentList);
//
//    List<String> convertValuePercent(List<ValuePercent> valuePercents);
//
//    /**
//     * 从redis获取用户的缓存信息
//     * @param userId
//     * @param product
//     * @return
//     */
//    String getUserStrategyFromRedis(Long userId, int product);
//
//    /**
//     * 构建用户获取用户投放策略缓存信息的key
//     * @param userId
//     * @param product
//     * @return
//     */
//    String buildUserStrategyKey(Long userId, int product);
//
//    /**
//     * 构建获取用户上传包名的key
//     * @param userId
//     * @return
//     */
//    String buildRedisUserPkgNamesKey(Long userId);
//
//    /**
//     * 生成Ecp系统广告计划日预算key
//     * @param adPlanId 广告计划ID
//     * @return Redis key
//     */
//    String buildRedisAdCharge(long adPlanId);
//
//    /**
//     * 生成Ecp系统广告组日消耗key
//     * @param adGroupId 广告组ID
//     * @return Redis key
//     */
//    String buildRedisAdChargeForGroup(long adGroupId);
//
//    /**
//     * 生成Ecp系统广告创意日消耗key
//     * @param adId 广告创意ID
//     * @return Redis key
//     */
//    String buildRedisAdChargeForCreative(long adId);
//
//    /**
//     * 构建广告点击累加的KEY<br/>
//     * <pre>
//     *     规则：nap:ad:click:adId:yyyyMMdd
//     * </pre>
//     *
//     * @param adId 广告ID
//     * @return redis key
//     */
//    String buildRedisAdClickCounter(long adId);
//
//    /**
//     * 构建调用策略服务的参数信息
//     * @return 用户参数
//     */
//    UserReqParams buildUserReqParams(String version, boolean newUc, long userId, UserInfo userInfo, UserRedisDataDomain userRedisDataDomain);
//
//    /**
//     * 构建广告点击按小时累加的Key
//     * @param adId 广告ID
//     * @return KEY
//     */
//    String buildRedisAdClickHourCounter(long adId);
//
//    /**
//     * 生成Ecp系统广告主日消耗key
//     * @param advertiserId 广告主ID
//     * @return Redis key
//     */
//    String buildRedisAdChargeForAdvertiser(Integer advertiserId, String day);
//
//    /**
//     * 获取广告创意行业顶级分类
//     * @param firstCategoryId
//     * @param secondCategoryId
//     * @return
//     */
//    CreativeIndustryTop getAdIndustryTop(Integer firstCategoryId, Integer secondCategoryId, int type);
//
//    /**
//     * 根据配置选择APPID
//     * @param userAdPutStrategyDomain
//     * @param feedStrategyConfig
//     * @param userReqParams
//     */
//    void chooseAppId(UserAdPutStrategyDomain userAdPutStrategyDomain, FeedStrategyConfig feedStrategyConfig, UserReqParams userReqParams);
//
//    /**
//     * 根据userId计算该用户下一次需要刷新策略的时间
//     * @param userId
//     * @param nowTime
//     * @return
//     */
//    long calculateNextRefreshTimestamp(long userId, long nowTime);
//
//    /**
//     * 生成Ecp系统广告计划日点击消耗key
//     * @param adPlanId 广告计划id
//     * @return
//     */
//    String buildRedisAdChargeForClick(Integer adPlanId);
//
//    /**
//     * 生成Ecp系统广告计划日曝光消耗key
//     * @param adPlanId 广告计划id
//     * @return
//     */
//    String buildRedisAdChargeForExposure(Integer adPlanId);
}
