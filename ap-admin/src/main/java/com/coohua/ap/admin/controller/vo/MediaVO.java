package com.coohua.ap.admin.controller.vo;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Data
public class MediaVO {
    /*媒体id 自增*/
    private Integer id;
    /*媒体名称*/
    private String mediaName;
    /*积分名称*/
    private String integralName;
    /*媒体分成比例*/
    private Double mediaProportion;

    /*积分汇率*/
    private Integer integralRate;
    /*联系人*/
    private String contact;
    /*联系电话*/
    private Integer contactPhone;
    /*状态（0：停用     1：启用）*/
    private Boolean state;
    /*创建时间*/
    private Date createTime;
    /*最后一次修改时间*/
    private Date updateTime;
//    /*RSA公钥*/
//    private String rsaPublicKey;
//    /*RSA私钥*/
//    private String rsaPrivateKey;
    /* 媒体签名key*/
    private String key;
    /*媒体有无积分（0：无积分  1：有积分）*/
    private Integer mediaScoreAttribute;
    /*媒体接入形式（0：sdk  1:api）*/
    private Integer mediaAccessForm;
    // 数据统计结果加权系数
    private Integer factor;
    // 兑换汇率
    private Integer rewardIntegralRate;


}
