package com.coohua.ap.admin.controller.vo;

import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.nap.web.vo
 * @create_time 2019-08-22
 */
@Data
public class StrategyMathcerVO {

    // 广告位名称
    private String posName;
    // 最终匹配的策略ID
    private Integer stragegyId;
    // 匹配时间
    private String timestamp;

    public StrategyMathcerVO() {
    }

    public StrategyMathcerVO(String posName, Integer stragegyId, String timestamp) {
        this.posName = posName;
        this.stragegyId = stragegyId;
        this.timestamp = timestamp;
    }
}
