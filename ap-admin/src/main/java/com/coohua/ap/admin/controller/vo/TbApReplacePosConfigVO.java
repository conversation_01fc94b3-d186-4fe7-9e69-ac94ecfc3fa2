package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.TbApReplacePosConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.util.Date;
import java.util.List;

@Data
public class TbApReplacePosConfigVO {

    private Integer id;

    private String strategyName;

    private String os;

    private String product;

    private String adTypeName;

    private String dsp;

    private List<TbApReplacePosPlatformConfigVO> platformConfig;

    private String extend1;

    private String extend2;

    private Byte isEnabled;

    private Byte isDeleted;

    private Date createTime;

    private Date updateTime;

    public TbApReplacePosConfigVO build(TbApReplacePosConfig tbApReplacePosConfig) throws IOException {
        TbApReplacePosConfigVO tbApReplacePosConfigVO = new TbApReplacePosConfigVO();
        tbApReplacePosConfigVO.setId(tbApReplacePosConfig.getId());
        tbApReplacePosConfigVO.setStrategyName(tbApReplacePosConfig.getStrategyName());
        tbApReplacePosConfigVO.setOs(tbApReplacePosConfig.getOs());
        tbApReplacePosConfigVO.setProduct(tbApReplacePosConfig.getProduct());
        tbApReplacePosConfigVO.setAdTypeName(tbApReplacePosConfig.getAdTypeName());
        tbApReplacePosConfigVO.setDsp(tbApReplacePosConfig.getDsp());
        ObjectMapper objectMapper = new ObjectMapper();
        List<TbApReplacePosPlatformConfigVO> platFormList = objectMapper.readValue(tbApReplacePosConfig.getPlatformConfig(), new TypeReference<List<TbApReplacePosPlatformConfigVO>>() {});
        tbApReplacePosConfigVO.setPlatformConfig(platFormList);
        tbApReplacePosConfigVO.setExtend1(tbApReplacePosConfig.getExtend1());
        tbApReplacePosConfigVO.setExtend2(tbApReplacePosConfig.getExtend2());
        tbApReplacePosConfigVO.setIsEnabled(tbApReplacePosConfig.getIsEnabled());
        tbApReplacePosConfigVO.setIsDeleted(tbApReplacePosConfig.getIsDeleted());
        tbApReplacePosConfigVO.setCreateTime(tbApReplacePosConfig.getCreateTime());
        tbApReplacePosConfigVO.setUpdateTime(tbApReplacePosConfig.getUpdateTime());
        return tbApReplacePosConfigVO;
    }


}
