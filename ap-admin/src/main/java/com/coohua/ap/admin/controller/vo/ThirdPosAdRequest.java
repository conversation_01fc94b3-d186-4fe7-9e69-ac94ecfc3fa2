package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.base.constants.AdTypeSub;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/13
 */
@Data
public class ThirdPosAdRequest {
    private Integer id;
    private Integer platformCode;
    private Integer companyId;
    private Integer os;
    private Integer appId;
    private String posName;
    private Integer adType;
    private List<String> adSubType;
    private Double adPrice;
    private Integer adTypeOwn;
    private List<Integer> adSite;
    private Boolean systemCreate = false;
    private String renderType;

    // 扩展字段
    private String account;
    private String name;
    private Integer csjCompanyId;
    private Integer gdtCompanyId;
    private Integer ksCompanyId;
    private Integer bdCompanyId;
    private Integer oppoCompanyId;
    private Integer csjAppId;
    private Integer gdtAppId;
    private Integer ksAppId;
    private Integer bdAppId;
    private Integer oppoAppId;

    private Integer isBidding;
    private Integer biddingType;
    private Integer isSetPrice;
    private Integer innerType;
    private String secret;
    /**
     * 投放渠道
     */
    private String dsp;

    public String buildThirdPosName(String appName){
        Platform platform = Platform.getPlatform(this.platformCode);
        String platformDesc = "";
        if (Platform.CSJ.equals(platform)) {
            platformDesc = "csj";
        }else if (Platform.GDT.equals(platform)){
            platformDesc = "gdt";
        }else if (Platform.KS.equals(platform)){
            platformDesc = "ks";
        }else if (Platform.BD.equals(platform)){
            platformDesc = "bd";
        }else if (Platform.OPPO.equals(platform)){
            platformDesc = "oppo";
        }

        if (StringUtils.isNotBlank(dsp)) {
            platformDesc = platformDesc + "-" + dsp;
        }

        if (StringUtils.isNotBlank(posName) && posName.contains("perk")) {
            platformDesc = platformDesc + "-" + "perk";
        }

        AdTypeSub adType = AdTypeSub.get(this.adTypeOwn);
        String subType = AdTypeSub.getAdTypeName(adType);
        appName = appName.replace("_android","").replace("_ios", "");
        if (this.getSystemCreate()){
            if (Integer.valueOf(1).equals(isBidding)){
                appName = appName + "-bidding";
            }
            String rx  = DateUtil.dateToString(new Date(),DateUtil.ISO_DATE_FORMAT);
            String typeStr = adType.getType() + "";
            String level = typeStr.length() == 4 ? "000":typeStr.substring(4);
            String osStr = os == 1?"android":"ios";
            return appName + "-" + osStr +"_"+ platformDesc + "-" + subType + "("+ rx +")" + level + "-"
                    + this.getAdPrice().intValue();
        }
        return  appName + "-" + platformDesc + "-" + subType + this.posName;
    }
}
