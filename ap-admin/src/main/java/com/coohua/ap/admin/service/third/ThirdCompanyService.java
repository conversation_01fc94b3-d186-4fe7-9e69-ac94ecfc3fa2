package com.coohua.ap.admin.service.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.OpType;
import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.ThirdCompanyMapper;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.service.third.dto.csj.req.AppQueryRequest;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AppInfo;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AppPageResponse;
import com.coohua.ap.admin.service.third.dto.csj.rsp.Response;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAppInfoQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAppInfo;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAppPageResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtResponse;
import com.coohua.ap.admin.service.third.dto.ks.request.KsQueryAppRequest;
import com.coohua.ap.admin.service.third.dto.ks.response.KsAppResponse;
import com.coohua.ap.admin.service.third.dto.ks.response.KsResponse;
import com.coohua.ap.admin.service.third.service.csj.CsjBaseService;
import com.coohua.ap.admin.service.third.service.gdt.GdtBaseService;
import com.coohua.ap.admin.service.third.service.ks.KsBaseService;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@Service
public class ThirdCompanyService {

    @Autowired
    private ThirdLogService thirdLogService;
    @Resource
    private ThirdCompanyMapper thirdCompanyMapper;
    @Autowired
    private CsjBaseService csjBaseService;
    @Autowired
    private GdtBaseService gdtBaseService;
    @Autowired
    private KsBaseService ksBaseService;

    public void queryList(Integer index,Integer limit,String name,Page<ThirdCompanyEntity> page){
        page.setItems(thirdCompanyMapper.pageList(index,limit,name));
        page.setCount(thirdCompanyMapper.pageListCount(index,limit,name));
    }

    public ThirdCompanyEntity load(Integer companyId){
        return thirdCompanyMapper.load(companyId);
    }

    @Transactional(transactionManager = "apdatasourceDataSourceTransactionManager")
    public void insert(ThirdCompanyEntity entity,String name,String account){
        int count = thirdCompanyMapper.countExist(entity.getMainBody());
        if (count > 0){
            throw new RuntimeException("主体名称已存在，请重新填写");
        }
        entity.setAppCount(getAppNumber(entity));
        thirdCompanyMapper.insert(entity);
        thirdLogService.saveLog(account,name, OpType.ADD_COMPANY,entity.getId(),null, JSON.toJSONString(entity));
    }

    @Transactional(transactionManager = "apdatasourceDataSourceTransactionManager")
    public void update(ThirdCompanyEntity entity,String name,String account){
        ThirdCompanyEntity entityDb = thirdCompanyMapper.load(entity.getId());
        ThirdCompanyEntity record = new ThirdCompanyEntity();
        record.setId(entity.getId());
        record.setSecurityKey(entity.getSecurityKey());
        record.setUserId(entity.getUserId());
        record.setMainBody(entity.getMainBody());
        record.setPlatformCode(entity.getPlatformCode());
        Platform platform = Platform.getPlatform(entity.getPlatformCode());
        record.setPlatformDesc(platform.getDesc());

        // 穿山甲
        if (Platform.CSJ.equals(platform)) {
            record.setMaskRuleId(entity.getMaskRuleId());
            record.setRoleId(entity.getRoleId());
        }

        thirdCompanyMapper.update(record);
        thirdLogService.saveLog(account,name,OpType.UPDATE_COMPANY,entity.getId(),JSON.toJSONString(entityDb),JSON.toJSONString(entity));
    }

    public List<Integer> getAllCompanyId(){
        return thirdCompanyMapper.selectAll();
    }

    public List<ThirdCompanyEntity> queryAll(){
        return thirdCompanyMapper.queryAll();
    }

    public void refreshCompanyApp(Integer id){
        ThirdCompanyEntity entity = thirdCompanyMapper.load(id);
        ThirdCompanyEntity record = new ThirdCompanyEntity();
        record.setId(id);
        record.setAppCount(getAppNumber(entity));
        thirdCompanyMapper.update(record);
    }


    private int getAppNumber(ThirdCompanyEntity entity){
        Platform platform = Platform.getPlatform(entity.getPlatformCode());
        if (Platform.CSJ.equals(platform)){
            AppQueryRequest request = buildBaseQuery(entity,1);

            Response<AppPageResponse>  responseResponse = csjBaseService.queryAppList(request);
            if (responseResponse.isSuccess()){
                return responseResponse.getData().getPage_info().getTotal_number();
            }else {
                throw new RuntimeException(responseResponse.getMessage());
            }
        }else if (Platform.GDT.equals(platform)){
            GdtAppInfoQueryRequest request = buildGdtBaseQuery(entity,1);
            GdtResponse<GdtAppPageResponse> responseGdtResponse = gdtBaseService.queryApp(request,entity.getSecurityKey());
            if (responseGdtResponse.isSuccess()){
                return responseGdtResponse.getData().getPage_info().getTotal_number();
            }else {
                throw new RuntimeException(responseGdtResponse.getMsg());
            }
        }else if (Platform.KS.equals(platform)){
            KsQueryAppRequest ksQueryAppRequest = buildKsBaseQuery(entity,1);
            KsResponse<List<KsAppResponse>> response = ksBaseService.queryAppList(ksQueryAppRequest);
            if (response.isSuccess()){
                return response.getPage_info().getTotal_count();
            }else {
                throw new RuntimeException(response.getError_msg());
            }
        }
        return 0;
    }

    /**
     * 查询该主体下所有应用...
     * @param entity 主体账户实体
     * @return 全部应用列表....
     */
    public List<ThirdAppEntity> queryAllThisCompany(ThirdCompanyEntity entity){
        List<ThirdAppEntity> thirdAppEntities = new ArrayList<>();
        try {
            Platform platform = Platform.getPlatform(entity.getPlatformCode());
            if (Platform.CSJ.equals(platform)) {
                List<AppInfo> appInfoList = queryCsjAll(entity);
                thirdAppEntities.addAll(convertCsj(entity, appInfoList));
            } else if (Platform.GDT.equals(platform)) {
                List<GdtAppInfo> gdtAppInfoList = queryGdtAll(entity);
                thirdAppEntities.addAll(convertGdt(entity, gdtAppInfoList));
            } else if (Platform.KS.equals(platform)){
                List<KsAppResponse> ksAppResponseList = queryKsAll(entity);
                thirdAppEntities.addAll(convertKs(entity, ksAppResponseList));
            }
        }catch (Exception e){
            log.error("Query Error:", e);
        }
        return thirdAppEntities;
    }


    public List<ThirdAppEntity> queryAllThisStatusEx(ThirdCompanyEntity entity){
        List<ThirdAppEntity> thirdAppEntities = new ArrayList<>();
        try {
            Platform platform = Platform.getPlatform(entity.getPlatformCode());
            if (Platform.CSJ.equals(platform)) {
                List<AppInfo> appInfoList = queryCsjAll(entity);
                thirdAppEntities.addAll(convertCsj(entity, appInfoList));
            } else if (Platform.GDT.equals(platform)) {
                List<GdtAppInfo> gdtAppInfoList = queryGdtAll(entity);
                thirdAppEntities.addAll(convertGdt(entity, gdtAppInfoList));
            } else if (Platform.KS.equals(platform)){
                List<KsAppResponse> ksAppResponseList = queryKsAll(entity);
                thirdAppEntities.addAll(convertKs(entity, ksAppResponseList));
            }
        }catch (Exception e){
            log.error("Query Error:", e);
        }
        return thirdAppEntities;
    }

    private List<ThirdAppEntity> convertCsj(ThirdCompanyEntity thirdCompanyEntity, List<AppInfo> appInfoList){
        return appInfoList.stream().map(appInfo -> {
            ThirdAppEntity entity = new ThirdAppEntity();
            entity.setSwitchFlag(1);
            entity.setPlatformCode(thirdCompanyEntity.getPlatformCode());
            entity.setCompanyId(thirdCompanyEntity.getId());
            entity.setMainBody(thirdCompanyEntity.getMainBody());
            entity.setAppId(String.valueOf(appInfo.getApp_id()));
            entity.setAppName(appInfo.getApp_name());
            App app = AppBuilder.like(appInfo.getApp_name());
            if (app == null){
                log.error("AppNotFind:{}",appInfo.getApp_name());
            }else {
                entity.setProduct(app.appId());
            }
            entity.setAppStatus(appInfo.getStatus() == 2?1:0);
            entity.setWorkLite(appInfo.getApp_category_code().toString());
            entity.setOs("ios".equals(appInfo.getOs_type()) ? 2:1);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("keyWords","");
            jsonObject.put("downloadUrl",appInfo.getDownload_url());
            jsonObject.put("packageName",appInfo.getPackage_name());
            jsonObject.put("apkSign",appInfo.getApk_sign());
            jsonObject.put("desc","");
            entity.setAppExt(jsonObject.toJSONString());
            Date now = new Date();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            return entity;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<ThirdAppEntity> convertKs(ThirdCompanyEntity thirdCompanyEntity,List<KsAppResponse> appResponseList){
        return appResponseList.stream().map(appInfo -> {
            ThirdAppEntity entity = new ThirdAppEntity();
            App app = AppBuilder.like(appInfo.getName());
            if (app == null){
                log.error("AppNotFind:{}",appInfo.getName());
            }else {
                entity.setProduct(app.appId());
            }
            entity.setSwitchFlag(1);
            entity.setPlatformCode(thirdCompanyEntity.getPlatformCode());
            entity.setCompanyId(thirdCompanyEntity.getId());
            entity.setMainBody(thirdCompanyEntity.getMainBody());
            entity.setAppId(appInfo.getAppId());
            entity.setAppName(appInfo.getName());
            entity.setAppStatus(appInfo.getAuditStatus() == 3 ?1:0);
            entity.setWorkLite(appInfo.getIndustryId().toString());
            entity.setOs(appInfo.getMediumType());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("downloadUrl","");
            jsonObject.put("packageName",appInfo.getPackageName());
            jsonObject.put("apkSign","");
            jsonObject.put("desc","");
            entity.setAppExt(jsonObject.toJSONString());
            Date now = new Date();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            return entity;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<ThirdAppEntity> convertGdt(ThirdCompanyEntity thirdCompanyEntity, List<GdtAppInfo> appInfoList){
        return appInfoList.stream().map(appInfo -> {
            ThirdAppEntity entity = new ThirdAppEntity();
            App app = AppBuilder.like(appInfo.getMedium_name());
            if (app == null){
                log.error("AppNotFind:{}",appInfo.getMedium_name());
            }else {
                entity.setProduct(app.appId());
            }
            entity.setSwitchFlag(1);
            entity.setPlatformCode(thirdCompanyEntity.getPlatformCode());
            entity.setCompanyId(thirdCompanyEntity.getId());
            entity.setMainBody(thirdCompanyEntity.getMainBody());
            entity.setAppId(String.valueOf(appInfo.getApp_id()));
            entity.setAppName(appInfo.getMedium_name());
            entity.setAppStatus("Approved".equals(appInfo.getStatus())?1:0);
            entity.setWorkLite(appInfo.getIndustry_id().toString());
            entity.setOs(appInfo.getOs());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("downloadUrl",appInfo.getDetail_url());
            jsonObject.put("packageName",appInfo.getPackage_name());
            jsonObject.put("apkSign","");
            jsonObject.put("desc","");
            entity.setAppExt(jsonObject.toJSONString());
            Date now = new Date();
            entity.setCreateTime(now);
            entity.setUpdateTime(now);
            return entity;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private AppQueryRequest buildBaseQuery(ThirdCompanyEntity entity,Integer page){
        AppQueryRequest request = new AppQueryRequest();
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setUser_id(Integer.valueOf(entity.getUserId()));
        request.setRole_id(entity.getRoleId());
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(entity.getSecurityKey(),timestamp,nonce));
        request.setPage(page);
        request.setPage_size(10);
        request.setVersion("1.0");
        return request;
    }

    private KsQueryAppRequest buildKsBaseQuery(ThirdCompanyEntity entity,Integer page){
        KsQueryAppRequest request = new KsQueryAppRequest();
        request.setPage(page);
        request.setPageSize(10);
        request.setAk(String.valueOf(entity.getUserId()));
        request.setSk(entity.getSecurityKey());
        return request;
    }
    private GdtAppInfoQueryRequest buildGdtBaseQuery(ThirdCompanyEntity entity,Integer page){
        GdtAppInfoQueryRequest request = new GdtAppInfoQueryRequest();
        request.setMember_id(Long.valueOf(entity.getUserId()));
        request.setPage(page);
        request.setPage_size(10);
        return request;
    }

    private List<AppInfo> queryCsjEx(ThirdCompanyEntity entity){
        List<AppInfo> appInfos = new ArrayList<>();
        AppQueryRequest request = buildBaseQuery(entity,1);
        request.setStatus(new Integer[]{3,4,5});
        int total;
        Response<AppPageResponse>  responseResponse = csjBaseService.queryAppList(request);
        if (responseResponse.isSuccess()){
            appInfos.addAll(responseResponse.getData().getApp_list());
            total = responseResponse.getData().getPage_info().getTotal_page();
        }else {
            throw new RuntimeException(responseResponse.getMessage());
        }
        int page = total;
        if (page > 1) {
            for (int i = 2; i <= page; i++) {
                AppQueryRequest tempRequest = buildBaseQuery(entity,i);
                Response<AppPageResponse>  tempResponse = csjBaseService.queryAppList(tempRequest);
                if (tempResponse.isSuccess()) {
                    appInfos.addAll(tempResponse.getData().getApp_list());
                } else {
                    throw new RuntimeException(tempResponse.getMessage());
                }
            }
        }

        return appInfos;
    }

    private List<AppInfo> queryCsjAll(ThirdCompanyEntity entity){
        List<AppInfo> appInfos = new ArrayList<>();
        AppQueryRequest request = buildBaseQuery(entity,1);
        int total;
        Response<AppPageResponse>  responseResponse = csjBaseService.queryAppList(request);
        if (responseResponse.isSuccess()){
            appInfos.addAll(responseResponse.getData().getApp_list());
            total = responseResponse.getData().getPage_info().getTotal_page();
        }else {
            throw new RuntimeException(responseResponse.getMessage());
        }
        int page = total;
        if (page > 1) {
            for (int i = 2; i <= page; i++) {
                AppQueryRequest tempRequest = buildBaseQuery(entity,i);
                Response<AppPageResponse>  tempResponse = csjBaseService.queryAppList(tempRequest);
                if (tempResponse.isSuccess()) {
                    appInfos.addAll(tempResponse.getData().getApp_list());
                } else {
                    throw new RuntimeException(tempResponse.getMessage());
                }
            }
        }

        return appInfos;
    }

    private List<KsAppResponse> queryKsAll(ThirdCompanyEntity entity){
        List<KsAppResponse> ksAppResponseList = new ArrayList<>();
        KsQueryAppRequest request = buildKsBaseQuery(entity,1);
        int total;
        KsResponse<List<KsAppResponse>> ksResponse = ksBaseService.queryAppList(request);
        if (ksResponse.isSuccess()){
            ksAppResponseList.addAll(ksResponse.getData());
            total = ksResponse.getPage_info().getTotal_count()/ksResponse.getPage_info().getPage_size();
        }else {
            throw new RuntimeException(ksResponse.getError_msg());
        }

        int page = total;
        if (page > 1) {
            for (int i = 2; i <= page; i++) {
                KsQueryAppRequest tempKsResponse = buildKsBaseQuery(entity,i);
                KsResponse<List<KsAppResponse>> tempResponse = ksBaseService.queryAppList(tempKsResponse);
                if (tempResponse.isSuccess()) {
                    ksAppResponseList.addAll(tempResponse.getData());
                } else {
                    throw new RuntimeException(tempResponse.getError_msg());
                }
            }
        }
        return ksAppResponseList;
    }

    private List<GdtAppInfo> queryGdtAll(ThirdCompanyEntity entity){
        List<GdtAppInfo> gdtAppInfos = new ArrayList<>();
        GdtAppInfoQueryRequest request = buildGdtBaseQuery(entity,1);
        int total;
        GdtResponse<GdtAppPageResponse> responseGdtResponse = gdtBaseService.queryApp(request,entity.getSecurityKey());
        if (responseGdtResponse.isSuccess()){
            gdtAppInfos.addAll(responseGdtResponse.getData().getList());
            total = responseGdtResponse.getData().getPage_info().getTotal_page();
        }else {
            throw new RuntimeException(responseGdtResponse.getMsg());
        }

        int page = total;
        if (page > 1) {
            for (int i = 2; i <= page; i++) {
                GdtAppInfoQueryRequest tempRequest = buildGdtBaseQuery(entity,i);
                GdtResponse<GdtAppPageResponse> tempResponse = null;
                // 最大重试3次
                int retryCount = 0;
                while (retryCount < 3) {
                    tempResponse = gdtBaseService.queryApp(tempRequest, entity.getSecurityKey());
                    if (tempResponse.isSuccess()) {
                        gdtAppInfos.addAll(tempResponse.getData().getList());
                        break;
                    } else if (tempResponse.getCode() != null && 11005 == tempResponse.getCode()) {
                        retryCount++;
                        log.warn("请求被限流,第{}次重试 page {}", retryCount, i);
                        try {
                            Thread.sleep(300);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            throw new RuntimeException("线程被中断", e);
                        }
                    } else {
                        throw new RuntimeException(tempResponse.getMessage());
                    }
                }
                if (retryCount == 3) {
                    log.error("请求GDT应用失败,重试次数:{},错误码:{},错误信息:{}",
                            retryCount, tempResponse.getCode(), tempResponse.getMsg());
                    throw new RuntimeException("重试3次仍被限流,请稍后再试");
                }
            }
        }
        return gdtAppInfos;
    }

    public void delete(Integer id,String name,String account){
        ThirdCompanyEntity entity = thirdCompanyMapper.load(id);
        Optional.ofNullable(entity).orElseThrow(() -> new RuntimeException("模板不存在"));
        thirdCompanyMapper.delete(entity.getId());
        thirdLogService.saveLog(account,name,OpType.UPDATE_APPLICATION,entity.getId(),JSON.toJSONString(entity),null);
    }
}
