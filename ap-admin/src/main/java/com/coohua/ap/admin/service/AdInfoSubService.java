package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.ap.admin.constants.PriceType;
import com.coohua.ap.admin.controller.vo.AdTypeConfigVo;
import com.coohua.ap.admin.controller.vo.ConfigOrientationVO;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.*;
import com.coohua.ap.admin.service.impl.ConfigOrientationServiceImpl;
import com.coohua.ap.admin.utils.third.AdUtils;
import com.coohua.ap.admin.utils.third.Lists;
import com.coohua.ap.admin.utils.third.Strings;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.exceptions.JedisMovedDataException;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Slf4j
@Service
public class AdInfoSubService {

    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private AdInfoNewMapper adInfoNewMapper;
    @Resource
    private AdConfigMapper adConfigMapper;
    @Resource
    private TbApBiddingMapper tbApBiddingMapper;
    @Resource
    private ConfigOrientationMapper configOrientationMapper;
    @Resource
    private ConfigBaseMapper configBaseMapper;
    @Autowired
    private ConfigOrientationServiceImpl configOrientationService;

    @Resource
    private AppConfigMapper appConfigMapper;

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    public void saveOrUpdateAdInfo(Long adId){
        AdInfo adInfoRecord = adInfoNewMapper.load(adId);
        AdInfo adInfo = new AdInfo();
        // 基础广告加载
        ThirdAdModel thirdAdModel = adInfoMapper.getById(adId);
        AdInfoEntity adInfoEntity = adInfoMapper.selectOneAd(adId,thirdAdModel.getProduct());
        List<TbApBidding> tbApBiddingList = tbApBiddingMapper.queryByAdIdAndProduct(adId,thirdAdModel.getProduct());
        boolean tbApBidding = tbApBiddingList.stream()
                .anyMatch(r->r.getSwitchFlag() == 1 && r.getDelFlag() == 1);
        // 映射
        convertAdInfo(adInfo,adInfoEntity,tbApBidding);
        // 有则更新 无插入
        if (adInfoRecord == null){
            adInfoNewMapper.insert(adInfo);
        }else {
            adInfo.setCreateTime(adInfoRecord.getCreateTime());
            adInfoNewMapper.update(adInfo);
        }
        // 更新缓存
        refreshAdRedis(adId);
    }

    private void convertAdInfo(AdInfo adInfo,AdInfoEntity adInfoEntity,boolean tbApBidding){
        // 基础属性
        adInfo.setAdId((long) adInfoEntity.getId());
        adInfo.setAdName(adInfoEntity.getName());
        adInfo.setProduct(adInfoEntity.getProduct());
        adInfo.setProductName(Optional.ofNullable(AppBuilder.getById(adInfoEntity.getProduct()))
                .orElse(App.MASTER).getProductName());
        adInfo.setState(adInfoEntity.getState());
        adInfo.setAdType(adInfoEntity.getType());
        adInfo.setAdTypeName(AdTypeSub.get(adInfoEntity.getType()).getTypeDesc());
        adInfo.setAdPos(adInfoEntity.getAdPos());
        // 广告计划
        AdPlanEntity adPlanEntity = adInfoEntity.getPlan();
        adInfo.setAdPlan(Math.toIntExact(adPlanEntity.getId()));
        adInfo.setAdPlanName(adPlanEntity.getName());

        adInfo.setEcpm(adInfoEntity.getBudget().getEcpm());
        if (tbApBidding){
            adInfo.setPriceType(PriceType.BIDDING.getCode());
        }else {
            adInfo.setPriceType(PriceType.NORMAL.getCode());
        }
        // 三方配置 过滤条件配置
        AdExtEntity adExtEntity = adInfoEntity.getExt();
        String posId = AdUtils.getAdExtPosIdString(adExtEntity);
        String appId = AdUtils.getAppIdString(adInfoEntity);
        PlatformVersion platformVersion = adInfoEntity.getOrientation().getPlatformVersion();
        RegisterTimeOrientation registerTimeOrientation = adInfoEntity.getOrientation().getRegisterTimeOrientation();

        adInfo.setOs(platformVersion.getPlatform());
        adInfo.setAppIdThird(appId);
        adInfo.setPosIdThird(posId);
        if (platformVersion.getPlatform() == 1) {
            adInfo.setAppVersionStart(platformVersion.getAndroidStartVersion());
            adInfo.setAppVersionEnd(platformVersion.getAndRoidEndVersion());
        }else if (platformVersion.getPlatform() == 2){
            adInfo.setAppVersionStart(platformVersion.getIosStartVersion());
            adInfo.setAppVersionEnd(platformVersion.getIosEndVersion());
        }
        adInfo.setRegisterTimeStart(registerTimeOrientation.getStartTime());
        adInfo.setRegisterTimeEnd(registerTimeOrientation.getEndTime());

        adInfo.setLockArea(adInfoEntity.getOrientation().getLockArea());
        adInfo.setFilterRegion(adInfoEntity.getOrientation().getFilterRegion());
        adInfo.setUserIdFilter(adInfoEntity.getOrientation().getTailNumber());
        adInfo.setChannels(adInfoEntity.getOrientation().getChannelId());
        adInfo.setAbTarget(adInfoEntity.getOrientation().getAbPoint());
        adInfo.setDsp(adInfoEntity.getOrientation().getDsp());
        adInfo.setLockActionPoint(adInfoEntity.getOrientation().getLockActionPoint());
        adInfo.setCreateTime(adInfoEntity.getCreateTime());
        adInfo.setUpdateTime(adInfoEntity.getUpdateTime());
    }


    public void saveOrUpdateConfigInfo(Integer id){
        // 已经创建完成 id 肯定是有的
        ConfigOrientationEntity configOrientationEntity = configOrientationMapper.queryOneById(id);
        ConfigOrientationVO vo = configOrientationService.convertConfigOrientationEntity2VO(configOrientationEntity);
        List<Integer> ids = new ArrayList<>(vo.getConfig().values());
        List<ConfigBaseEntity> configBaseEntities = configBaseMapper.queryByIds(ids);

        AdConfig adConfig = new AdConfig();
        AdConfig adConfigRecord = adConfigMapper.load(id);
        // 映射
        convertToSubConfig(adConfig,vo,configBaseEntities);
        // 有则更新 无插入
        if (adConfigRecord == null){
            adConfigMapper.insert(adConfig);
        }else {
            adConfig.setCreateTime(adConfigRecord.getCreateTime());
            adConfigMapper.update(adConfig);
        }
        // 更新缓存
        refreshConfigRedis(id);
    }

    private void convertToSubConfig(AdConfig adConfig,ConfigOrientationVO vo, List<ConfigBaseEntity> configBaseEntities){
        adConfig.setId(vo.getId());
        adConfig.setConfigName(vo.getName());
        adConfig.setProduct(vo.getProduct());
        // 四合一
        adConfig.setConfig(JSON.toJSONString(convertConfigBaseToArrayList(configBaseEntities)));
        adConfig.setAdPos(vo.getAdPos());
        adConfig.setOs(vo.getOs());
        // 定向配置
        adConfig.setFilterRegion(vo.getRegion());
        adConfig.setLockArea(vo.getRegionSide());
        adConfig.setAnonymous(vo.getAnonymous());
        if (Integer.valueOf(1).equals(vo.getRegist().getLimit())) {
            adConfig.setRegisterTimeEnd(vo.getRegist().getEnd());
            adConfig.setRegisterTimeStart(vo.getRegist().getStart());
        }
        if (Integer.valueOf(1).equals(vo.getVersion().getLimit())) {
            adConfig.setAppVersionEnd(vo.getVersion().getEnd());
            adConfig.setAppVersionStart(vo.getVersion().getStart());
        }
        if (Integer.valueOf(1).equals(vo.getSdkVersion().getLimit())) {
            adConfig.setSdkVersionEnd(vo.getSdkVersion().getEnd());
            adConfig.setSdkVersionStart(vo.getSdkVersion().getStart());
        }

        adConfig.setUserIdMatch(vo.getTailNumber());
        adConfig.setState(vo.isState()?1:0);
        adConfig.setPriority(vo.getPriority());
        adConfig.setChannels(vo.getChannelId());
        adConfig.setAbTarget(vo.getAbTest());
        adConfig.setDsp(vo.getDsp());
        adConfig.setLockActionPoint(vo.getLockActionPoint());

        adConfig.setUpdateTime(vo.getUpdateTime());
        adConfig.setCreateTime(vo.getCreateTime());
    }

    // 将瀑布流转化为AdType流
    private List<String> convertConfigBaseToArrayList(List<ConfigBaseEntity> configBaseEntities){
        Set<String> adTypeList = new HashSet<>();
        for (ConfigBaseEntity configBaseEntity : configBaseEntities){
            List<AdTypeConfigVo> adTypeConfigVoList = JSONArray.parseArray(configBaseEntity.getConfig(),AdTypeConfigVo.class);
            for (AdTypeConfigVo configVo:adTypeConfigVoList){
                addLayerToBasic(configVo.getAdType(),adTypeList);
                if (Lists.noEmpty(configVo.getLayer())) {
                    for (String adTypeLayer : configVo.getLayer()){
                        addLayerToBasic(adTypeLayer,adTypeList);
                    }
                }
            }
        }
        return new ArrayList<>(adTypeList);
    }

    private void addLayerToBasic(String layer,Set<String> adTypeList){
        if (Strings.noEmpty(layer)) {
            if (layer.contains("|")) {
                String[] rs = layer.split("[|]");
                if (rs.length > 0) {
                    adTypeList.addAll(Arrays.asList(rs));
                }
            } else {
                adTypeList.add(layer);
            }
        }
    }

    public void refreshAllConfigToRedis(){
        // 获取所有广告，无论是否有效
        for (App app :AppBuilder.values()){
            try {
                log.info("正在更新 {} 的策略",app.getProductName());
                List<AdConfig> adConfigList = adConfigMapper.queryByProduct(app.appId());
                try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
                    Pipeline pipeline = jedis.pipelined();
                    for (AdConfig adConfig : adConfigList){
                        // redis中累加
                        String key = RedisBuilder.buildConfigSub(adConfig.getId());
                        pipeline.set(key,JSON.toJSONString(adConfig));
                    }
                    pipeline.sync();
                } catch (JedisMovedDataException jmde) {
                    apClusterRedisService.get("EmptyKey");
                }
                log.info("完成更新 {} 的策略",app.getProductName());
            }catch (Exception e){
                log.error("更新 {} 广告策略,error:",app.getProductName(),e);
            }
        }
    }

    public void sycnAlreadyExistConfigToNewSub(){
        for (App app :AppBuilder.values()){
            log.info("开始更新产品策略 {} ",app.getProductName());
            List<ConfigOrientationEntity> configOrientationEntities = configOrientationMapper.queryByProduct(app.appId());
            if (Lists.isEmpty(configOrientationEntities)){
                continue;
            }

            List<AdConfig> adConfigList = adConfigMapper.queryByProduct(app.appId());
            Map<Integer,AdConfig> adConfigMap = adConfigList.stream()
                    .collect(Collectors.toMap(AdConfig::getId, r->r,(r1, r2)->r1));

            for (ConfigOrientationEntity configOrientationEntity: configOrientationEntities){
                ConfigOrientationVO vo = configOrientationService.convertConfigOrientationEntity2VO(configOrientationEntity);
                List<Integer> ids = new ArrayList<>(vo.getConfig().values());
                List<ConfigBaseEntity> configBaseEntities = configBaseMapper.queryByIds(ids);

                AdConfig adConfig = new AdConfig();
                AdConfig adConfigRecord = adConfigMap.get(vo.getId());
                // 映射
                convertToSubConfig(adConfig,vo,configBaseEntities);
                // 有则更新 无插入
                if (adConfigRecord == null){
                    adConfigMapper.insert(adConfig);
                }else {
                    adConfig.setCreateTime(adConfigRecord.getCreateTime());
                    adConfigMapper.update(adConfig);
                }
            }

            log.info("完成更新产品策略 {} ",app.getProductName());
        }
    }

    /**
     * 同步已存在的广告到新系统
     */
    public void sycnAlreadyExistAdToNewSub(){
        // TODO 改为批量写入和更新
        for (App app :AppBuilder.values()){
            log.info("开始更新产品 {} ",app.getProductName());
            List<AdInfoEntity> adInfoEntityList = adInfoMapper.selectAllProductAd(app.appId(),1);
            if (Lists.isEmpty(adInfoEntityList)){
                continue;
            }
            List<Integer> adIdList = adInfoEntityList.stream().map(AdInfoEntity::getId).collect(Collectors.toList());
            List<TbApBidding> tbApBiddingList = tbApBiddingMapper.queryAdIdListAndProduct(adIdList,app.getAppId());
            tbApBiddingList = tbApBiddingList.stream().filter(r->r.getDelFlag() == 1&& r.getSwitchFlag() ==1).collect(Collectors.toList());


            List<AdInfo> adInfoList = adInfoNewMapper.queryByProduct(app.appId());
            Map<Long,AdInfo> adInfoMap =  adInfoList.stream()
                    .collect(Collectors.toMap(AdInfo::getAdId, r->r,(r1, r2)->r1));

            Map<Long,TbApBidding> tbApBiddingMap =  tbApBiddingList.stream()
                    .collect(Collectors.toMap(TbApBidding::getAdId, r->r,(r1, r2)->r1));


            for (AdInfoEntity adInfoEntity: adInfoEntityList){
                AdInfo adInfoRecord = adInfoMap.get((long) adInfoEntity.getId());
                TbApBidding tbApBidding = tbApBiddingMap.get((long) adInfoEntity.getId());
                AdInfo adInfo = new AdInfo();
                convertAdInfo(adInfo,adInfoEntity,tbApBidding == null);
                if (adInfoRecord == null){
                    adInfoNewMapper.insert(adInfo);
                }else {
                    adInfo.setCreateTime(adInfoRecord.getCreateTime());
                    adInfoNewMapper.update(adInfo);
                }
            }

            log.info("完成更新产品 {} ",app.getProductName());
        }
    }

    public void refreshAllAdToRedis(){
        // 获取所有广告，无论是否有效
        for (App app :AppBuilder.values()){
            try {
                log.info("正在更新 {} 的广告",app.getProductName());
                List<AdInfo> adInfoList = adInfoNewMapper.queryByProduct(app.appId());
                try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
                    Pipeline pipeline = jedis.pipelined();
                    for (AdInfo adInfo : adInfoList){
                        // redis中累加
                        String key = RedisBuilder.buildAdSubInfo(adInfo.getAdId());
                        pipeline.set(key,JSON.toJSONString(adInfo));
                    }
                    pipeline.sync();
                } catch (JedisMovedDataException jmde) {
                    apClusterRedisService.get("EmptyKey");
                }
                log.info("完成更新 {} 的广告",app.getProductName());
            }catch (Exception e){
                log.error("更新 {} 广告异常,error:",app.getProductName(),e);
            }
        }
    }

    private void refreshAdRedis(Long adId){
        String key = RedisBuilder.buildAdSubInfo(adId);
        AdInfo adInfo = adInfoNewMapper.load(adId);
        apClusterRedisService.set(key, JSON.toJSONString(adInfo));
        log.info("已经刷新 {} 的Redis缓存",adInfo);
    }

    private void refreshConfigRedis(Integer id){
        // 先确定Config对应adType - 在确认Config对应的adId
        String key = RedisBuilder.buildConfigSub(id);
        AdConfig adConfig = adConfigMapper.load(id);
        apClusterRedisService.set(key, JSON.toJSONString(adConfig));
        log.info("已经刷新 {} 的Redis缓存",adConfig.getId());
    }
}
