package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbConfigNewFilter;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description tb_config_new_filterMapper
 * <AUTHOR> @date 2021-06-23
 */
@Mapper
@Repository
public interface TbConfigNewFilterMapper {

    /**
     * 新增
     **/
    int insert(TbConfigNewFilter tbConfigNewFilter);

    /**
     * 刪除
     **/
    int delete(int id);

    /**
     * 更新
     **/
    int update(TbConfigNewFilter tbConfigNewFilter);

    /**
     * 查询 根据主键 id 查询
     **/
    TbConfigNewFilter load(int id);

    /**
     * 查询 分页查询
     **/
    List<TbConfigNewFilter> pageList(int offset,int pagesize);

    /**
     * 查询 分页查询 count
     **/
    int pageListCount(int offset,int pagesize);


    @Select({"select * from tb_config_new_filter where config_id = #{configId} and del_flag = 1"})
    List<TbConfigNewFilter> selectListByConfigId(@Param("configId") Integer configId);

    @Update({"update tb_config_new_filter set index_ix = #{newIndex} where id = #{id}"})
    void updateById(@Param("id")Integer id,@Param("newIndex") Integer index);

}
