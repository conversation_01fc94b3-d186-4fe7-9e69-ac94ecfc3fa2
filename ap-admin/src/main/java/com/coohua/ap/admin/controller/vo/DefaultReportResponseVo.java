package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.AdDataReportEntity;

import java.text.ParseException;
import java.util.List;

/**
 * @ClassName: DefaultReportResponseVo
 * @Description: 对数据报表返回数据的约束性
 * @Author: fan jin yang
 * @Date: 2020/4/24
 * @Version: V1.0
 **/
public abstract class DefaultReportResponseVo {

    public abstract List<AdDataReportEntity> convertReportEntity() throws ParseException;

}
