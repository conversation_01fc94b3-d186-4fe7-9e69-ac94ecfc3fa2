package com.coohua.ap.admin.controller.vo;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by liuliandong on 17/4/16.
 */
public class AdExt {
    //------API激励视频------
    private int selfEndCard;    //激励视频是否使用自家的endCard
    private int endCardReward;  //endCardHtml点击引导显示及金币数（0为不显示引导）
    private int showVideoCard;  //是否展示激励视频卡片    0-否 1-是
    // ----- DSP -----
    private String iosPosId; // iOS广告位ID, JS广告ID(复用此字段)
    private String iosBakPosId; // iOS备用广告位ID
    private String androidPosId; // Android广告位ID
    private String androidBakPosId; // Android备选广告位ID
    private String iosAppId;     //iOS广点通APPID
    private String androidAppId; //安卓广点通APPID
    private int preType; // 优先选择
    private boolean downloadDrive; // 是否激励下载
    private int extendType; // 扩展类型：0-无，1-原生广告，2-模板广告，3-激励视频广告，4-全屏广告,5-信息流原生，6-插屏原生，7-draw视频广告,8-直客视频横屏，9-直客视频竖屏
    private boolean template; // （已作废，为兼容老版本保留）是否是模版广告，true:是，false：否
    private int templateImgSize; // 模版广告大小图设置，0-大图，1-小图
    private int mintegralType; //mintegral投放类型，0-静态图，1-视频
    private String adTag;
    private String adTagRatio;
    private boolean showAdLabel; // 是否显示广告标签
    private String adLabel; // 广告标签
    private String adSource; // 广告来源描述
    // ----- DSP END -----fs

    // ----- Native, CPC, CPD -----
    private int activationTime; // 用户下载后，安装 到 激活的时间间隔，满足这个时间间隔，可以加二跳礼盒。（单位：秒）
    private int style;  // 广告样式 101-小图，201-大图，301-组图，401-视频,501-banner图
    private String title; // 广告标题／JS广告域名定投(复用此字段)
    private String content; // 广告内容
    private String clkUrl; // 广告落地页地址
    private List<String> imgUrls = new ArrayList<>(); // 广告图片物料地址
    private String impTrackUrl; // 广告曝光回调链接
    private String clkTrackUrl; // 广告点击回调链接
    private String appPkgName; // 下载类广告的APP包名
    private String downloadUrl; // 下载类广告 APP下载链接
    // 视频相关
    private String adm; // 视频资源链接
    private String videoStartTrackUrl; // 视频播放开始监控汇报地址
    private String videoFinishTrackUrl; // 视频播放结束监控汇报地址
    private String videoPauseTrackUrl; // 视频播放暂停监控汇报地址
    private String itid; // iOS广告的iTunes id，用于内部打开。
    private int width; // 视频宽度，单位：像素
    private int height; // 视频高度，单位：像素
    private int duration; // 视频时长，单位：毫秒
    private long countDownGift; // 对于视频下载类广告，倒计时此字段指定的毫秒数后，可以县级下载获取礼盒按钮。
    // ----- Native, CPC, CPD END -----

    // ----- SHARE -----
    private String shareImgUrl; // 分享图片地址
    private int shareType; // 分享类型 1:微信朋友圈，2:微信，3:QQ，4:QQ空间
    private int styleType; // 样式类型 1:图片+链接，2:文字+链接，3:多次分享，4:分享网址
    private Boolean noAppId; // AppId类型 true:noAppId，false:AppId
    private boolean detailPageShare; // 是否在详情页分享
    private boolean newuserShare; // 是否新手任务分享
    private String url; // 分享链接 热搜链接
    private String replaceShareDomain; // 替换的分享连接域名
    private int industry; // 所属行业
    private int sharePrice; // 分享单价

    // ----- SHARE END -----

    // ----- HOT WORD -----
    private String word;//热搜礼盒，直客广告热词
    private int pr;//热搜礼盒获得礼盒的概率
    private int twiceJumpTimes; // 获取礼盒需要客户端jump的次数（二跳次数）
    private int sourceFrom; // 广告来源，1-百度，2-搜狗
    private int fetchStrategy; // 拉取策略，1-客户端替换关键词，2-客户端拉取链接
    private String hotWordAdUrl; // 广告链接
    private String fetchUrl; // 拉取接口
    // ----- HOT WORD -----

    // CPO广告
    private String deeplinkUrl; // 原生应用落地页地址
    private int deeplinkOpenType; // 原生 or 网页
    private String deeplinkPkgName; // 需要唤起的原生应用包名
    private int deeplinkAppType; // 调起应用类型，1-唯品会，2-淘宝，3-苏宁，4-京东


    private int isMiniProgram;//是否微信小程序 0-否 1-是
    private String miniProgramId;//微信小程序id
    private String miniProgramPath; //微信小程序跳转路径
    private String miniProgramWxId;//小程序关联微信ID


    private String miniProgramName;//小程序名称
    private String miniIcon;//小程序icon
    private String miniShareTitle;//分享标题
    private String miniShareImage;//分享图片
    private String miniMiddleBtnText;//小程序中间页按钮文案
    private String miniMiddleImage;//小程序中间页全屏图
    private Integer miniOpenStyle; // 小程序打开方式：1-FEED流直接打开，2-中间引导页打开
    private String miniLogoImgUrl; // 小程序中间引导页LOGO图片地址
    private String miniName; // 小程序中间引导页名称
    private String miniProductDesc; // 小程序中间引导页产品描述
    private String miniProductImgUrl; // 小程序中间引导页产品配图
    private String miniMissionDesc; // 小程序中间引导页 任务描述，格式：item1\nitem2\nitem3...
    private String miniGuideImgUrl; // 小程序中间引导页 引导图片
    private String miniRemainMissionDesc; // 小程序中间引导页 留存任务描述 20字符以内
    private String miniProgramPkg; // 关联小程序包名
    private String miniFeedCreditImg; // 小程序引导图：信息流激励
    private String miniFeedNormalImg; // 小程序引导图：信息流普通
    private String miniTaskImg; // 小程序引导图：任务大厅
    private Boolean miniBindMiddlePage; // 是否已绑定中转小程序
    private Integer miniNonRepeatPeriod; // 小程序排重周期，0-长期，1-短期

    private int miniStartWay;//小程序启动方式
    private String adAppId;//广告主appid
    private String adOriginalId;//广告主原始id
    private String adMiniPath;//广告主跳转路径
    private String adMiniAppId;//广告主小程序appid
    private String adPackageName;//广告主客户端包名
    private String ownAppId;//自家appid
    private String ownOriginalId;//自家原始id
    private String ownMiniPath;//自家跳转路径
    private String ownMiniAppId;//自家小程序appid
    private String ownPackageName;//自家客户端包名

    private String transferMiniProgramId; // 中转小程序id
    private String transferMiniProgramPath; // 中转小程序路径
    private String transferMiniProgramWxId; // 中转小程序关联微信id

    private Integer otherBrowser;// 热搜广告-3-是否外部浏览器打开

    // 点点赚相关配置
    private int readNum; // 阅读篇数
    private int readTime; // 每篇停留时长,单位秒
    private long rewardGold; // 奖励金币数
    private int priority; //优先级
    private int clickEarnType; // 点点赚类型 1-新闻站，2-互动
    private long clickEarnBudget; // 点点赚投放限额，单位分
    private Integer readNumRange;//阅读篇数范围
    private Integer readTimeRange;// 阅读时长范围
    private int guidType; //引导选项，0-仅底部，1-仅悬浮，2-悬浮+底部，默认为0
    private int templateType; // 模板提示类型，0-新闻站，1-互动，2-刷广点通弹窗
    private String indexTip; // 首页提示
    private String readTip; // 阅读引导
    private String pageTip;// 翻页引导
    private String continuousPageTip;// 连续翻页引导

    // 应用推荐相关配置
    private String downloadDesc; // 下载描述
    private String signDesc; // 签到描述
    private Integer appAdType; // 广告类型，0:CPA类型，1:唤醒类
    private String signUrl; // 签到地址
    private Integer awakeType; // 唤醒类型 0:deepLink ,1:className
    private String icon;// 图标
    private Boolean hasDeeperTask; // 是否有深度任务
    private Integer deeperTaskID; // 深度任务ID
    private Integer deeperTaskGold; // 深度任务奖励
    private String deeperTaskDesc; // 深度任务描述

    // CPA改版后相关配置
    private float apkSize; // 安装包大小
    private long downloadNum; //下载人数

    // CPC小程是否按照时长奖励，默认为false表示不按照时长奖励
    private boolean timeReward;

    String gdtUnitId;

    private boolean defaultAdTwiceJump; // 打底广告是否二跳

    // JS广告配置 begin
    private String jsDomainName; // 定投域名

    private JsAdPosConfig jsAdPosClose; // JS广告 查看全文广告位配置

    private JsAdPosConfig jsAdPosOpen; // JS广告 资讯末尾广告位配置

    private List<JsAdPosConfig> jsAdPosRecommend; // JS广告 相关推荐广告位配置
    // JS广告配置 end

    // 阅读60秒广告相关配置
    private int openType;    // 打开类型类型，0:内开，1:外开,2:时长+多跳
    private String detail;  // 详情文案
    private String buttonDesc;  // 按钮文案

    private boolean recommend; // 是否是首推任务，true:是首推任务，false:不是
    private boolean twiceJump; // 是否是二跳，true:二跳，false:不是，默认为false
    private int twiceJumpTime; // 二跳时间
    private int jumpTimes; // 跳转次数

    // 直客视频相关字段
    // 复用字段：width:视频宽度， height:视频高度，duration:视频时长，单位毫秒， content:宣传文案
    // clkUrl:广告落地页地址, impTrackUrl:广告曝光回调链接, clkTrackUrl: 广告点击回调链接
    private String logoUrl; // logo图片地址
    private String videoUrl; // 视频地址
    private String coverImgUrl; // 封面图地址
    private String videoSize; // 视频大小
    private Long playNum; // 播放次数

    private String adTypeDesc; // 广告类型描述，用于数据平台做广告类型的统一

    public boolean isRecommend() {
        return recommend;
    }

    public void setRecommend(boolean recommend) {
        this.recommend = recommend;
    }

    public boolean isTwiceJump() {
        return twiceJump;
    }

    public void setTwiceJump(boolean twiceJump) {
        this.twiceJump = twiceJump;
    }

    public int getTwiceJumpTime() {
        return twiceJumpTime;
    }

    public void setTwiceJumpTime(int twiceJumpTime) {
        this.twiceJumpTime = twiceJumpTime;
    }

    public AdExt() {

    }

    public String getIosPosId() {
        return iosPosId;
    }

    public void setIosPosId(String iosPosId) {
        this.iosPosId = iosPosId;
    }

    public String getIosBakPosId() {
        return iosBakPosId;
    }

    public void setIosBakPosId(String iosBakPosId) {
        this.iosBakPosId = iosBakPosId;
    }

    public String getAndroidPosId() {
        return androidPosId;
    }

    public void setAndroidPosId(String androidPosId) {
        this.androidPosId = androidPosId;
    }

    public String getAndroidBakPosId() {
        return androidBakPosId;
    }

    public void setAndroidBakPosId(String androidBakPosId) {
        this.androidBakPosId = androidBakPosId;
    }

    public int getPreType() {
        return preType;
    }

    public void setPreType(int preType) {
        this.preType = preType;
    }

    public int getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(int activationTime) {
        this.activationTime = activationTime;
    }

    public int getStyle() {
        return style;
    }

    public void setStyle(int style) {
        this.style = style;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getClkUrl() {
        return clkUrl;
    }

    public void setClkUrl(String clkUrl) {
        this.clkUrl = clkUrl;
    }

    public List<String> getImgUrls() {
        return imgUrls;
    }

    public void setImgUrls(List<String> imgUrls) {
        this.imgUrls = imgUrls;
    }

    public String getAppPkgName() {
        return appPkgName;
    }

    public void setAppPkgName(String appPkgName) {
        this.appPkgName = appPkgName;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getShareImgUrl() {
        return shareImgUrl;
    }

    public void setShareImgUrl(String shareImgUrl) {
        this.shareImgUrl = shareImgUrl;
    }

    public int getShareType() {
        return shareType;
    }

    public void setShareType(int shareType) {
        this.shareType = shareType;
    }

    public int getJumpTimes() {
        return jumpTimes;
    }

    public void setJumpTimes(int jumpTimes) {
        this.jumpTimes = jumpTimes;
    }

    public int getStyleType() {

        return styleType;
    }

    public void setStyleType(int styleType) {
        this.styleType = styleType;
    }

    public Boolean getNoAppId() {
        return noAppId;
    }

    public void setNoAppId(Boolean noAppId) {
        this.noAppId = noAppId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public int getPr() {
        return pr;
    }

    public void setPr(int pr) {
        this.pr = pr;
    }

    public String getImpTrackUrl() {
        return impTrackUrl;
    }

    public void setImpTrackUrl(String impTrackUrl) {
        this.impTrackUrl = impTrackUrl;
    }

    public String getClkTrackUrl() {
        return clkTrackUrl;
    }

    public void setClkTrackUrl(String clkTrackUrl) {
        this.clkTrackUrl = clkTrackUrl;
    }

    public String getAdm() {
        return adm;
    }

    public void setAdm(String adm) {
        this.adm = adm;
    }

    public String getVideoStartTrackUrl() {
        return videoStartTrackUrl;
    }

    public void setVideoStartTrackUrl(String videoStartTrackUrl) {
        this.videoStartTrackUrl = videoStartTrackUrl;
    }

    public String getVideoFinishTrackUrl() {
        return videoFinishTrackUrl;
    }

    public void setVideoFinishTrackUrl(String videoFinishTrackUrl) {
        this.videoFinishTrackUrl = videoFinishTrackUrl;
    }

    public String getVideoPauseTrackUrl() {
        return videoPauseTrackUrl;
    }

    public void setVideoPauseTrackUrl(String videoPauseTrackUrl) {
        this.videoPauseTrackUrl = videoPauseTrackUrl;
    }

    public String getItid() {
        return itid;
    }

    public void setItid(String itid) {
        this.itid = itid;
    }

    public int getWidth() {
        return width;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getCoverImgUrl() {
        return coverImgUrl;
    }

    public void setCoverImgUrl(String coverImgUrl) {
        this.coverImgUrl = coverImgUrl;
    }

    public String getVideoSize() {
        return videoSize;
    }

    public void setVideoSize(String videoSize) {
        this.videoSize = videoSize;
    }

    public Long getPlayNum() {
        return playNum;
    }

    public int getEndCardReward() {
        return endCardReward;
    }

    public void setEndCardReward(int endCardReward) {
        this.endCardReward = endCardReward;
    }

    public int getShowVideoCard() {
        return showVideoCard;
    }

    public void setShowVideoCard(int showVideoCard) {
        this.showVideoCard = showVideoCard;
    }

    public void setPlayNum(Long playNum) {
        this.playNum = playNum;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public long getCountDownGift() {
        return countDownGift;
    }

    public void setCountDownGift(long countDownGift) {
        this.countDownGift = countDownGift;
    }

    public String getIosAppId() {
        return iosAppId;
    }

    public void setIosAppId(String iosAppId) {
        this.iosAppId = iosAppId;
    }

    public String getAndroidAppId() {
        return androidAppId;
    }

    public void setAndroidAppId(String androidAppId) {
        this.androidAppId = androidAppId;
    }

    public String getReplaceShareDomain() {
        return replaceShareDomain;
    }

    public void setReplaceShareDomain(String replaceShareDomain) {
        this.replaceShareDomain = replaceShareDomain;
    }

    public String getDeeplinkUrl() {
        return deeplinkUrl;
    }

    public void setDeeplinkUrl(String deeplinkUrl) {
        this.deeplinkUrl = deeplinkUrl;
    }

    public int getDeeplinkOpenType() {
        return deeplinkOpenType;
    }

    public void setDeeplinkOpenType(int deeplinkOpenType) {
        this.deeplinkOpenType = deeplinkOpenType;
    }

    public String getDeeplinkPkgName() {
        return deeplinkPkgName;
    }

    public void setDeeplinkPkgName(String deeplinkPkgName) {
        this.deeplinkPkgName = deeplinkPkgName;
    }

    public int getDeeplinkAppType() {
        return deeplinkAppType;
    }

    public void setDeeplinkAppType(int deeplinkAppType) {
        this.deeplinkAppType = deeplinkAppType;
    }

    public int getTwiceJumpTimes() {
        return twiceJumpTimes;
    }

    public void setTwiceJumpTimes(int twiceJumpTimes) {
        this.twiceJumpTimes = twiceJumpTimes;
    }

    public boolean isDetailPageShare() {
        return detailPageShare;
    }

    public void setDetailPageShare(boolean detailPageShare) {
        this.detailPageShare = detailPageShare;
    }

    public boolean isTemplate() {
        return template;
    }

    public void setTemplate(boolean template) {
        this.template = template;
    }

    public int getTemplateImgSize() {
        return templateImgSize;
    }

    public void setTemplateImgSize(int templateImgSize) {
        this.templateImgSize = templateImgSize;
    }

    public int getIndustry() {
        return industry;
    }

    public void setIndustry(int industry) {
        this.industry = industry;
    }

    public int getSharePrice() {
        return sharePrice;
    }

    public void setSharePrice(int sharePrice) {
        this.sharePrice = sharePrice;
    }

    public boolean isNewuserShare() {
        return newuserShare;
    }

    public void setNewuserShare(boolean newuserShare) {
        this.newuserShare = newuserShare;
    }

    public int getMintegralType() {
        return mintegralType;
    }

    public void setMintegralType(int mintegralType) {
        this.mintegralType = mintegralType;
    }

    public int getIsMiniProgram() {
        return isMiniProgram;
    }

    public void setIsMiniProgram(int isMiniProgram) {
        this.isMiniProgram = isMiniProgram;
    }

    public String getMiniProgramId() {
        return miniProgramId;
    }

    public void setMiniProgramId(String miniProgramId) {
        this.miniProgramId = miniProgramId;
    }

    public String getMiniProgramPath() {
        return miniProgramPath;
    }

    public void setMiniProgramPath(String miniProgramPath) {
        this.miniProgramPath = miniProgramPath;
    }

    public String getMiniProgramWxId() {
        return miniProgramWxId;
    }

    public void setMiniProgramWxId(String miniProgramWxId) {
        this.miniProgramWxId = miniProgramWxId;
    }

    public String getMiniProgramName() {
        return miniProgramName;
    }

    public void setMiniProgramName(String miniProgramName) {
        this.miniProgramName = miniProgramName;
    }

    public String getMiniIcon() {
        return miniIcon;
    }

    public void setMiniIcon(String miniIcon) {
        this.miniIcon = miniIcon;
    }

    public String getMiniShareTitle() {
        return miniShareTitle;
    }

    public void setMiniShareTitle(String miniShareTitle) {
        this.miniShareTitle = miniShareTitle;
    }

    public String getMiniShareImage() {
        return miniShareImage;
    }

    public void setMiniShareImage(String miniShareImage) {
        this.miniShareImage = miniShareImage;
    }

    public String getMiniMiddleBtnText() {
        return miniMiddleBtnText;
    }

    public void setMiniMiddleBtnText(String miniMiddleBtnText) {
        this.miniMiddleBtnText = miniMiddleBtnText;
    }

    public String getMiniMiddleImage() {
        return miniMiddleImage;
    }

    public void setMiniMiddleImage(String miniMiddleImage) {
        this.miniMiddleImage = miniMiddleImage;
    }

    public int getMiniStartWay() {
        return miniStartWay;
    }

    public void setMiniStartWay(int miniStartWay) {
        this.miniStartWay = miniStartWay;
    }

    public String getAdAppId() {
        return adAppId;
    }

    public void setAdAppId(String adAppId) {
        this.adAppId = adAppId;
    }

    public String getAdOriginalId() {
        return adOriginalId;
    }

    public void setAdOriginalId(String adOriginalId) {
        this.adOriginalId = adOriginalId;
    }

    public String getAdMiniPath() {
        return adMiniPath;
    }

    public void setAdMiniPath(String adMiniPath) {
        this.adMiniPath = adMiniPath;
    }

    public String getAdMiniAppId() {
        return adMiniAppId;
    }

    public void setAdMiniAppId(String adMiniAppId) {
        this.adMiniAppId = adMiniAppId;
    }

    public String getOwnAppId() {
        return ownAppId;
    }

    public void setOwnAppId(String ownAppId) {
        this.ownAppId = ownAppId;
    }

    public String getOwnOriginalId() {
        return ownOriginalId;
    }

    public void setOwnOriginalId(String ownOriginalId) {
        this.ownOriginalId = ownOriginalId;
    }

    public String getOwnMiniPath() {
        return ownMiniPath;
    }

    public void setOwnMiniPath(String ownMiniPath) {
        this.ownMiniPath = ownMiniPath;
    }

    public String getOwnMiniAppId() {
        return ownMiniAppId;
    }

    public void setOwnMiniAppId(String ownMiniAppId) {
        this.ownMiniAppId = ownMiniAppId;
    }

    public String getAdPackageName() {
        return adPackageName;
    }

    public void setAdPackageName(String adPackageName) {
        this.adPackageName = adPackageName;
    }

    public String getOwnPackageName() {
        return ownPackageName;
    }

    public void setOwnPackageName(String ownPackageName) {
        this.ownPackageName = ownPackageName;
    }

    public Integer getMiniOpenStyle() {
        return miniOpenStyle;
    }

    public void setMiniOpenStyle(Integer miniOpenStyle) {
        this.miniOpenStyle = miniOpenStyle;
    }

    public String getMiniLogoImgUrl() {
        return miniLogoImgUrl;
    }

    public void setMiniLogoImgUrl(String miniLogoImgUrl) {
        this.miniLogoImgUrl = miniLogoImgUrl;
    }

    public String getMiniName() {
        return miniName;
    }

    public void setMiniName(String miniName) {
        this.miniName = miniName;
    }

    public String getMiniProductDesc() {
        return miniProductDesc;
    }

    public void setMiniProductDesc(String miniProductDesc) {
        this.miniProductDesc = miniProductDesc;
    }

    public String getMiniProductImgUrl() {
        return miniProductImgUrl;
    }

    public void setMiniProductImgUrl(String miniProductImgUrl) {
        this.miniProductImgUrl = miniProductImgUrl;
    }

    public String getMiniMissionDesc() {
        return miniMissionDesc;
    }

    public void setMiniMissionDesc(String miniMissionDesc) {
        this.miniMissionDesc = miniMissionDesc;
    }

    public String getMiniGuideImgUrl() {
        return miniGuideImgUrl;
    }

    public void setMiniGuideImgUrl(String miniGuideImgUrl) {
        this.miniGuideImgUrl = miniGuideImgUrl;
    }

    public String getMiniRemainMissionDesc() {
        return miniRemainMissionDesc;
    }

    public void setMiniRemainMissionDesc(String miniRemainMissionDesc) {
        this.miniRemainMissionDesc = miniRemainMissionDesc;
    }

    public boolean isDownloadDrive() {
        return downloadDrive;
    }

    public void setDownloadDrive(boolean downloadDrive) {
        this.downloadDrive = downloadDrive;
    }

    public String getAdTag() {
        return adTag;
    }

    public void setAdTag(String adTag) {
        this.adTag = adTag;
    }

    public String getAdTagRatio() {
        return adTagRatio;
    }

    public void setAdTagRatio(String adTagRatio) {
        this.adTagRatio = adTagRatio;
    }

    public String getGdtUnitId() {
        return gdtUnitId;
    }

    public void setGdtUnitId(String gdtUnitId) {
        this.gdtUnitId = gdtUnitId;
    }

    public Integer getOtherBrowser() {
        return otherBrowser;
    }

    public void setOtherBrowser(Integer otherBrowser) {
        this.otherBrowser = otherBrowser;
    }

    public String getMiniProgramPkg() {
        return miniProgramPkg;
    }

    public void setMiniProgramPkg(String miniProgramPkg) {
        this.miniProgramPkg = miniProgramPkg;
    }

    public int getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(int sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    public int getFetchStrategy() {
        return fetchStrategy;
    }

    public void setFetchStrategy(int fetchStrategy) {
        this.fetchStrategy = fetchStrategy;
    }

    public String getHotWordAdUrl() {
        return hotWordAdUrl;
    }

    public void setHotWordAdUrl(String hotWordAdUrl) {
        this.hotWordAdUrl = hotWordAdUrl;
    }

    public String getFetchUrl() {
        return fetchUrl;
    }

    public void setFetchUrl(String fetchUrl) {
        this.fetchUrl = fetchUrl;
    }

    public String getMiniFeedCreditImg() {
        return miniFeedCreditImg;
    }

    public void setMiniFeedCreditImg(String miniFeedCreditImg) {
        this.miniFeedCreditImg = miniFeedCreditImg;
    }

    public String getMiniFeedNormalImg() {
        return miniFeedNormalImg;
    }

    public void setMiniFeedNormalImg(String miniFeedNormalImg) {
        this.miniFeedNormalImg = miniFeedNormalImg;
    }

    public String getMiniTaskImg() {
        return miniTaskImg;
    }

    public void setMiniTaskImg(String miniTaskImg) {
        this.miniTaskImg = miniTaskImg;
    }

    public boolean isShowAdLabel() {
        return showAdLabel;
    }

    public void setShowAdLabel(boolean showAdLabel) {
        this.showAdLabel = showAdLabel;
    }

    public String getAdLabel() {
        return adLabel;
    }

    public void setAdLabel(String adLabel) {
        this.adLabel = adLabel;
    }

    public String getTransferMiniProgramId() {
        return transferMiniProgramId;
    }

    public void setTransferMiniProgramId(String transferMiniProgramId) {
        this.transferMiniProgramId = transferMiniProgramId;
    }

    public String getTransferMiniProgramPath() {
        return transferMiniProgramPath;
    }

    public int getReadNum() {
        return readNum;
    }

    public void setReadNum(int readNum) {
        this.readNum = readNum;
    }

    public int getReadTime() {
        return readTime;
    }

    public void setReadTime(int readTime) {
        this.readTime = readTime;
    }

    public long getRewardGold() {
        return rewardGold;
    }

    public void setRewardGold(long rewardGold) {
        this.rewardGold = rewardGold;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getClickEarnType() {
        return clickEarnType;
    }

    public void setClickEarnType(int clickEarnType) {
        this.clickEarnType = clickEarnType;
    }

    public long getClickEarnBudget() {
        return clickEarnBudget;
    }

    public void setClickEarnBudget(long clickEarnBudget) {
        this.clickEarnBudget = clickEarnBudget;
    }

    public void setTransferMiniProgramPath(String transferMiniProgramPath) {
        this.transferMiniProgramPath = transferMiniProgramPath;
    }

    public String getTransferMiniProgramWxId() {
        return transferMiniProgramWxId;
    }

    public void setTransferMiniProgramWxId(String transferMiniProgramWxId) {
        this.transferMiniProgramWxId = transferMiniProgramWxId;
    }

    public int getExtendType() {
        return extendType;
    }

    public void setExtendType(int extendType) {
        this.extendType = extendType;
    }

    public Boolean getMiniBindMiddlePage() {
        return miniBindMiddlePage;
    }

    public void setMiniBindMiddlePage(Boolean miniBindMiddlePage) {
        this.miniBindMiddlePage = miniBindMiddlePage;
    }

    public boolean isDefaultAdTwiceJump() {
        return defaultAdTwiceJump;
    }

    public void setDefaultAdTwiceJump(boolean defaultAdTwiceJump) {
        this.defaultAdTwiceJump = defaultAdTwiceJump;
    }

    public String getDownloadDesc() {
        return downloadDesc;
    }

    public void setDownloadDesc(String downloadDesc) {
        this.downloadDesc = downloadDesc;
    }

    public String getSignDesc() {
        return signDesc;
    }

    public void setSignDesc(String signDesc) {
        this.signDesc = signDesc;
    }

    public Integer getAppAdType() {
        return appAdType;
    }

    public void setAppAdType(Integer appAdType) {
        this.appAdType = appAdType;
    }

    public String getSignUrl() {
        return signUrl;
    }

    public void setSignUrl(String signUrl) {
        this.signUrl = signUrl;
    }

    public Integer getAwakeType() {
        return awakeType;
    }

    public void setAwakeType(Integer awakeType) {
        this.awakeType = awakeType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Boolean getHasDeeperTask() {
        return hasDeeperTask;
    }

    public void setHasDeeperTask(Boolean hasDeeperTask) {
        this.hasDeeperTask = hasDeeperTask;
    }

    public Integer getDeeperTaskID() {
        return deeperTaskID;
    }

    public void setDeeperTaskID(Integer deeperTaskID) {
        this.deeperTaskID = deeperTaskID;
    }

    public Integer getDeeperTaskGold() {
        return deeperTaskGold;
    }

    public void setDeeperTaskGold(Integer deeperTaskGold) {
        this.deeperTaskGold = deeperTaskGold;
    }

    public String getDeeperTaskDesc() {
        return deeperTaskDesc;
    }

    public void setDeeperTaskDesc(String deeperTaskDesc) {
        this.deeperTaskDesc = deeperTaskDesc;
    }

    public float getApkSize() {
        return apkSize;
    }

    public void setApkSize(float apkSize) {
        this.apkSize = apkSize;
    }

    public long getDownloadNum() {
        return downloadNum;
    }

    public void setDownloadNum(long downloadNum) {
        this.downloadNum = downloadNum;
    }

    public String getAdSource() {
        return adSource;
    }

    public void setAdSource(String adSource) {
        this.adSource = adSource;
    }

    public Integer getMiniNonRepeatPeriod() {
        return miniNonRepeatPeriod;
    }

    public void setMiniNonRepeatPeriod(Integer miniNonRepeatPeriod) {
        this.miniNonRepeatPeriod = miniNonRepeatPeriod;
    }

    public boolean isTimeReward() {
        return timeReward;
    }

    public void setTimeReward(boolean timeReward) {
        this.timeReward = timeReward;
    }

    public String getJsDomainName() {
        return jsDomainName;
    }

    public void setJsDomainName(String jsDomainName) {
        this.jsDomainName = jsDomainName;
    }

    public JsAdPosConfig getJsAdPosClose() {
        return jsAdPosClose;
    }

    public void setJsAdPosClose(JsAdPosConfig jsAdPosClose) {
        this.jsAdPosClose = jsAdPosClose;
    }

    public JsAdPosConfig getJsAdPosOpen() {
        return jsAdPosOpen;
    }

    public void setJsAdPosOpen(JsAdPosConfig jsAdPosOpen) {
        this.jsAdPosOpen = jsAdPosOpen;
    }

    public List<JsAdPosConfig> getJsAdPosRecommend() {
        return jsAdPosRecommend;
    }

    public void setJsAdPosRecommend(List<JsAdPosConfig> jsAdPosRecommend) {
        this.jsAdPosRecommend = jsAdPosRecommend;
    }

    public Integer getReadNumRange() {
        return readNumRange;
    }

    public void setReadNumRange(Integer readNumRange) {
        this.readNumRange = readNumRange;
    }

    public Integer getReadTimeRange() {
        return readTimeRange;
    }

    public void setReadTimeRange(Integer readTimeRange) {
        this.readTimeRange = readTimeRange;
    }

    public int getOpenType() {
        return openType;
    }

    public void setOpenType(int openType) {
        this.openType = openType;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getButtonDesc() {
        return buttonDesc;
    }

    public void setButtonDesc(String buttonDesc) {
        this.buttonDesc = buttonDesc;
    }

    public int getGuidType() {
        return guidType;
    }

    public void setGuidType(int guidType) {
        this.guidType = guidType;
    }

    public int getTemplateType() {
        return templateType;
    }

    public void setTemplateType(int templateType) {
        this.templateType = templateType;
    }

    public String getIndexTip() {
        return indexTip;
    }

    public void setIndexTip(String indexTip) {
        this.indexTip = indexTip;
    }

    public String getReadTip() {
        return readTip;
    }

    public void setReadTip(String readTip) {
        this.readTip = readTip;
    }

    public String getPageTip() {
        return pageTip;
    }

    public void setPageTip(String pageTip) {
        this.pageTip = pageTip;
    }

    public String getContinuousPageTip() {
        return continuousPageTip;
    }

    public void setContinuousPageTip(String continuousPageTip) {
        this.continuousPageTip = continuousPageTip;
    }

    public String getAdTypeDesc() {
        return adTypeDesc;
    }

    public void setAdTypeDesc(String adTypeDesc) {
        this.adTypeDesc = adTypeDesc;
    }

    public int getSelfEndCard() {
        return selfEndCard;
    }

    public void setSelfEndCard(int selfEndCard) {
        this.selfEndCard = selfEndCard;
    }

    @Override
    public String toString() {
        return "AdExt{" +
                "selfEndCard=" + selfEndCard +
                ", endCardReward=" + endCardReward +
                ", showVideoCard=" + showVideoCard +
                ", iosPosId='" + iosPosId + '\'' +
                ", iosBakPosId='" + iosBakPosId + '\'' +
                ", androidPosId='" + androidPosId + '\'' +
                ", androidBakPosId='" + androidBakPosId + '\'' +
                ", iosAppId='" + iosAppId + '\'' +
                ", androidAppId='" + androidAppId + '\'' +
                ", preType=" + preType +
                ", downloadDrive=" + downloadDrive +
                ", extendType=" + extendType +
                ", template=" + template +
                ", templateImgSize=" + templateImgSize +
                ", mintegralType=" + mintegralType +
                ", adTag='" + adTag + '\'' +
                ", adTagRatio='" + adTagRatio + '\'' +
                ", showAdLabel=" + showAdLabel +
                ", adLabel='" + adLabel + '\'' +
                ", adSource='" + adSource + '\'' +
                ", activationTime=" + activationTime +
                ", style=" + style +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", clkUrl='" + clkUrl + '\'' +
                ", imgUrls=" + imgUrls +
                ", impTrackUrl='" + impTrackUrl + '\'' +
                ", clkTrackUrl='" + clkTrackUrl + '\'' +
                ", appPkgName='" + appPkgName + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", adm='" + adm + '\'' +
                ", videoStartTrackUrl='" + videoStartTrackUrl + '\'' +
                ", videoFinishTrackUrl='" + videoFinishTrackUrl + '\'' +
                ", videoPauseTrackUrl='" + videoPauseTrackUrl + '\'' +
                ", itid='" + itid + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", duration=" + duration +
                ", countDownGift=" + countDownGift +
                ", shareImgUrl='" + shareImgUrl + '\'' +
                ", shareType=" + shareType +
                ", styleType=" + styleType +
                ", noAppId=" + noAppId +
                ", detailPageShare=" + detailPageShare +
                ", newuserShare=" + newuserShare +
                ", url='" + url + '\'' +
                ", replaceShareDomain='" + replaceShareDomain + '\'' +
                ", industry=" + industry +
                ", sharePrice=" + sharePrice +
                ", word='" + word + '\'' +
                ", pr=" + pr +
                ", twiceJumpTimes=" + twiceJumpTimes +
                ", sourceFrom=" + sourceFrom +
                ", fetchStrategy=" + fetchStrategy +
                ", hotWordAdUrl='" + hotWordAdUrl + '\'' +
                ", fetchUrl='" + fetchUrl + '\'' +
                ", deeplinkUrl='" + deeplinkUrl + '\'' +
                ", deeplinkOpenType=" + deeplinkOpenType +
                ", deeplinkPkgName='" + deeplinkPkgName + '\'' +
                ", deeplinkAppType=" + deeplinkAppType +
                ", isMiniProgram=" + isMiniProgram +
                ", miniProgramId='" + miniProgramId + '\'' +
                ", miniProgramPath='" + miniProgramPath + '\'' +
                ", miniProgramWxId='" + miniProgramWxId + '\'' +
                ", miniProgramName='" + miniProgramName + '\'' +
                ", miniIcon='" + miniIcon + '\'' +
                ", miniShareTitle='" + miniShareTitle + '\'' +
                ", miniShareImage='" + miniShareImage + '\'' +
                ", miniMiddleBtnText='" + miniMiddleBtnText + '\'' +
                ", miniMiddleImage='" + miniMiddleImage + '\'' +
                ", miniOpenStyle=" + miniOpenStyle +
                ", miniLogoImgUrl='" + miniLogoImgUrl + '\'' +
                ", miniName='" + miniName + '\'' +
                ", miniProductDesc='" + miniProductDesc + '\'' +
                ", miniProductImgUrl='" + miniProductImgUrl + '\'' +
                ", miniMissionDesc='" + miniMissionDesc + '\'' +
                ", miniGuideImgUrl='" + miniGuideImgUrl + '\'' +
                ", miniRemainMissionDesc='" + miniRemainMissionDesc + '\'' +
                ", miniProgramPkg='" + miniProgramPkg + '\'' +
                ", miniFeedCreditImg='" + miniFeedCreditImg + '\'' +
                ", miniFeedNormalImg='" + miniFeedNormalImg + '\'' +
                ", miniTaskImg='" + miniTaskImg + '\'' +
                ", miniBindMiddlePage=" + miniBindMiddlePage +
                ", miniNonRepeatPeriod=" + miniNonRepeatPeriod +
                ", miniStartWay=" + miniStartWay +
                ", adAppId='" + adAppId + '\'' +
                ", adOriginalId='" + adOriginalId + '\'' +
                ", adMiniPath='" + adMiniPath + '\'' +
                ", adMiniAppId='" + adMiniAppId + '\'' +
                ", adPackageName='" + adPackageName + '\'' +
                ", ownAppId='" + ownAppId + '\'' +
                ", ownOriginalId='" + ownOriginalId + '\'' +
                ", ownMiniPath='" + ownMiniPath + '\'' +
                ", ownMiniAppId='" + ownMiniAppId + '\'' +
                ", ownPackageName='" + ownPackageName + '\'' +
                ", transferMiniProgramId='" + transferMiniProgramId + '\'' +
                ", transferMiniProgramPath='" + transferMiniProgramPath + '\'' +
                ", transferMiniProgramWxId='" + transferMiniProgramWxId + '\'' +
                ", otherBrowser=" + otherBrowser +
                ", readNum=" + readNum +
                ", readTime=" + readTime +
                ", rewardGold=" + rewardGold +
                ", priority=" + priority +
                ", clickEarnType=" + clickEarnType +
                ", clickEarnBudget=" + clickEarnBudget +
                ", readNumRange=" + readNumRange +
                ", readTimeRange=" + readTimeRange +
                ", guidType=" + guidType +
                ", templateType=" + templateType +
                ", indexTip='" + indexTip + '\'' +
                ", readTip='" + readTip + '\'' +
                ", pageTip='" + pageTip + '\'' +
                ", continuousPageTip='" + continuousPageTip + '\'' +
                ", downloadDesc='" + downloadDesc + '\'' +
                ", signDesc='" + signDesc + '\'' +
                ", appAdType=" + appAdType +
                ", signUrl='" + signUrl + '\'' +
                ", awakeType=" + awakeType +
                ", icon='" + icon + '\'' +
                ", hasDeeperTask=" + hasDeeperTask +
                ", deeperTaskID=" + deeperTaskID +
                ", deeperTaskGold=" + deeperTaskGold +
                ", deeperTaskDesc='" + deeperTaskDesc + '\'' +
                ", apkSize=" + apkSize +
                ", downloadNum=" + downloadNum +
                ", timeReward=" + timeReward +
                ", gdtUnitId='" + gdtUnitId + '\'' +
                ", defaultAdTwiceJump=" + defaultAdTwiceJump +
                ", jsDomainName='" + jsDomainName + '\'' +
                ", jsAdPosClose=" + jsAdPosClose +
                ", jsAdPosOpen=" + jsAdPosOpen +
                ", jsAdPosRecommend=" + jsAdPosRecommend +
                ", openType=" + openType +
                ", detail='" + detail + '\'' +
                ", buttonDesc='" + buttonDesc + '\'' +
                ", recommend=" + recommend +
                ", twiceJump=" + twiceJump +
                ", twiceJumpTime=" + twiceJumpTime +
                ", jumpTimes=" + jumpTimes +
                ", logoUrl='" + logoUrl + '\'' +
                ", videoUrl='" + videoUrl + '\'' +
                ", coverImgUrl='" + coverImgUrl + '\'' +
                ", videoSize='" + videoSize + '\'' +
                ", playNum=" + playNum +
                ", adTypeDesc='" + adTypeDesc + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdExt adExt = (AdExt) o;
        return selfEndCard==adExt.selfEndCard&&
                preType == adExt.preType &&
                downloadDrive == adExt.downloadDrive &&
                extendType == adExt.extendType &&
                template == adExt.template &&
                templateImgSize == adExt.templateImgSize &&
                mintegralType == adExt.mintegralType &&
                showAdLabel == adExt.showAdLabel &&
                activationTime == adExt.activationTime &&
                style == adExt.style &&
                width == adExt.width &&
                height == adExt.height &&
                duration == adExt.duration &&
                countDownGift == adExt.countDownGift &&
                shareType == adExt.shareType &&
                styleType == adExt.styleType &&
                detailPageShare == adExt.detailPageShare &&
                newuserShare == adExt.newuserShare &&
                industry == adExt.industry &&
                sharePrice == adExt.sharePrice &&
                pr == adExt.pr &&
                twiceJumpTimes == adExt.twiceJumpTimes &&
                sourceFrom == adExt.sourceFrom &&
                fetchStrategy == adExt.fetchStrategy &&
                deeplinkOpenType == adExt.deeplinkOpenType &&
                deeplinkAppType == adExt.deeplinkAppType &&
                isMiniProgram == adExt.isMiniProgram &&
                miniStartWay == adExt.miniStartWay &&
                readNum == adExt.readNum &&
                readTime == adExt.readTime &&
                rewardGold == adExt.rewardGold &&
                priority == adExt.priority &&
                clickEarnType == adExt.clickEarnType &&
                clickEarnBudget == adExt.clickEarnBudget &&
                guidType == adExt.guidType &&
                templateType == adExt.templateType &&
                Float.compare(adExt.apkSize, apkSize) == 0 &&
                downloadNum == adExt.downloadNum &&
                timeReward == adExt.timeReward &&
                defaultAdTwiceJump == adExt.defaultAdTwiceJump &&
                openType == adExt.openType &&
                recommend == adExt.recommend &&
                twiceJump == adExt.twiceJump &&
                endCardReward == adExt.endCardReward &&
                showVideoCard == adExt.showVideoCard &&
                twiceJumpTime == adExt.twiceJumpTime &&
                jumpTimes == adExt.jumpTimes &&
                Objects.equals(iosPosId, adExt.iosPosId) &&
                Objects.equals(iosBakPosId, adExt.iosBakPosId) &&
                Objects.equals(androidPosId, adExt.androidPosId) &&
                Objects.equals(androidBakPosId, adExt.androidBakPosId) &&
                Objects.equals(iosAppId, adExt.iosAppId) &&
                Objects.equals(androidAppId, adExt.androidAppId) &&
                Objects.equals(adTag, adExt.adTag) &&
                Objects.equals(adTagRatio, adExt.adTagRatio) &&
                Objects.equals(adLabel, adExt.adLabel) &&
                Objects.equals(adSource, adExt.adSource) &&
                Objects.equals(title, adExt.title) &&
                Objects.equals(content, adExt.content) &&
                Objects.equals(clkUrl, adExt.clkUrl) &&
                Objects.equals(imgUrls, adExt.imgUrls) &&
                Objects.equals(impTrackUrl, adExt.impTrackUrl) &&
                Objects.equals(clkTrackUrl, adExt.clkTrackUrl) &&
                Objects.equals(appPkgName, adExt.appPkgName) &&
                Objects.equals(downloadUrl, adExt.downloadUrl) &&
                Objects.equals(adm, adExt.adm) &&
                Objects.equals(videoStartTrackUrl, adExt.videoStartTrackUrl) &&
                Objects.equals(videoFinishTrackUrl, adExt.videoFinishTrackUrl) &&
                Objects.equals(videoPauseTrackUrl, adExt.videoPauseTrackUrl) &&
                Objects.equals(itid, adExt.itid) &&
                Objects.equals(shareImgUrl, adExt.shareImgUrl) &&
                Objects.equals(noAppId, adExt.noAppId) &&
                Objects.equals(url, adExt.url) &&
                Objects.equals(replaceShareDomain, adExt.replaceShareDomain) &&
                Objects.equals(word, adExt.word) &&
                Objects.equals(hotWordAdUrl, adExt.hotWordAdUrl) &&
                Objects.equals(fetchUrl, adExt.fetchUrl) &&
                Objects.equals(deeplinkUrl, adExt.deeplinkUrl) &&
                Objects.equals(deeplinkPkgName, adExt.deeplinkPkgName) &&
                Objects.equals(miniProgramId, adExt.miniProgramId) &&
                Objects.equals(miniProgramPath, adExt.miniProgramPath) &&
                Objects.equals(miniProgramWxId, adExt.miniProgramWxId) &&
                Objects.equals(miniProgramName, adExt.miniProgramName) &&
                Objects.equals(miniIcon, adExt.miniIcon) &&
                Objects.equals(miniShareTitle, adExt.miniShareTitle) &&
                Objects.equals(miniShareImage, adExt.miniShareImage) &&
                Objects.equals(miniMiddleBtnText, adExt.miniMiddleBtnText) &&
                Objects.equals(miniMiddleImage, adExt.miniMiddleImage) &&
                Objects.equals(miniOpenStyle, adExt.miniOpenStyle) &&
                Objects.equals(miniLogoImgUrl, adExt.miniLogoImgUrl) &&
                Objects.equals(miniName, adExt.miniName) &&
                Objects.equals(miniProductDesc, adExt.miniProductDesc) &&
                Objects.equals(miniProductImgUrl, adExt.miniProductImgUrl) &&
                Objects.equals(miniMissionDesc, adExt.miniMissionDesc) &&
                Objects.equals(miniGuideImgUrl, adExt.miniGuideImgUrl) &&
                Objects.equals(miniRemainMissionDesc, adExt.miniRemainMissionDesc) &&
                Objects.equals(miniProgramPkg, adExt.miniProgramPkg) &&
                Objects.equals(miniFeedCreditImg, adExt.miniFeedCreditImg) &&
                Objects.equals(miniFeedNormalImg, adExt.miniFeedNormalImg) &&
                Objects.equals(miniTaskImg, adExt.miniTaskImg) &&
                Objects.equals(miniBindMiddlePage, adExt.miniBindMiddlePage) &&
                Objects.equals(miniNonRepeatPeriod, adExt.miniNonRepeatPeriod) &&
                Objects.equals(adAppId, adExt.adAppId) &&
                Objects.equals(adOriginalId, adExt.adOriginalId) &&
                Objects.equals(adMiniPath, adExt.adMiniPath) &&
                Objects.equals(adMiniAppId, adExt.adMiniAppId) &&
                Objects.equals(adPackageName, adExt.adPackageName) &&
                Objects.equals(ownAppId, adExt.ownAppId) &&
                Objects.equals(ownOriginalId, adExt.ownOriginalId) &&
                Objects.equals(ownMiniPath, adExt.ownMiniPath) &&
                Objects.equals(ownMiniAppId, adExt.ownMiniAppId) &&
                Objects.equals(ownPackageName, adExt.ownPackageName) &&
                Objects.equals(transferMiniProgramId, adExt.transferMiniProgramId) &&
                Objects.equals(transferMiniProgramPath, adExt.transferMiniProgramPath) &&
                Objects.equals(transferMiniProgramWxId, adExt.transferMiniProgramWxId) &&
                Objects.equals(otherBrowser, adExt.otherBrowser) &&
                Objects.equals(readNumRange, adExt.readNumRange) &&
                Objects.equals(readTimeRange, adExt.readTimeRange) &&
                Objects.equals(indexTip, adExt.indexTip) &&
                Objects.equals(readTip, adExt.readTip) &&
                Objects.equals(pageTip, adExt.pageTip) &&
                Objects.equals(continuousPageTip, adExt.continuousPageTip) &&
                Objects.equals(downloadDesc, adExt.downloadDesc) &&
                Objects.equals(signDesc, adExt.signDesc) &&
                Objects.equals(appAdType, adExt.appAdType) &&
                Objects.equals(signUrl, adExt.signUrl) &&
                Objects.equals(awakeType, adExt.awakeType) &&
                Objects.equals(icon, adExt.icon) &&
                Objects.equals(hasDeeperTask, adExt.hasDeeperTask) &&
                Objects.equals(deeperTaskID, adExt.deeperTaskID) &&
                Objects.equals(deeperTaskGold, adExt.deeperTaskGold) &&
                Objects.equals(deeperTaskDesc, adExt.deeperTaskDesc) &&
                Objects.equals(gdtUnitId, adExt.gdtUnitId) &&
                Objects.equals(jsDomainName, adExt.jsDomainName) &&
                Objects.equals(jsAdPosClose, adExt.jsAdPosClose) &&
                Objects.equals(jsAdPosOpen, adExt.jsAdPosOpen) &&
                Objects.equals(jsAdPosRecommend, adExt.jsAdPosRecommend) &&
                Objects.equals(detail, adExt.detail) &&
                Objects.equals(buttonDesc, adExt.buttonDesc) &&
                Objects.equals(logoUrl, adExt.logoUrl) &&
                Objects.equals(videoUrl, adExt.videoUrl) &&
                Objects.equals(coverImgUrl, adExt.coverImgUrl) &&
                Objects.equals(videoSize, adExt.videoSize) &&
                Objects.equals(playNum, adExt.playNum) &&
                Objects.equals(adTypeDesc, adExt.adTypeDesc);
    }

    @Override
    public int hashCode() {
        return Objects.hash(selfEndCard,showVideoCard,endCardReward,iosPosId, iosBakPosId, androidPosId, androidBakPosId, iosAppId, androidAppId, preType, downloadDrive, extendType, template, templateImgSize, mintegralType, adTag, adTagRatio, showAdLabel, adLabel, adSource, activationTime, style, title, content, clkUrl, imgUrls, impTrackUrl, clkTrackUrl, appPkgName, downloadUrl, adm, videoStartTrackUrl, videoFinishTrackUrl, videoPauseTrackUrl, itid, width, height, duration, countDownGift, shareImgUrl, shareType, styleType, noAppId, detailPageShare, newuserShare, url, replaceShareDomain, industry, sharePrice, word, pr, twiceJumpTimes, sourceFrom, fetchStrategy, hotWordAdUrl, fetchUrl, deeplinkUrl, deeplinkOpenType, deeplinkPkgName, deeplinkAppType, isMiniProgram, miniProgramId, miniProgramPath, miniProgramWxId, miniProgramName, miniIcon, miniShareTitle, miniShareImage, miniMiddleBtnText, miniMiddleImage, miniOpenStyle, miniLogoImgUrl, miniName, miniProductDesc, miniProductImgUrl, miniMissionDesc, miniGuideImgUrl, miniRemainMissionDesc, miniProgramPkg, miniFeedCreditImg, miniFeedNormalImg, miniTaskImg, miniBindMiddlePage, miniNonRepeatPeriod, miniStartWay, adAppId, adOriginalId, adMiniPath, adMiniAppId, adPackageName, ownAppId, ownOriginalId, ownMiniPath, ownMiniAppId, ownPackageName, transferMiniProgramId, transferMiniProgramPath, transferMiniProgramWxId, otherBrowser, readNum, readTime, rewardGold, priority, clickEarnType, clickEarnBudget, readNumRange, readTimeRange, guidType, templateType, indexTip, readTip, pageTip, continuousPageTip, downloadDesc, signDesc, appAdType, signUrl, awakeType, icon, hasDeeperTask, deeperTaskID, deeperTaskGold, deeperTaskDesc, apkSize, downloadNum, timeReward, gdtUnitId, defaultAdTwiceJump, jsDomainName, jsAdPosClose, jsAdPosOpen, jsAdPosRecommend, openType, detail, buttonDesc, recommend, twiceJump, twiceJumpTime, jumpTimes, logoUrl, videoUrl, coverImgUrl, videoSize, playNum, adTypeDesc);
    }

}
