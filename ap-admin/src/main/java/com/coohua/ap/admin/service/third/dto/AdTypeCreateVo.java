package com.coohua.ap.admin.service.third.dto;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/9/28
 */
@Data
public class AdTypeCreateVo {
    private Integer adType;
    private Integer adTypeOwn;
    private List<Integer> adSite;
    private List<String> adSubType;

    private Boolean isBidding;
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public static AdTypeCreateVo fromString(String msg){
        return JSON.parseObject(msg,AdTypeCreateVo.class);
    }
}
