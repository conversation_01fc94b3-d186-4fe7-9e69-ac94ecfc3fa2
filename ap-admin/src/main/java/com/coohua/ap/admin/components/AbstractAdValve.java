package com.coohua.ap.admin.components;

import com.coohua.ap.admin.controller.vo.Ad;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.components
 * @create_time 2019-11-04
 */
public abstract class AbstractAdValve implements AdValve {

    protected AdValve prev;

    protected AdValve next;

    public AdValve getPrev() {
        return prev;
    }

    public void setPrev(AdValve prev) {
        this.prev = prev;
    }

    public AdValve getNext() {
        return next;
    }

    public void setNext(AdValve next) {
        this.next = next;
    }

    @Override
    public List<Ad> filter(List<Ad> adList) {
        List<Ad> retList = doFilter(adList); // 执行实现类的具体filter逻辑
        return getNext() != null ? getNext().filter(retList) : retList;
    }

    abstract List<Ad> doFilter(List<Ad> adList);
}
