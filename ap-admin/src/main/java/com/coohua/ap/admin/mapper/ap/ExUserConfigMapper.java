package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ExUserConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description ex_user_config
 * <AUTHOR>
 * @since  2021-10-28
 */
@Mapper
@Repository
public interface ExUserConfigMapper {

    /**
     * 新增
     **/
    int insert(ExUserConfig exUserConfig);

    /**
     * 刪除
     **/
    int delete(int id);

    /**
     * 更新
     **/
    int update(ExUserConfig exUserConfig);

    /**
     * 查询 根据主键 id 查询
     **/
    ExUserConfig load(int id);

    /**
     * 查询 分页查询
     **/
    List<ExUserConfig> pageList(@Param("offset") int offset, @Param("pageSize") int pagesize, @Param("product")Integer product);

    /**
     * 查询 分页查询 count
     **/
    int pageListCount(@Param("offset") int offset,@Param("pageSize") int pagesize,@Param("product")Integer product);

}