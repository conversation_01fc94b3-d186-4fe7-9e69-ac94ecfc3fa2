package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdCallBackConfig;
import com.coohua.ap.admin.service.third.dto.CallBackPrVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description third_call_back_config
 * <AUTHOR>
 * @date 2021-10-25
 */
@Mapper
@Repository
public interface ThirdCallBackConfigMapper {

    /**
     * 新增
     **/
    int insert(ThirdCallBackConfig thirdCallBackConfig);

    /**
     * 刪除
     **/
    int delete(int id);

    /**
     * 更新
     * <AUTHOR>
     * @date 2021/10/25
     **/
    int update(ThirdCallBackConfig thirdCallBackConfig);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR>
     * @date 2021/10/25
     **/
    ThirdCallBackConfig load(int id);

    /**
     * 查询 分页查询
     **/
    List<ThirdCallBackConfig> pageList(int offset, int pagesize);

    /**
     * 查询 分页查询 count
     **/
    int pageListCount(int offset,int pagesize);


    @Select({"<script>",
            "select ad_id from third_call_back_config where is_set = 1 and secret is not null and ad_id in ",
            "<foreach collection='adIds' index='index' item='item' open='(' separator=',' close=')'> #{item} </foreach>",
            "</script>",
    })
    List<Long> queryExistList(@Param("adIds")List<Long> adId);


    @Select({"select * from third_call_back_config where ad_id not in (select id from tb_ap_ad_information)"})
    List<ThirdCallBackConfig> queryNExistListInfo();

    @Select("select * from `bp-ap`.third_call_back_config where ad_id = #{adId}")
    ThirdCallBackConfig queryByAdId(@Param("adId")Long adId);

    @Select("select * from third_call_back_config where is_set = 1 and secret is null")
    List<ThirdCallBackConfig> queryNoConfig();

    @Select("select product,COUNT(1) as count from third_call_back_config where is_set = 1 GROUP BY product")
    List<CallBackPrVo> queryAll();
}