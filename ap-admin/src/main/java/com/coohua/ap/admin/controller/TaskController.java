package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.TaskVO;
import com.coohua.ap.admin.model.TbApTaskModel;
import com.coohua.ap.admin.service.TaskService;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.constants.TaskType;
import com.coohua.ap.base.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/20
 */
@RestController
@RequestMapping("/task")
public class TaskController extends BaseController {

    @Autowired
    private TaskService taskService;

    @RequestMapping("/getTask")
    public BaseResponse getTask(@RequestParam("taskType") int taskType, HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse(0);
        int appId = SessionUtils.getUserProduct(request);
        TaskVO vo = taskService.queryByAppAndTaskType(taskType, appId);
        vo.setTaskTypeName(TaskType.find(vo.getTaskType()).getDesc());
        if (vo == null) { // 如果是空，表示该产品线还没添加过这个任务，H5进入新增模式，初始化表单为空
            baseResponse.setRet(1);
        } else {
            baseResponse.setData(vo);
        }

        return baseResponse;
    }

    @RequestMapping("/list")
    public BaseResponse list(@RequestParam(name = "name", required = false) String name, HttpServletRequest request) {
        int appId = SessionUtils.getUserProduct(request);
        List<TaskVO> ret = taskService.queryList(appId, name);
        BaseResponse response = new BaseResponse(0);
        if (ret == null) {
            response.setRet(1);
        } else {
            response.setData(ret);
        }
        return response;
    }

    @RequestMapping("/addTask")
    public BaseResponse addTask(@RequestBody TaskVO taskVO, HttpServletRequest request) {
        if (taskVO.getTaskType() == null) {
            throw new BusinessException(400, "param taskType is null.");
        }

        int appId = SessionUtils.getUserProduct(request);
        taskVO.setAppId(appId);

        TaskType taskType = TaskType.find(taskVO.getTaskType());
        if (taskType == null) {
            throw new BusinessException(400, "No such task type error.");
        }

        taskVO.setTaskName(taskVO.getTaskName());
        TbApTaskModel model = taskService.addNewTask(taskVO);
        BaseResponse response = new BaseResponse(0);
        if (model == null) {
            response.setRet(500);
            response.setData("add task error.");
        }
        return response;
    }

    @RequestMapping("updateTask")
    public BaseResponse updateTask(@RequestBody TaskVO taskVO) {
        if (taskVO.getId() == null) {
            throw new BusinessException(400, "param id is null.");
        }
        if (StringUtils.isEmpty(taskVO.getTaskConfig())) {
            throw new BusinessException(400, "param taskConfig is empty.");
        }

        TbApTaskModel model = taskService.updateTask(taskVO);
        BaseResponse response = new BaseResponse(0);
        if (model == null) {
            response.setRet(500);
            response.setData("add task error.");
        }
        return response;
    }
}
