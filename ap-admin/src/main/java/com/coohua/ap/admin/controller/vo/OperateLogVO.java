package com.coohua.ap.admin.controller.vo;

import lombok.Data;

/**
 * <pre>
 *
 * </pre>
 *
 */
@Data
public class OperateLogVO {
    /**
     * 操作日志ID
     */
    private Integer id;
    /**
     * 操作的项目ID
     */
    private Integer itemId;
    /**
     * 操作日志类型
     * @see com.coohua.ap.admin.constants.OperateLogType
     */
    private String operateLogType;
    /**
     * 操作人IP
     */
    private String ip;
    /**
     * 操作人用户ID
     */
    private Integer userId;
    /**
     * 操作人用户名
     */
    private String userName;
    /**
     * 操作类型
     * @see com.coohua.ap.admin.aop.OperateType
     */
    private String operateType;
    /**
     * 如果operateType是UPDATE，oldValue表示修改前的值，如果operateType是INSERT，oldValue为空
     */
    private String oldValue;
    /**
     * 如果operateType是UPDATE，newValue表示修改后的值，如果operateType是INSERT，newValue为新增的配置值
     */
    private String newValue;
    /**
     * 操作时间
     */
    private String operateTime;

}
