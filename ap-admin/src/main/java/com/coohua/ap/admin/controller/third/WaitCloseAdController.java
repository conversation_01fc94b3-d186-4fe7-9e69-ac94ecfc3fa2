package com.coohua.ap.admin.controller.third;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.NeedCloseAdVo;
import com.coohua.ap.admin.service.third.CloseAdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/12/2
 */
@Slf4j
@RequestMapping("third/waitClose")
@RestController
public class WaitCloseAdController {

    @Autowired
    private CloseAdService closeAdService;


    @RequestMapping(value = "list")
    @ResponseBody
    public BaseResponse queryNeedCloseAd(
            @RequestParam(value = "product") Integer product,
            @RequestParam(value = "platform") Integer platform,
            @RequestParam(value = "os") Integer os,
            @RequestParam(value = "appId") Long appId,
            @RequestParam(value = "type",required = false) String type
    ){
        BaseResponse baseResponse = new BaseResponse(0);
        List<NeedCloseAdVo> needCloseAdVos = closeAdService.queryNeedCloseAd(platform,product,type,appId,os,false);
        baseResponse.setData(needCloseAdVos);
        return baseResponse;
    }

    @RequestMapping(value = "close")
    @ResponseBody
    public BaseResponse closeThisAd( @RequestParam(value = "posId") Long posId){
        closeAdService.closeAd(Collections.singletonList(posId),false);
        return new BaseResponse(0);
    }

    @RequestMapping(value = "closeBatch")
    @ResponseBody
    public BaseResponse closeAdBatch(@RequestBody List<Long> ids){
        closeAdService.closeAd(ids,false);
        return new BaseResponse(0);
    }
}
