package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.AdPosCreateType;
import com.coohua.ap.admin.constants.CreateReason;
import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.TbApSubsidiesConfigVO;
import com.coohua.ap.admin.domain.CreateExceptionAdPosDto;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.*;
import com.coohua.ap.admin.service.impl.ConfigBaseServiceImpl;
import com.coohua.ap.admin.service.third.ThirdAdPosService;
import com.coohua.ap.admin.task.ExceptionAdPosTask;
import com.coohua.ap.admin.utils.DingPush;
import com.coohua.ap.admin.utils.third.WallFallUtils;
import com.coohua.ap.base.constants.*;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import com.coohua.user.event.api.dto.ExceptionAdPos;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TbApSubsidiesConfigService {

    @Autowired
    private TbApSubsidiesConfigMapper tbApSubsidiesConfigMapper;
    @Resource
    private ConfigBaseServiceImpl configBaseService;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Autowired
    private ThirdAdPosService thirdAdPosService;
    @Autowired
    private ExceptionAdPosTask exceptionAdPosTask;
    @Resource
    private TbApReplaceAdMapper tbApReplaceAdMapper;
    @Resource
    private ConfigBaseMapper configBaseMapper;
    @Resource
    private ConfigOrientationMapper configOrientationMapper;
    @Autowired
    private TbApDiminishBiddingMonitorService tbApDiminishBiddingMonitorService;
    @Resource
    private AdInfoSubService adInfoSubService;
    @ApolloJsonValue("${skip.replace.ad.list:[391296,428180,428181,428163]}")
    private List<Long> skipReplaceAdIdList;



    public Page<TbApSubsidiesConfigVO> queryList(Page<TbApSubsidiesConfigVO> page) throws IOException {
        int from = (page.getPageNo() - 1) * page.getPageSize();

        List<TbApSubsidiesConfig> tbApReplacePosConfigList = tbApSubsidiesConfigMapper.pageList(from,page.getPageSize());
        List<TbApSubsidiesConfigVO> tbApReplacePosConfigListVO = new ArrayList<>();
        tbApReplacePosConfigList.forEach(config -> {
            TbApSubsidiesConfigVO tbApSubsidiesConfigVO = new TbApSubsidiesConfigVO();
            BeanUtils.copyProperties(config, tbApSubsidiesConfigVO);
            tbApReplacePosConfigListVO.add(tbApSubsidiesConfigVO);
        });
        page.setCount(tbApSubsidiesConfigMapper.pageListCount(from,page.getPageSize()));
        page.setItems(tbApReplacePosConfigListVO);
        return page;
    }

    /**
     * 开关配置
     * @param id
     * @param isEnabled
     */

    public void updateEnable(Long id, Integer isEnabled) {
        TbApSubsidiesConfig tbApReplacePosConfig = tbApSubsidiesConfigMapper.load(id);
        Optional.ofNullable(tbApReplacePosConfig).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));
        try {
            tbApSubsidiesConfigMapper.updateEnable(id, isEnabled);
        } catch (Exception e) {
            log.error("关闭插入分层失败", e);
            new RuntimeException("关闭分层失败！");
        }
    }

    public TbApSubsidiesConfig queryReplaceByAbTest(String os, Integer appId, Integer categoryId, String strategyName) {
        TbApSubsidiesConfig configs = tbApSubsidiesConfigMapper.queryReplaceByAbTest(os, appId, categoryId, strategyName);
        return configs;
    }

    public TbApSubsidiesConfig queryReplaceBiddingConfig(String os, Integer appId) {
        TbApSubsidiesConfig configs = tbApSubsidiesConfigMapper.queryReplaceBiddingConfig(os, appId);
        return configs;
    }

    /**
     * 根据配置主键，更新执行状态
     * @param id
     */
    public void updateStatus(Long id) {
        TbApSubsidiesConfig tbApSubsidiesConfig = tbApSubsidiesConfigMapper.load(id);
        Optional.ofNullable(tbApSubsidiesConfig).orElseThrow(() -> new RuntimeException("未查询到相关配置！"));
        try {
            tbApSubsidiesConfigMapper.updateStatus(id);
        } catch (Exception e) {
            log.error("插入分层修改状态失败", e);
            new RuntimeException("插入分层修改状态失败！");
        }
    }

    /**
     * 查询未关闭且未执行的配置
     */
    public List<TbApSubsidiesConfig> queryAllValidAndInsert(Integer status) {
        List<TbApSubsidiesConfig> tbApSubsidiesConfigs = tbApSubsidiesConfigMapper.queryAllValidAndInsert(status);
        return tbApSubsidiesConfigs;
    }

    public List<TbApSubsidiesConfig> queryValidByProduct(String os, String prodcut, String strategyName){
        return tbApSubsidiesConfigMapper.queryValidByProduct(os, prodcut, strategyName);
    }

    public List<AdTypeSub> findValidAdTypeSubList(TbApSubsidiesConfig vo){

        int[] createAdType = new int[]{Objects.requireNonNull(AdPosCreateType.getByDesc(vo.getAdTypeName())).code};
        int adType =  generateAdType(vo.getPlatform(), createAdType);
        // 找到所有的广告位类型
        List<AdTypeSub> allAdTypeSub = AdTypeSub.adTypeList.stream().filter(adTypeSub -> String.valueOf(adTypeSub.type)
                .startsWith(String.valueOf(adType))).collect(Collectors.toList());

        // 查找已经导入的广告位
        List<Integer> userAdTypeSubList = adInfoMapper.queryAdTypeSubByAdTypeSub(vo.getAppId(), String.valueOf(adType));

        // 查看未导入但已经使用的Type
        Set<Integer> replaceAdTypeList = tbApReplaceAdMapper.queryByProduct(vo.getProduct()).stream().map(TbApReplaceAd::getAdType).collect(Collectors.toSet());
        // 排除锁区
        Set<Integer> userAdTypeSet = configBaseService.queryLockOrLimitAdType().get(vo.getAppId());
        // 可用广告位类型
        List<AdTypeSub> validAdTypeSubList = allAdTypeSub.stream().filter(adTypeSub -> !userAdTypeSubList.contains(adTypeSub.getType())
                && (CollectionUtils.isEmpty(userAdTypeSet) || !userAdTypeSet.contains(adTypeSub.getType()))
                && !replaceAdTypeList.contains(adTypeSub.getType())).
                sorted(Comparator.comparingInt(AdTypeSub::getType))
                .collect(Collectors.toList());

        return validAdTypeSubList;

    }

    private Integer generateAdType(Integer platform, int[] createAdType) {
        Integer platformCode = ThirdPlatformType.getByCode(platform).getCode();
        int adType = 0;
        if (ThirdPlatformType.CSJ.getCode().equals(platformCode)) {
            if (AdType.TOUTIAO_VIDEO_TEMPLATE.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.TOUTIAO_VIDEO_TEMPLATE.type;
            } else if (AdType.CSJ_NEW_CHAPIN.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.CSJ_NEW_CHAPIN.type;
                createAdType[0] = AdPosCreateType.NCP.code;
            }
        } else if (ThirdPlatformType.GDT.getCode().equals(platformCode)) {
            if (AdType.GDT_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.GDT_VIDEO.type;
            } else if (AdType.GDT_CHAPING.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.GDT_CHAPING.type;
            }
        } else if (ThirdPlatformType.KS.getCode().equals(platformCode)) {
            if (AdType.KS_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.KS_VIDEO.type;
            } else if (AdType.KS_NEW_CHAPIN.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.KS_NEW_CHAPIN.type;
                createAdType[0] = AdPosCreateType.NCP.code;
            }
        } else if (ThirdPlatformType.BD.getCode().equals(platformCode)) {
            if (AdType.BAIDU_SUB_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.BAIDU_SUB_VIDEO.type;
            } else if (AdType.BAIDU_CHAPIN_NEW.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.BAIDU_CHAPIN_NEW.type;
                createAdType[0] = AdPosCreateType.NCP.code;
            }
        } else if (ThirdPlatformType.SMG.getCode().equals(platformCode)) {
            if (AdType.SIG_MOB_VIDEO.desc().contains(AdPosCreateType.getDescByCode(createAdType[0]))) {
                adType = AdType.SIG_MOB_VIDEO.type;
            }
        }
        return adType;
    }

    public Integer createAndSendMsg(List<CreateExceptionAdPosDto> exceptionAdPosList, String today, AdPosCreateType adPosType) {
        if (exceptionAdPosList.size() > 0) {
            // 查询系统当前为开的广告位进行创建
            String productName = exceptionAdPosList.get(0).getProductName();
            List<Long> exAdIds = exceptionAdPosList.stream().map(ExceptionAdPos::getAdId).collect(Collectors.toList());
            List<Long> openAdIdList = adInfoMapper.queryOpenAdIds(exAdIds);
            List<Long> lockedOrLimitAd = exceptionAdPosTask.queryLimitOrLockedAdList(exceptionAdPosList);
            exceptionAdPosList = exceptionAdPosList.stream()
                    .filter(r -> openAdIdList.contains(r.getAdId()))
                    .filter(r -> !lockedOrLimitAd.contains(r.getAdId()))
                    .filter(r -> !skipReplaceAdIdList.contains(r.getAdId()))
                    .collect(Collectors.toList());

            if (exceptionAdPosList.size() == 0) {
                log.info(productName + "增加分层当前无广告可创建...");
                return 0;
            }
            // 发送异常错误通知
            sendErrorPosMsg(exceptionAdPosList, today, adPosType);
            // 查询异常的相关广告位
            List<Long> posIds = exceptionAdPosList.stream().map(ExceptionAdPos::getPosId).collect(Collectors.toList());
            Map<Long, CreateExceptionAdPosDto> createReasonMap = exceptionAdPosList.stream()
                    .collect(Collectors.toMap(CreateExceptionAdPosDto::getPosId, r -> r, (r1, r2) -> r1));

            // 查询异常广告
            List<ThirdAdPosEntity> list = thirdAdPosService.queryByPosId(posIds);
            log.info("API-查询到{}{}个需要替换分层的广告位...", productName, list.size());
            // 创建
            Integer count = thirdAdPosService.createExceptionAdPos(list, adPosType, createReasonMap);
            // 查询非API创建广告
            List<ThirdAdPosEntity> otherList = exceptionAdPosTask.buildOwnAdPos(exceptionAdPosList, list, adPosType);
            log.info("广告库-查询到{}{}个需要替换分层的广告位...", productName, otherList.size());
            // 创建其他广告
            thirdAdPosService.createExceptionAdPos(otherList, adPosType, createReasonMap);
            // 发送创建通知
            sendCreatePosMsg(count, exceptionAdPosList.size(), adPosType);
            return count;
        }
        return 0;
    }

    public void convertBiddingAdPosDto(ThirdAdModel ad, CreateExceptionAdPosDto createExceptionAdPosDto, AdInfo adInfo, AdPosCreateType createType, TbApSubsidiesConfig replace, CreateReason createReason) {
        createExceptionAdPosDto.setAdId(ad.getId());
        createExceptionAdPosDto.setAppId(ad.getProduct());
        createExceptionAdPosDto.setProduct(adInfo.getProductName());
        createExceptionAdPosDto.setPosId(Long.valueOf(adInfo.getPosIdThird()));
        createExceptionAdPosDto.setPosName(adInfo.getAdName());
        createExceptionAdPosDto.setAdType(ad.getType());
        createExceptionAdPosDto.setTypeName(createType.getDesc());
        createExceptionAdPosDto.setOs(replace.getOs());
        createExceptionAdPosDto.setSourceName(Platform.getPlatform(replace.getPlatform()).getDesc());
        createExceptionAdPosDto.setCreateDesc(createReason.getDesc());
        createExceptionAdPosDto.setCreateType(createReason.getType());
    }

    public void convertAdToExceptionAdPosDto(ThirdAdModel ad, CreateExceptionAdPosDto createExceptionAdPosDto, AdInfo adInfo, AdPosCreateType createType, TbApSubsidiesConfig replace, CreateReason createReason) {
        createExceptionAdPosDto.setAdId(ad.getId());
        createExceptionAdPosDto.setAppId(ad.getProduct());
        createExceptionAdPosDto.setProduct(replace.getProduct());
        createExceptionAdPosDto.setProductName(adInfo.getProductName());
        createExceptionAdPosDto.setPosId(Long.valueOf(adInfo.getPosIdThird()));
        createExceptionAdPosDto.setPosName(adInfo.getAdName());
        createExceptionAdPosDto.setAdType(ad.getType());
        createExceptionAdPosDto.setTypeName(createType.getDesc());
        createExceptionAdPosDto.setOs(replace.getOs());
        createExceptionAdPosDto.setSourceName(Platform.getPlatform(replace.getPlatform()).getDesc());
        createExceptionAdPosDto.setCreateDesc(createReason.getDesc());
        createExceptionAdPosDto.setCreateType(createReason.getType());
    }


    private void sendErrorPosMsg(List<CreateExceptionAdPosDto> exceptionAdPosList,String today,AdPosCreateType adPosType){
        CompletableFuture.runAsync(()->{
            String title = "## "+ today+ "视频类型：["+adPosType.getDesc()+"]分层广告位做替换：\n\n";
            Map<String,List<ExceptionAdPos>> resMap = exceptionAdPosList.stream().collect(Collectors.groupingBy(ExceptionAdPos::getProductName));
            String content = "";
            for (Map.Entry<String, List<ExceptionAdPos>> entry : resMap.entrySet()) {
                String pr = entry.getKey();
                List<ExceptionAdPos> v = entry.getValue();
                content += "> " + pr + "出现 " + v.size() +"个分层替换 \n\n";
                String.format("分层替换adIdList为: %s",v.stream().map(ExceptionAdPos::getAdId).collect(Collectors.toList()));
            }


            content = content + " 请登录 [广告后台](http://bp-ap-admin.coohua.com/#/login) 进行处理 \n\n";
            DingPush.sendMsg(title+content);
        },executor);
    }

    private void sendCreatePosMsg(Integer count,Integer size,AdPosCreateType adPosType){
        CompletableFuture.runAsync(()->{
            String title = "## 成功创建["+ adPosType.getDesc() +"]广告位：\n\n";
            String content = "### 共查询到" + size + "个需更新的分层广告位,成功创建"+ count+"个 \n\n";
            content = content + " 请登录 [广告后台](http://bp-ap-admin.coohua.com/#/login) 进行查看 \n\n";
            DingPush.sendMsg(title+content);
        },executor);
    }

    public Set<TbApSubsidiesConfig> findTbApSubsidesConfigList(TbApDiminishBiddingMonitor monitor) {
        String configName = monitor.getConfigName();
        Set<TbApSubsidiesConfig> tbApSubsidiesConfigList = new HashSet<>();
        if (configName.contains("bidding")) {
            TbApSubsidiesConfig tbApSubsidiesConfig = tbApSubsidiesConfigMapper.queryReplaceBiddingConfig(monitor.getOs(), monitor.getAppId());
            tbApSubsidiesConfigList.add(tbApSubsidiesConfig);
        }

        if (configName.contains("-")) {
            List<Integer> configs = extractNumberList(configName);
            if (CollectionUtils.isNotEmpty(configs)) {
                configs.forEach(id ->{
                    TbApSubsidiesConfig tbApSubsidiesConfig = tbApSubsidiesConfigMapper.queryByIdForBidding(id);
                    if (tbApSubsidiesConfig != null){
                        tbApSubsidiesConfigList.add(tbApSubsidiesConfig);
                    }
                });
            }
        }
        return tbApSubsidiesConfigList;
    }


    public List<Integer> extractNumberList(String input) {
        String leftPart = input.split("-")[0];
        if (!leftPart.contains("_")) {
            return Collections.singletonList(Integer.parseInt(leftPart));
        }
        String[] parts = leftPart.split("_");
        List<Integer> numbers = new ArrayList<>();
        for (String part : parts) {
            numbers.add(Integer.parseInt(part));
        }
        return numbers;
    }

    public void onlineAdLayers(TbApSubsidiesConfig tbApSubsidiesConfig) {

        Integer adPos = thirdAdPosService.preAdPos(tbApSubsidiesConfig);
        int os = OSType.find(tbApSubsidiesConfig.getOs()).getCode();
        if (adPos == null) {
            XxlJobLogger.log("临时上线广告位无法找到adPos{}", tbApSubsidiesConfig.toString());
            log.info("临时上线广告位无法找到adPos{}", tbApSubsidiesConfig.toString());
            return;
        }

        ConfigOrientationEntity configOrientationEntity = configOrientationMapper.queryByAbTestWithoutState(tbApSubsidiesConfig.getAppId(), tbApSubsidiesConfig.getPriority(), adPos, os, tbApSubsidiesConfig.getAbTest());
        if (configOrientationEntity == null) {
            XxlJobLogger.log("临时上线广告位无法找到策略项", tbApSubsidiesConfig.toString());
            log.info("临时上线广告位无法找到策略项{}", tbApSubsidiesConfig.toString());
            return;
        }


        // 获取配置项
        String baseConfig = configOrientationEntity.getConfig();
        List<Integer> baseConfigIdList = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(baseConfig);
        for (String key : jsonObject.keySet()) {
            baseConfigIdList.add(jsonObject.getInteger(key));
        }
        // 获取配置项
        List<ConfigBaseEntity> configBaseEntityList = configBaseMapper.queryByIds(baseConfigIdList);

        if (CollectionUtils.isEmpty(configBaseEntityList)) {
            XxlJobLogger.log("not find configBaseEntityList");
            log.info("no configBaseEntityList exist:{}", configBaseEntityList.size());
            return;
        }

        // 用于创建三方广告位
        int[] createAdType = new int[]{Objects.requireNonNull(AdPosCreateType.getByDesc(tbApSubsidiesConfig.getAdTypeName())).code};

        int adType = tbApDiminishBiddingMonitorService.getAdType(tbApSubsidiesConfig);

        if (adType == 0) {
            log.info("platform:{} no have createAdType exist:{}", tbApSubsidiesConfig.getPlatform(), createAdType);
            return;
        }

        // 广告位类型-价格
        Map<Integer, Integer> adTypeSubPriceMap = new HashMap<>();
        // 配置项类型-配置项ID
        Map<String, Integer> baseConfigMap = new HashMap<>();

        changeOnlineWallFall(adTypeSubPriceMap, baseConfigMap, configBaseEntityList, adType, tbApSubsidiesConfig);

        if (MapUtils.isEmpty(adTypeSubPriceMap)) {
            XxlJobLogger.log("adTypeSubPriceMap is empty");
            log.info("adTypeSubPriceMap is empty");
            return;
        }

        log.info("save configOrientation start.....");

        String orientationBaseConfig = JSON.toJSONString(baseConfigMap);
        configOrientationEntity.setConfig(orientationBaseConfig);
//                    configOrientationMapper.insertConfigOrientation(newConfigOrientationEntity);

        log.info("save configOrientation end.....");


        // 同时写入ad_config
        adInfoSubService.saveOrUpdateConfigInfo(configOrientationEntity.getId());
        thirdAdPosService.sendCreatePosMsg(tbApSubsidiesConfig.getProductName(), tbApSubsidiesConfig.getAdTypeName(), adTypeSubPriceMap.size(),
                adTypeSubPriceMap.size(), "夜间临时上线广告位", ThirdPlatformType.getByCode(tbApSubsidiesConfig.getPlatform()).getDesc(), tbApSubsidiesConfig.getOs());
    }


    public void offlineAdLayers(TbApSubsidiesConfig tbApSubsidiesConfig) {
        Integer adPos = thirdAdPosService.preAdPos(tbApSubsidiesConfig);
        int os = OSType.find(tbApSubsidiesConfig.getOs()).getCode();
        ConfigOrientationEntity configOrientationEntity = configOrientationMapper.queryByPriorityWithoutState(tbApSubsidiesConfig.getAppId(), tbApSubsidiesConfig.getConfigPriority(), adPos, os);

        if (configOrientationEntity.getPriority().equals(tbApSubsidiesConfig.getPriority())) {
            XxlJobLogger.log("online orientation already exists this priority:" + tbApSubsidiesConfig.getPriority());
            log.info("online orientation already exists this priority:{}", tbApSubsidiesConfig.getPriority());
            return;
        }
        // 获取配置项
        String baseConfig = configOrientationEntity.getConfig();
        List<Integer> baseConfigIdList = new ArrayList<>();
        JSONObject jsonObject = JSON.parseObject(baseConfig);
        for (String key : jsonObject.keySet()) {
            baseConfigIdList.add(jsonObject.getInteger(key));
        }
        // 获取配置项
        List<ConfigBaseEntity> configBaseEntityList = configBaseMapper.queryByIds(baseConfigIdList);

        if (CollectionUtils.isEmpty(configBaseEntityList)) {
            XxlJobLogger.log("not find configBaseEntityList");
            log.info("no configBaseEntityList exist:{}", configBaseEntityList.size());
            return;
        }
        Map<Integer, String> typeAndConfigMap = configBaseEntityList.stream().collect(Collectors.toMap(ConfigBaseEntity::getType, ConfigBaseEntity::getConfig));

        // 用于创建三方广告位
        int[] createAdType = new int[]{Objects.requireNonNull(AdPosCreateType.getByDesc(tbApSubsidiesConfig.getAdTypeName())).code};

        int adType = tbApDiminishBiddingMonitorService.getAdType(tbApSubsidiesConfig);

        if (adType == 0) {
            log.info("platform:{} no have createAdType exist:{}", tbApSubsidiesConfig.getPlatform(), createAdType);
            return;
        }

        ConfigOrientationEntity needReplaceConfigEntity = configOrientationMapper.queryByAbTestWithoutState(tbApSubsidiesConfig.getAppId(), tbApSubsidiesConfig.getPriority(), adPos, os, tbApSubsidiesConfig.getAbTest());
        String replaceConfig = needReplaceConfigEntity.getConfig();
        List<Integer> replaceConfigList = new ArrayList<>();
        JSONObject replaceJsonObject = JSON.parseObject(replaceConfig);
        for (String key : replaceJsonObject.keySet()) {
            replaceConfigList.add(replaceJsonObject.getInteger(key));
        }
        List<ConfigBaseEntity> needReplaceConfigEntityList = configBaseMapper.queryByIds(replaceConfigList);

        needReplaceConfigEntityList.forEach(configBaseEntity -> {
            configBaseEntity.setConfig(typeAndConfigMap.get(configBaseEntity.getType()));
            configBaseMapper.updateConfig(configBaseEntity);
        });
        // 同时写入ad_config
        adInfoSubService.saveOrUpdateConfigInfo(needReplaceConfigEntity.getId());
        List<TbApReplaceAd> tbApReplaceAds = tbApReplaceAdMapper.queryByStrategyId(tbApSubsidiesConfig.getId());
        thirdAdPosService.sendCreatePosMsg(tbApSubsidiesConfig.getProductName(), tbApSubsidiesConfig.getAdTypeName(), tbApReplaceAds.size(),
                tbApReplaceAds.size(), "夜间临时下线线广告位", ThirdPlatformType.getByCode(tbApSubsidiesConfig.getPlatform()).getDesc(), tbApSubsidiesConfig.getOs());

    }



    /**
     * 新建瀑布流
     *
     * @param adTypeSubPriceMap    广告位类型-价格
     * @param baseConfigMap        配置项类型-配置ID
     * @param configBaseEntityList 原配置项
     * @param adType               广告位类型（自有大类）
     * @param vo                   补贴配置
     */
    public void changeOnlineWallFall(Map<Integer, Integer> adTypeSubPriceMap, Map<String, Integer> baseConfigMap,
                                      List<ConfigBaseEntity> configBaseEntityList, int adType, TbApSubsidiesConfig vo) {

        List<AdTypeSub> validAdTypeSubList = new ArrayList<>();
        List<TbApReplaceAd> tbApReplaceAdList = tbApReplaceAdMapper.queryByStrategyId(vo.getId());
        tbApReplaceAdList.forEach( replace -> {
            validAdTypeSubList.add(new AdTypeSub(replace.getAdType(), "0", replace.getAdTypeName()));
        });

        // 新瀑布流
        StringBuilder newConfig = new StringBuilder();
        configBaseEntityList.forEach(configBaseEntity -> {
            ConfigBaseEntity newConfigBaseEntity = new ConfigBaseEntity();
            BeanUtils.copyProperties(configBaseEntity, newConfigBaseEntity);
            if (Integer.valueOf(ConfigType.AD_CONFIG.code()).equals(configBaseEntity.getType())) {
                configBaseMapper.updateConfig(newConfigBaseEntity);
                baseConfigMap.put(String.valueOf(newConfigBaseEntity.getType()), newConfigBaseEntity.getId());
            } else {
                if (newConfig.length() == 0) {
                    ConfigBaseVO oldConfigBaseVo = configBaseService.queryById(configBaseEntity.getId());
                    List<TbApReplaceAd> tbApReplaceAds = tbApReplaceAdMapper.queryByStrategyId(vo.getId());
                    List<String> addExtendDataList = new ArrayList<>();
                    for (TbApReplaceAd tbApReplaceAd : tbApReplaceAds) {
                        adTypeSubPriceMap.put(tbApReplaceAd.getAdType(), tbApReplaceAd.getEcpm());
                        addExtendDataList.add(tbApReplaceAd.getAdType() + "_100_price:" + tbApReplaceAd.getEcpm());
                    }
                    int targetType = WallFallUtils.getFirstTargetType(oldConfigBaseVo.getConfigPre());
                    String newConfigBaseVoStr =  WallFallUtils.processJson(oldConfigBaseVo.getConfigPre(), addExtendDataList, targetType, adTypeSubPriceMap, adType);
                    newConfig.append(newConfigBaseVoStr);
                    newConfigBaseEntity.setConfig(newConfig.toString());

                } else {
                    newConfigBaseEntity.setConfig(newConfig.toString());
                }
                configBaseMapper.updateConfig(newConfigBaseEntity);
                baseConfigMap.put(String.valueOf(newConfigBaseEntity.getType()), newConfigBaseEntity.getId());
            }
        });
        tbApReplaceAdList.forEach( replace -> {
            tbApReplaceAdMapper.batchUpdate(tbApReplaceAdList);
        });

    }

    /**
     * 下线瀑布流
     *
     * @param adTypeSubPriceMap    广告位类型-价格
     * @param baseConfigMap        配置项类型-配置ID
     * @param configBaseEntityList 原配置项
     * @param adType               广告位类型（自有大类）
     * @param vo                   补贴配置
     */
    private void changeOfflineWallFall(Map<Integer, Integer> adTypeSubPriceMap, Map<String, Integer> baseConfigMap,
                                       List<ConfigBaseEntity> configBaseEntityList, int adType, TbApSubsidiesConfig vo) {

        List<AdTypeSub> validAdTypeSubList = new ArrayList<>();
        List<TbApReplaceAd> tbApReplaceAdList = tbApReplaceAdMapper.queryByStrategyId(vo.getId());
        tbApReplaceAdList.forEach( replace -> {
            validAdTypeSubList.add(new AdTypeSub(replace.getAdType(), "0", replace.getAdTypeName()));
        });

        // 新瀑布流
        StringBuilder newConfig = new StringBuilder();
        configBaseEntityList.forEach(configBaseEntity -> {
            ConfigBaseEntity newConfigBaseEntity = new ConfigBaseEntity();
            BeanUtils.copyProperties(configBaseEntity, newConfigBaseEntity);
            newConfigBaseEntity.setUpdateTime(new Date());
            if (Integer.valueOf(ConfigType.AD_CONFIG.code()).equals(configBaseEntity.getType())) {
                configBaseMapper.updateConfig(newConfigBaseEntity);
                baseConfigMap.put(String.valueOf(newConfigBaseEntity.getType()), newConfigBaseEntity.getId());
            } else {
                if (newConfig.length() == 0) {
                    ConfigBaseVO oldConfigBaseVo = configBaseService.queryById(configBaseEntity.getId());
                    String newConfigBaseVoStr = WallFallUtils.processJson(oldConfigBaseVo, validAdTypeSubList, vo.getLayerEcpm(), adTypeSubPriceMap, vo.getAutoGenerateEcpm(), adType);
                    newConfig.append(newConfigBaseVoStr);
                    newConfigBaseEntity.setConfig(newConfig.toString());
                } else {
                    newConfigBaseEntity.setConfig(newConfig.toString());
                }
                configBaseMapper.updateConfig(newConfigBaseEntity);
                baseConfigMap.put(String.valueOf(newConfigBaseEntity.getType()), newConfigBaseEntity.getId());
            }
        });

    }


}
