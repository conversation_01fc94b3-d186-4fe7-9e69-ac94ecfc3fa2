package com.coohua.ap.admin.components;

import com.coohua.ap.admin.controller.vo.Ad;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.components
 * @create_time 2019-11-04
 */
public class DefaultFilterValve implements FilterValve {

    private AdValve head;

    @Override
    public AdValve setHead(AdValve adValve) {
        this.head = adValve;
        return head;
    }

    @Override
    public List<Ad> doChain(List<Ad> adList) {
        return head.filter(adList);
    }
}
