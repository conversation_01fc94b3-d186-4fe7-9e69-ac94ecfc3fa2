package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.utils.IpUtils;
import com.coohua.ap.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseController {
    @ExceptionHandler
    @ResponseBody
    public BaseResponse exception(HttpServletRequest request, HttpServletResponse response, Exception ex) {
        if (ex instanceof BusinessException) {
            BusinessException bex = (BusinessException) ex;
            int errCode = bex.code;
            log.error("Business error. code:{},message:{},ip:{}.", errCode, bex.message, IpUtils.getReomteIp(request));
            log.error("stack: ", ex);
            return BaseResponse.build(errCode, bex.message);
        } else if (ex instanceof MissingServletRequestParameterException) {//Controller 请求参数异常
            return BaseResponse.build(BusinessException.REQUEST_PARAM_ERR.code, BusinessException.REQUEST_PARAM_ERR.message);
        } else if (ex instanceof ClassCastException){
            log.warn("ClassCastException:",ex);
            return BaseResponse.build(410,"服务器处理异常");
        } else {
            log.error("Server error 500.", ex);
            ex.printStackTrace();
            return BaseResponse.build(BusinessException.SERVER_ERR.code, BusinessException.SERVER_ERR.message);
        }
    }
}
