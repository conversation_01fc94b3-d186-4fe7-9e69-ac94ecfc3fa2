package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.aop.OperateLog;
import com.coohua.ap.admin.aop.OperateType;
import com.coohua.ap.admin.constants.OperateLogType;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.mapper.ap.ConfigBaseMapper;
import com.coohua.ap.admin.mapper.ap.ConfigOrientationMapper;
import com.coohua.ap.admin.model.ConfigBaseEntity;
import com.coohua.ap.admin.model.ConfigOrientationEntity;
import com.coohua.ap.admin.service.AdInfoSubService;
import com.coohua.ap.admin.service.ConfigOrientationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.service.impl
 * Project Name : nap
 * Created by Zhimin Xu on 2018/3/14 下午6:01
 * </pre>
 */
@Service
public class ConfigOrientationServiceImpl implements ConfigOrientationService,ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Resource
    private ConfigOrientationMapper configOrientationMapper;
    @Autowired
    private AdInfoSubService adInfoSubService;

    @Resource
    private ConfigBaseMapper configBaseMapper;

    @Override
    public void listConfigOrientation(Page<ConfigOrientationVO> page, int product, String name, int adPos, int state, String id) {
        ConfigOrientationEntity entity = new ConfigOrientationEntity();
        entity.setProduct(product);
        entity.setName(name);
        entity.setAdPos(adPos);
        entity.setState(state);
        if (StringUtils.isNotEmpty(id)) {
            entity.setId(Integer.parseInt(id));
        }
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<ConfigOrientationEntity> entities = configOrientationMapper.queryAllByProduct(entity, from, page.getPageSize());
        Long count = configOrientationMapper.countAllByProduct(entity);
        List<ConfigOrientationVO> vos = new ArrayList<>();
        for (ConfigOrientationEntity ent : entities) {
            vos.add(convertConfigOrientationEntity2VO(ent));
        }

        page.setCount(count.intValue());
        page.setItems(vos);
    }

    @Override
    public List<ConfigBaseVO> listConfigBaseIdAndName(int product) {
        List<ConfigBaseEntity> entityList = configBaseMapper.queryIdAndNameList(product);
        List<ConfigBaseVO> list = new ArrayList<>(entityList.size());
        for (ConfigBaseEntity entity : entityList) {
            list.add(new ConfigBaseVO(entity.getId(), entity.getName(), entity.getProduct(), entity.getType()));
        }
        return list;
    }

    @Override
    @OperateLog(type = OperateType.INSERT,logType = OperateLogType.STRATEGY_CONFIG)
    public ConfigOrientationVO saveConfig(ConfigOrientationVO configOrientationVO) {
        ConfigOrientationEntity entity = convertConfigOrientationVO2Entity(configOrientationVO);
        int result = configOrientationMapper.insertConfigOrientation(entity);
        // 同时写ad_config
        adInfoSubService.saveOrUpdateConfigInfo(entity.getId());
        if (result == 1) {
            return convertConfigOrientationEntity2VO(entity);
        }
        return null;
    }

    @Override
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.STRATEGY_CONFIG)
    public ConfigOrientationVO updateConfig(ConfigOrientationVO configOrientationVO) {
        ConfigOrientationEntity entity = convertConfigOrientationVO2Entity(configOrientationVO);
        int result = configOrientationMapper.updateConfigOrientation(entity);
        adInfoSubService.saveOrUpdateConfigInfo(entity.getId());
        if (result == 1) {
            return convertConfigOrientationEntity2VO(entity);
        }
        return null;
    }

    @Override
    @OperateLog(type = OperateType.UPDATE_STATE,logType = OperateLogType.STRATEGY_CONFIG)
    public ConfigOrientationVO updateState(ConfigOrientationVO configOrientationVO) {
        ConfigOrientationEntity entity = new ConfigOrientationEntity();
        entity.setId(configOrientationVO.getId());
        entity.setState(configOrientationVO.isState() ? 1 : 0);
        int i = configOrientationMapper.updateConfigOrientationState(entity);
        adInfoSubService.saveOrUpdateConfigInfo(entity.getId());
        if(i==0){
            return null;
        }
        return configOrientationVO;
    }

    private ConfigOrientationEntity convertConfigOrientationVO2Entity(ConfigOrientationVO configOrientationVO) {
        ConfigOrientationEntity entity = new ConfigOrientationEntity();
        entity.setName(configOrientationVO.getName());
        entity.setAdPos(configOrientationVO.getAdPos());
        entity.setProduct(configOrientationVO.getProduct());
        entity.setOs(configOrientationVO.getOs());
        entity.setRegion(configOrientationVO.getRegion());
        entity.setRegionSide(configOrientationVO.getRegionSide());
        entity.setAnonymous(configOrientationVO.getAnonymous());
        entity.setRegist(JSONObject.toJSONString(configOrientationVO.getRegist()));
        entity.setIncome(JSONObject.toJSONString(configOrientationVO.getIncome()));
        entity.setVersion(JSONObject.toJSONString(configOrientationVO.getVersion()));
        entity.setTfPlatform(JSONObject.toJSONString(configOrientationVO.getTfPlatform()));
        entity.setTailNumber(configOrientationVO.getTailNumber());
        entity.setConfig(JSONObject.toJSONString(configOrientationVO.getConfig()));
        entity.setState(configOrientationVO.isState() ? 1 : 0);
        entity.setPriority(configOrientationVO.getPriority());
        entity.setId(configOrientationVO.getId());
        entity.setUserPkg(configOrientationVO.getUserPkg());
        entity.setChannelId(configOrientationVO.getChannelId());
        entity.setManufacturer(configOrientationVO.getManufacturer());
        entity.setSdkVersion(JSONObject.toJSONString(configOrientationVO.getSdkVersion()));
        entity.setAbTest(configOrientationVO.getAbTest());
        entity.setDsp(configOrientationVO.getDsp());
        entity.setLockActionPoint(configOrientationVO.getLockActionPoint());
        return entity;
    }

    public ConfigOrientationVO convertConfigOrientationEntity2VO(ConfigOrientationEntity ent) {
        ConfigOrientationVO vo = new ConfigOrientationVO();
        vo.setId(ent.getId());
        vo.setName(ent.getName());
        vo.setAdPos(ent.getAdPos());
        vo.setProduct(ent.getProduct());
        vo.setOs(ent.getOs());
        vo.setRegion(ent.getRegion());
        vo.setRegionSide(ent.getRegionSide() == null? 0:ent.getRegionSide());
        vo.setAnonymous(ent.getAnonymous());
        vo.setState(ent.getState() == 1);
        vo.setPriority(ent.getPriority());
        vo.setCreateTime(ent.getCreateTime());
        vo.setUpdateTime(ent.getUpdateTime());
        vo.setRegist(JSONObject.parseObject(ent.getRegist(), RegistVO.class));
        vo.setIncome(JSONObject.parseObject(ent.getIncome(), IncomeVO.class));
        vo.setVersion(JSONObject.parseObject(ent.getVersion(), VersionVO.class));
        vo.setTfPlatform(JSONObject.parseObject(ent.getTfPlatform(), TfPlatformVo.class));
        if (vo.getTfPlatform() == null){
            vo.setTfPlatform(new TfPlatformVo(){{
                setLimit(0);
                setPlatform(0);
            }});
        }
        vo.setTailNumber(ent.getTailNumber());
        vo.setConfig(JSONObject.parseObject(ent.getConfig(), Map.class));
        vo.setUserPkg(ent.getUserPkg());
        vo.setChannelId(ent.getChannelId());
        vo.setManufacturer(ent.getManufacturer());
        if (StringUtils.isEmpty(ent.getSdkVersion())){
            vo.setSdkVersion(new VersionVO(){{
                setLimit(0);
                setStart("");
                setEnd("");
            }});
        }else {
            vo.setSdkVersion(JSONObject.parseObject(ent.getSdkVersion(), VersionVO.class));
        }
        vo.setAbTest(ent.getAbTest());
        vo.setDsp(ent.getDsp());
        vo.setLockActionPoint(ent.getLockActionPoint());
        return vo;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
