package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.OperateLogVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.service.OperateLogService;
import com.coohua.ap.admin.utils.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: jinyang fan
 * @date: 2020.01.09
 * @package: com.coohua.ap.admin.controller
 * @description:
 */
@RestController
@RequestMapping("/log")
public class OperateLogController {

    @Autowired
    private OperateLogService operateLogService;

    @RequestMapping("/list")
    public BaseResponse list(HttpServletRequest request,
                             Page<OperateLogVO> page,
                             @RequestParam(name = "itemId", required = false) Integer itemId,
                             @RequestParam(name = "userId", required = false) Integer userId,
                             @RequestParam(name = "userName", required = false) String userName,
                             @RequestParam(name = "operateType", required = false) Integer operateType,
                             @RequestParam(name = "operateLogType", required = false) Integer operateLogType) {
        BaseResponse ret = new BaseResponse(0);
        int userProduct = SessionUtils.getUserProduct(request);
        operateLogService.queryPageList(itemId, userId, userName, operateType, operateLogType, userProduct, page);
        ret.setData(page);
        return ret;
    }
}
