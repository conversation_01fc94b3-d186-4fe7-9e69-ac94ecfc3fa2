package com.coohua.ap.admin.service.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.OpType;
import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.ThirdAppLoadMapper;
import com.coohua.ap.admin.mapper.ap.ThirdAppMapper;
import com.coohua.ap.admin.mapper.ap.ThirdCompanyMapper;
import com.coohua.ap.admin.model.ThirdAdPosEntity;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdAppLoad;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.service.third.dto.AdTypeCreateVo;
import com.coohua.ap.admin.service.third.dto.bd.req.BdAdQueryRequest;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdAdDetailResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdAdQueryResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdResponse;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosQueryRequest;
import com.coohua.ap.admin.service.third.dto.csj.req.AppCreateRequest;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AdPosInfo;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AdPosPageResponse;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AppCreateResponse;
import com.coohua.ap.admin.service.third.dto.csj.rsp.Response;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAppCreateRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAppInfoQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosInfo;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosPageResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtCreateAppResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtResponse;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionQueryRequest;
import com.coohua.ap.admin.service.third.dto.ks.response.KsPositionResponse;
import com.coohua.ap.admin.service.third.dto.ks.response.KsResponse;
import com.coohua.ap.admin.service.third.service.bd.BdBaseService;
import com.coohua.ap.admin.service.third.service.csj.CsjBaseService;
import com.coohua.ap.admin.service.third.service.gdt.GdtBaseService;
import com.coohua.ap.admin.service.third.service.ks.KsBaseService;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@Service
public class ThirdAppService {

    @Autowired
    private CsjBaseService csjBaseService;
    @Autowired
    private GdtBaseService gdtBaseService;
    @Autowired
    private KsBaseService ksBaseService;
    @Resource
    private ThirdAppMapper thirdAppMapper;
    @Autowired
    private ThirdCompanyMapper thirdCompanyMapper;
    @Autowired
    private ThirdLogService thirdLogService;
    @Autowired
    private ThirdCompanyService thirdCompanyService;
    @Resource
    private ThirdAppLoadMapper thirdAppLoadMapper;
    @Autowired
    private BdBaseService bdBaseService;

    public void queryList(Integer offset, Integer limit, String name,
                          Integer os,
                          Long appId,
                          Page<ThirdAppEntity> page){
        List<ThirdAppEntity> thirdAppEntities = thirdAppMapper.pageList(offset,limit,name,os,appId,page.getAppId());
        List<ThirdAppEntity> rs = thirdAppEntities.stream()
                .peek(r-> r.setProductName(Optional.ofNullable(AppBuilder.getById(r.getProduct())).orElse(App.MASTER).getProductName()))
                .collect(Collectors.toList());
        page.setItems(rs);
        page.setCount(thirdAppMapper.pageListCount(offset,limit,name,os,appId,page.getAppId()));
    }

    public void queryListLoad(String batchNo,Integer offset, Integer limit,Page<ThirdAppLoad> page){
        page.setItems(thirdAppLoadMapper.pageList(offset,limit,page.getAppId(),batchNo));
        page.setCount(thirdAppLoadMapper.pageListCount(offset,limit,page.getAppId(),batchNo));
    }

    /**
     * @param entityRequest
     * @param account
     * @param name
     */
    @Transactional(transactionManager = "apdatasourceDataSourceTransactionManager")
    public void insert(ThirdAppEntity entityRequest,String account,String name){
        Date now = new Date();
        ThirdAppEntity entity = new ThirdAppEntity();
        entity.setAppName(entityRequest.getAppName());
        entity.setAppExt(entityRequest.getAppExt());
        JSONObject jsonObject = JSONObject.parseObject(entityRequest.getAppExt());

        // 主体账号信息查询
        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(entityRequest.getCompanyId());
        entity.setCompanyId(thirdCompanyEntity.getId());
        entity.setMainBody(thirdCompanyEntity.getMainBody());
        entity.setProduct(entityRequest.getProduct());
        entity.setPlatformCode(entityRequest.getPlatformCode());

        // 调用三方
        // 穿山甲
        Platform platform = Platform.getPlatform(entityRequest.getPlatformCode());
        if (Platform.CSJ.equals(platform)){
            createInCsj(entity,thirdCompanyEntity,entityRequest,jsonObject);
        }else if(Platform.GDT.equals(platform)){
            createInGdt(entity,thirdCompanyEntity,entityRequest,jsonObject);
        }else if (Platform.KS.equals(platform)){
            throw new RuntimeException("暂不支持快手应用的API创建！");
        }else if (Platform.BD.equals(platform)){
            // 百度应用创建
            entity.setAppId(entityRequest.getAppId());
            entity.setAppStatus(1);
        }else if (Platform.OPPO.equals(platform)){
            // OPPO应用创建
            entity.setAppId(entityRequest.getAppId());
            entity.setAppStatus(1);
        }

        entity.setWorkLite(entityRequest.getWorkLite());
        entity.setOs(entityRequest.getOs());
        entity.setSwitchFlag(1);
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        thirdAppMapper.insert(entity);

        thirdCompanyService.refreshCompanyApp(entityRequest.getCompanyId());
        // 记录日志
        thirdLogService.saveLog(account,name, OpType.ADD_APPLICATION,entity.getId(),null, JSON.toJSONString(entity));
    }

    private void createInGdt(ThirdAppEntity entity,ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity entityRequest,JSONObject jsonObject){
        GdtAppCreateRequest request = new GdtAppCreateRequest();
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
        request.setMedium_name(entityRequest.getAppName());
        request.setIndustry_id(Integer.parseInt(entityRequest.getWorkLite()));
        request.setOs(entityRequest.getOs());
        request.setDetail_url(jsonObject.getString("downloadUrl"));
        request.setAffiliation("Own");
        request.setPackage_name(jsonObject.getString("packageName"));
        request.setKeywords(jsonObject.getString("keyWords"));
        request.setDescription(jsonObject.getString("desc"));
        GdtResponse<GdtCreateAppResponse> response = gdtBaseService.createApp(request,thirdCompanyEntity.getSecurityKey());
        if (response.isSuccess()){
            entity.setAppId(String.valueOf(response.getData().getApp_id()));
            entity.setAppStatus(1);
        }else {
            throw new RuntimeException(response.getMsg());
        }
    }

    private void createInCsj(ThirdAppEntity entity,ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity entityRequest,JSONObject jsonObject){
        AppCreateRequest request = new AppCreateRequest();
        request.setUser_id(Long.valueOf(thirdCompanyEntity.getUserId()).intValue());
        request.setRole_id(thirdCompanyEntity.getRoleId());
        request.setTimestamp(CsjBaseService.invokeTime());
        request.setNonce(CsjBaseService.invokeNonce());
        request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),request.getTimestamp(),request.getNonce()));
        request.setVersion("1.0");
        request.setStatus(2);
        request.setApp_category_code(Integer.valueOf(entityRequest.getWorkLite()));
        request.setApp_name(entityRequest.getAppName());
        request.setPackage_name(jsonObject.getString("packageName"));
        request.setDownload_url(jsonObject.getString("downloadUrl"));

        request.setApk_sign(jsonObject.getString("apkSign"));

        request.setMask_rule_id(thirdCompanyEntity.getMaskRuleId());


        Response<AppCreateResponse> createAppResponse = csjBaseService.createApplication(request);
        if (createAppResponse.isSuccess()){
            entity.setAppId(String.valueOf(createAppResponse.getData().getApp_id()));
            entity.setAppStatus(2 == createAppResponse.getData().getStatus() ? 1 : 0);
        }else {
            throw new RuntimeException(createAppResponse.getMessage());
        }
    }

    @Transactional(transactionManager = "apdatasourceDataSourceTransactionManager")
    public void update(ThirdAppEntity entityRequest,String account,String name){
        ThirdAppEntity entity = thirdAppMapper.load(entityRequest.getId());

        ThirdAppEntity record = new ThirdAppEntity();
        record.setId(entityRequest.getId());
//        record.setWorkLite(entityRequest.getWorkLite());
//        record.setAppName(entityRequest.getAppName());
        record.setProduct(entityRequest.getProduct());
        Date now = new Date();

//        JSONObject jsonObject = JSONObject.parseObject(entityRequest.getAppExt());
//
//        // 主体账号信息查询
//        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(entityRequest.getCompanyId());
//
//        // 调用三方
//        // 穿山甲
//        if (entityRequest.getPlatformCode() == 1){
//            AppUpdateRequest request = new AppUpdateRequest();
//            request.setUser_id(Long.valueOf(thirdCompanyEntity.getUserId()).intValue());
//            request.setRole_id(thirdCompanyEntity.getRoleId());
//            request.setTimestamp(CsjBaseService.invokeTime());
//            request.setNonce(CsjBaseService.invokeNonce());
//            request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),request.getTimestamp(),request.getNonce()));
//            request.setVersion("1.0");
//            request.setStatus(2);
//            request.setApp_category_code(Integer.valueOf(entityRequest.getWorkLite()));
//            request.setApp_name(entityRequest.getAppName());
//            request.setApp_id(Math.toIntExact(entityRequest.getAppId()));
//            request.setPackage_name(jsonObject.getString("packageName"));
//            request.setDownload_url(jsonObject.getString("downloadUrl"));
//
//            request.setApk_sign(jsonObject.getString("apkSign"));
//
//            request.setMask_rule_id(thirdCompanyEntity.getMaskRuleId());
//
//
//            Response<AppUpdateResponse> createAppResponse = csjBaseService.updateApplication(request);
//            if (createAppResponse.isSuccess()){
//                record.setAppId(createAppResponse.getData().getApp_id());
//                record.setAppStatus(2 == createAppResponse.getData().getStatus() ? 1 : 0);
//                record.setAppName(createAppResponse.getData().getApp_name());
//                record.setAppExt(entityRequest.getAppExt());
//            }else {
//                throw new RuntimeException(createAppResponse.getMessage());
//            }
//        }else if(entityRequest.getPlatformCode() == 2){
//            GdtAppUpdateRequest request = new GdtAppUpdateRequest();
//            request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
//            request.setMedium_name(entityRequest.getAppName());
//            request.setApp_id(entity.getAppId());
//            request.setIndustry_id(Integer.parseInt(entityRequest.getWorkLite()));
//            request.setOs(entityRequest.getOs());
//            request.setDetail_url(jsonObject.getString("downloadUrl"));
//            request.setAffiliation("Own");
//            request.setPackage_name(jsonObject.getString("packageName"));
//            request.setKeywords(jsonObject.getString("keyWords"));
//            request.setDescription(jsonObject.getString("desc"));
//            GdtResponse<Void> response = gdtBaseService.updateApp(request,thirdCompanyEntity.getSecurityKey());
//            if (response.isSuccess()){
//                record.setAppExt(entityRequest.getAppExt());
//            }else {
//                throw new RuntimeException(response.getMsg());
//            }
//        }

        record.setUpdateTime(now);
        thirdAppMapper.update(record);
        // 记录日志
        thirdLogService.saveLog(account,name, OpType.UPDATE_APPLICATION,entity.getId(),JSON.toJSONString(entity),JSON.toJSONString(record));
    }

    public void updateSwitch(Integer id,Integer state,String account,String name){
        ThirdAppEntity entity = thirdAppMapper.load(id);

        ThirdAppEntity record = new ThirdAppEntity();
        record.setId(id);
        record.setSwitchFlag(state);
        record.setUpdateTime(new Date());
        int updateRow = thirdAppMapper.update(record);

        if (updateRow == 0){
            log.error("未修改{}",id);
            return;
        }

        // 记录日志
        thirdLogService.saveLog(account,name, OpType.UPDATE_APPLICATION,id,JSON.toJSONString(entity),JSON.toJSONString(record));
    }

    public List<ThirdAppEntity> queryAll(){
        return thirdAppMapper.queryAll();
    }

    public void insertOrUpdate(List<ThirdAppEntity> thirdAppEntities){
        // 查询
        thirdAppEntities.forEach(thirdAppEntity -> {
            int count = thirdAppMapper.existAppCheck(thirdAppEntity.getAppId(),thirdAppEntity.getCompanyId());
            if (count == 0) {
                thirdAppMapper.insert(thirdAppEntity);
            }
        });
    }

    public void insertOrUpdateTemp(List<ThirdAppLoad> thirdAppEntities){
        // 查询
        thirdAppEntities.forEach(thirdAppEntity -> {
            int count = thirdAppLoadMapper.existAppCheck(Long.valueOf(thirdAppEntity.getAppId()),thirdAppEntity.getCompanyId());
            if (count == 0) {
                thirdAppLoadMapper.insert(thirdAppEntity);
            }
        });
    }

    public void insert(List<ThirdAppLoad> thirdAppEntities){
        thirdAppEntities.forEach(thirdAppEntity -> thirdAppLoadMapper.insert(thirdAppEntity));
    }

    public void deleteBatch(String uid){
        // 删除本批次
        thirdAppLoadMapper.deleteBatch(uid);
    }

    public void deleteBatchByCpy(Integer uid){
        // 删除本批次
        thirdAppLoadMapper.deleteBatchByCpy(uid);
    }

    public List<ThirdAppLoad> queryTempByIds(List<Integer> ids){
        return thirdAppLoadMapper.queryByIds(ids);
    }

    public void copyToNewRecord(List<Integer> ids,Integer product){
        if (product == null){
            return;
        }
        Date now = new Date();
        List<ThirdAppEntity> thirdAppEntities = thirdAppLoadMapper.queryCurrentByIds(ids);
        for (ThirdAppEntity thirdAppEntity: thirdAppEntities){
            thirdAppEntity.setProduct(product);
            thirdAppEntity.setCreateTime(now);
            thirdAppEntity.setUpdateTime(now);
            thirdAppMapper.insert(thirdAppEntity);
        }
    }

    public List<ThirdAdPosEntity> queryAllPosList(ThirdAppEntity thirdAppEntity){
        List<ThirdAdPosEntity> entities = new ArrayList<>();
        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
        try {
            Platform platform = Platform.getPlatform(thirdAppEntity.getPlatformCode());
            if (Platform.CSJ.equals(platform)) {
                entities.addAll(queryAllCsj(thirdCompanyEntity, thirdAppEntity));
            } else if (Platform.GDT.equals(platform)) {
                entities.addAll(queryAllGdt(thirdCompanyEntity, thirdAppEntity));
            }else if (Platform.KS.equals(platform)){
                entities.addAll(queryAllKs(thirdCompanyEntity, thirdAppEntity));
            }else if (Platform.BD.equals(platform)){
                entities.addAll(queryAllBd(thirdCompanyEntity, thirdAppEntity));
            }
        }catch (Exception e){
            log.error("Error:",e);
        }
        return entities;
    }

    private List<ThirdAdPosEntity> queryAllCsj(ThirdCompanyEntity companyEntity,ThirdAppEntity thirdAppEntity){
        AdPosQueryRequest request = buildRequest(companyEntity,thirdAppEntity,1);
        Response<AdPosPageResponse> responseResponse = csjBaseService.queryAdPosList(request);

        int total;
        List<AdPosInfo> adPosInfoList = new ArrayList<>();
        if (responseResponse.isSuccess()){
            adPosInfoList.addAll(responseResponse.getData().getAd_slot_list());
            total = responseResponse.getData().getPageInfo().getTotal_page();
        }else {
            throw new RuntimeException(responseResponse.getMessage());
        }

        int page = total;
        if (page > 1){
            for (int i = 2; i <= page; i++) {
                AdPosQueryRequest tempRequest = buildRequest(companyEntity,thirdAppEntity,i);
                Response<AdPosPageResponse> tempResponse = csjBaseService.queryAdPosList(tempRequest);
                if (tempResponse.isSuccess()) {
                    adPosInfoList.addAll(tempResponse.getData().getAd_slot_list());
                } else {
                    throw new RuntimeException(tempResponse.getMessage());
                }
            }
        }
        return convertCsj(thirdAppEntity,adPosInfoList);
    }

    private AdPosQueryRequest buildRequest(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,Integer page){
        AdPosQueryRequest request = new AdPosQueryRequest();
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setUser_id(Math.toIntExact(Long.valueOf(thirdCompanyEntity.getUserId())));
        request.setRole_id(thirdCompanyEntity.getRoleId());
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),timestamp,nonce));
        request.setPage(page);
        request.setPage_size(10);
        request.setVersion("1.0");
        request.setApp_id(new Integer[]{Math.toIntExact(Long.parseLong(thirdAppEntity.getAppId()))});
        return request;
    }


    private List<ThirdAdPosEntity> convertCsj(ThirdAppEntity entity, List<AdPosInfo> adPosInfoList){
        return adPosInfoList.stream().map(adPosInfo -> {
            ThirdAdPosEntity thirdAdPosEntity = new ThirdAdPosEntity();
            thirdAdPosEntity.setSwitchFlag(1);
            thirdAdPosEntity.setPlatformCode(entity.getPlatformCode());
            thirdAdPosEntity.setCompanyId(entity.getCompanyId());
            thirdAdPosEntity.setMainBody(entity.getMainBody());
            thirdAdPosEntity.setApplicationId(entity.getId());
            thirdAdPosEntity.setAppId(entity.getAppId());
            thirdAdPosEntity.setAppName(entity.getAppName());
            thirdAdPosEntity.setPosId(Long.valueOf(adPosInfo.getAd_slot_id()));
            thirdAdPosEntity.setPosName(adPosInfo.getAd_slot_name());
            thirdAdPosEntity.setPosStatus(1);
            AdTypeCreateVo vo = new AdTypeCreateVo();
            vo.setAdType(csjBaseService.convertToCsjType(adPosInfo.getAd_slot_type()));
            vo.setAdSubType(convertFlushCsj(adPosInfo.getAd_categories()));
            thirdAdPosEntity.setAdType(vo.toString());
            thirdAdPosEntity.setIncomeStatus(0);
            thirdAdPosEntity.setAdPrice(BigDecimal.ZERO);
            Date now = new Date();
            thirdAdPosEntity.setCreateTime(now);
            thirdAdPosEntity.setUpdateTime(now);
            return thirdAdPosEntity;
        }).collect(Collectors.toList());
    }


    private List<String> getSubType(String[] crtTemplate){
        if (crtTemplate == null){
            return new ArrayList<>();
        }
        return Arrays.stream(crtTemplate).map(stringK ->{
            switch (stringK){
                case "TT_BP":return "上文下图";
                case "RP_LT":return "左图右文";
                case "H_IMG":return "横版视频模板";
                default: return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<String> convertFlushCsj(Integer[] adSubTypecjs){
        if (adSubTypecjs == null){
            return new ArrayList<>();
        }
        return Arrays.stream(adSubTypecjs).map(intX ->{
            switch (intX){
                case 1:return "大图";
                case 2:return "组图";
                case 3:return "单图";
                case 4:return "视频";
                default:return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<ThirdAdPosEntity> queryAllBd(ThirdCompanyEntity companyEntity,ThirdAppEntity thirdAppEntity){
        List<BdAdDetailResponse> datas = Lists.newArrayList();
        BdResponse<BdAdQueryResponse> response = null;
        for(int i = 1; response.getData().getData().size() == 100 || response == null; i++){
            BdAdQueryRequest request = new BdAdQueryRequest();
            request.setPage_no(i);
            request.setPage_size(100);
            request.setApp_sids(Collections.singletonList(thirdAppEntity.getAppId()));

            response = bdBaseService.queryPosList(request,companyEntity.getUserId(),companyEntity.getSecurityKey());

            datas.addAll(response.getData().getData());
        }

        List<ThirdAdPosEntity> res = datas.stream().map(adPosInfo -> {
            ThirdAdPosEntity thirdAdPosEntity = new ThirdAdPosEntity();
            thirdAdPosEntity.setSwitchFlag(1);
            thirdAdPosEntity.setPlatformCode(thirdAppEntity.getPlatformCode());
            thirdAdPosEntity.setCompanyId(thirdAppEntity.getCompanyId());
            thirdAdPosEntity.setMainBody(thirdAppEntity.getMainBody());
            thirdAdPosEntity.setApplicationId(thirdAppEntity.getId());
            thirdAdPosEntity.setAppId(thirdAppEntity.getAppId());
            thirdAdPosEntity.setAppName(thirdAppEntity.getAppName());
            thirdAdPosEntity.setPosId(adPosInfo.getTu_id());
            thirdAdPosEntity.setPosName(adPosInfo.getAd_name());
            AdTypeCreateVo adTypeCreateVo = new AdTypeCreateVo();
            adTypeCreateVo.setAdType(convertAdType(adPosInfo.getAd_type()));
            thirdAdPosEntity.setAdType(adTypeCreateVo.toString());
            thirdAdPosEntity.setPosStatus(1);
            thirdAdPosEntity.setIncomeStatus(0);
            thirdAdPosEntity.setAdPrice(BigDecimal.valueOf(adPosInfo.getCpm()));
            Date now = new Date();
            thirdAdPosEntity.setCreateTime(now);
            thirdAdPosEntity.setUpdateTime(now);
            return thirdAdPosEntity;
        }).collect(Collectors.toList());

        return res;
    }
    private List<ThirdAdPosEntity> queryAllKs(ThirdCompanyEntity companyEntity,ThirdAppEntity thirdAppEntity){
        KsPositionQueryRequest request = buildKsBaseQuery(companyEntity,thirdAppEntity,1);
        KsResponse<List<KsPositionResponse>> ksResponse = ksBaseService.queryPositionInfo(request);

        int total;
        List<KsPositionResponse> ksAdPosInfoList = new ArrayList<>();
        if (ksResponse.isSuccess()){
            ksAdPosInfoList.addAll(ksResponse.getData());
            total = ksResponse.getPage_info().getTotal_count();
        }else {
            throw new RuntimeException(ksResponse.getError_msg());
        }

        int page = total;
        if (page > 1){
            for (int i = 2; i <= page; i++) {
                KsPositionQueryRequest tempRequest = buildKsBaseQuery(companyEntity,thirdAppEntity,i);
                KsResponse<List<KsPositionResponse>> tempResponse = ksBaseService.queryPositionInfo(tempRequest);
                if (tempResponse.isSuccess()) {
                    ksAdPosInfoList.addAll(tempResponse.getData());
                } else {
                    throw new RuntimeException(tempResponse.getError_msg());
                }
            }
        }

        return convertKs(thirdAppEntity,ksAdPosInfoList);
    }

    private List<ThirdAdPosEntity> queryAllGdt(ThirdCompanyEntity companyEntity,ThirdAppEntity thirdAppEntity){
        GdtAdPosQueryRequest request = buildGdtBaseQuery(companyEntity,thirdAppEntity,1);
        GdtResponse<GdtAdPosPageResponse> gdtResponse = gdtBaseService.queryAdPos(request,companyEntity.getSecurityKey());

        int total;
        List<GdtAdPosInfo> gdtAdPosInfoList = new ArrayList<>();
        if (gdtResponse.isSuccess()){
            gdtAdPosInfoList.addAll(gdtResponse.getData().getList());
            total = gdtResponse.getData().getPage_info().getTotal_page();
        }else {
            throw new RuntimeException(gdtResponse.getMsg());
        }

        int page = total;
        if (page > 1){
            for (int i = 2; i <= page; i++) {
                GdtAdPosQueryRequest tempRequest = buildGdtBaseQuery(companyEntity,thirdAppEntity,i);
                GdtResponse<GdtAdPosPageResponse> tempResponse = gdtBaseService.queryAdPos(tempRequest,companyEntity.getSecurityKey());
                if (tempResponse.isSuccess()) {
                    gdtAdPosInfoList.addAll(tempResponse.getData().getList());
                } else {
                    throw new RuntimeException(tempResponse.getMsg());
                }
            }
        }

        return convertGdt(thirdAppEntity,gdtAdPosInfoList);
    }

    private GdtAdPosQueryRequest buildGdtBaseQuery(ThirdCompanyEntity companyEntity,ThirdAppEntity thirdAppEntity,Integer page){
        GdtAdPosQueryRequest request = new GdtAdPosQueryRequest();
        request.setMember_id(Long.valueOf(companyEntity.getUserId()));
        request.setPage(page);
        request.setPage_size(10);
        GdtAppInfoQueryRequest.FilterBean filterBean = new GdtAppInfoQueryRequest.FilterBean();
        filterBean.setField("app_id");
        filterBean.setOperator("EQUALS");
        filterBean.setValues(new String[]{String.valueOf(thirdAppEntity.getAppId())});
        request.setFiltering(Collections.singletonList(filterBean));
        return request;
    }

    private KsPositionQueryRequest buildKsBaseQuery(ThirdCompanyEntity companyEntity,ThirdAppEntity thirdAppEntity,Integer page){
        KsPositionQueryRequest request = new KsPositionQueryRequest();
        request.setAk(String.valueOf(companyEntity.getUserId()));
        request.setSk(companyEntity.getSecurityKey());
        request.setAppId(String.valueOf(thirdAppEntity.getAppId()));
        request.setPage(page);
        request.setPageSize(10);
        return request;
    }

    private List<ThirdAdPosEntity> convertKs(ThirdAppEntity entity, List<KsPositionResponse> ksAdPosInfoList){
        return ksAdPosInfoList.stream().filter(pos -> pos.getAd_style() <=5).map(adPosInfo -> {
            ThirdAdPosEntity thirdAdPosEntity = new ThirdAdPosEntity();
            thirdAdPosEntity.setSwitchFlag(1);
            thirdAdPosEntity.setPlatformCode(entity.getPlatformCode());
            thirdAdPosEntity.setCompanyId(entity.getCompanyId());
            thirdAdPosEntity.setMainBody(entity.getMainBody());
            thirdAdPosEntity.setApplicationId(entity.getId());
            thirdAdPosEntity.setAppId(entity.getAppId());
            thirdAdPosEntity.setAppName(entity.getAppName());
            thirdAdPosEntity.setPosId(adPosInfo.getPosition_id());
            thirdAdPosEntity.setPosName(adPosInfo.getName());
            AdTypeCreateVo adTypeCreateVo = new AdTypeCreateVo();
            adTypeCreateVo.setAdType(convertAdType(adPosInfo.getAd_style()));
            thirdAdPosEntity.setAdType(adTypeCreateVo.toString());
            thirdAdPosEntity.setPosStatus(1);
            thirdAdPosEntity.setIncomeStatus(0);
            thirdAdPosEntity.setAdPrice(BigDecimal.valueOf(adPosInfo.getCpm_floor()));
            Date now = new Date();
            thirdAdPosEntity.setCreateTime(now);
            thirdAdPosEntity.setUpdateTime(now);
            return thirdAdPosEntity;
        }).collect(Collectors.toList());
    }

    private Integer convertAdType(Integer adType){
        switch (adType){
            case 1:
            case 5:
                return 2;
            case 2:return 4;
            case 3:return 3;
            case 4:return 1;
            default:throw new RuntimeException("未查询到广告类型...");
        }
    }

    private List<ThirdAdPosEntity> convertGdt(ThirdAppEntity entity, List<GdtAdPosInfo> gdtAdPosInfoList){
        return gdtAdPosInfoList.stream().map(adPosInfo -> {
            ThirdAdPosEntity thirdAdPosEntity = new ThirdAdPosEntity();
            thirdAdPosEntity.setSwitchFlag(1);
            thirdAdPosEntity.setPlatformCode(entity.getPlatformCode());
            thirdAdPosEntity.setCompanyId(entity.getCompanyId());
            thirdAdPosEntity.setMainBody(entity.getMainBody());
            thirdAdPosEntity.setApplicationId(entity.getId());
            thirdAdPosEntity.setAppId(entity.getAppId());
            thirdAdPosEntity.setAppName(entity.getAppName());
            thirdAdPosEntity.setPosId(adPosInfo.getPlacement_id());
            thirdAdPosEntity.setPosName(adPosInfo.getPlacement_name());
            AdTypeCreateVo adTypeCreateVo = new AdTypeCreateVo();
            adTypeCreateVo.setAdType(gdtBaseService.getScene(adPosInfo.getScene()));
            adTypeCreateVo.setAdSubType(getSubType(adPosInfo.getAd_crt_template_type()));
            thirdAdPosEntity.setAdType(adTypeCreateVo.toString());
            thirdAdPosEntity.setPosStatus(1);
            thirdAdPosEntity.setIncomeStatus(0);
            thirdAdPosEntity.setAdPrice(BigDecimal.valueOf(adPosInfo.getEcpm_price()/100));
            Date now = new Date();
            thirdAdPosEntity.setCreateTime(now);
            thirdAdPosEntity.setUpdateTime(now);
            return thirdAdPosEntity;
        }).collect(Collectors.toList());
    }

    public ThirdAppEntity loadSingle(Integer id){
        return thirdAppMapper.load(id);
    }

    public void deleteApp(Integer id,String account,String name){
        ThirdAppEntity entity = thirdAppMapper.load(id);
        Optional.ofNullable(entity).orElseThrow(() -> new RuntimeException("应用不存在"));
        thirdAppMapper.delete(entity.getId());
        thirdLogService.saveLog(account,name,OpType.UPDATE_APPLICATION,entity.getId(),JSON.toJSONString(entity),null);
    }
}
