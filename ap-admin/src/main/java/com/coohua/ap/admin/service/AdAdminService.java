package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.aop.OperateLog;
import com.coohua.ap.admin.aop.OperateType;
import com.coohua.ap.admin.constants.BusinessConstants;
import com.coohua.ap.admin.constants.OperateLogType;
import com.coohua.ap.admin.constants.PriceType;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mapper.ap.IndustryMapper;
import com.coohua.ap.admin.mapper.ap.TbApBiddingMapper;
import com.coohua.ap.admin.model.*;
import com.coohua.ap.admin.utils.ListUtils;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.admin.utils.third.Lists;
import com.coohua.ap.base.constants.AdConstants;
import com.coohua.ap.base.constants.AdTypeSub;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liu liandong on 2017/4/14.
 */
@Slf4j
@Service
public class AdAdminService {

    private Logger rootLog = LogManager.getRootLogger();

//    private ApplicationContext applicationContext;

    @Resource
    AdInfoMapper adInfoMapper;
    @Autowired
    private AdInfoSubService adInfoSubService;

    @Resource
    private IndustryMapper industryMapper;
    @Autowired
    private RefreshRedisService refreshRedisService;
    @Resource
    private TbApBiddingMapper tbApBiddingMapper;

    public AdPlan planEntityToVo(AdPlanEntity adPlanEntity) {
        AdPlan adPlan = new AdPlan();
        adPlan.setId(adPlanEntity.getId().intValue());
        adPlan.setOpen(adPlanEntity.getState() == 1);
        adPlan.setName(adPlanEntity.getName());
        adPlan.setClientName(adPlanEntity.getClientName());
        adPlan.setProductName(adPlanEntity.getProductName());
        adPlan.setSalesman(adPlanEntity.getSalesman());
        adPlan.setProduct(adPlanEntity.getProduct());
        return adPlan;
    }

    public AdPlanEntity planVoToEntity(AdPlan adPlan) {
        AdPlanEntity adPlanEntity = new AdPlanEntity();
        adPlanEntity.setId((long) adPlan.getId());
        adPlanEntity.setName(adPlan.getName());
        adPlanEntity.setState(adPlan.isOpen() ? 1 : 0);
        adPlanEntity.setClientName(adPlan.getClientName());
        adPlanEntity.setProductName(adPlan.getProductName());
        adPlanEntity.setSalesman(adPlan.getSalesman());
        adPlanEntity.setProduct(adPlan.getProduct());
        return adPlanEntity;
    }

    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @OperateLog(type = OperateType.INSERT,logType = OperateLogType.AD_PLAN_CONFIG)
    public AdPlan insertAdPlan(AdPlan adPlan) {
        /*进行实体的转换*/
        AdPlanEntity adPlanEntity = planVoToEntity(adPlan);
        /*数据添加并返回状态*/
        int ret = adInfoMapper.insertPlan(adPlanEntity);
        if (ret == 1) {
            /*返回id
             * id哪里来的？
             * */
            adPlan.setId(adPlanEntity.getId().intValue());
            return adPlan;
        } else {
            return null;
        }
    }

    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.AD_PLAN_CONFIG)
    public AdPlan updateAdPlan(AdPlan adPlan) {
        AdPlanEntity adPlanEntity = planVoToEntity(adPlan);
        int ret = adInfoMapper.updatePlan(adPlanEntity);
        if (ret == 1) {
            return adPlan;
        } else {
            return null;
        }
    }

    public List<AdPlan> selectAllAdPlans(int userProduct, Page page, String name) {
        /*
         * 用于分页查询
         * */
        int startIndex = (page.getPageNo() - 1) * page.getPageSize();
        List<AdPlanEntity> adPlanEntities = adInfoMapper.selectAllPlans(userProduct, startIndex, page.getPageSize(), name);
        int adPlanCount = adInfoMapper.selectAdPlanCount(userProduct);
        List<AdPlan> adPlen = new ArrayList<AdPlan>();
        for (AdPlanEntity adPlanEntity : adPlanEntities) {
            AdPlan adPlan = planEntityToVo(adPlanEntity);
            adPlen.add(adPlan);
        }
        page.setCount(adPlanCount);
        return adPlen;
    }

    public List<AdPlan> selectAllAdPlansNoPage(int userProduct, Page page) {
        List<AdPlanEntity> adPlanEntities = adInfoMapper.selectAllPlans(userProduct, -1, -1, AdConstants.EMPTY_STRING);
        List<AdPlan> adPlen = new ArrayList<>();
        for (AdPlanEntity entity : adPlanEntities) {
            adPlen.add(planEntityToVo(entity));
        }
        return adPlen;
    }

    public Ad adEntityToVo(AdInfoEntity adInfoEntity) {
        Ad ad = new Ad();
        ad.setId(adInfoEntity.getId());
        ad.setName(adInfoEntity.getName());
//        ad.setPtype(adInfoEntity.getPtype());
        ad.setType(adInfoEntity.getType());
        ad.setOpen(adInfoEntity.getState() == 1);
        ad.setStartDate(adInfoEntity.getPutDatePeriod().getStartDate());
        ad.setEndDate(adInfoEntity.getPutDatePeriod().getForever() == 1 ? "" : adInfoEntity.getPutDatePeriod().getEndDate());
        ad.setNoEndDate(adInfoEntity.getPutDatePeriod().getForever() == 1);
        ad.setPlan(planEntityToVo(adInfoEntity.getPlan()));
        ad.setOrientation(adOrientationEntityToVo(adInfoEntity.getOrientation()));
        ad.setBudget(adBudgetEntityToVo(adInfoEntity.getBudget()));
        ad.setExt(adExtEntityToVo(adInfoEntity.getExt()));
        ad.setState(getAdState(ad));
        ad.setTimeBucketVOList(convertTimeBucket2TimeBucketVO(adInfoEntity.getTimeBucket()));
        ad.setProduct(adInfoEntity.getProduct());
        ad.setAdPos(buildAdPosVO(adInfoEntity.getAdPos()));
        ad.setLastUpdateTime(DateUtil.dateToStringWithTime(adInfoEntity.getUpdateTime()));
        return ad;
    }

    private List<Integer> buildAdPosVO(String adPos) {
        List<Integer> retList = new ArrayList<>();
        for (String pos : adPos.split(AdConstants.SYMBOL_COMMA)) {
            if (StringUtils.isNotEmpty(pos.trim())) {
                retList.add(Integer.parseInt(pos));
            }
        }
        return retList;
    }

    // 将timebucket转化为timebucketVO
    private List<TimeBucketVO> convertTimeBucket2TimeBucketVO(TimeBucket timeBucket) {
        if (timeBucket == null) {
            return null;
        }

        List<TimeBucketVO> timeBucketVOList = new ArrayList<>();

        int[][] buckets = timeBucket.getBucket();
        if (buckets == null) {
            return null;
        }

        for (int i = 0; i < buckets.length; i++) {
            TimeBucketVO vo = new TimeBucketVO();
            vo.setName(BusinessConstants.WEEK_NAME[i]);
            vo.setWeek(i);
            List<Bucket> time = new ArrayList<>();
            for (int j = 0; j < buckets[i].length; j++) {
                Bucket bucket = new Bucket();
                bucket.setTime(j);
                bucket.setShow(buckets[i][j] == 1);
                time.add(bucket);
            }
            vo.setTime(time);
            timeBucketVOList.add(vo);
        }

        return timeBucketVOList;
    }

    private AdInfoEntity adVoToEntity(Ad ad) {
        AdInfoEntity adInfoEntity = new AdInfoEntity();
        adInfoEntity.setId(ad.getId());
        AdPlanEntity adPlanEntity = adInfoMapper.selectOnePlan((long) ad.getPlan().getId(), ad.getProduct());
        ad.getPlan().setName(adPlanEntity.getName());
        adInfoEntity.setPlan(adPlanEntity);
        adInfoEntity.setName(ad.getName());
        adInfoEntity.setState(1);
//        adInfoEntity.setPtype(ad.getPtype());
        adInfoEntity.setType(ad.getType());
        adInfoEntity.setState(ad.isOpen() ? 1 : 0);
        adInfoEntity.getPutDatePeriod().setStartDate(ad.getStartDate());
        adInfoEntity.getPutDatePeriod().setEndDate(ad.isNoEndDate() ? "" : ad.getEndDate());
        adInfoEntity.getPutDatePeriod().setForever(ad.isNoEndDate() ? 1 : 0);
        adInfoEntity.getTimeBucket().setBucket(convertTimeBucketVO2Array(ad.getTimeBucketVOList()));
        adInfoEntity.getTimeBucket().setAllday(calculateIsAllDay(adInfoEntity.getTimeBucket().getBucket()));
        adInfoEntity.setProduct(ad.getProduct());
        adInfoEntity.setAdPos(buildAdPos(ad.getAdPos()));
        adInfoEntity.setDsp(ad.getOrientation().getDsp());

//        if (Strings.noEmpty(adInfoEntity.getName())){
//            if (adInfoEntity.getName().contains("测试")){
//                if (Strings.isEmpty(adInfoEntity.getOrientation().getTailNumber())
//                        &&adInfoEntity.getOrientation().getPlatformVersion() != null
//                        && Strings.isEmpty(adInfoEntity.getOrientation().getPlatformVersion().getAndroidStartVersion())
//                        && Strings.isEmpty(adInfoEntity.getOrientation().getPlatformVersion().getAndRoidEndVersion())
//                        && Strings.isEmpty(adInfoEntity.getOrientation().getPlatformVersion().getIosStartVersion())
//                        && Strings.isEmpty(adInfoEntity.getOrientation().getPlatformVersion().getIosEndVersion())
//                ){
//                    adInfoEntity.setState(0);
//                    log.info("测试广告 {} 未定向用户 拒绝打开",adInfoEntity.getId());
//                }
//            }
//        }
        return adInfoEntity;
    }

    private String buildAdPos(List<Integer> adPos) {
        StringBuilder sb = new StringBuilder();
        for (Integer adPo : adPos) {
            sb.append(adPo).append(",");
        }

        return sb.toString();
    }

    private int calculateIsAllDay(int[][] bucket) {
        for (int i = 0; i < bucket.length; i++) {
            for (int j = 0; j < bucket[i].length; j++) {
                if (bucket[i][j] == 0) { // 有其中一个时段不投放，就不是全天投放
                    return 0;
                }
            }
        }

        return 1;
    }

    private int[][] convertTimeBucketVO2Array(List<TimeBucketVO> timeBucketVOList) {
        int[][] ret = new int[7][48];

        int i = 0;
        int j = 0;
        for (TimeBucketVO vo : timeBucketVOList) {
            List<Bucket> bucketList = vo.getTime();
            for (Bucket bucket : bucketList) {
                ret[i][j++] = bucket.isShow() ? 1 : 0;
            }
            j = 0;
            i++;
        }

        return ret;
    }

    private AdBudget adBudgetEntityToVo(AdBudgetEntity adBudgetEntity) {
        AdBudget adBudget = new AdBudget();
        adBudget.setEnableBudget(adBudgetEntity.getBudget().getFlag() == 1);
        adBudget.setBudget((int) adBudgetEntity.getBudget().getValue());
        adBudget.setIdleTime(adBudgetEntity.getClickInterval());
        adBudget.setNoIdleTime(adBudgetEntity.getClickInterval() < 0);
        adBudget.setPerMaxTimes(adBudgetEntity.getDayClickLimit());
        adBudget.setNoPerMaxTimes(adBudgetEntity.getDayClickLimit() < 0);
        adBudget.setNoExposureIdleTime(adBudgetEntity.getExposureInterval() < 0);
        adBudget.setExposureIdleTime(adBudgetEntity.getExposureInterval());
        adBudget.setNoExposureMaxTimes(adBudgetEntity.getDayExposureLimit() < 0);
        adBudget.setExposureMaxTimes(adBudgetEntity.getDayExposureLimit());
        adBudget.setEcpm(adBudgetEntity.getEcpm());
        return adBudget;
    }

    private AdBudgetEntity adBudgetVoToEntity(int id, AdBudget adBudget) {
        AdBudgetEntity adBudgetEntity = new AdBudgetEntity();
        adBudgetEntity.setId(id);
        adBudgetEntity.getBudget().setFlag(adBudget.isEnableBudget() ? 1 : 0);
        adBudgetEntity.getBudget().setValue(adBudget.getBudget());
        adBudgetEntity.setClickInterval(adBudget.isNoIdleTime() ? -1 : adBudget.getIdleTime());
        adBudgetEntity.setDayClickLimit(adBudget.isNoPerMaxTimes() ? -1 : adBudget.getPerMaxTimes());
        adBudgetEntity.setExposureInterval(adBudget.isNoExposureIdleTime() ? -1 : adBudget.getExposureIdleTime());
        adBudgetEntity.setDayExposureLimit(adBudget.isNoExposureMaxTimes() ? -1 : adBudget.getExposureMaxTimes());
        adBudgetEntity.setEcpm(adBudget.getEcpm());
        return adBudgetEntity;
    }

    private AdOrientation adOrientationEntityToVo(AdOrientationEntity adOrientationEntity) {
        AdOrientation adOrientation = new AdOrientation();
        adOrientation.setNoRegion(adOrientationEntity.getRegion().getAll() == 0);
        adOrientation.setRegions(adOrientationEntity.getRegion().getRegion());
        adOrientation.setMinIosVersion(adOrientationEntity.getPlatformVersion().getIosStartVersion());
        adOrientation.setMaxIosVersion(adOrientationEntity.getPlatformVersion().getIosEndVersion());
        adOrientation.setNoIos(adOrientationEntity.getPlatformVersion().getPlatform() == 1);
        adOrientation.setMinAndroidVersion(adOrientationEntity.getPlatformVersion().getAndroidStartVersion());
        adOrientation.setMaxAndroidVersion(adOrientationEntity.getPlatformVersion().getAndRoidEndVersion());
        adOrientation.setNoAndroid(adOrientationEntity.getPlatformVersion().getPlatform() == 2);
        adOrientation.setTailNumber(adOrientationEntity.getTailNumber());
        adOrientation.setPosition(adOrientationEntity.getPosition());
        adOrientation.setContainsPkg(jsonListToStr(adOrientationEntity.getContainsPkg()));
        adOrientation.setNotContainsPkg(jsonListToStr(adOrientationEntity.getNotContainsPkg()));
        adOrientation.setUserPkg(adOrientationEntity.getUserPkg());
        adOrientation.setChannelId(adOrientationEntity.getChannelId());
        adOrientation.setAbPoint(adOrientationEntity.getAbPoint());
        adOrientation.setFilterRegion(adOrientationEntity.getFilterRegion());
        adOrientation.setLockArea(adOrientationEntity.getLockArea());
        adOrientation.setDsp(adOrientationEntity.getDsp());
        adOrientation.setLockActionPoint(adOrientationEntity.getLockActionPoint());
        if (adOrientationEntity.getIncomeOrientation() == null) {
            adOrientation.setNoIncomeLimit(Boolean.TRUE);
            adOrientation.setMinIncome(0);
            adOrientation.setMaxIncome(0);
        } else {
            adOrientation.setNoIncomeLimit(adOrientationEntity.getIncomeOrientation().getNoLimit() == 1);
            adOrientation.setMinIncome(adOrientationEntity.getIncomeOrientation().getStartIncome() == null ? 0 : adOrientationEntity.getIncomeOrientation().getStartIncome());
            adOrientation.setMaxIncome(adOrientationEntity.getIncomeOrientation().getEndIncome() == null ? 0 : adOrientationEntity.getIncomeOrientation().getEndIncome());
        }
        if (adOrientationEntity.getRegisterTimeOrientation() == null) {
            adOrientation.setNoRegistTimeLimit(Boolean.TRUE);
            adOrientation.setMinRegistTime(AdConstants.EMPTY_STRING);
            adOrientation.setMaxRegistTime(AdConstants.EMPTY_STRING);
        } else {
            adOrientation.setNoRegistTimeLimit(adOrientationEntity.getRegisterTimeOrientation().getNoLimit() == 1);
            adOrientation.setMinRegistTime(adOrientationEntity.getRegisterTimeOrientation().getStartTime() == null ? "" : adOrientationEntity.getRegisterTimeOrientation().getStartTime());
            adOrientation.setMaxRegistTime(adOrientationEntity.getRegisterTimeOrientation().getEndTime() == null ? "" : adOrientationEntity.getRegisterTimeOrientation().getEndTime());
        }
        if (adOrientationEntity.getRegistLongerOrientation() == null) {
            adOrientation.setNoRegistLongerLimit(Boolean.TRUE);
            adOrientation.setMinRegistLonger(AdConstants.EMPTY_STRING);
            adOrientation.setMaxRegistLonger(AdConstants.EMPTY_STRING);
        } else {
            adOrientation.setNoRegistLongerLimit(adOrientationEntity.getRegistLongerOrientation().getNoLimit() == 1);
            adOrientation.setMinRegistLonger(adOrientationEntity.getRegistLongerOrientation().getStartRegistLonger() == null ? AdConstants.EMPTY_STRING : adOrientationEntity.getRegistLongerOrientation().getStartRegistLonger());
            adOrientation.setMaxRegistLonger(adOrientationEntity.getRegistLongerOrientation().getEndRegistLonger() == null ? AdConstants.EMPTY_STRING : adOrientationEntity.getRegistLongerOrientation().getEndRegistLonger());
        }
        String brand = adOrientationEntity.getBrand();
        if (StringUtils.isEmpty(brand)) {
            adOrientation.setBrandLimit(false);
            adOrientation.setBrandLimitList(new ArrayList<>());
        } else {
            BrandLimitModel brandLimitModel = JSONObject.parseObject(adOrientationEntity.getBrand(), BrandLimitModel.class);
            adOrientation.setBrandLimit(brandLimitModel.getBrandLimit() == 1);
            adOrientation.setBrandLimitList(brandLimitModel.getBrandLimitList());
        }
        return adOrientation;
    }

    private AdOrientationEntity adOrientationVoToEntity(int id, AdOrientation adOrientation) {
        AdOrientationEntity adOrientationEntity = new AdOrientationEntity();
        adOrientationEntity.setId(id);
        adOrientationEntity.getRegion().setAll(adOrientation.isNoRegion() ? 0 : 1);
        adOrientationEntity.getRegion().setRegion(adOrientation.getRegions());
        int platform = 0;
        if (adOrientation.isNoIos() && !adOrientation.isNoAndroid()) {
            platform = 1;
        }
        if (!adOrientation.isNoIos() && adOrientation.isNoAndroid()) {
            platform = 2;
        }
        adOrientationEntity.getPlatformVersion().setPlatform(platform);
        adOrientationEntity.getPlatformVersion().setIosStartVersion(adOrientation.getMinIosVersion());
        adOrientationEntity.getPlatformVersion().setIosEndVersion(adOrientation.getMaxIosVersion());
        adOrientationEntity.getPlatformVersion().setAndroidStartVersion(adOrientation.getMinAndroidVersion());
        adOrientationEntity.getPlatformVersion().setAndRoidEndVersion(adOrientation.getMaxAndroidVersion());
        adOrientationEntity.setTailNumber(adOrientation.getTailNumber());
        adOrientationEntity.setPosition(adOrientation.getPosition());
        // 设置总收入限制定向
        adOrientationEntity.getIncomeOrientation().setNoLimit(adOrientation.isNoIncomeLimit() ? 1 : 0);
        adOrientationEntity.getIncomeOrientation().setStartIncome(adOrientation.getMinIncome());
        adOrientationEntity.getIncomeOrientation().setEndIncome(adOrientation.getMaxIncome());
        // 设置注册时间限制定向
        adOrientationEntity.getRegisterTimeOrientation().setNoLimit(adOrientation.isNoRegistTimeLimit() ? 1 : 0);
        adOrientationEntity.getRegisterTimeOrientation().setStartTime(adOrientation.getMinRegistTime());
        adOrientationEntity.getRegisterTimeOrientation().setEndTime(adOrientation.getMaxRegistTime());
        // 设置注册时长定向
        adOrientationEntity.getRegistLongerOrientation().setNoLimit(adOrientation.isNoRegistLongerLimit() ? 1 : 0);
        adOrientationEntity.getRegistLongerOrientation().setStartRegistLonger(adOrientation.getMinRegistLonger());
        adOrientationEntity.getRegistLongerOrientation().setEndRegistLonger(adOrientation.getMaxRegistLonger());

        adOrientationEntity.setContainsPkg(strToJsonList(adOrientation.getContainsPkg()));
        adOrientationEntity.setNotContainsPkg(strToJsonList(adOrientation.getNotContainsPkg()));
        adOrientationEntity.setUserPkg(adOrientation.getUserPkg());
        adOrientationEntity.setChannelId(adOrientation.getChannelId());
        adOrientationEntity.setAbPoint(adOrientation.getAbPoint());
        adOrientationEntity.setFilterRegion(adOrientation.getFilterRegion());
        adOrientationEntity.setLockArea(adOrientation.getLockArea());

        BrandLimitModel brandLimitModel = new BrandLimitModel();
        brandLimitModel.setBrandLimit(adOrientation.isBrandLimit() ? 1 : 0);
        brandLimitModel.setBrandLimitList(adOrientation.getBrandLimitList());
        adOrientationEntity.setBrand(JSONObject.toJSONString(brandLimitModel));
        adOrientationEntity.setDsp(adOrientation.getDsp());
        adOrientationEntity.setLockActionPoint(adOrientation.getLockActionPoint());
        return adOrientationEntity;
    }

    private String strToJsonList(String str) {
        if (StringUtils.isEmpty(str)) {
            return JSONObject.toJSONString(new ArrayList<>());
        }

        List<String> strList = new ArrayList<>();
        for (String sp : str.split(AdConstants.SYMBOL_COMMA)) {
            strList.add(sp);
        }

        return JSONObject.toJSONString(strList);
    }

    private String jsonListToStr(String jsonList) {
        List<String> list = JSONObject.parseArray(jsonList, String.class);
        if (CollectionUtils.isEmpty(list)) {
            return AdConstants.EMPTY_STRING;
        }

        StringBuilder sb = new StringBuilder();
        for (String str : list) {
            sb.append(str).append(AdConstants.SYMBOL_COMMA);
        }
        String result = sb.toString();
        if (result.endsWith(AdConstants.SYMBOL_COMMA)) {
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    private AdExt adExtEntityToVo(AdExtEntity adExtEntity) {
        Ext ext = adExtEntity.getExt();
        if (ext == null) {
            return new AdExt();
        }
        AdExt adExt = new AdExt();
        adExt.setEndCardReward(ext.getEndCardReward());
        adExt.setSelfEndCard(ext.getSelfEndCard());
        adExt.setIosPosId(ext.getIosPosId());
        adExt.setIosBakPosId(ext.getIosBakPosId());
        adExt.setAndroidPosId(ext.getAndroidPosId());
        adExt.setAndroidBakPosId(ext.getAndroidBakPosId());
        adExt.setIosAppId(ext.getIosAppId());
        adExt.setAndroidAppId(ext.getAndroidAppId());
        adExt.setPreType(ext.getPreType() == null ? 1 : ext.getPreType());
        adExt.setDownloadDrive(ext.getDownloadDrive() == null ? false : ext.getDownloadDrive());
//        adExt.setExtendType(ext.getExtendType() == null && ext.getTemplate() == null ? 0 : ext.getExtendType());
        adExt.setTemplate(ext.getTemplate() == null ? false : ext.getTemplate());
//        if (ext.getExtendType() == null) {
//            adExt.setExtendType(adExt.isTemplate() ? ExtendType.TEMPLETE.code() : ExtendType.ORIGIN.code());
//        } else {
//            adExt.setExtendType(ext.getExtendType());
//        }

        adExt.setDefaultAdTwiceJump(ext.getDefaultAdTwiceJump() == null ? false : ext.getDefaultAdTwiceJump());

        adExt.setTemplateImgSize(ext.getTemplateImgSize() == null ? 0 : ext.getTemplateImgSize());
        adExt.setMintegralType(ext.getMintegralType() == null ? 0 : ext.getMintegralType());
        adExt.setActivationTime(ext.getActivationTime() == null ? 0 : ext.getActivationTime());
        adExt.setStyle(ext.getStyle() == null ? 101 : ext.getStyle());
        adExt.setTitle(ext.getTitle());
        adExt.setContent(ext.getContent());
        adExt.setClkUrl(ext.getClkUrl());
        adExt.setImgUrls(ext.getImgUrl());
        adExt.setAdTag(ext.getAdTag());
        adExt.setAdTagRatio(ext.getAdTagRatio());
        adExt.setShowAdLabel(ext.getShowAdLabel());
        adExt.setAdLabel(ext.getAdLabel());
        String impTrackUrl = "";
        if (!ListUtils.isEmpty(ext.getImpTrackUrl())) {
            impTrackUrl = StringUtils.trim(ext.getImpTrackUrl().get(0));
        }
        adExt.setImpTrackUrl(impTrackUrl);
        String clickTrackUrl = "";
        if (!ListUtils.isEmpty(ext.getClkTrackUrl())) {
            clickTrackUrl = ext.getClkTrackUrl().get(0);
        }
        adExt.setClkTrackUrl(clickTrackUrl);
        adExt.setAppPkgName(ext.getAppPkgName());
        adExt.setDownloadUrl(ext.getDownloadUrl());
        String shareImgUrl = "";
        if (!ListUtils.isEmpty(ext.getShareImgUrl())) {
            shareImgUrl = ext.getShareImgUrl().get(0);
        }
        adExt.setShareImgUrl(shareImgUrl);
        String url = "";
        if (!ListUtils.isEmpty(ext.getUrls())) {
            url = ext.getUrls().get(0);
        } else if (!StringUtils.isEmpty(ext.getUrl())) {
            url = ext.getUrl();
        }
        adExt.setUrl(url);
        if (!ListUtils.isEmpty(ext.getTitles())) {
            String title = ext.getTitles().get(0);
            adExt.setTitle(title);
        }
        if (!ListUtils.isEmpty(ext.getContents())) {
            String content = ext.getContents().get(0);
            adExt.setContent(content);
        }
        adExt.setShareType(ext.getShareType() == null ? 1 : ext.getShareType());
        adExt.setStyleType(ext.getStyleType() == null ? 1 : ext.getStyleType());
        adExt.setReplaceShareDomain(ext.getReplaceShareDomain());
        adExt.setIndustry(ext.getIndustry());
        adExt.setSharePrice(ext.getSharePrice());

        adExt.setWord(ext.getWord());
        if (ext.getPr() != null) {
            adExt.setPr(ext.getPr());
        }
        adExt.setSourceFrom(ext.getSourceFrom());
        adExt.setFetchStrategy(ext.getFetchStrategy());
        adExt.setHotWordAdUrl(ext.getHotWordAdUrl() == null ? "" : ext.getHotWordAdUrl());
        adExt.setFetchUrl(ext.getFetchUrl() == null ? "" : ext.getFetchUrl());

        adExt.setTwiceJumpTimes(ext.getTwiceJumpTimes());

        adExt.setNoAppId(ext.getNoAppId() == null ? true : ext.getNoAppId());
        adExt.setDetailPageShare(ext.getDetailPageShare() == null ? false : ext.getDetailPageShare());
        adExt.setNewuserShare(ext.getNewuserShare() == null ? false : ext.getNewuserShare());

        // 视频相关 旧的数据没有下面的字段，为避免空指针异常，先进行非空校验
        adExt.setAdm(StringUtils.isNotEmpty(ext.getAdm()) ? ext.getAdm() : "");
        adExt.setItid(StringUtils.isNotEmpty(ext.getItid()) ? ext.getItid() : "");
        adExt.setWidth(ext.getWidth() != null ? ext.getWidth() : 0);
        adExt.setHeight(ext.getHeight() != null ? ext.getHeight() : 0);
        adExt.setDuration(ext.getDuration() != null ? ext.getDuration() : 0);
        adExt.setCountDownGift(ext.getCountDownGift() != null ? ext.getCountDownGift() : 0L);
        adExt.setVideoStartTrackUrl(!ListUtils.isEmpty(ext.getVideoStartTrackUrl()) ? ext.getVideoStartTrackUrl().get(0) : "");
        adExt.setVideoFinishTrackUrl(!ListUtils.isEmpty(ext.getVideoFinishTrackUrl()) ? ext.getVideoFinishTrackUrl().get(0) : "");
        adExt.setVideoPauseTrackUrl(!ListUtils.isEmpty(ext.getVideoPauseTrackUrl()) ? ext.getVideoPauseTrackUrl().get(0) : "");

        // CPO广告
        adExt.setDeeplinkAppType(ext.getDeeplinkAppType());
        adExt.setDeeplinkOpenType(ext.getDeeplinkOpenType());
        adExt.setDeeplinkPkgName(ext.getDeeplinkPkgName());
        adExt.setDeeplinkUrl(ext.getDeeplinkUrl());


        adExt.setMiniMiddleImage(ext.getMiniMiddleImage());
        adExt.setMiniIcon(ext.getMiniIcon());
        adExt.setMiniShareTitle(ext.getMiniShareTitle());
        adExt.setMiniShareImage(ext.getMiniShareImage());
        adExt.setMiniMiddleBtnText(ext.getMiniMiddleBtnText());
        adExt.setMiniProgramName(ext.getMiniProgramName());


        adExt.setMiniStartWay(ext.getMiniStartWay());
        adExt.setAdAppId(ext.getAdAppId());
        adExt.setAdOriginalId(ext.getAdOriginalId());
        adExt.setAdMiniAppId(ext.getAdMiniAppId());
        adExt.setAdMiniPath(ext.getAdMiniPath());
        adExt.setOwnAppId(ext.getOwnAppId());
        adExt.setOwnOriginalId(ext.getOwnOriginalId());
        adExt.setOwnMiniAppId(ext.getOwnMiniAppId());
        adExt.setOwnMiniPath(ext.getOwnMiniPath());

        adExt.setAdPackageName(ext.getAdPackageName());
        adExt.setOwnPackageName(ext.getOwnPackageName());

        adExt.setGdtUnitId(ext.getGdtUnitId());
        adExt.setOtherBrowser(ext.getOtherBrowser());

        adExt.setJsDomainName(ext.getJsDomainName());
        adExt.setJsAdPosClose(JSONObject.parseObject(ext.getJsAdPosClose(), JsAdPosConfig.class));
        adExt.setJsAdPosOpen(JSONObject.parseObject(ext.getJsAdPosOpen(), JsAdPosConfig.class));
        adExt.setJsAdPosRecommend(JSONArray.parseArray(ext.getJsAdPosRecommend(), JsAdPosConfig.class));
        return adExt;
    }

    private AdExtEntity adExtVoToEntity(int id, AdExt adExt) {
        AdExtEntity adExtEntity = new AdExtEntity();
        adExtEntity.setId(id);
        Ext ext = new Ext();
        adExtEntity.setExt(ext);
        ext.setSelfEndCard(adExt.getSelfEndCard());
        ext.setShowVideoCard(adExt.getShowVideoCard());
        ext.setEndCardReward(adExt.getEndCardReward());
        ext.setIosPosId(adExt.getIosPosId());
        ext.setIosBakPosId(adExt.getIosBakPosId());
        ext.setAndroidPosId(adExt.getAndroidPosId());
        ext.setAndroidBakPosId(adExt.getAndroidBakPosId());
        ext.setIosAppId(adExt.getIosAppId());
        ext.setAndroidAppId(adExt.getAndroidAppId());
        ext.setPreType(adExt.getPreType());
        ext.setDownloadDrive(adExt.isDownloadDrive());
//        ext.setExtendType(adExt.getExtendType());
//        ext.setTemplate(adExt.getExtendType() == ExtendType.TEMPLETE.code());
        ext.setTemplateImgSize(adExt.getTemplateImgSize());
        ext.setMintegralType(adExt.getMintegralType());
        ext.setActivationTime(adExt.getActivationTime());
        ext.setStyle(adExt.getStyle());
        ext.setTitle(adExt.getTitle());
        ext.setAdTag(adExt.getAdTag());
        ext.setAdTagRatio(adExt.getAdTagRatio());
        ext.setShowAdLabel(adExt.isShowAdLabel());
        ext.setAdLabel(adExt.getAdLabel());
        ext.setContent(adExt.getContent());
        ext.setClkUrl(StringUtils.trim(adExt.getClkUrl()));
        ext.setImgUrl(adExt.getImgUrls());
        if (!StringUtils.isEmpty(adExt.getImpTrackUrl())) {
            ext.getImpTrackUrl().add(adExt.getImpTrackUrl());
        }
        if (!StringUtils.isEmpty(adExt.getClkTrackUrl())) {
            ext.getClkTrackUrl().add(adExt.getClkTrackUrl());
        }
        ext.setAdm(adExt.getAdm());
        if (!StringUtils.isEmpty(adExt.getVideoStartTrackUrl())) {
            ext.getVideoStartTrackUrl().add(adExt.getVideoStartTrackUrl());
        }
        if (!StringUtils.isEmpty(adExt.getVideoFinishTrackUrl())) {
            ext.getVideoFinishTrackUrl().add(adExt.getVideoFinishTrackUrl());
        }
        if (!StringUtils.isEmpty(adExt.getVideoPauseTrackUrl())) {
            ext.getVideoPauseTrackUrl().add(adExt.getVideoPauseTrackUrl());
        }
        ext.setDownloadUrl(adExt.getDownloadUrl());
        ext.setItid(adExt.getItid());
        ext.setWidth(adExt.getWidth());
        ext.setHeight(adExt.getHeight());
        ext.setDuration(adExt.getDuration());
        ext.setCountDownGift(adExt.getCountDownGift());

        ext.setAppPkgName(adExt.getAppPkgName());
        ext.setActivationTime(adExt.getActivationTime());

        ext.getTitles().add(adExt.getTitle());
        ext.getContents().add(adExt.getContent());
        ext.getUrls().add(StringUtils.trim(adExt.getUrl()));
        ext.getShareImgUrl().add(StringUtils.trim(adExt.getShareImgUrl()));
        ext.setShareType(adExt.getShareType());
        ext.setIndustry(adExt.getIndustry());
        ext.setSharePrice(adExt.getSharePrice());
        ext.setStyleType(adExt.getStyleType());
        ext.setReplaceShareDomain(adExt.getReplaceShareDomain());
        ext.setNoAppId(adExt.getNoAppId() == null ? true : adExt.getNoAppId());
        ext.setDetailPageShare(adExt.isDetailPageShare());
        ext.setNewuserShare(adExt.isNewuserShare());
        ext.setWord(adExt.getWord());
        ext.setUrl(StringUtils.trim(adExt.getUrl()));
        ext.setPr(adExt.getPr());
        ext.setTwiceJumpTimes(adExt.getTwiceJumpTimes());
        ext.setSourceFrom(adExt.getSourceFrom());
        ext.setFetchStrategy(adExt.getFetchStrategy());
        ext.setHotWordAdUrl(adExt.getHotWordAdUrl());
        ext.setFetchUrl(adExt.getFetchUrl());

        ext.setDeeplinkAppType(adExt.getDeeplinkAppType());
        ext.setDeeplinkOpenType(adExt.getDeeplinkOpenType());
        ext.setDeeplinkPkgName(adExt.getDeeplinkPkgName());
        ext.setDeeplinkUrl(adExt.getDeeplinkUrl());

        ext.setMiniMiddleImage(adExt.getMiniMiddleImage());
        ext.setMiniIcon(adExt.getMiniIcon());
        ext.setMiniShareTitle(adExt.getMiniShareTitle());
        ext.setMiniShareImage(adExt.getMiniShareImage());
        ext.setMiniMiddleBtnText(adExt.getMiniMiddleBtnText());
        ext.setMiniProgramName(adExt.getMiniProgramName());

        ext.setMiniStartWay(adExt.getMiniStartWay());
        ext.setAdAppId(adExt.getAdAppId());
        ext.setAdOriginalId(adExt.getAdOriginalId());
        ext.setAdMiniAppId(adExt.getAdMiniAppId());
        ext.setAdMiniPath(adExt.getAdMiniPath());
        ext.setOwnAppId(adExt.getOwnAppId());
        ext.setOwnOriginalId(adExt.getOwnOriginalId());
        ext.setOwnMiniAppId(adExt.getOwnMiniAppId());
        ext.setOwnMiniPath(adExt.getOwnMiniPath());

        ext.setAdPackageName(adExt.getAdPackageName());
        ext.setOwnPackageName(adExt.getOwnPackageName());

        ext.setGdtUnitId(adExt.getGdtUnitId());
        ext.setOtherBrowser(adExt.getOtherBrowser());

        ext.setDefaultAdTwiceJump(adExt.isDefaultAdTwiceJump());

        ext.setJsDomainName(adExt.getJsDomainName());
        ext.setJsAdPosClose(JSONObject.toJSONString(adExt.getJsAdPosClose()));
        ext.setJsAdPosOpen(JSONObject.toJSONString(adExt.getJsAdPosOpen()));
        ext.setJsAdPosRecommend(JSONObject.toJSONString(adExt.getJsAdPosRecommend()));
        return adExtEntity;
    }

    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @OperateLog(type = OperateType.INSERT,logType = OperateLogType.AD_CONFIG)
    public Ad insertAd(Ad ad) {
        AdInfoEntity adInfoEntity = adVoToEntity(ad);
        int ret = adInfoMapper.insertAd(adInfoEntity);
        if (ret == 1) {
            ad.setId(adInfoEntity.getId());
            adInfoMapper.insertAdOrientation(adOrientationVoToEntity(adInfoEntity.getId(), ad.getOrientation()));
            adInfoMapper.insertAdBudget(adBudgetVoToEntity(adInfoEntity.getId(), ad.getBudget()));
            adInfoMapper.insertAdExt(adExtVoToEntity(adInfoEntity.getId(), ad.getExt()));
            ad.setState(getAdState(ad));
            // 刷新Redis
            refreshRedisService.refreshRedis((long) ad.getId());
            refreshRedisService.addAdId(ad.getProduct());
            // 保存Ad
            adInfoSubService.saveOrUpdateAdInfo((long) ad.getId());
            return ad;
        } else {
            return null;
        }
    }


    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.AD_CONFIG)
    public void updateBatch(BatchUpdateAdRequest request,Integer product){
        Date now = new Date();
        if (Lists.noEmpty(request.getUpdateIds())){
            for (Integer adId : request.getUpdateIds()){
                Integer count = adInfoMapper.updateState(adId,request.getState(),now);
                if (count > 0){
                    log.info("更新开关成功 {} {}",adId,request.getState());
                }
                adInfoMapper.updateAdOrientation(adOrientationVoToEntity(adId,request.getOrientation()));
                // 刷新Redis
                refreshRedisService.refreshRedis((long) adId);
                adInfoSubService.saveOrUpdateAdInfo((long) adId);
            }
        }
    }

    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.AD_CONFIG)
    public Ad updateAd(Ad ad, AdInfoEntity oldValue) {
        AdInfoEntity adInfoEntity = adVoToEntity(ad);
        int ret = adInfoMapper.updateAd(adInfoEntity, handleStateUpdateTimeFlag(adInfoEntity, oldValue));
        if (ret == 1) {
            adInfoMapper.updateAdOrientation(adOrientationVoToEntity(adInfoEntity.getId(), ad.getOrientation()));
            adInfoMapper.updateAdBudget(adBudgetVoToEntity(adInfoEntity.getId(), ad.getBudget()));
            adInfoMapper.updateAdExt(adExtVoToEntity(adInfoEntity.getId(), ad.getExt()));
            ad.setState(getAdState(ad));
            // 更新bidding开关
            updateBiddingSwitch(ad);
            // 刷新Redis
            refreshRedisService.refreshRedis((long) ad.getId());
            adInfoSubService.saveOrUpdateAdInfo((long) ad.getId());
            return ad;
        } else {
            return null;
        }
    }

    private void updateBiddingSwitch(Ad ad){
        List<TbApBidding> tbApBiddingList = tbApBiddingMapper.queryByAdIdAndProduct((long) ad.getId(),ad.getProduct());
        if (tbApBiddingList.size() > 0){
            for (TbApBidding tbApBidding : tbApBiddingList){
                if (ad.getState() == 1){
                    if (tbApBidding.getSwitchFlag()!=1){
                        tbApBidding.setUpdateTime(new Date());
                        tbApBidding.setSwitchFlag(1);
                        tbApBiddingMapper.update(tbApBidding);
                    }
                }else {
                    if (tbApBidding.getSwitchFlag()!=0){
                        tbApBidding.setUpdateTime(new Date());
                        tbApBidding.setSwitchFlag(0);
                        tbApBiddingMapper.update(tbApBidding);
                    }

                }
            }
        }
    }

    public Integer changeAdInfoFlag(Integer id,Integer switchFlag){
        Date now = new Date();
        Integer count = adInfoMapper.updateState(id,switchFlag,now);
        refreshRedisService.refreshRedis(Long.valueOf(id));
        adInfoSubService.saveOrUpdateAdInfo((long) id);
        return count;
    }

    private String handleStateUpdateTimeFlag(AdInfoEntity ad, AdInfoEntity oldValue) {
        return ad.getState() != oldValue.getState() ? "1" : "";
    }

    public List<Ad> selectAllAds(String adName, String adPlan, String adId, String adType, Page page,
                                 List<Integer> filteredAdIds, int product, List<Integer> adPos, String userPkg,
                                 String appId, String posId,String status) {
        List<AdInfoEntity> adInfoEntityList;
        Integer state = null;
        if (StringUtils.isNotEmpty(status)){
            if ("1".equals(status)){
                state = 1;
            }else {
                state = 0;
            }
        }
        if (page != null) {
            int startIndex = (page.getPageNo() - 1) * page.getPageSize();
            adInfoEntityList = adInfoMapper.selectAllAds(adName, adPlan, adId, adType, startIndex, page.getPageSize(), filteredAdIds, product, adPos, userPkg,appId,posId,state);
            int count = adInfoMapper.selectAdsCount(adName, adPlan, adId, adType, filteredAdIds, product, adPos, userPkg,appId,posId,state); // 获取条件下 的广告总数
            page.setCount(count);
        } else {
            adInfoEntityList = adInfoMapper.selectAllAds(adName, adPlan, adId, adType, 0, 0, filteredAdIds, product, adPos, userPkg,appId,posId,state);
        }

        List<Ad> ads = new ArrayList<>();
        for (AdInfoEntity adInfoEntity : adInfoEntityList) {
            Ad ad = adEntityToVo(adInfoEntity);
            ads.add(ad);
        }
        // 处理 Bidding标签
        List<Integer> adIdList = ads.stream().map(Ad::getId).collect(Collectors.toList());
        if (adIdList.size() > 0) {
            List<TbApBidding> tbApBiddingList = tbApBiddingMapper.queryAdIdListAndProduct(adIdList, product);
            Map<Long, TbApBidding> tbApBiddingMap = tbApBiddingList.stream().collect(Collectors.toMap(TbApBidding::getAdId, r -> r, (r1, r2) -> r2));
            for (Ad ad : ads) {
                TbApBidding tbApBidding = tbApBiddingMap.get((long) ad.getId());
                if (tbApBidding != null) {
                    BiddingConfigVo biddingConfigVo = new BiddingConfigVo().build(tbApBidding);
                    if (tbApBidding.getPriority() == null) {
                        tbApBidding.setPriority(0);
                    }
                    biddingConfigVo.setAdName(ad.getName());
                    biddingConfigVo.setAdTypeName(AdTypeSub.get(ad.getType()).getTypeDesc());
                    ad.setPriceType(PriceType.BIDDING.getCode());
                    ad.setBiddingVo(biddingConfigVo);
                } else {
                    ad.setPriceType(PriceType.NORMAL.getCode());
                }
            }
        }
        return ads;
    }

    public static int getAdStatus(Ad ad) {
        return new AdAdminService().getAdState(ad);
    }

    public int getAdState(Ad ad) {
        int state = 1;
        try {
            FastDateFormat sdf = FastDateFormat.getInstance("yyyy-MM-dd");
            Date now = new Date();
            boolean inDate;
            if (ad.isNoEndDate()) {
                if (StringUtils.isEmpty(ad.getStartDate().trim())) {
                    inDate = true;
                } else {
                    Date startDate = sdf.parse(ad.getStartDate());
                    inDate = startDate.getTime() <= now.getTime();
                }
            } else {
                Date startDate = sdf.parse(ad.getStartDate());
                Date endDate = new Date(sdf.parse(ad.getEndDate()).getTime() + 24 * 3600 * 1000l);
                inDate = startDate.getTime() <= now.getTime() && endDate.getTime() >= now.getTime();
            }
            if (!inDate) {
                state = 3;
            } else {
                Calendar cal = Calendar.getInstance();
                cal.setTime(now);
                int week = (cal.get(Calendar.DAY_OF_WEEK) + 5) % 7;
                Date sDate = sdf.parse(sdf.format(now));
                int i = (int) ((now.getTime() - sDate.getTime()) / (1800 * 1000));
                boolean isShow = true;
                if (ad.getTimeBucketVOList() != null && !ad.getTimeBucketVOList().isEmpty()) {
                    Bucket bucket = ad.getTimeBucketVOList().get(week).getTime().get(i);
                    isShow = bucket.isShow();
                }
                if (ad.isOpen() && isShow) {
                    state = 1;
                } else {
                    state = 2;
                }
            }
        } catch (ParseException ex) {
            rootLog.error("日期解析异常, {}", ex.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return state;
    }

//    /**
//     * 查询所有广告的父类型，并映射出父类型名称
//     *
//     * @param userProduct
//     * @return
//     */
//    public List<AdPtype> selectAdPtype(int userProduct) {
//        List<AdPtype> data = new ArrayList<>();
//
//        List<Integer> ptypes = adInfoMapper.selectAllAdPtypes(userProduct);
//        //处理映射关系
//        for (Integer ptype : ptypes) {
//            data.add(new AdPtype(ptype, AdType.getTypeDesc(ptype)));
//        }
//        Collections.sort(data);
//        return data;
//    }

//    /**
//     * 查询指定父类型的所有广告子类型，并映射出子类型的名称
//     *
//     * @param ptype
//     * @param userProduct
//     * @return
//     */
//    public List<AdSubtype> selectAdTypeByPtype(int ptype, int userProduct) {
//        List<AdSubtype> data = new ArrayList<>();
//        List<Integer> types = adInfoMapper.selectAllAdTypesByPtype(ptype, userProduct);
//        // 处理映射关系
//        for (Integer type : types) {
//            data.add(new AdSubtype(type, AdType.getSubtypeDesc(ptype, type)));
//        }
//        Collections.sort(data);
//        return data;
//    }

//    /**
//     * 分享广告落地页URL域名监控
//     *
//     * @param oldValue 旧值
//     * @param newValue 新值
//     */
//    public void shareAdUrlMonitor(AdInfoEntity oldValue, Ad newValue) {
//        // 不是分享类的广告，直接返回
//        if (newValue == null || AdType.get(newValue.getType()) != AdType.ZHIKE_SHARE) {
//            return;
//        }
//
//        if (oldValue == null) { // 新增的情况
//            String url = newValue.getExt().getUrl();
//            HttpDomainUtils.addDomain(HttpDomainUtils.getDomain(url), String.valueOf(newValue.getId()), newValue.getExt().getTitle());
//        } else { // 修改的情况
//            // 比较url是否一致，若不一致，重新添加监控
//            String newUrl = newValue.getExt().getUrl();
//            List<String> oldUrls = oldValue.getExt().getExt().getUrls();
//            for (String oldUrl : oldUrls) {
//                if (StringUtils.isNotEmpty(newUrl) && !newUrl.equals(oldUrl)) {
//                    // 与旧的URL不一致，需要重新监控
//                    HttpDomainUtils.addDomain(HttpDomainUtils.getDomain(newUrl), String.valueOf(newValue.getId()), newValue.getExt().getTitle());
//                }
//            }
//        }
//    }

//    public int addEcpAdInfo(String name, int ecpAdId) {
//        AdInfoEntity entity = new AdInfoEntity();
//        entity.setName(name);
//        entity.setType(0);
////        entity.setPtype(2);
//        entity.setEcpAdId(ecpAdId);
//        entity.setState(0);
//        entity.setProduct(0);
//        return adInfoMapper.insertEcpAd(entity);
//    }

    public List<IndustryVO> selectAllIndustryInfo() {
        List<IndustryVO> retList = new ArrayList<>();

        List<IndustryEntity> entities = industryMapper.selectAll();
        for (IndustryEntity entity : entities) {
            IndustryVO vo = new IndustryVO();
            vo.setId(entity.getId());
            vo.setCode(entity.getCode());
            vo.setName(entity.getName());
            retList.add(vo);
        }

        return retList;
    }

//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        this.applicationContext = applicationContext;
//    }
}
