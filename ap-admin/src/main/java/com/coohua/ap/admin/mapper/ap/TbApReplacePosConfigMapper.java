package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbApReplacePosConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface TbApReplacePosConfigMapper {

    /**
     * 新增
     **/
    int insert(TbApReplacePosConfig tbApReplacePosConfig);

    /**
     * 更新
     **/
    int update(TbApReplacePosConfig tbApReplacePosConfig);

    /**
     * 查询 分页查询
     **/
    List<TbApReplacePosConfig> pageList(@Param("offset") int offset, @Param("pageSize") int pagesize);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR> @date 2021/10/13
     **/
    TbApReplacePosConfig load(int id);

    /**
     * 查询 分页查询 count
     **/
    int pageListCount(@Param("offset") int offset,@Param("pageSize") int pagesize);

    @Select("select * from tb_ap_replace_pos_config where is_enabled = 1 and is_deleted = 0 ")
    List<TbApReplacePosConfig> getAllValid();

    @Update("update tb_ap_replace_pos_config set is_deleted = 0 where id = #{id} ")
    void deleteOne(@Param("id") Integer id);

    @Update("update tb_ap_replace_pos_config set is_enabled = #{isEnabled} where id = #{id}")
    void updateEnable(@Param("id") Integer id, @Param("isEnabled") Integer isEnabled);

}
