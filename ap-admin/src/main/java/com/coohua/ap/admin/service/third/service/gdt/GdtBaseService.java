package com.coohua.ap.admin.service.third.service.gdt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.admin.service.third.dto.gdt.req.*;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.*;
import com.coohua.ap.admin.utils.Env;
import com.coohua.ap.admin.utils.third.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/1/5
 * 腾讯优量汇
 * @link https://docs.qq.com/doc/DRmJFZ1lqWnRHckdE?_t=1609837611571
 * @link http://developers.adnet.qq.com/doc/reporting/index
 */
@Slf4j
@Service
public class GdtBaseService {

    @Autowired
    private Env env;

    private static final String URL = "https://api.adnet.qq.com";
    private static final String TEST_URL = "https://test-api.adnet.qq.com";

    private static final Map<String,Object> publicHeader = new HashMap<String,Object>(){{
        put("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
    }};

    private static Map<String,Object> invokeHeader(String memberId,Integer timestamp,String sign,boolean isForm){
        Map<String, Object> header = new HashMap<>(publicHeader);
        String token = Base64.getEncoder()
                .encodeToString(String.join(",", memberId, timestamp.toString(), sign).getBytes());
        header.put("token",token);
        if (!isForm){
            header.put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        }
        return header;
    }

    private static Integer invokeTime(){
        return (int) (System.currentTimeMillis() / 1000);
    }

    private static String invokeSign(String secret,String memberId,Integer time){
        String beforeSign = memberId + secret + time;
        String sign = DigestUtils.sha1Hex(beforeSign);
        log.info("{} -->GDT SIGN:{}",beforeSign,sign);
        return sign;
    }

    private String invokeUrl(){
        return env.isTest()? TEST_URL:URL;
    }
    
    public  GdtResponse<GdtCreateAppResponse> createApp(GdtAppCreateRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/medium/add";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,true);
        String text = HttpClients.fromPost(baseUrl,header,toMap(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<GdtCreateAppResponse>>(){});
    }

    public GdtResponse<Void> updateApp(GdtAppUpdateRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/medium/update";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,true);
        String text = HttpClients.fromPost(baseUrl,header,toMap(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<Void>>(){});
    }

    public GdtResponse<GdtAppPageResponse> queryApp(GdtAppInfoQueryRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/medium/list";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<GdtAppPageResponse>>(){});
    }


    public GdtResponse<GdtAddAdposResponse> createAdPos(GdtAdPosCreateRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/add";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<GdtAddAdposResponse>>(){});
    }


    public GdtResponse<Void> updateAdPos(GdtAdPosUpdateRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/update";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<Void>>(){});
    }

    public GdtResponse<Void> pauseAdPos(GdtAdPosPauseOrStartRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/pause";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<Void>>(){});
    }

    public GdtResponse<Void> freezeAdPos(GdtAdPosPauseOrStartRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/freeze";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<Void>>(){});
    }

    public GdtResponse<Void> deleteAdPos(GdtAdPosPauseOrStartRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/delete";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<Void>>(){});
    }

    public GdtResponse<Void> startAdPos(GdtAdPosPauseOrStartRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/start";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<Void>>(){});
    }

    public GdtResponse<GdtAdPosPageResponse> queryAdPos(GdtAdPosQueryRequest request, String secret){
        String baseUrl = invokeUrl() + "/open/v1.1/placement/list";
        Integer timestamp = invokeTime();
        String sign = invokeSign(secret,request.getMember_id().toString(),timestamp);
        Map<String,Object> header = invokeHeader(request.getMember_id().toString(),timestamp,sign,false);
        String text = HttpClients.jsonPost(baseUrl,header,JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<GdtResponse<GdtAdPosPageResponse>>(){});
    }

    private static Map<String,Object> toMap(Object request){
        return JSONObject.parseObject(JSON.toJSONString(request));
    }

    public Integer getScene(String scene){
        switch (scene) {
            case "FLASH":
                return 1;
            case "FLOW":
                return 2;
            case "INSERTION":
                return 5;
            case "REWARDED_VIDEO":
                return 4;
            default:return 0;
        }
    }
}
