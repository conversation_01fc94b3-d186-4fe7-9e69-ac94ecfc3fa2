<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApThirdPlatformTypeMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbApThirdPlatformTypeConfig">
        <result column="id" property="id"/>
        <result column="ad_platform_name" property="adPlatformName"/>
        <result column="ad_platform_code" property="adPlatformCode"/>
        <result column="ad_pos_name" property="adPosName"/>
        <result column="ad_type" property="adType"/>
        <result column="ad_type_name" property="adTypeName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
                ad_platform_name,
                ad_platform_code,
                ad_pos_name,
                ad_type,
                ad_type_name,
                create_time,
                update_time
    </sql>


    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_ap_third_platform_type
    </select>


</mapper>