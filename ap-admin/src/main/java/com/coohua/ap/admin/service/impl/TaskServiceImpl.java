package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.controller.vo.TaskVO;
import com.coohua.ap.admin.mapper.ap.TbApTaskMapper;
import com.coohua.ap.admin.model.TbApTaskModel;
import com.coohua.ap.admin.service.TaskService;
import com.coohua.ap.base.constants.TaskType;
import com.coohua.ap.base.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/20
 */
@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    private TbApTaskMapper tbApTaskMapper;

    @Override
    public TbApTaskModel addNewTask(TaskVO taskVO) {
        TbApTaskModel model = new TbApTaskModel(taskVO);
        int addState = tbApTaskMapper.addTask(model);
        if (addState != 1) {
            throw new BusinessException(500, "Add new task error.");
        }

        return tbApTaskMapper.queryById(model.getId());
    }

    @Override
    public TbApTaskModel updateTask(TaskVO taskVO) {
        TbApTaskModel model = new TbApTaskModel();
        model.setTaskName(taskVO.getTaskName());
        model.setId(taskVO.getId());
        model.setDescription(taskVO.getDescription());
        model.setTaskConfig(taskVO.getTaskConfig());
        model.setState(taskVO.isState()?1:0);
        model.setPriority(taskVO.getPriority());
        model.setUserTagId(taskVO.getUserTagId());
        int updateState = tbApTaskMapper.updateTaskById(model);
        if (updateState != 1) {
            throw new BusinessException(500, "Update task error.");
        }
        return tbApTaskMapper.queryById(taskVO.getId());
    }

    @Override
    public TaskVO queryByAppAndTaskType(int taskType, int appId) {
        TbApTaskModel model = tbApTaskMapper.queryByType(taskType, appId);
        if (model == null) {
            return null;
        }
        return new TaskVO(model);
    }

    @Override
    public List<TaskVO> queryList(int appId, String name) {
        List<TbApTaskModel> list = tbApTaskMapper.queryByApp(appId, name);
        List<TaskVO> ret = new ArrayList<>();
        for (TbApTaskModel model : list) {
            TaskVO taskVO = new TaskVO(model);
            taskVO.setTaskTypeName(TaskType.find(taskVO.getTaskType()).getDesc());
            ret.add(taskVO);
        }
        return ret;
    }


}
