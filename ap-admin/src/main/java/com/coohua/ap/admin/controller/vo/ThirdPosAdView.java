package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/13
 */
@Data
public class ThirdPosAdView {

    /**
     * id
     */
    private Integer id;

    /**
     * 开关状态
     */
    private Integer switchFlag;

    /**
     * 平台
     */
    private Integer platformCode;

    /**
     * 主体
     */
    private Integer companyId;

    /**
     * main_body
     */
    private String mainBody;


    private Integer applicationId;
    /**
     * 应用id
     */
    private String appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * pos_id
     */
    private String posId;

    /**
     * 广告位名称
     */
    private String posName;

    /**
     * pos_status
     */
    private Integer posStatus;

    /**
     * 广告类型
     */
    private Integer adType;

    private List<Integer> adSite;

    private Integer adTypeOwn;


    private List<String> adSubType;

    /**
     * income_status
     */
    private Integer incomeStatus;

    private BigDecimal adPrice;

    /**
     * create_time
     */
    private String createTime;

    /**
     * update_time
     */
    private String updateTime;

    // 1-Android 2-IOS
    private Integer os;

}
