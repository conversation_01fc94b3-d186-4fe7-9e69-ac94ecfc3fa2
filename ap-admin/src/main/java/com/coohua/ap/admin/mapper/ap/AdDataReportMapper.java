package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.controller.vo.AdReportDauVO;
import com.coohua.ap.admin.model.AdDataReportEntity;
import com.coohua.ap.admin.model.AdReportDauEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

public interface AdDataReportMapper {


    @Insert("insert into tb_ap_data_report (media_name,app_id,app_name,pos_id,pos_name,pv,click,click_rate,ecpm," +
            "revenue,request_count,fill_rate,date_time,data_source,account_id,type_name) values (#{mediaName},#{appId},#{appName},#{posId},#{posName}" +
            ",#{pv},#{click},#{clickRate},#{ecpm},#{revenue},#{requestCount},#{fillRate},#{dateTime},#{dataSource},#{accountId},#{typeName})")
    void addReportData(AdDataReportEntity adDataReportEntity);


    @Delete("delete from tb_ap_data_report where date_time>=#{startDate} and date_time<=#{endDate}")
    void delReportData(@Param("startDate") String startDate, @Param("endDate") String endDate);

    @Select("select data_source as dataSource,app_name as appName,app_id as appId from tb_ap_data_report group by app_id having data_source=#{dataSource}")
    List<AdDataReportEntity> queryAppMessage(@Param("dataSource") int dataSource);

    @Select("select data_source as dataSource,app_id as appId,pos_id as posId,pos_name as posName FROM `tb_ap_data_report` group by pos_id having data_source=#{dataSource} and app_id=#{appId}")
    List<AdDataReportEntity> queryPosMessage(@Param("dataSource") int dataSource,@Param("appId") long appId);

    @SelectProvider(type = SqlProvider.class, method = "queryReportData")
    List<AdDataReportEntity> queryReportData(@Param("startDate") String dateStart,
                                             @Param("endDate") String dateEnd,
                                             @Param("sourceId") String sourceId,
                                             @Param("appId") String appId,
                                             @Param("posId") String posId);


    @Insert("insert into tb_ap_product_dau (product_name,os,dau,logday) values (#{productName},#{os},#{dau},#{logday})" )
    void addDauData(AdReportDauEntity adReportDauEntity);

    @Select("select product_name as productName," +
            "os as os," +
            "dau as dau," +
            "logday as logday" +
            " from tb_ap_product_dau" +
            " where product_name=#{productName}" +
            " and os=#{os}" +
            " and logday=#{date}")
    AdReportDauEntity queryDauByNameOsDate(@Param("productName")String productName,
    @Param("os")String os,
    @Param("date")String date);

    @Delete("delete from tb_ap_product_dau where logday>=#{startDate} and logday<=#{endDate}")
    void delDau(@Param("startDate")String startDate,@Param("endDate")String endDate);

    class SqlProvider{

        public String queryReportData(Map<String,String> param){
            String sourceId = param.get("sourceId");
            String appId =  param.get("appId");
            String posId =  param.get("posId");
            String sql = "select media_name as mediaName," +
                    "app_id as appId," +
                    "app_name as appName," +
                    "pos_id as posId," +
                    "pos_name as posName," +
                    "pv as pv," +
                    "click as click," +
                    "click_rate as clickRate," +
                    "ecpm as ecpm," +
                    "revenue as revenue," +
                    "request_count as requestCount," +
                    "fill_rate as fillRate," +
                    "date_time as dateTime," +
                    "data_source as dataSource," +
                    "account_id as accountId," +
                    "type_name as typeName " +
                    "from tb_ap_data_report where date_time>=#{startDate} and date_time<=#{endDate} ";
            if(sourceId != null){
                sql = sql+"and data_source=#{sourceId} ";
            }
            if(appId != null){
                sql = sql+"and app_id = #{appId} ";
            }
            if(StringUtils.isNotBlank(posId)){
                sql = sql+"and pos_id = #{posId} ";
            }
            return sql + "order by date_time asc";
        }
    }
}
