package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.coohua.ap.admin.aop.OperateLog;
import com.coohua.ap.admin.aop.OperateType;
import com.coohua.ap.admin.constants.OperateLogType;
import com.coohua.ap.admin.controller.vo.ConfigVO;
import com.coohua.ap.admin.mapper.ap.ConfigMapper;
import com.coohua.ap.admin.model.Config;
import com.coohua.ap.admin.service.ConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.nap.service.impl
 * @create_time 2019-08-20
 */
@Service
public class ConfigServiceImpl implements ConfigService {

    @Autowired
    private ConfigMapper configMapper;

    @Override
    public String getConfigValue(String name,int product) {
        Config config = this.configMapper.selectOne(name,product);
        if (config != null) {
            return config.getValue();
        } else {
            return null;
        }
    }

    @Override
    public int insert(String name, String value) {
        Config config = new Config();
        config.setName(name);
        config.setValue(value);
        return this.configMapper.insertOne(config);
    }

    @Override
    public int insert(Config config) {
        return this.configMapper.insertOne(config);
    }

    @Override
    @OperateLog(type = OperateType.UPDATE,logType = OperateLogType.GLOBAL_CONFIG)
    public ConfigVO update(ConfigVO configVO) {
        Config config = new Config();
        config.setName(configVO.getName());
        config.setValue(configVO.getValue());
        config.setProduct(configVO.getProduct());
        int state = this.configMapper.updateOne(config);
        configVO.setId(config.getId());
        return configVO;
    }

    @Override
    public List<Config> getAll() {
        return this.configMapper.selectAll();
    }

    @Override
    public List<Config> getAllByProduct(int product) {
        return this.configMapper.selectAllByProduct(product);
    }

    @Override
    public String get(String name,int product) {
        Config config = this.configMapper.selectOne(name,product);
        if (config != null) {
            return config.getValue();
        } else {
            return null;
        }
    }

    @Override
    public Config getConfig(String name,int product) {
        Config config = this.configMapper.selectOne(name,product);
        return config;
    }

    @Override
    public List<String> getJsAdChannelSourceList(int product) {
        Config config = getConfig("js.ad.channel.source",product);
        if (config == null || StringUtils.isBlank(config.getValue())) {
            return null;
        }

        return JSONArray.parseArray(config.getValue(), String.class);
    }
}
