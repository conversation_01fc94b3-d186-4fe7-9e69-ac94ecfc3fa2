package com.coohua.ap.admin.controller.vo;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON> on 2017/4/12.
 */
public class AdOrientation {
    private boolean noRegion;
    private List<Integer> regions;
    private boolean noIos;
    private String minIosVersion;
    private String maxIosVersion;
    private boolean noAndroid;
    private String minAndroidVersion;
    private String maxAndroidVersion;
    private String tailNumber; // 尾号限制
    private String position; // 投放位置限制
    private boolean noIncomeLimit; // 是否有总收入限制
    private int minIncome; // 最小总收入限制
    private int maxIncome; // 最大总收入限制
    private boolean noRegistTimeLimit; // 是否有注册时间限制
    private String minRegistTime; // 最小注册时间限制
    private String maxRegistTime; // 最大注册时间限制
    private String containsPkg; // 包含包名定向
    private String notContainsPkg; // 不包含包名定向
    private boolean noRegistLongerLimit; // 是否有
    private String minRegistLonger; // 最小注册时长限制
    private String maxRegistLonger; // 最大注册时长限制
    private String userPkg; // 用户使用的包名
    private String channelId; // 渠道id
    private String abPoint; // 渠道id
    private Integer filterRegion; // 受限区
    private Integer lockArea; // 锁区
    private boolean brandLimit; // 是否限制机型
    private List<String> brandLimitList; // 机型限制
    private String dsp; // 投放渠道
    private Integer lockActionPoint; // 锁区作用点

    public Integer getLockActionPoint() {
        return lockActionPoint;
    }

    public void setLockActionPoint(Integer lockActionPoint) {
        this.lockActionPoint = lockActionPoint;
    }

    public boolean isNoRegion() {
        return noRegion;
    }

    public void setNoRegion(boolean noRegion) {
        this.noRegion = noRegion;
    }

    public List<Integer> getRegions() {
        return regions;
    }

    public void setRegions(List<Integer> regions) {
        this.regions = regions;
    }

    public boolean isNoIos() {
        return noIos;
    }

    public void setNoIos(boolean noIos) {
        this.noIos = noIos;
    }

    public boolean isNoAndroid() {
        return noAndroid;
    }

    public void setNoAndroid(boolean noAndroid) {
        this.noAndroid = noAndroid;
    }

    public String getMinIosVersion() {
        return minIosVersion;
    }

    public void setMinIosVersion(String minIosVersion) {
        this.minIosVersion = minIosVersion;
    }

    public String getMaxIosVersion() {
        return maxIosVersion;
    }

    public void setMaxIosVersion(String maxIosVersion) {
        this.maxIosVersion = maxIosVersion;
    }

    public String getMinAndroidVersion() {
        return minAndroidVersion;
    }

    public void setMinAndroidVersion(String minAndroidVersion) {
        this.minAndroidVersion = minAndroidVersion;
    }

    public String getMaxAndroidVersion() {
        return maxAndroidVersion;
    }

    public void setMaxAndroidVersion(String maxAndroidVersion) {
        this.maxAndroidVersion = maxAndroidVersion;
    }

    public String getTailNumber() {
        return tailNumber;
    }

    public void setTailNumber(String tailNumber) {
        this.tailNumber = tailNumber;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public boolean isNoIncomeLimit() {
        return noIncomeLimit;
    }

    public void setNoIncomeLimit(boolean noIncomeLimit) {
        this.noIncomeLimit = noIncomeLimit;
    }

    public int getMinIncome() {
        return minIncome;
    }

    public void setMinIncome(int minIncome) {
        this.minIncome = minIncome;
    }

    public int getMaxIncome() {
        return maxIncome;
    }

    public void setMaxIncome(int maxIncome) {
        this.maxIncome = maxIncome;
    }

    public boolean isNoRegistTimeLimit() {
        return noRegistTimeLimit;
    }

    public void setNoRegistTimeLimit(boolean noRegistTimeLimit) {
        this.noRegistTimeLimit = noRegistTimeLimit;
    }

    public String getMinRegistTime() {
        return minRegistTime;
    }

    public void setMinRegistTime(String minRegistTime) {
        this.minRegistTime = minRegistTime;
    }

    public String getMaxRegistTime() {
        return maxRegistTime;
    }

    public void setMaxRegistTime(String maxRegistTime) {
        this.maxRegistTime = maxRegistTime;
    }

    public String getContainsPkg() {
        return containsPkg;
    }

    public void setContainsPkg(String containsPkg) {
        this.containsPkg = containsPkg;
    }

    public String getNotContainsPkg() {
        return notContainsPkg;
    }

    public void setNotContainsPkg(String notContainsPkg) {
        this.notContainsPkg = notContainsPkg;
    }

    public boolean isNoRegistLongerLimit() {
        return noRegistLongerLimit;
    }

    public void setNoRegistLongerLimit(boolean noRegistLongerLimit) {
        this.noRegistLongerLimit = noRegistLongerLimit;
    }

    public String getMinRegistLonger() {
        return minRegistLonger;
    }

    public void setMinRegistLonger(String minRegistLonger) {
        this.minRegistLonger = minRegistLonger;
    }

    public String getMaxRegistLonger() {
        return maxRegistLonger;
    }

    public void setMaxRegistLonger(String maxRegistLonger) {
        this.maxRegistLonger = maxRegistLonger;
    }

    public String getUserPkg() {
        return userPkg;
    }

    public void setUserPkg(String userPkg) {
        this.userPkg = userPkg;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public boolean isBrandLimit() {
        return brandLimit;
    }

    public void setBrandLimit(boolean brandLimit) {
        this.brandLimit = brandLimit;
    }

    public List<String> getBrandLimitList() {
        return brandLimitList;
    }

    public void setBrandLimitList(List<String> brandLimitList) {
        this.brandLimitList = brandLimitList;
    }

    public String getAbPoint() {
        return abPoint;
    }

    public void setAbPoint(String abPoint) {
        this.abPoint = abPoint;
    }

    public Integer getFilterRegion() {
        return filterRegion;
    }

    public void setFilterRegion(Integer filterRegion) {
        this.filterRegion = filterRegion;
    }

    public Integer getLockArea() {
        return lockArea;
    }

    public void setLockArea(Integer lockArea) {
        this.lockArea = lockArea;
    }

    public String getDsp() {return dsp;}

    public void setDsp(String dsp) {this.dsp = dsp;}

    @Override
    public String toString() {
        return "AdOrientation{" +
                "noRegion=" + noRegion +
                ", regions=" + regions +
                ", noIos=" + noIos +
                ", minIosVersion='" + minIosVersion + '\'' +
                ", maxIosVersion='" + maxIosVersion + '\'' +
                ", noAndroid=" + noAndroid +
                ", minAndroidVersion='" + minAndroidVersion + '\'' +
                ", maxAndroidVersion='" + maxAndroidVersion + '\'' +
                ", tailNumber='" + tailNumber + '\'' +
                ", position='" + position + '\'' +
                ", noIncomeLimit=" + noIncomeLimit +
                ", minIncome=" + minIncome +
                ", maxIncome=" + maxIncome +
                ", noRegistTimeLimit=" + noRegistTimeLimit +
                ", minRegistTime='" + minRegistTime + '\'' +
                ", maxRegistTime='" + maxRegistTime + '\'' +
                ", containsPkg='" + containsPkg + '\'' +
                ", notContainsPkg='" + notContainsPkg + '\'' +
                ", noRegistLongerLimit=" + noRegistLongerLimit +
                ", minRegistLonger='" + minRegistLonger + '\'' +
                ", maxRegistLonger='" + maxRegistLonger + '\'' +
                ", userPkg='" + userPkg + '\'' +
                ", channelId='" + channelId + '\'' +
                ", abPoint='" + abPoint + '\'' +
                ", filterRegion=" + filterRegion +
                ", lockArea=" + lockArea +
                ", brandLimit=" + brandLimit +
                ", brandLimitList=" + brandLimitList +
                ", dsp=" + dsp +
                ", lockActionPoint=" + lockActionPoint +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AdOrientation that = (AdOrientation) o;
        return noRegion == that.noRegion &&
                noIos == that.noIos &&
                noAndroid == that.noAndroid &&
                noIncomeLimit == that.noIncomeLimit &&
                minIncome == that.minIncome &&
                maxIncome == that.maxIncome &&
                noRegistTimeLimit == that.noRegistTimeLimit &&
                noRegistLongerLimit == that.noRegistLongerLimit &&
                brandLimit == that.brandLimit &&
                Objects.equals(regions, that.regions) &&
                Objects.equals(minIosVersion, that.minIosVersion) &&
                Objects.equals(maxIosVersion, that.maxIosVersion) &&
                Objects.equals(minAndroidVersion, that.minAndroidVersion) &&
                Objects.equals(maxAndroidVersion, that.maxAndroidVersion) &&
                Objects.equals(tailNumber, that.tailNumber) &&
                Objects.equals(position, that.position) &&
                Objects.equals(minRegistTime, that.minRegistTime) &&
                Objects.equals(maxRegistTime, that.maxRegistTime) &&
                Objects.equals(containsPkg, that.containsPkg) &&
                Objects.equals(notContainsPkg, that.notContainsPkg) &&
                Objects.equals(minRegistLonger, that.minRegistLonger) &&
                Objects.equals(maxRegistLonger, that.maxRegistLonger) &&
                Objects.equals(userPkg, that.userPkg) &&
                Objects.equals(channelId, that.channelId) &&
                Objects.equals(dsp, that.dsp) &&
                Objects.equals(lockActionPoint, that.lockActionPoint) &&
                Objects.equals(brandLimitList, that.brandLimitList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(noRegion, regions, noIos, minIosVersion, maxIosVersion, noAndroid, minAndroidVersion, maxAndroidVersion, tailNumber, position, noIncomeLimit, minIncome, maxIncome, noRegistTimeLimit, minRegistTime, maxRegistTime, containsPkg, notContainsPkg, noRegistLongerLimit, minRegistLonger, maxRegistLonger, userPkg, channelId, dsp, lockActionPoint, brandLimit, brandLimitList);
    }

}
