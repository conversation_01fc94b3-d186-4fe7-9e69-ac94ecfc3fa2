<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApThirdConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbApThirdConfig" >
        <result column="ad_id" property="adId" />
        <result column="pos_id" property="posId" />
        <result column="app_id" property="appId" />
        <result column="product" property="product" />
        <result column="os" property="os" />
        <result column="ad_type" property="adType" />
    </resultMap>

    <sql id="Base_Column_List">
                ad_id,
                pos_id,
                app_id,
                product,
                os,
                ad_type
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.TbApThirdConfig">
        INSERT INTO tb_ap_third_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != adId">
                ad_id,
            </if>
            <if test="null != posId">
                pos_id,
            </if>
            <if test="null != appId">
                app_id,
            </if>
            <if test="null != product">
                product,
            </if>
            <if test="null != os">
                os,
            </if>
            <if test="null != adType">
                ad_type
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != adId">
                #{adId},
            </if>
            <if test="null != posId">
                #{posId},
            </if>
            <if test="null != appId ">
                #{appId},
            </if>
            <if test="null != product">
                #{product},
            </if>
            <if test="null != os">
                #{os},
            </if>
            <if test="null != adType">
                #{adType}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM tb_ap_third_config
        WHERE ad_id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.TbApThirdConfig">
        UPDATE tb_ap_third_config
        <set>
            <if test="null != adId">ad_id = #{adId},</if>
            <if test="null != posId">pos_id = #{posId},</if>
            <if test="null != appId">app_id = #{appId},</if>
            <if test="null != product">product = #{product},</if>
            <if test="null != product">os = #{os},</if>
            <if test="null != adType">ad_type = #{adType}</if>
        </set>
        WHERE ad_id = #{adId}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_third_config
        WHERE ad_id = #{adId}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_third_config
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_ap_third_config
    </select>

</mapper>