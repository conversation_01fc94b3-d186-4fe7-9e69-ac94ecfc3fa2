package com.coohua.ap.admin;

import com.coohua.ap.base.utils.TimeCost;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;
import top.zrbcool.pepper.boot.mybatis.EnableDataSource;

/**
 * Description:
 *
 * <AUTHOR>
 * @since  2019-09-30
 */
@SpringBootApplication(scanBasePackages = "com.coohua.ap")
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
        // DB配置
        "ap.datasource","bp.db.ecpnew",
        "ap.redis.cluster",
        "ad.user.ad.api.referer",
        // 系统级全局配置，在ad.common下
        "ad.overall.config","ad.rocket.mq"})
@EnableAutoChangeApolloConfig
@EnableMotan(namespace = "user-event")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableDataSource(namespace = "apdatasource", mapperPackages = "com.coohua.ap.admin.mapper.ap")
@EnableDataSource(namespace = "ecpnew", mapperPackages = "com.coohua.ap.admin.mapper.ecpnew")
@EnableScheduling
@Slf4j
public class ApAdminApplication {
    public static void main(String[] args) {
        try {
            TimeCost timeCost = new TimeCost();
            SpringApplication.run(ApAdminApplication.class, args);
            log.info("Ad admin project started in {}ms", timeCost.getCostMillSeconds());
        } catch (Exception e) {
            log.error("error. ", e);
        }
    }
}
