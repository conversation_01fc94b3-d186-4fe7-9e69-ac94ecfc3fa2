package com.coohua.ap.admin.service.third.dto.csj.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Data
public class AdPosCreateRequest {
    private Integer user_id;
    private Integer role_id;
    private Integer timestamp;
    private Integer nonce;
    private String sign;
    private String version;
    private Integer app_id;
    private Integer ad_slot_type;
    private String ad_slot_name;
    private Integer mask_rule_id;


    private Integer render_type;
    private Integer orientation;
    private String reward_name;
    private Integer reward_count;
    private Integer reward_is_callback;
    private String reward_callback_url;
    private Integer reward_back_type;

    private Integer slide_banner;
    private Integer width;
    private Integer height;
    private Integer[] ad_categories;
    private String cpm;

    private Tpl[] tpl_list;
    private Integer accept_material_type;
    private Integer ad_rollout_size;
    private Integer skip_duration;
    private Integer use_endcard;
    private Integer bidding_type;
    private String use_mediation;

    @Data
    public static class Tpl{
        private Integer tpl_id;
    }

}
