package com.coohua.ap.admin.service;

import com.coohua.ap.admin.mapper.ap.AdminUserMapper;
import com.coohua.ap.admin.model.AdminUseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by <PERSON><PERSON> on 2017/3/10.
 */
@Service
public class AdminUserService {

    @Autowired
    private AdminUserMapper adminUserMapper;

    public AdminUseEntity check(String userName, String password) {
        AdminUseEntity adminUseEntity = new AdminUseEntity();
        adminUseEntity.setUserName(userName);
        adminUseEntity.setPassword(password);
        return adminUserMapper.selectOne(adminUseEntity);
    }
}
