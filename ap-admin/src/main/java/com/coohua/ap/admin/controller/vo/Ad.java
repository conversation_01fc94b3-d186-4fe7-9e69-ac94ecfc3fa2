package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON> <PERSON><PERSON> on 2017/4/12.
 */
@Data
public class Ad {
    private AdPlan plan;
    private int id;
    private String name;
    private int state; // 1 投放中 2 已暂停 3 已结束
//    private int ptype; //广告父类型
    private int type; // 广告类型
    private boolean noEndDate;
    private String startDate;
    private String endDate;
    private AdOrientation orientation;
    private List<TimeBucketVO> timeBucketVOList = new ArrayList<>(); // 时段设置， 7 * 48
    private AdBudget budget;
    private AdExt ext;
    private boolean open;
    private int product;
    private List<Integer> adPos = new ArrayList<>(); // 广告投放位置配置
    private boolean cache = false;
    private String posIdTemp;
    /**
     * @see com.coohua.ap.admin.constants.PriceType
     */
    private Integer priceType;
    private String lastUpdateTime;
    private BiddingConfigVo biddingVo;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Ad ad = (Ad) o;
        return id == ad.id &&
                state == ad.state &&
//                ptype == ad.ptype &&
                type == ad.type &&
                noEndDate == ad.noEndDate &&
                open == ad.open &&
                product == ad.product &&
                Objects.equals(plan, ad.plan) &&
                Objects.equals(name, ad.name) &&
                Objects.equals(startDate, ad.startDate) &&
                Objects.equals(endDate, ad.endDate) &&
                Objects.equals(orientation, ad.orientation) &&
                Objects.equals(timeBucketVOList, ad.timeBucketVOList) &&
                Objects.equals(budget, ad.budget) &&
                Objects.equals(ext, ad.ext) &&
                Objects.equals(adPos, ad.adPos);
    }

    @Override
    public int hashCode() {

        return Objects.hash(plan, id, name, state, type, noEndDate, startDate, endDate, orientation, timeBucketVOList, budget, ext, open, product, adPos);
    }

}
