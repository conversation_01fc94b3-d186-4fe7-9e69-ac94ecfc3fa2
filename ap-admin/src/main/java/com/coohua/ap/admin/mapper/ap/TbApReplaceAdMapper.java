package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbApReplaceAd;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface TbApReplaceAdMapper {

    int insert(TbApReplaceAd tbApReplaceAd);

    int delete(int id);

    int update(TbApReplaceAd tbApReplaceAd);

    int batchUpdate(List<TbApReplaceAd> tbApReplaceAdList);

    TbApReplaceAd load(int id);

    List<TbApReplaceAd> pageList(@Param("offset") int offset,
                               @Param("pageSize") int pagesize,
                               @Param("product")Integer product,
                               @Param("adId") String adId);

    int pageListCount(@Param("offset") int offset,
                      @Param("pageSize") int pagesize,
                      @Param("product")Integer product,
                      @Param("adId") String adId);

    @Select("select * from tb_ap_replace_ad where strategy_id = #{id}")
    List<TbApReplaceAd> queryByStrategyId(long id);

    @Select("select * from tb_ap_replace_ad where product = #{product}")
    Set<TbApReplaceAd> queryByProduct(@Param("product") String product);
}
