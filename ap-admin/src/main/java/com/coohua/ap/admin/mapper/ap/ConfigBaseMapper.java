package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ConfigBaseEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <pre>
 *      DB配置管理接口
 * <hr/>
 * Package Name : com.coohua.nap.dao
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/13 上午10:01
 * </pre>
 */
public interface ConfigBaseMapper {

    /**
     * 根据管理员所属产品，分页查询配置信息
     * @param configBaseEntity 用户产品信息
     * @return 配置列表
     */
    List<ConfigBaseEntity> queryAllByProduct(@Param("configBase") ConfigBaseEntity configBaseEntity,
                                             @Param("from") int from, @Param("offset") int offset,@Param("queryType")Integer queryType);

    /**
     * 查询所有配置信息
     * @return 配置列表
     */
    List<ConfigBaseEntity> queryAll();

    /**
     * 查询一条配置信息
     * @return 配置
     */
    ConfigBaseEntity queryOneById(int id);

    /**
     * 根据管理员所属产品，查询所有配置的数量，用于分页
     * @param configBaseEntity 产品信息
     * @return
     */
    long countAllByProduct(@Param("configBase") ConfigBaseEntity configBaseEntity,@Param("queryType")Integer queryType);

    /**
     * 添加一个配置，根据用户产品，并填充ID
     * @param configBaseEntity 配置详情
     * @return 数据库影响条数
     */
    int insertConfig(ConfigBaseEntity configBaseEntity);

    /**
     * 根据配置ID修改配置
     * @param configBaseEntity 修改详情
     * @return 数据库影响条数
     */
    int updateConfig(ConfigBaseEntity configBaseEntity);

    /**
     * 根据配置ID修改配置状态
     * @param configBaseEntity 修改的状态
     * @return 数据库影响条数
     */
    int updateConfigState(ConfigBaseEntity configBaseEntity);

    /**
     * 根据配置ID 修改删除状态
     * @param configBaseEntity 修改的状态 至少包含id 和DEL_FLAG
     * @return 影响行数
     */
    int updateConfigDelFlag(ConfigBaseEntity configBaseEntity);

    /**
     * 根据产品查询 此产品下的所有的ID和名称的映射
     * @param product 产品标识
     * @return ID和名称的映射关系
     */
    List<ConfigBaseEntity> queryIdAndNameList(@Param("product") int product);

    @Select("select count(1) from tb_ap_config_base where name = #{name} and product = #{product} and del_flag = 1")
    int countByName(@Param("name")String name,@Param("product")Integer product);

    @Select("select count(1) from tb_ap_config_base where product = #{product} and name like concat('%', #{name},'%') ")
    int countByNameLike(@Param("name")String name,@Param("product")Integer product);

    @Select({"SELECT * FROM tb_ap_config_base WHERE `state` = 1 and (name like '%锁区%' or name like '%受限区%')"})
    List<ConfigBaseEntity> queryLimitOrLockedConfig();

    @Select({"<script>",
            "select * from tb_ap_config_base where " +
                    " id in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    List<ConfigBaseEntity> queryByIds(@Param("ids") List<Integer> ids);


    @Select({"SELECT * FROM tb_ap_config_orientation WHERE product = #{product} "})
    List<ConfigBaseEntity> queryHighestPriority(@Param("product") Integer product);
}
