package com.coohua.ap.admin.service.third;

import com.coohua.ap.admin.constants.OpType;
import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.*;
import com.coohua.ap.admin.service.third.dto.CallBackPrVo;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.CratePosResponse;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosUpdateRequest;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AdPosUpdateResponse;
import com.coohua.ap.admin.service.third.dto.csj.rsp.Response;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosUpdateRequest;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtResponse;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionQueryRequest;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionRequest;
import com.coohua.ap.admin.service.third.dto.ks.response.KsPositionResponse;
import com.coohua.ap.admin.service.third.dto.ks.response.KsResponse;
import com.coohua.ap.admin.service.third.service.bd.BdBaseService;
import com.coohua.ap.admin.service.third.service.csj.CsjBaseService;
import com.coohua.ap.admin.service.third.service.gdt.GdtBaseService;
import com.coohua.ap.admin.service.third.service.ks.KsBaseService;
import com.coohua.ap.admin.utils.third.AdUtils;
import com.coohua.ap.admin.utils.third.CallBackUtils;
import com.coohua.ap.base.constants.AdTypeConvertVo;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.support.core.container.AppBuilder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/10/25
 * 三方回调设置升级更新
 */
@Slf4j
@Service
public class ThirdCallBackService {

    @Autowired
    private CsjBaseService csjBaseService;
    @Autowired
    private GdtBaseService gdtBaseService;
    @Autowired
    private KsBaseService ksBaseService;
    @Autowired
    private BdBaseService bdBaseService;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private ThirdAppMapper thirdAppMapper;
    @Resource
    private ThirdCompanyMapper thirdCompanyMapper;
    @Resource
    private ThirdAdPosMapper thirdAdPosMapper;
    @Autowired
    private ThirdLogService thirdLogService;
    @Resource
    private ThirdCallBackConfigMapper thirdCallBackConfigMapper;
    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor executor;

    private final static List<String> platformList = new ArrayList<String>(){{
       add(Platform.CSJ.getDesc());
       add(Platform.GDT.getDesc());
       add(Platform.KS.getDesc());
       add(Platform.BD.getDesc());
    }};

    public List<CallBackPrVo> queryList(){
        return thirdCallBackConfigMapper.queryAll().stream().map(callBackPrVo -> {
            callBackPrVo.setProductName(AppBuilder.getById(callBackPrVo.getProduct()).desc());
            return callBackPrVo;
        }).collect(Collectors.toList());
    }

    public void closeSingleUrl(Long adId,Integer product){
        AdInfoEntity adInfoEntity = adInfoMapper.selectOneAd(adId,product);
        if (!adTypeFilter(adInfoEntity)){
            return;
        }
        buildAndDone(Collections.singletonList(adInfoEntity),product,adInfoEntity.getOrientation().getPlatformVersion().getPlatform(),this::closeAdCallBackUrl);
    }

    /**
     * 给单独一个广告设置回调链接
     * @param adId 广告ID
     * @param product 内部产品Id
     * @see AppBuilder
     */
    public void openSingleUrl(Long adId,Integer product){
        AdInfoEntity adInfoEntity = adInfoMapper.selectOneAd(adId,product);
        if (adInfoEntity == null || !adTypeFilter(adInfoEntity)){
            return;
        }

        List<AdInfoEntity> filterAdList = filterNoSetUrl(Collections.singletonList(adInfoEntity));
        buildAndDone(filterAdList,product,adInfoEntity.getOrientation().getPlatformVersion().getPlatform(),this::openAdCallBackUrl);
    }

    /**
     * 给单独一个广告设置回调链接-不调用三方
     * @param adId 广告ID
     * @param product 内部产品Id
     */
    public void setSingleUrl(Long adId,Integer product){
        AdInfoEntity adInfoEntity = adInfoMapper.selectOneAd(adId,product);
        if (adInfoEntity == null || !adTypeFilter(adInfoEntity)){
            return;
        }

        Long posId = AdUtils.getPosId(adInfoEntity);
        ThirdAdPosEntity thirdAdPosEntity = thirdAdPosMapper.queryByPosId(posId);
        if (thirdAdPosEntity == null){
            return;
        }
        CreateUrlBean createUrlBean = new CreateUrlBean();

        createUrlBean.setAdId(adId);
        createUrlBean.setProduct(product);
        createUrlBean.setAppId(thirdAdPosEntity.getAppId());
        createUrlBean.setPosId(thirdAdPosEntity.getPosId());
        saveOrUpdate(createUrlBean,thirdAdPosEntity.getCallBackKey());
    }

    /**
     * 批量开启某应用下全部激励视频
     * @param product 产品内部APP_ID
     * @param os 1-Android 2-IOS
     */
    public void openAllUrl(Integer product,Integer os){
        CompletableFuture.runAsync(()->{
            List<AdInfoEntity> adInfoEntityList = adInfoMapper.selectAllProductAd(product,1);

            List<AdInfoEntity> filterAdList = adInfoEntityList.stream()
                    .filter(this::adTypeFilter)
                    .filter(adInfoEntity -> this.osFilter(adInfoEntity,os))
                    .collect(Collectors.toList());

            filterAdList = filterNoSetUrl(filterAdList);
            buildAndDone(filterAdList,product,os,this::openAdCallBackUrl);
        },executor);
    }
    public void openBDAllUrl(Integer product,Integer os){
        CompletableFuture.runAsync(()->{
            List<AdInfoEntity> adInfoEntityList = adInfoMapper.selectAllProductAd(product,1);

            List<AdInfoEntity> filterAdList = adInfoEntityList.stream()
                    .filter(this::adTypeFilter)
                    .filter(adInfoEntity -> this.osFilter(adInfoEntity,os))
                    .collect(Collectors.toList());

            filterAdList = filterNoSetUrl(filterAdList);
            buildAndDone(filterAdList,product,os,this::openAdCallBackUrlBD);
        },executor);
    }

    private void buildAndDone(List<AdInfoEntity> filterAdList,Integer product,Integer os, Consumer<? super CreateUrlBean> action){
        try{
            if (filterAdList == null || filterAdList.size() == 0){
                log.error("[{}]-未找到需要设置的广告位...",product);
                return;
            }

            Map<String,Platform> appIdMap = new HashMap<>();
            List<CreateUrlBean> createUrlBeans = filterAdList.stream()
                    .map(adInfoEntity -> {
                        CreateUrlBean createUrlBean = new CreateUrlBean();
                        String posIdStr = os == 1 ? adInfoEntity.getExt().getExt().getAndroidPosId() : adInfoEntity.getExt().getExt().getIosPosId();
                        String appId = os == 1 ? adInfoEntity.getExt().getExt().getAndroidAppId() : adInfoEntity.getExt().getExt().getIosAppId();
                        Long posId = Long.valueOf(posIdStr);
                        createUrlBean.setAdId((long) adInfoEntity.getId());
                        createUrlBean.setPosId(posId);
                        createUrlBean.setProduct(product);
                        createUrlBean.setPrice(adInfoEntity.getBudget().getEcpm());
                        AdTypeConvertVo adTypeConvertVo = AdTypeSub.adTypeMapFl.get(adInfoEntity.getType());
                        Platform platform = Platform.getPlatform(adTypeConvertVo.getAdSource());
                        appIdMap.put(appId,platform);
                        createUrlBean.setAppId(appId);
                        createUrlBean.setPlatform(platform);
                        return createUrlBean;
                    }).collect(Collectors.toList());

            List<ThirdAppEntity> thirdAppEntities = appIdMap.keySet().stream()
                    .map(app-> thirdAppMapper.queryByProductAndOsAndAppId(product,os,app,appIdMap.get(app).getCode()))
                    .collect(Collectors.toList());

            Map<String,ThirdCompanyEntity> companyEntityMap = new HashMap<>();
            thirdAppEntities.forEach(thirdAppEntity -> {
                ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
                companyEntityMap.put(thirdAppEntity.getAppId(),thirdCompanyEntity);
            });

            createUrlBeans.stream()
                    .peek(createUrlBean -> createUrlBean.setThirdCompanyEntity(companyEntityMap.get(createUrlBean.getAppId())))
                    .forEach(action);
        }catch (Exception e){
            log.error("ThirdCallBackService buildAndDone close ", e);
        }
    }

    @Data
    private static class CreateUrlBean{
        private Long posId;
        private Long adId;
        private String appId;
        private Integer price;
        private Platform platform;
        private Integer product;
        private ThirdCompanyEntity thirdCompanyEntity;
    }

    private boolean adTypeFilter(AdInfoEntity adInfoEntity){
        AdTypeConvertVo adTypeConvertVo = AdTypeSub.adTypeMapFl.get(adInfoEntity.getType());
        return "视频".equals(adTypeConvertVo.getSubTypeName()) && platformList.contains(adTypeConvertVo.getAdSource());
    }

    private boolean osFilter(AdInfoEntity adInfoEntity,Integer os){
        return os.equals(adInfoEntity.getOrientation().getPlatformVersion().getPlatform());
    }

    /**
     * 过滤已经成功设置回调链接的URL
     * @param adInfoEntityList 广告列表
     * @return 未设置URL的广告列表
     */
    private List<AdInfoEntity> filterNoSetUrl(List<AdInfoEntity> adInfoEntityList){
        List<Long> adIds = adInfoEntityList.stream().map(adInfoEntity -> (long) adInfoEntity.getId()).collect(Collectors.toList());
        if (adIds.size() == 0){
            return new ArrayList<>();
        }
        List<Long> idList = thirdCallBackConfigMapper.queryExistList(adIds);
        return adInfoEntityList.stream().filter(r -> !idList.contains((long)r.getId())).collect(Collectors.toList());
    }


    public void closeUrl(Integer product,Integer os){
        CompletableFuture.runAsync(()->{
            List<AdInfoEntity> adInfoEntityList = adInfoMapper.selectAllProductAd(product,1);

            List<AdInfoEntity> filterAdList = adInfoEntityList.stream()
                    .filter(this::adTypeFilter)
                    .filter(adInfoEntity -> this.osFilter(adInfoEntity,os))
                    .collect(Collectors.toList());

            buildAndDone(filterAdList,product,os,this::closeAdCallBackUrl);
        },executor);
    }

    public void closeUrlBD(Integer product,Integer os){
        CompletableFuture.runAsync(()->{
            List<AdInfoEntity> adInfoEntityList = adInfoMapper.selectAllProductAd(product,1);

            List<AdInfoEntity> filterAdList = adInfoEntityList.stream()
                    .filter(this::adTypeFilter)
                    .filter(adInfoEntity -> this.osFilter(adInfoEntity,os))
                    .collect(Collectors.toList());

            buildAndDone(filterAdList,product,os,this::closeAdCallBackUrlBD);
        },executor);
    }

    private void openAdCallBackUrlBD(CreateUrlBean createUrlBean){
        if (Platform.BD.equals(createUrlBean.getPlatform())){
            openAdCallBackUrl(createUrlBean);
        }
    }
    private void openAdCallBackUrl(CreateUrlBean createUrlBean){
        try{
            if (createUrlBean.getThirdCompanyEntity() == null){
                return;
            }

            String secret = "";
            try {
                if (Platform.CSJ.equals(createUrlBean.getPlatform())){
                    secret = openCsjCallBackUrl(createUrlBean.getPosId(),createUrlBean.getPrice(),createUrlBean.getThirdCompanyEntity());
//                    return;
                }else if (Platform.GDT.equals(createUrlBean.getPlatform())){
                    secret = openGdtCallBackUrl(createUrlBean.getPosId(),createUrlBean.getPrice(),createUrlBean.getThirdCompanyEntity());
//                    return;
                }else if (Platform.KS.equals(createUrlBean.getPlatform())){
                    secret = openKsCallBackUrl(createUrlBean.getPosId(),createUrlBean.getPrice(),createUrlBean.getThirdCompanyEntity());
//                    return;
                }else if (Platform.BD.equals(createUrlBean.getPlatform())){
                    secret = openBDCallBackUrl(createUrlBean.getPosId(),createUrlBean.getPrice(),createUrlBean.getThirdCompanyEntity());
                }else{
                    return;
                }
            }catch (Exception e){
                log.error("Open Er:",e);
                return;
            }


            // 有则更新 无则写入
            saveOrUpdate(createUrlBean,secret);
            thirdLogService.saveLog("SYSTEM","SYSTEM", OpType.UPDATE_ADPOS,0,createUrlBean.getPosId().toString(),"提交回调链接");
        }catch (Exception e){
            log.error("ThirdCallBackService openAdCallBackUrl createUrlBean : {}", createUrlBean, e);
        }
        log.info("openAdCallBackUrl createUrlBean : {}", createUrlBean);
    }


    private void saveOrUpdate(CreateUrlBean createUrlBean,String secret){
        ThirdCallBackConfig callBackConfig = thirdCallBackConfigMapper.queryByAdId(createUrlBean.getAdId());
        Date now = new Date();
        if (callBackConfig == null){
            callBackConfig = new ThirdCallBackConfig();
            callBackConfig.setAdId(createUrlBean.getAdId());
            callBackConfig.setAppId(createUrlBean.getAppId());
            callBackConfig.setProduct(createUrlBean.getProduct());
            callBackConfig.setPosId(createUrlBean.getPosId());
            callBackConfig.setSecret(secret);
            callBackConfig.setIsSet(1);
            callBackConfig.setCreateTime(now);
            callBackConfig.setUpdateTime(now);
            thirdCallBackConfigMapper.insert(callBackConfig);
        }else {
            ThirdCallBackConfig record = new ThirdCallBackConfig();
            record.setId(callBackConfig.getId());
            record.setSecret(secret);
            record.setIsSet(1);
            record.setUpdateTime(now);
            thirdCallBackConfigMapper.update(record);
        }
    }

    private void closeAdCallBackUrlBD(CreateUrlBean createUrlBean){
        if (Platform.BD.equals(createUrlBean.getPlatform())) {
            closeAdCallBackUrl(createUrlBean);
        }
    }

    private void closeAdCallBackUrl(CreateUrlBean createUrlBean){
        try {
            if (Platform.CSJ.equals(createUrlBean.getPlatform())){
                closeCsjCallBackUrl(createUrlBean.getPosId(),createUrlBean.getThirdCompanyEntity());
//                return;
            }else if (Platform.GDT.equals(createUrlBean.getPlatform())){
                closeGdtCallBackUrl(createUrlBean.getPosId(),createUrlBean.getThirdCompanyEntity());
//                return;
            }else if (Platform.BD.equals(createUrlBean.getPlatform())){
                closeBDCallBackUrl(createUrlBean.getPosId(),createUrlBean.getThirdCompanyEntity());
            }else{
                return;
            }
        }catch (Exception e){
            log.error("closeAdCallBackUrl Do Err createUrlBean : {}  :", createUrlBean ,e);
            return;
        }


        ThirdCallBackConfig callBackConfig = thirdCallBackConfigMapper.queryByAdId(createUrlBean.getAdId());
        Date now = new Date();
        if (callBackConfig == null){
            callBackConfig = new ThirdCallBackConfig();
            callBackConfig.setAdId(createUrlBean.getAdId());
            callBackConfig.setAppId(createUrlBean.getAppId());
            callBackConfig.setPosId(createUrlBean.getPosId());
            callBackConfig.setProduct(createUrlBean.getProduct());
            callBackConfig.setSecret("");
            callBackConfig.setIsSet(0);
            callBackConfig.setCreateTime(now);
            callBackConfig.setUpdateTime(now);
            thirdCallBackConfigMapper.insert(callBackConfig);
        }else {
            ThirdCallBackConfig record = new ThirdCallBackConfig();
            record.setId(callBackConfig.getId());
            record.setSecret("");
            record.setIsSet(0);
            record.setUpdateTime(now);
            thirdCallBackConfigMapper.update(record);
        }
        thirdLogService.saveLog("SYSTEM","SYSTEM", OpType.UPDATE_ADPOS,0,createUrlBean.getPosId().toString(),"关闭回调链接");
        log.info("closeAdCallBackUrl createUrlBean : {}", createUrlBean);
    }

    /**
     * 打开穿山甲服务器广告位URL回调
     * @param posId 三方广告位ID
     * @param price ECPM
     * @param companyEntity 公司主题
     * @return 密钥
     */
    private String openCsjCallBackUrl(Long posId,Integer price,ThirdCompanyEntity companyEntity){
        AdPosUpdateRequest request = invokeCsjRequest(posId,companyEntity);
        request.setReward_is_callback(1);
        request.setReward_callback_url(CallBackUtils.getCallBackUrl(Platform.CSJ,price));
        request.setUpdate_security_key(1);
        Response<AdPosUpdateResponse> response = csjBaseService.updateAdPos(request);

        if (!response.isSuccess()){
            throw new RuntimeException(response.getMessage());
        }
        return response.getData().getReward_security_key();
    }


    private AdPosUpdateRequest invokeCsjRequest(Long posId, ThirdCompanyEntity companyEntity){
        AdPosUpdateRequest request = new AdPosUpdateRequest();

        request.setUser_id(Integer.valueOf(companyEntity.getUserId()));
        request.setRole_id(companyEntity.getRoleId());
        request.setMask_rule_id(companyEntity.getMaskRuleId());
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(companyEntity.getSecurityKey(),timestamp,nonce));
        request.setVersion("1.0");
        request.setAd_slot_id(Math.toIntExact(posId));
        return request;
    }

    /**
     * 关闭穿山甲广告服务器回调URL
     * @param posId 广告位PosID
     * @param companyEntity 公司主体
     */
    private void closeCsjCallBackUrl(Long posId, ThirdCompanyEntity companyEntity){
        AdPosUpdateRequest request = invokeCsjRequest(posId,companyEntity);
        request.setReward_is_callback(0);
        Response<AdPosUpdateResponse> response = csjBaseService.updateAdPos(request);

        if (!response.isSuccess()){
            throw new RuntimeException(response.getMessage());
        }
    }

    private String openGdtCallBackUrl(Long posId,Integer price, ThirdCompanyEntity thirdCompanyEntity){
        GdtAdPosUpdateRequest request = new GdtAdPosUpdateRequest();
        request.setPlacement_id(posId);
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));

        String secret = CallBackUtils.createSecret();
        request.setNeed_server_verify("NeedServerVerify");
        request.setTransfer_url(CallBackUtils.getCallBackUrl(Platform.GDT,price));
        request.setSecret(secret);
        GdtResponse<Void> response = gdtBaseService.updateAdPos(request,thirdCompanyEntity.getSecurityKey());
        if (!response.isSuccess()){
            throw new RuntimeException(Optional.ofNullable(response.getMsg()).orElse(response.getMessage()));
        }
        return secret;
    }

    private String openKsCallBackUrl(Long posId,Integer price, ThirdCompanyEntity thirdCompanyEntity){
        // 先查询明细
        KsPositionQueryRequest request = new KsPositionQueryRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setAk(String.valueOf(Long.valueOf(thirdCompanyEntity.getUserId())));
        request.setSk(thirdCompanyEntity.getSecurityKey());
        request.setPositionId(posId);
        KsResponse<List<KsPositionResponse>> responseList = ksBaseService.queryPositionInfo(request);
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        KsPositionResponse ks = responseList.getData().get(0);
        KsPositionRequest ksPositionRequest = new KsPositionRequest();
        ksPositionRequest.setAk(String.valueOf(Long.valueOf(thirdCompanyEntity.getUserId())));
        ksPositionRequest.setSk(thirdCompanyEntity.getSecurityKey());
        ksPositionRequest.setAppId(ks.getApp_id());
        ksPositionRequest.setPositionId(posId);
        ksPositionRequest.setName(ks.getName());
        ksPositionRequest.setCallbackUrl(CallBackUtils.getCallBackUrl(Platform.KS,price));
        ksPositionRequest.setCallbackStatus(1);
        ksPositionRequest.setMaterialTypeList(new ArrayList<Integer>(){{
            add(1);
        }});
        ksPositionRequest.setRenderType(ks.getRender_type());
        ksPositionRequest.setAdStyle(ks.getAd_style());
        ksPositionRequest.setRewardedType(11);
        ksPositionRequest.setRewardedNum(ks.getRewarded_num());
        KsResponse<Boolean> response = ksBaseService.updatePosition(ksPositionRequest);
        if (!response.isSuccess()){
            throw new RuntimeException(response.getError_msg());
        }

        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        KsResponse<List<KsPositionResponse>> requestResult = ksBaseService.queryPositionInfo(request);
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        KsPositionResponse rs = requestResult.getData().get(0);
        return rs.getCallback_sk();
    }

    private String openBDCallBackUrl(Long posId,Integer price, ThirdCompanyEntity thirdCompanyEntity){
        CratePosResponse bean = new CratePosResponse();
        bean.setTu_id(String.valueOf(posId));

        CratePosResponse.AdInfo adInfo = new CratePosResponse.AdInfo();
        adInfo.setReward_video_return_url(CallBackUtils.getCallBackUrl(Platform.BD, price));
        adInfo.setReward_video_return_control(1);
        bean.setAd_info(adInfo);

        log.info("ThirdCallBackService openBDCallBackUrl bean : {} thirdCompanyEntity getUserId : {} thirdCompanyEntity getSecurityKey : {}", bean, thirdCompanyEntity.getUserId(), thirdCompanyEntity.getSecurityKey());
        BdResponse<CratePosResponse> response =
                bdBaseService.updatePos(bean,
                        thirdCompanyEntity.getUserId(),
                        thirdCompanyEntity.getSecurityKey());
        if (!response.isSuccess()){
            throw new RuntimeException(response.getMessage());
        }

        return response.getData().getAd_info().getReward_video_return_token();
    }

    private void closeBDCallBackUrl(Long posId, ThirdCompanyEntity thirdCompanyEntity){
        CratePosResponse bean = new CratePosResponse();
        bean.setTu_id(String.valueOf(posId));

        CratePosResponse.AdInfo adInfo = new CratePosResponse.AdInfo();
        adInfo.setReward_video_return_control(0);
        bean.setAd_info(adInfo);

        log.info("ThirdCallBackService closeBDCallBackUrl bean : {} thirdCompanyEntity getUserId : {} thirdCompanyEntity getSecurityKey : {}", bean, thirdCompanyEntity.getUserId(), thirdCompanyEntity.getSecurityKey());
        BdResponse<CratePosResponse> response =
                bdBaseService.updatePos(bean,
                        thirdCompanyEntity.getUserId(),
                        thirdCompanyEntity.getSecurityKey());
        if (!response.isSuccess()){
            throw new RuntimeException(response.getMessage());
        }
    }

    private void closeGdtCallBackUrl(Long posId, ThirdCompanyEntity thirdCompanyEntity){
        GdtAdPosUpdateRequest request = new GdtAdPosUpdateRequest();
        request.setPlacement_id(posId);
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
        request.setNeed_server_verify("NotNeedServerVerify");
        GdtResponse<Void> response = gdtBaseService.updateAdPos(request,thirdCompanyEntity.getSecurityKey());
        if (!response.isSuccess()){
            throw new RuntimeException(Optional.ofNullable(response.getMsg()).orElse(response.getMessage()));
        }
    }
}
