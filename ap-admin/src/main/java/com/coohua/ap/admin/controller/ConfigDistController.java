package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.AdPosVO;
import com.coohua.ap.admin.controller.vo.AdTypeOutVo;
import com.coohua.ap.base.constants.AdPosType;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeConvertVo;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.vo.WebMessage;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/6/7
 */

@RestController
@RequestMapping("/config")
public class ConfigDistController {


    /**
     * 查询广告类型
     */
    @GetMapping("/getAdTypeDist")
    public WebMessage<List<AdTypeConvertVo>> queryAllAdType(){
        return WebMessage.build(new ArrayList<>(AdTypeSub.adTypeMapFl.values()));
    }

    @GetMapping("/getAdPosType")
    public WebMessage<List<AdPosVO>> queryAllAdPosType(){
        return WebMessage.build(Stream.of(AdPosType.values())
                .map(adPosType -> new AdPosVO(adPosType.getCode(),adPosType.getDesc()))
                .collect(Collectors.toList())
        );
    }
}
