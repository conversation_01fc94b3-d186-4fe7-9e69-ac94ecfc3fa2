package com.coohua.ap.admin.service.third.service.ks;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.admin.mapper.ap.ThirdAppMapper;
import com.coohua.ap.admin.mapper.ap.ThirdCompanyMapper;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionQueryRequest;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionRequest;
import com.coohua.ap.admin.service.third.dto.ks.request.KsQueryAppRequest;
import com.coohua.ap.admin.service.third.dto.ks.request.KsSetPriceRequest;
import com.coohua.ap.admin.service.third.dto.ks.response.CreatePosBean;
import com.coohua.ap.admin.service.third.dto.ks.response.KsAppResponse;
import com.coohua.ap.admin.service.third.dto.ks.response.KsPositionResponse;
import com.coohua.ap.admin.service.third.dto.ks.response.KsResponse;
import com.coohua.ap.admin.utils.MD5Utils;
import com.coohua.ap.admin.utils.third.HttpClients;
import com.coohua.ap.base.constants.AdPosType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/8/13
 * @description 由于业务变更，不再提供应用修改、应用创建的API 这里支持广告位查询、创建、设置价格
 * 快手没有测试环境
 * @link https://ad-video-coohua.oss-cn-beijing.aliyuncs.com/pdf/ks_query_update.pdf
 * @link https://ad-video-coohua.oss-cn-beijing.aliyuncs.com/pdf/ks_ssp_v18.pdf
 * @link https://docs.qingque.cn/d/home/<USER>
 */
@Slf4j
@Service
public class KsBaseService {

    private final static String BASE_URL = "https://u.kuaishou.com";

    private static final Map<String,Object> publicHeader = new HashMap<String,Object>(){{
        put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
    }};

    private static String invokeRequestUrl(String uri){
        return BASE_URL + uri;
    }

    private static Sign invokeSign(String ak,String sk){
        return new Sign(ak,sk,System.currentTimeMillis()/1000);
    }

    @Data
    private static class Sign{
        private String ak;
        private String sk;
        private Long timestamp;

        public Sign(String ak, String sk, Long timestamp) {
            this.ak = ak;
            this.sk = sk;
            this.timestamp = timestamp;
        }

        public Map<String,Object> getSignParam(String uri){
            Map<String,Object> resultMap = new HashMap<>();
            resultMap.put("ak",this.ak);
            resultMap.put("timestamp",this.timestamp);
            String result = uri + "?ak=%s&sk=%s&timestamp=%s";
            String sign = MD5Utils.getMd5Sum(String.format(result,this.ak,this.sk,this.timestamp));
            log.info("KS-SIGN:{}",sign);
            resultMap.put("sign",sign);
            return resultMap;
        }
    }

    /**
     * 查询快手应用列表
     * @param request APP请求
     * @return 快手应用列表
     */
    public KsResponse<List<KsAppResponse>> queryAppList(KsQueryAppRequest request){
        String uri = "/api/app/get";
        Sign sign = invokeSign(request.getAk(),request.getSk());
        String text = HttpClients.jsonPostParam(invokeRequestUrl(uri),publicHeader,sign.getSignParam(uri),JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<KsResponse<List<KsAppResponse>>>(){});
    }

    /**
     * 查询广告位置明细
     * @param request 广告位请求
     * @return 广告位详情
     */
    public KsResponse<List<KsPositionResponse>> queryPositionInfo(KsPositionQueryRequest request){
        String uri = "/api/position/get";
        Sign sign = invokeSign(request.getAk(),request.getSk());
        String text = HttpClients.jsonPostParam(invokeRequestUrl(uri),publicHeader,sign.getSignParam(uri),JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<KsResponse<List<KsPositionResponse>>>(){});
    }

    /**
     * 创建广告位
     * @param request 物料
     * @return 广告位id
     */
    public KsResponse<String> createPosition(KsPositionRequest request){
        String uri = "/api/position/add";
        Sign sign = invokeSign(request.getAk(),request.getSk());
        String text = HttpClients.jsonPostParam(invokeRequestUrl(uri),publicHeader,sign.getSignParam(uri),JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<KsResponse<String>>(){});
    }

    /**
     * 更新广告位
     * @param request 物料
     * @return 是否成功 TRUE-成功
     */
    public KsResponse<Boolean> updatePosition(KsPositionRequest request){
        String uri = "/api/position/modify";
        Sign sign = invokeSign(request.getAk(),request.getSk());
        String text = HttpClients.jsonPostParam(invokeRequestUrl(uri),publicHeader,sign.getSignParam(uri),JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<KsResponse<Boolean>>(){});
    }

    /**
     * 设置CPM价格
     * @param request CPM价格
     * @return 是否设置成功 TRUE-成功
     */
    public KsResponse<Boolean> setPositionPrice(KsSetPriceRequest request){
        String uri = "/api/position/modCpmFloor";
        Sign sign = invokeSign(request.getAk(),request.getSk());
        String text = HttpClients.jsonPostParam(invokeRequestUrl(uri),publicHeader,sign.getSignParam(uri),JSON.toJSONString(request));
        return JSON.parseObject(text,new TypeReference<KsResponse<Boolean>>(){});
    }

    private static File createCsv(List exportData, LinkedHashMap<String,String> map, String outPutPath, String fileName) {
        File csvFile = null;
        BufferedWriter csvFileOutputStream = null;
        try {
            File file = new File(outPutPath);
            if (!file.exists()) {
                file.mkdir();
            }
            //定义文件名格式并创建
            csvFile = File.createTempFile(fileName, ".csv", new File(outPutPath));
            // utf8使正确读取分隔符","
            csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), "UTF-8"), 1024);
            // 写入文件头部
            for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
                java.util.Map.Entry propertyEntry = (java.util.Map.Entry) propertyIterator.next();
                csvFileOutputStream.write("\"" + (String) propertyEntry.getValue() != null ? (String) propertyEntry.getValue() : "" + "\"");
                if (propertyIterator.hasNext()) {
                    //使用,分隔
                    csvFileOutputStream.write(",");
                }
            }
            csvFileOutputStream.newLine();
            // 写入文件内容
            for (Iterator iterator = exportData.iterator(); iterator.hasNext();) {
                Object row = iterator.next();
                for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext();) {
                    java.util.Map.Entry propertyEntry = (java.util.Map.Entry) propertyIterator.next();
                    csvFileOutputStream.write(BeanUtils.getProperty(row, (String) propertyEntry.getKey()));
                    if (propertyIterator.hasNext()) {
                        //使用|分隔
                        csvFileOutputStream.write(",");
                    }
                }
                if (iterator.hasNext()) {
                    csvFileOutputStream.newLine();
                }
            }
            csvFileOutputStream.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (csvFileOutputStream != null) {
                    csvFileOutputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return csvFile;
    }

    public void tes(){
        List<String> line = new ArrayList<>();
        line.add("激励,7826017870,782600085");
        Map<String,List<String>> map = line.stream().collect(Collectors.groupingBy(r-> {
                    String[]dr = r.split(",");
                    return dr[2] +"_"+dr[0];
        }));

        map.forEach((k,v) ->{
            String appId = k.split("_")[0];
            String vr = k.split("_")[1];
            AdPosType adPosType = AdPosType.Rewarded_video;
            if (vr.equals("插屏")){
                adPosType = AdPosType.Table_plaque;
            }
            ThirdAppEntity thirdAppEntity = thirdAppMapper.queryByAppId(appId);
            ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
            List<String> posIdList = v.stream().map(r ->{
                String[]dr = r.split(",");
                return dr[1];
            }).collect(Collectors.toList());
            createDmPos(Long.valueOf(thirdCompanyEntity.getUserId()).toString(),thirdCompanyEntity.getSecurityKey(),posIdList,thirdAppEntity.getAppId().toString(),thirdAppEntity.getAppName(),adPosType);
        });

    }
    @Resource
    private ThirdCompanyMapper thirdCompanyMapper;
    @Resource
    private ThirdAppMapper thirdAppMapper;

    public KsResponse<CreatePosBean> createDmPos(String ak, String sk, List<String> posIdList, String appId, String appName, AdPosType adPosType){
        String uri = "/api/physicalPosition/batchCreatePhysicalPosition";
        Sign sign = invokeSign(ak,sk);
        Map<String, File> paramFile = new HashMap<>();
        LinkedHashMap<String,String> headMap = new LinkedHashMap<String,String>(){{
            put("appid","appid");
            put("uid","uid");
            put("appname","appname");
            put("posid","posid");
            put("firstscenename","firstscenename");
            put("secondscenename","secondscenename");
            put("thirdscenename","thirdscenename");
        }};

        List<Map<String,Object>> rlList = posIdList.stream().map(posId ->{
            Map<String,Object> valueMap = new LinkedHashMap<>();
            valueMap.put("appid",appId);
            valueMap.put("uid",ak);
            valueMap.put("appname",appName);
            valueMap.put("posid",posId);
            if (AdPosType.Rewarded_video.equals(adPosType)) {
                valueMap.put("firstscenename", "激励视频");
                valueMap.put("secondscenename", "获取奖励/权益/机会");
            }else if (AdPosType.Table_plaque.equals(adPosType)){
                valueMap.put("firstscenename", "插屏广告");
                valueMap.put("secondscenename", "场景切换/退出App");
            }
            valueMap.put("thirdscenename","全局");
            return valueMap;
        }).collect(Collectors.toList());

        File file = createCsv(rlList,headMap,"/data/coohua","index");
        paramFile.put("file",file);
        String result = HttpClients.upload(invokeRequestUrl(uri),sign.getSignParam(uri),paramFile,true);
        log.info(">>> {}",result);
        return null;
    }


}
