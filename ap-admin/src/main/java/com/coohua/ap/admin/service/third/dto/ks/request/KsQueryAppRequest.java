package com.coohua.ap.admin.service.third.dto.ks.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021/8/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KsQueryAppRequest extends KsBasicSignRequest {

    private Integer page = 1;
    private Integer pageSize = 10;

    private Integer auditStatus;
    private Integer mediumType;
    private String name;
    private String appId;
}
