<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdAdPosMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdAdPosEntity" >
        <result column="id" property="id" />
        <result column="switch_flag" property="switchFlag" />
        <result column="platform_code" property="platformCode" />
        <result column="company_id" property="companyId" />
        <result column="main_body" property="mainBody" />
        <result column="application_id" property="applicationId" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="pos_id" property="posId" />
        <result column="pos_name" property="posName" />
        <result column="pos_status" property="posStatus" />
        <result column="ad_type" property="adType" />
        <result column="income_status" property="incomeStatus" />
        <result column="ad_price" property="adPrice" />
        <result column="call_back_key" property="callBackKey" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                switch_flag,
                platform_code,
                company_id,
                main_body,
                application_id,
                app_id,
                app_name,
                pos_id,
                pos_name,
                pos_status,
                ad_type,
                income_status,
                ad_price,
                call_back_key,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdAdPosEntity">
        INSERT INTO third_ad_pos
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != switchFlag'>
                switch_flag,
            </if>
            <if test ='null != platformCode'>
                platform_code,
            </if>
            <if test ='null != companyId'>
                company_id,
            </if>
            <if test ='null != mainBody'>
                main_body,
            </if>
            <if test ='null != applicationId'>
                application_id,
            </if>
            <if test ='null != appId'>
                app_id,
            </if>
            <if test ='null != appName'>
                app_name,
            </if>
            <if test ='null != posId'>
                pos_id,
            </if>
            <if test ='null != posName'>
                pos_name,
            </if>
            <if test ='null != posStatus'>
                pos_status,
            </if>
            <if test ='null != adType'>
                ad_type,
            </if>
            <if test ='null != incomeStatus'>
                income_status,
            </if>
            <if test ='null != adPrice'>
                ad_price,
            </if>
            <if test ='null != callBackKey'>
                call_back_key,
            </if>
            <if test ='null != createTime'>
                create_time,
            </if>
            <if test ='null != updateTime'>
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != switchFlag'>
                #{switchFlag},
            </if>
            <if test ='null != platformCode'>
                #{platformCode},
            </if>
            <if test ='null != companyId'>
                #{companyId},
            </if>
            <if test ='null != mainBody'>
                #{mainBody},
            </if>
            <if test ='null != applicationId'>
                #{applicationId},
            </if>
            <if test ='null != appId'>
                #{appId},
            </if>
            <if test ='null != appName'>
                #{appName},
            </if>
            <if test ='null != posId'>
                #{posId},
            </if>
            <if test ='null != posName'>
                #{posName},
            </if>
            <if test ='null != posStatus'>
                #{posStatus},
            </if>
            <if test ='null != adType'>
                #{adType},
            </if>
            <if test ='null != incomeStatus'>
                #{incomeStatus},
            </if>
            <if test ='null != adPrice'>
                #{adPrice},
            </if>
            <if test ='null != callBackKey'>
                #{callBackKey},
            </if>
            <if test ='null != createTime'>
                #{createTime},
            </if>
            <if test ='null != updateTime'>
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_ad_pos
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdAdPosEntity">
        UPDATE third_ad_pos
        <set>
            <if test ='null != switchFlag'>switch_flag = #{switchFlag},</if>
            <if test ='null != platformCode'>platform_code = #{platformCode},</if>
            <if test ='null != companyId'>company_id = #{companyId},</if>
            <if test ='null != mainBody'>main_body = #{mainBody},</if>
            <if test ='null != applicationId'>application_id = #{applicationId},</if>
            <if test ='null != appId'>app_id = #{appId},</if>
            <if test ='null != appName'>app_name = #{appName},</if>
            <if test ='null != posId'>pos_id = #{posId},</if>
            <if test ='null != posName'>pos_name = #{posName},</if>
            <if test ='null != posStatus'>pos_status = #{posStatus},</if>
            <if test ='null != adType'>ad_type = #{adType},</if>
            <if test ='null != incomeStatus'>income_status = #{incomeStatus},</if>
            <if test ='null != adPrice'>ad_price = #{adPrice},</if>
            <if test ='null != callBackKey'>call_back_key = #{callBackKey},</if>
            <if test ='null != createTime'>create_time = #{createTime},</if>
            <if test ='null != updateTime'>update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_ad_pos
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_ad_pos
        WHERE 1 = 1
        <if test=' null != appName and appName != ""'> AND app_name like concat('%',#{appName},'%') </if>
        <if test=' null != appId and appId != ""'> AND app_id like concat('%',#{appId},'%') </if>
        <if test=' null != posName and posName != ""'> AND pos_name like concat('%',#{posName},'%') </if>
        <if test=' null != posId'> AND pos_id = #{posId}  </if>
        <if test=' null != incomeStatus'> AND income_status = #{incomeStatus} </if>
        <if test=' null != adType and adType != ""'> AND ad_type like concat('{"adType":',#{adType},'%') </if>
        <if test=" null != appIdList and appIdList.size() >0">
            and app_id in
            <foreach collection="appIdList" item="appId" index="index" open="(" separator=", " close=")">
                #{appId}
            </foreach>
        </if>
        <if test=' null != stTime'> AND create_time &gt;=  #{stTime} </if>
        <if test=' null != edTime'> AND create_time &lt;= #{edTime} </if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_ad_pos
        WHERE 1 = 1
        <if test=' null != appName and appName != ""'> AND app_name like concat('%',#{appName},'%') </if>
        <if test=' null != appId and appId != ""'> AND app_id like concat('%',#{appId},'%') </if>
        <if test=' null != posName and posName != ""'> AND pos_name like concat('%',#{posName},'%') </if>
        <if test=' null != posId'> AND pos_id = #{posId} </if>
        <if test=' null != incomeStatus'> AND income_status = #{incomeStatus} </if>
        <if test=' null != adType and adType != ""'> AND ad_type like concat('{"adType":',#{adType},'%') </if>
        <if test=" null != appIdList and appIdList.size() >0">
            and app_id in
            <foreach collection="appIdList" item="appId" index="index" open="(" separator=", " close=")">
                #{appId}
            </foreach>
        </if>
        <if test=' null != stTime'> AND create_time &gt;=  #{stTime} </if>
        <if test=' null != edTime'> AND create_time &lt;= #{edTime} </if>
    </select>

</mapper>