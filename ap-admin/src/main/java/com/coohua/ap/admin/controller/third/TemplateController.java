package com.coohua.ap.admin.controller.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.model.ThirdTemplateEntity;
import com.coohua.ap.admin.service.third.ThirdCompanyService;
import com.coohua.ap.admin.service.third.ThirdTemplateService;
import com.coohua.ap.admin.utils.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

import static com.coohua.ap.admin.controller.vo.BaseResponse.CODE_SYS_ERROR;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@RestController
@RequestMapping("third/template")
public class TemplateController {


    @Autowired
    private ThirdTemplateService thirdTemplateService;

    @RequestMapping("list")
    @ResponseBody
    public BaseResponse list(@RequestParam(value = "name",required = false) String name, Page<ThirdTemplateView> page, HttpServletRequest request) {
        int from = (page.getPageNo() - 1) * page.getPageSize();
        thirdTemplateService.list(from,page.getPageSize(),name,page);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping("addOne")
    public BaseResponse addOne(@RequestBody Map<String,String> reqMap, HttpServletRequest request) {
        thirdTemplateService.insert(reqMap.get("modelName"),reqMap.get("templateList"),
                SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("updateOne")
    public BaseResponse updateOne(@RequestBody Map<String,String> reqMap,HttpServletRequest request) {
        thirdTemplateService.update(Integer.valueOf(reqMap.get("id")),reqMap.get("modelName"),reqMap.get("templateList"),
                SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("copy")
    public BaseResponse copy(Integer id,HttpServletRequest request){
        thirdTemplateService.copyOne(id, SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("delete")
    public BaseResponse delete(Integer id,HttpServletRequest request){
        thirdTemplateService.delete(id, SessionUtils.getAccountName(request),SessionUtils.getAccount(request));
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }
}
