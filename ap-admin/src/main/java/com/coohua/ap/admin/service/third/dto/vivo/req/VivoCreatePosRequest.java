package com.coohua.ap.admin.service.third.dto.vivo.req;

import com.coohua.ap.admin.service.third.dto.vivo.BidLabel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/9
 */
@Data
public class VivoCreatePosRequest {

    private String accountName;
    private String mediaId;
    private String positionName;
    private Integer positionType;
    private Integer accessType;
    private Integer renderType;
    private Integer bidMode;
    private BidLabel[] bidLabels;
    private Integer secondScene;
    private Integer[] shieldSecondIndustryId;
    private String[] shieldPackages;
    private String[] adNorms;
    private Integer orientation;
    private Integer[] renderStyles;

}
