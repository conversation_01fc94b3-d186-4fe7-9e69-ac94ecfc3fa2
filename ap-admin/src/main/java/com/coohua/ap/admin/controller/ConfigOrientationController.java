package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.ConfigOrientationVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.service.ConfigOrientationService;
import com.coohua.ap.admin.utils.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <pre>
 *      配置定向条件配置
 * <hr/>
 * Package Name : com.coohua.nap.web
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/14 下午5:03
 * </pre>
 */
@Controller
@RequestMapping("/config/orientation")
public class ConfigOrientationController extends BaseController {

    @Autowired
    private ConfigOrientationService configOrientationService;

    @RequestMapping("/list")
    @ResponseBody
    public Page<ConfigOrientationVO> list(@RequestParam("adPos") int adPos,
                                          @RequestParam(value = "name", required = false) String name,
                                          @RequestParam("state") int state,
                                          @RequestParam(value = "id", required = false) String id,
                                          Page<ConfigOrientationVO> page, HttpServletRequest request) {
        int product = SessionUtils.getUserProduct(request);

        configOrientationService.listConfigOrientation(page, product, name, adPos, state, id);

        return page;
    }

    @RequestMapping("/configIdList")
    @ResponseBody
    public List<ConfigBaseVO> configIdList(HttpServletRequest request) {
        int product = SessionUtils.getUserProduct(request);
        return configOrientationService.listConfigBaseIdAndName(product);
    }

    @RequestMapping("/save")
    @ResponseBody
    public ConfigOrientationVO save(@RequestBody ConfigOrientationVO configOrientationVO,
                                    HttpServletRequest request) {
        configOrientationVO.setProduct(SessionUtils.getUserProduct(request));
        return configOrientationService.saveConfig(configOrientationVO);
    }

    @RequestMapping("/update")
    @ResponseBody
    public ConfigOrientationVO update(@RequestBody ConfigOrientationVO configOrientationVO,
                                      HttpServletRequest request) {
        configOrientationVO.setProduct(SessionUtils.getUserProduct(request));
        return configOrientationService.updateConfig(configOrientationVO);
    }

    @RequestMapping("/updateState")
    @ResponseBody
    public BaseResponse updateState(@RequestBody ConfigOrientationVO configOrientationVO,
                                    HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse(0);
        try {
            ConfigOrientationVO configOrientationVO1 = configOrientationService.updateState(configOrientationVO);

            if (configOrientationVO1 !=null) {
                baseResponse.setData("OK");
            }
        } catch (Exception e) {
            baseResponse.setRet(1);
            baseResponse.setData("ERR");
            e.printStackTrace();
        }
        return baseResponse;
    }

}
