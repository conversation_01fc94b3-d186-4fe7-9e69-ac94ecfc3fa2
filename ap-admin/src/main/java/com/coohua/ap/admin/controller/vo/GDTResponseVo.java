package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.AdDataReportEntity;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: GDTResponseVo
 * @Description: 广点通数据返回模板
 * @Author: fan jin yang
 * @Date: 2020/5/8
 * @Version: V1.0
 **/
@Data
public class GDTResponseVo extends DefaultReportResponseVo{

    private Long code;

    private String message;

    private Data data = new Data();

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    @Override
    public List<AdDataReportEntity> convertReportEntity() throws ParseException {
        List<AdDataReportEntity> list = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if(data!=null&&!CollectionUtils.isEmpty(data.getList())){
            for(ReportData reportData:data.getList()){
                if(reportData.is_summary){
                    continue;
                }
                AdDataReportEntity adDataReportEntity = new AdDataReportEntity();
                adDataReportEntity.setMediaName(reportData.getMedium_name());
                adDataReportEntity.setAppId(reportData.getApp_id());
                // 应用名称的映射
                if("步步多康".equalsIgnoreCase(reportData.getMedium_name())){
                    adDataReportEntity.setAppName("好运天气_android");
                }else if ("如意天气".equalsIgnoreCase(reportData.getMedium_name())){
                    adDataReportEntity.setAppName("好运天气_ios");
                }else {
                    if(reportData.getPlacement_name().contains("android")||reportData.getPlacement_name().contains("Android")){
                        adDataReportEntity.setAppName(appNameReplace(reportData.getMedium_name())+"_android");
                    }else {
                        adDataReportEntity.setAppName(appNameReplace(reportData.getMedium_name())+"_ios");
                    }
                }
                adDataReportEntity.setPosId(reportData.getPlacement_id());
                adDataReportEntity.setPosName(reportData.getPlacement_name());
                adDataReportEntity.setPv(Long.valueOf(handelNumString(reportData.getPv(),",")));
                adDataReportEntity.setClick(Long.valueOf(handelNumString(reportData.getClick(),",")));
                adDataReportEntity.setClickRate(Float.valueOf(handelNumString(reportData.getClick_rate(),"%")));
                adDataReportEntity.setEcpm(reportData.getEcpm());
                adDataReportEntity.setTypeName(reportData.getPlacement_type());
                adDataReportEntity.setRevenue(Float.valueOf(handelNumString(reportData.getRevenue(),",")));
                adDataReportEntity.setDateTime(simpleDateFormat.parse(reportData.getDate()));
                list.add(adDataReportEntity);
            }
        }
        return list;
    }

    private String appNameReplace(String appName){
        return appName.replace("步步多健","恐龙世界");
    }

    static class Data{

        private List<ReportData> list = new ArrayList<>();

        public List<ReportData> getList() {
            return list;
        }

        public void setList(List<ReportData> list) {
            this.list = list;
        }
    }

    static class ReportData{
        /**
         * 本条数据的时间
         */
        private String date;

        /**
         * 应用id
         */
        private Long app_id;
        /**
         * 广告位id
         */
        private Long placement_id;

        /**
         * 广告类型
         */
        private String placement_type;
        /**
         * 账号名称
         */
        private String member_id;
        /**
         * 应用名称
         */
        private String medium_name;
        /**
         * 广告位名称
         */
        private String placement_name;
        /**
         * 曝光量
         */
        private String pv;
        /**
         * 点击量
         */
        private String click;
        /**
         * 点击率
         */
        private String click_rate;
        /**
         * 千人收入
         */
        private Float ecpm;
        /**
         * 预估收入
         */
        private String revenue;
        /**
         * 填充率
         */
        private String fill_rate;
        /**
         * 请求数
         */
        private String request_count;
        /**
         * 是否总数据
         */
        private boolean is_summary;

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public Long getApp_id() {
            return app_id;
        }

        public void setApp_id(Long app_id) {
            this.app_id = app_id;
        }

        public Long getPlacement_id() {
            return placement_id;
        }

        public void setPlacement_id(Long placement_id) {
            this.placement_id = placement_id;
        }

        public String getPlacement_type() {
            return placement_type;
        }

        public void setPlacement_type(String placement_type) {
            this.placement_type = placement_type;
        }

        public String getMember_id() {
            return member_id;
        }

        public void setMember_id(String member_id) {
            this.member_id = member_id;
        }

        public String getMedium_name() {
            return medium_name;
        }

        public void setMedium_name(String medium_name) {
            this.medium_name = medium_name;
        }

        public String getPlacement_name() {
            return placement_name;
        }

        public void setPlacement_name(String placement_name) {
            this.placement_name = placement_name;
        }

        public String getPv() {
            return pv;
        }

        public void setPv(String pv) {
            this.pv = pv;
        }

        public String getClick() {
            return click;
        }

        public void setClick(String click) {
            this.click = click;
        }

        public String getClick_rate() {
            return click_rate;
        }

        public void setClick_rate(String click_rate) {
            this.click_rate = click_rate;
        }

        public Float getEcpm() {
            return ecpm;
        }

        public void setEcpm(Float ecpm) {
            this.ecpm = ecpm;
        }

        public String getRevenue() {
            return revenue;
        }

        public void setRevenue(String revenue) {
            this.revenue = revenue;
        }

        public String getFill_rate() {
            return fill_rate;
        }

        public void setFill_rate(String fill_rate) {
            this.fill_rate = fill_rate;
        }

        public String getRequest_count() {
            return request_count;
        }

        public void setRequest_count(String request_count) {
            this.request_count = request_count;
        }

        public boolean isIs_summary() {
            return is_summary;
        }

        public void setIs_summary(boolean is_summary) {
            this.is_summary = is_summary;
        }
    }

    private String handelNumString(String num,String sign){
        String[] split = num.split(sign);
        StringBuilder stringBuilder = new StringBuilder();
        for (String s :split){
            stringBuilder.append(s);
        }
        return stringBuilder.toString();
    }
}


