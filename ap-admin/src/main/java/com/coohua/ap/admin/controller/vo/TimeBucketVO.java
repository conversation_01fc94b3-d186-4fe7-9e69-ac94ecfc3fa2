package com.coohua.ap.admin.controller.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * Package Name : com.coohua.nap.web.vo
 * Project Name : nap
 * Created by <PERSON>_<PERSON>_ on 2017/5/15 下午5:31
 */
public class TimeBucketVO {

    private int week; // 星期标号，从0开始，0-6

    private String name; // 星期名称，例如：星期一

    private List<Bucket> time = new ArrayList<>(); // 这一天的48个时段，每个时段是否可投放广告

    public int getWeek() {
        return week;
    }

    public void setWeek(int week) {
        this.week = week;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Bucket> getTime() {
        return time;
    }

    public void setTime(List<Bucket> time) {
        this.time = time;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TimeBucketVO that = (TimeBucketVO) o;

        if (week != that.week) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        return time != null ? time.equals(that.time) : that.time == null;
    }

    @Override
    public String toString() {
        return "TimeBucket{" +
                "week=" + week +
                ", name='" + name + '\'' +
                ", time=" + time +
                '}';
    }
}
