<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ClickConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ClickConfig" >
        <result column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="exposure" property="exposure" />
        <result column="click_a" property="clickA" />
        <result column="rate_a" property="rateA" />
        <result column="click_b" property="clickB" />
        <result column="rate_b" property="rateB" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                app_id,
                exposure,
                click_a,
                rate_a,
                click_b,
                rate_b,
                del_flag,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ClickConfig">
        INSERT INTO click_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != appId and '' != appId">
                app_id,
            </if>
            <if test="null != exposure and '' != exposure">
                exposure,
            </if>
            <if test="null != clickA and '' != clickA">
                click_a,
            </if>
            <if test="null != rateA and '' != rateA">
                rate_a,
            </if>
            <if test="null != clickB and '' != clickB">
                click_b,
            </if>
            <if test="null != rateB and '' != rateB">
                rate_b,
            </if>
            <if test="null != delFlag and '' != delFlag">
                del_flag,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != appId and '' != appId">
                #{appId},
            </if>
            <if test="null != exposure and '' != exposure">
                #{exposure},
            </if>
            <if test="null != clickA and '' != clickA">
                #{clickA},
            </if>
            <if test="null != rateA and '' != rateA">
                #{rateA},
            </if>
            <if test="null != clickB and '' != clickB">
                #{clickB},
            </if>
            <if test="null != rateB and '' != rateB">
                #{rateB},
            </if>
            <if test="null != delFlag and '' != delFlag">
                #{delFlag},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime ">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM click_config
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ClickConfig">
        UPDATE click_config
        <set>
            <if test="null != appId and '' != appId">app_id = #{appId},</if>
            <if test="null != exposure and '' != exposure">exposure = #{exposure},</if>
            <if test="null != clickA and '' != clickA">click_a = #{clickA},</if>
            <if test="null != rateA and '' != rateA">rate_a = #{rateA},</if>
            <if test="null != clickB and '' != clickB">click_b = #{clickB},</if>
            <if test="null != rateB and '' != rateB">rate_b = #{rateB},</if>
            <if test="null != delFlag">del_flag = #{delFlag},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM click_config
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM click_config
        where del_flag = 1
        <if test=" 0 != appId">  and app_id = #{appId}</if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM click_config
        where del_flag = 1
        <if test=" 0 != appId">  and app_id = #{appId}</if>
    </select>

</mapper>