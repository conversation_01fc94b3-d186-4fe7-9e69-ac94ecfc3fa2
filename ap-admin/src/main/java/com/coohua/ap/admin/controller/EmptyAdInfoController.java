package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.EmptyAdInfoVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.domain.EmptyAdCountDomain;
import com.coohua.ap.admin.service.EmptyAdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName: EmptyAdInfoController
 * @Description: 无广告返回记录接口
 * @Author: fan jin yang
 * @Date: 2020/3/30
 * @Version: V1.0
 **/
@RestController
@RequestMapping("/emptyAd")
public class EmptyAdInfoController {


    @Autowired
    private EmptyAdService emptyAdService;

    @RequestMapping("/getEmptyCount")
    public BaseResponse getEmptyAdCount(int appId){
        List<EmptyAdCountDomain> emptyAdCountDomains = emptyAdService.queryEmptyCount(appId);
        return  BaseResponse.build(emptyAdCountDomains);
    }

    @RequestMapping("/getEmptyAdInfo")
    public BaseResponse getEmptyAdInfoList(Page<EmptyAdInfoVO> page){
        Page<EmptyAdInfoVO> emptyAdInfoVOPage = emptyAdService.queryEmptyAdInfo(page);
        return BaseResponse.build(emptyAdInfoVOPage);
    }
}
