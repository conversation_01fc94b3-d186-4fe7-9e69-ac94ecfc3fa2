package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdAdPosLoad;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description 广告位
 * <AUTHOR>
 * @date 2021-08-31
 */
@Mapper
@Repository
public interface ThirdAdPosLoadMapper {

    /**
     * 新增
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int insert(ThirdAdPosLoad thirdAdPosLoad);

    /**
     * 刪除
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int delete(int id);

    /**
     * 更新
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int update(ThirdAdPosLoad thirdAdPosLoad);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR>
     * @date 2021/08/31
     **/
    ThirdAdPosLoad load(int id);

    /**
     * 查询 分页查询
     * <AUTHOR>
     * @date 2021/08/31
     **/
    List<ThirdAdPosLoad> pageList(int offset, int pagesize);

    /**
     * 查询 分页查询 count
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int pageListCount(int offset,int pagesize);

}