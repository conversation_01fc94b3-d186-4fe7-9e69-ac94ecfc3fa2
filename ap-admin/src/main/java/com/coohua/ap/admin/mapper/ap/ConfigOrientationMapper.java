package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ConfigOrientationEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.dao
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/13 下午2:53
 * </pre>
 */
public interface ConfigOrientationMapper {

    /**
     * 根据用户产品分页查询配置信息
     * @param configOrientationEntity 用户产品
     * @param from 查询开始位置
     * @param offset 查询长度
     * @return 分页列表
     */
    List<ConfigOrientationEntity> queryAllByProduct(@Param("configOrientation") ConfigOrientationEntity configOrientationEntity,
                                                    @Param("from") int from,
                                                    @Param("offset") int offset);

    /**
     * 查询所有的配置信息
     * @return 配置信息列表
     */
    List<ConfigOrientationEntity> queryAll();
    /**
     * 匹配用户查询的总条数
     * @param configOrientationEntity 用户产品信息
     * @return 数据条数
     */
    Long countAllByProduct(@Param("configOrientation") ConfigOrientationEntity configOrientationEntity);

    /**
     * 新增一条配置
     * @param configOrientationEntity 用户配置
     * @return 影响条数
     */
    int insertConfigOrientation(ConfigOrientationEntity configOrientationEntity);

    /**
     * 更新一条配置
     * @param configOrientationEntity 用户配置
     * @return 影响条数
     */
    int updateConfigOrientation(ConfigOrientationEntity configOrientationEntity);

    /**
     * 更新状态
     * @param configOrientationEntity 待修改状态
     * @return 影响条数
     */
    int updateConfigOrientationState(ConfigOrientationEntity configOrientationEntity);
    /**
     * 查询一条配置
     * @param id 配置id
     * @return 具体数据
     */
    ConfigOrientationEntity queryOneById(int id);


    @Select("select * from tb_ap_config_orientation where product = #{product}")
    List<ConfigOrientationEntity> queryByProduct(@Param("product")Integer product);

    /**
     * 查询最高优先级
     *
     * @param product 产品
     * @param adPos   广告位类型
     * @param os      平台
     * @return 优先级最高的配置
     */
    @Select("select * from tb_ap_config_orientation where product = #{product} and ad_pos=#{adPos} and os=#{os}  and region!=1 and state=1 and anonymous=0 order by priority desc limit 1 ")
    ConfigOrientationEntity getMostPriorityConfig(@Param("product") Integer product, @Param("adPos") Integer adPos, @Param("os") Integer os);

    /**
     * 根据产品优先级查找策略
     *
     * @param product  产品
     * @param priority 优先级
     * @param adPos    广告位大类
     * @param os       系统
     * @return 策略配置
     */
    @Select("select * from tb_ap_config_orientation where product = #{product} and priority = #{priority} and ad_pos= #{adPos} and os=#{os} and region!=1 and state=1 and (ab_test = '1' or ab_test is null or ab_test='')")
    ConfigOrientationEntity queryByPriority(@Param("product") Integer product, @Param("priority") Integer priority, @Param("adPos") int adPos, @Param("os") int os);

    @Select("select * from tb_ap_config_orientation where product = #{product} and priority = #{priority} and ad_pos = #{adPos} and os = #{os} and region != 1 and state = 1 and ab_test = #{abTest} order by id desc ")
    ConfigOrientationEntity queryByAbTest(@Param("product") Integer product, @Param("priority") Integer priority, @Param("adPos") int adPos, @Param("os") int os, @Param("abTest") String abTest);

    @Select("select * from tb_ap_config_orientation where product = #{product} and config like concat('%',#{configId},'%') limit 1")
    ConfigOrientationEntity queryByConfigBase(@Param("product") Integer product, @Param("configId") Integer configId);

    @Select("select * from tb_ap_config_orientation where product = #{product} and priority = #{priority} and ad_pos = #{adPos} and os = #{os} and region != 1 and ab_test = #{abTest} order by id desc ")
    ConfigOrientationEntity queryByAbTestWithoutState(@Param("product") Integer product, @Param("priority") Integer priority, @Param("adPos") int adPos, @Param("os") int os, @Param("abTest") String abTest);

    @Select("select * from tb_ap_config_orientation where product = #{product} and priority = #{priority} and ad_pos= #{adPos} and os=#{os} and region!=1 and (ab_test = '1' or ab_test is null or ab_test='')")
    ConfigOrientationEntity queryByPriorityWithoutState(@Param("product") Integer product, @Param("priority") Integer priority, @Param("adPos") int adPos, @Param("os") int os);
}
