package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.BiddingConfigVo;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.service.BiddingConfigService;
import com.coohua.ap.admin.utils.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/10/13
 */
@RequestMapping("bidding")
@RestController
public class BiddingConfigController {

    @Autowired
    private BiddingConfigService biddingConfigService;

    @RequestMapping("/list")
    @ResponseBody
    public Page<BiddingConfigVo> list(Page<BiddingConfigVo> page,@RequestParam(value = "adId",required = false)String adId, HttpServletRequest request) {
        biddingConfigService.queryList(page, SessionUtils.getUserProduct(request),adId);
        return page;
    }

    @RequestMapping("/addOne")
    public BaseResponse add(@RequestParam("adId")Long adId,
                            @RequestParam("adPosType")Integer adPosType,
                            @RequestParam("startEcpm")Integer startEcpm,
                            @RequestParam("endEcpm")Integer endEcpm,
                            @RequestParam("priority")Integer priority,
                            @RequestParam("biddingType")Integer biddingType,
                            @RequestParam("playType")Integer playType,
                            HttpServletRequest request){
        if (priority == null){
            priority = 0;
        }
        biddingConfigService.insert(SessionUtils.getUserProduct(request),adPosType,adId,startEcpm,endEcpm,priority,biddingType,playType);
        return new BaseResponse(0);
    }

    @RequestMapping("/updateOne")
    public BaseResponse update(@RequestParam("id")Integer id,
                               @RequestParam("adId")Long adId,
                               @RequestParam("adPosType")Integer adPosType,
                               @RequestParam("startEcpm")Integer startEcpm,
                               @RequestParam("endEcpm")Integer endEcpm,
                               @RequestParam("priority")Integer priority,
                               @RequestParam("biddingType")Integer biddingType,
                               @RequestParam("playType")Integer playType,
                               HttpServletRequest request){
        if (priority == null){
            priority = 0;
        }
        biddingConfigService.update(id,SessionUtils.getUserProduct(request),adPosType,adId,startEcpm,endEcpm,priority,biddingType,playType);
        return new BaseResponse(0);
    }

    @RequestMapping("/switchFlag")
    public BaseResponse switchFlag(@RequestParam("id")Integer id,
                                   @RequestParam("switchFlag")Integer switchFlag,
                                   HttpServletRequest request){
        biddingConfigService.switchFlag(id,switchFlag);
        return new BaseResponse(0);
    }

    @RequestMapping("delFlag")
    public BaseResponse delFlag(@RequestParam("id")Integer id,
                                @RequestParam("delFlag")Integer delFlag,
                                HttpServletRequest request){
        biddingConfigService.delFlag(id,delFlag);
        return new BaseResponse(0);
    }
}
