package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.domain.AzkabanExecuteStatusRsp;
import com.coohua.ap.admin.domain.AzkabanFlowExecuteRsp;
import com.coohua.ap.admin.utils.third.HttpClients;
import com.coohua.ap.admin.utils.third.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2022/7/13
 */
@Slf4j
@Service
public class AzkabanService {
    private final static String baseUrl = "http://172.16.11.50:9091";
    private String sessionId;

    public void  refreshSessionId(){
        Map<String,Object> param = new HashMap<>();
        param.put("action","login");
        param.put("username","api");
        param.put("password","azkaban");

        Map<String,Object> header = new HashMap<>();
        header.put("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        String res = HttpClients.fromPost(baseUrl,header,param);
        sessionId =  JSONObject.parseObject(res).getString("session.id");
        log.info(">>>> LOGIN AZKABAN,SessionID={}...",sessionId);
    }

    // 调起Azkaban任务
    public AzkabanFlowExecuteRsp callExecute(String project, String flow){
        return callExecute(project,flow,null,null);
    }
    public AzkabanFlowExecuteRsp callExecute(String project, String flow, String flowParam, String flowVale){
        String url = baseUrl+String.format("/executor?ajax=executeFlow&project=%s&flow=%s&session.id=%s",project,flow,sessionId);
        if (Strings.noEmpty(flowParam)){
            url = url + "&flowOverride[" + flowParam + "]=" + flowVale;
        }
        return JSON.parseObject(HttpClients.GET(url),AzkabanFlowExecuteRsp.class);
    }

    public AzkabanExecuteStatusRsp getExecuteStatus(Integer execid){
        String url = baseUrl+String.format("/executor?ajax=fetchexecflow&execid=%s&session.id=%s",execid,sessionId);
        return JSON.parseObject(HttpClients.GET(url),AzkabanExecuteStatusRsp.class);
    }

    public void waitTaskEnd(Integer execid,String product){
        waitTaskEnd(execid,product,null,null);
    }

        public void waitTaskEnd(Integer execid,String product,String flow,String flowValue){
        try {
            AzkabanExecuteStatusRsp rsp = getExecuteStatus(execid);
            log.info("Current:{}",rsp.getStatus());
            int retryCount = 0;
            while (!"succeeded".equalsIgnoreCase(rsp.getStatus())){
                try {
                    TimeUnit.SECONDS.sleep(10);
                } catch (InterruptedException e) {
                    log.info("Err:",e);
                }
                rsp = getExecuteStatus(execid);
                log.info("Current:{},{}",product,rsp.getStatus());
                if ("failed".equalsIgnoreCase(rsp.getStatus())){
                    // 重试
                    AzkabanFlowExecuteRsp retry = callExecute(product,rsp.getFlow(),flow,flowValue);
                    retryCount ++;
                    TimeUnit.SECONDS.sleep(10);
                    rsp = getExecuteStatus(retry.getExecid());
                    if (retryCount > 3){
                        log.info("RetryMoreThan3 Failed, {}",product);
                        return;
                    }
                }
            }
        }catch (Exception e){
            log.error("E:",e);
        }

    }
}
