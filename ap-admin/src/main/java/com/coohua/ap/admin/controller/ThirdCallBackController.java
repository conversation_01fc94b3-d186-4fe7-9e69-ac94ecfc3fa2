package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.service.third.ThirdCallBackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/10/29
 */
@Slf4j
@RestController
@RequestMapping("callback")
public class ThirdCallBackController {

    @Autowired
    private ThirdCallBackService thirdCallBackService;

    @RequestMapping("list")
    @ResponseBody
    public BaseResponse queryAllSetProduct(){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(thirdCallBackService.queryList());
        return baseResponse;
    }

    @RequestMapping("byProduct")
    @ResponseBody
    public BaseResponse updateByProduct(@RequestParam("product")Integer product,
                                        @RequestParam("os")Integer os,
                                        @RequestParam("types")Integer types,
                                        HttpServletRequest request){
        if (types == 1){
            thirdCallBackService.openAllUrl(product,os);
        }else {
            thirdCallBackService.closeUrl(product,os);
        }
        return new BaseResponse(0);
    }

    @RequestMapping("BDbyProduct")
    @ResponseBody
    public BaseResponse updateBDByProduct(@RequestParam("product")Integer product,
                                        @RequestParam("os")Integer os,
                                        @RequestParam("types")Integer types,
                                        HttpServletRequest request){
        if (types == 1){
            thirdCallBackService.openBDAllUrl(product,os);
        }else {
            thirdCallBackService.closeUrlBD(product,os);
        }
        return new BaseResponse(0);
    }

    @RequestMapping("byAdId")
    @ResponseBody
    public BaseResponse updateByAdId(@RequestParam("product")Integer product,
                                     @RequestParam("adId")Long adId,
                                     @RequestParam("types")Integer types,
                                     HttpServletRequest request){
        if (types == 1){
            thirdCallBackService.openSingleUrl(adId,product );
        }else {
            thirdCallBackService.closeSingleUrl(adId,product );
        }
        return new BaseResponse(0);
    }
}
