package com.coohua.ap.admin.controller.vo;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.nap.vo.admin
 * @create_time 2019-05-21
 */
public class JsAdPosConfig {
    private String channelId;

    private String channelSource;

    private Integer jsStyle;

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannelSource() {
        return channelSource;
    }

    public void setChannelSource(String channelSource) {
        this.channelSource = channelSource;
    }

    public Integer getJsStyle() {
        return jsStyle;
    }

    public void setJsStyle(Integer jsStyle) {
        this.jsStyle = jsStyle;
    }

    @Override
    public String toString() {
        return "JsAdPosConfig{" +
                "channelId='" + channelId + '\'' +
                ", channelSource='" + channelSource + '\'' +
                ", jsStyle=" + jsStyle +
                '}';
    }
}
