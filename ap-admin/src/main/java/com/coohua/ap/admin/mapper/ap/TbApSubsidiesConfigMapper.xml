<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApSubsidiesConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbApSubsidiesConfig" >
        <result column="id" property="id" />
        <result column="strategy_name" property="strategyName" />
        <result column="os" property="os" />
        <result column="app_id" property="appId" />
        <result column="product" property="product" />
        <result column="product_name" property="productName" />
        <result column="ad_type_name" property="adTypeName" />
        <result column="platform" property="platform" />
        <result column="ab_test" property="abTest" />
        <result column="is_ab_test" property="isAbTest" />
        <result column="config_priority" property="configPriority" />
        <result column="auto_generate_ecpm" property="autoGenerateEcpm" />
        <result column="layer_ecpm" property="layerEcpm" />
        <result column="priority" property="priority" />
        <result column="has_bidding" property="hasBidding" />
        <result column="is_enabled" property="isEnabled" />
        <result column="status" property="status" />
        <result column="only_night_launch" property="onlyNightLaunch" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        strategy_name,
        os,
        app_id,
        product,
        product_name,
        ad_type_name,
        platform,
        ab_test,
        is_ab_test,
        layer_ecpm,
        config_priority,
        auto_generate_ecpm,
        priority,
        has_bidding,
        is_enabled,
        status,
        only_night_launch,
        create_time,
        update_time
    </sql>

    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_subsidies_config
        WHERE id = #{id}
    </select>



    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_subsidies_config
        where is_enabled = 1
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_ap_subsidies_config
    </select>

</mapper>