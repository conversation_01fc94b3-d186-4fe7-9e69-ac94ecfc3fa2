package com.coohua.ap.admin.service.third.service.bd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdResponse;
import com.coohua.ap.admin.utils.third.Strings;
import org.apache.http.HttpStatus;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.openssl.PEMReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpVersion;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.HttpClientBuilder;
import org.bouncycastle.crypto.digests.MD5Digest;
import org.bouncycastle.util.encoders.Base64;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.Security;
import java.security.Signature;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/30
 */
@Slf4j
public class UbapiClient {
    private String host;
    private PrivateKey privateKey;
    private String accessKey;

    public UbapiClient(String host, String accessKey, String dsaPrivateKey) {
        this.host = host;
        this.accessKey = accessKey;
        this.privateKey = parsePEMPrivateKey(dsaPrivateKey);
    }

    public HttpResponse get(String uri) throws IOException {
        String method = "GET";
        return execute(method, uri, null, null);
    }

    public <T> T post(String uri,byte[] content, TypeReference<T> pClass){
        try {
            HttpResponse response = execute("POST", uri, MediaType.APPLICATION_JSON_VALUE, content);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity(), "utf-8");
                log.info("==> BD Response: {}",result);
                if (Strings.noEmpty(result)){
                    return JSON.parseObject(result,pClass);
                }
            }
        }catch (Exception e){
            log.error("RequestBaidu Ex: ",e);
        }
        throw new RuntimeException("请求百度API失败..");
    }

    public HttpResponse post(String uri, File file) throws IOException {
        return execute("POST", uri, file);
    }

    public HttpResponse put(String uri, String contentType, byte[] content) throws IOException {
        return execute("PUT", uri, contentType, content);
    }

    private HttpResponse execute(String method, String uri, String contentType, byte[] content) throws IOException {


        Long timeStamp = System.currentTimeMillis();

        // 1. 准备签名需要的数据
        List<String> itemsToBeSinged = new ArrayList<String>(6);
        // 1.1 accessKey
        itemsToBeSinged.add(accessKey);
        // 1.2 method
        itemsToBeSinged.add(method);
        // 1.3 uri
        itemsToBeSinged.add(uri);
        // 1.4 时间
        itemsToBeSinged.add(Long.toString(timeStamp));
        // 1.5 contentType
        // 1.6 body 的md5
        if (contentType != null && content != null) {
            itemsToBeSinged.add(contentType);

            MD5Digest digest = new MD5Digest();
            byte[] contentBytes = content;
            digest.update(contentBytes, 0, contentBytes.length);
            byte[] digestBytes = new byte[digest.getDigestSize()];
            digest.doFinal(digestBytes, 0);
            itemsToBeSinged.add(new String(Hex.encode(digestBytes)));
        } else {
            itemsToBeSinged.add(""); // empty ContentType
            itemsToBeSinged.add(""); // empty content md5
        }

        // 2. 签名
        String stringTobeSigned = StringUtils.join(itemsToBeSinged, "\n");

        log.info("string to be sign:{}" ,stringTobeSigned);
        byte[] signatureBytes = signMessage(privateKey, stringTobeSigned.getBytes());

        String signature = new String(Base64.encode(signatureBytes));

        // 3. 发起请求
        RequestBuilder requestBuilder = RequestBuilder.create(method);
        RequestConfig.Builder configBuilder = RequestConfig.custom();
        configBuilder.setSocketTimeout(60000)
                .setConnectTimeout(30000)
                .setConnectionRequestTimeout(5000);
        RequestConfig config = configBuilder.build();

        requestBuilder.setConfig(config);
        requestBuilder.setUri(host + uri);
        requestBuilder.setVersion(HttpVersion.HTTP_1_1);


        requestBuilder.addHeader("x-ub-date", Long.toString(timeStamp));
        requestBuilder.addHeader("x-ub-authorization", accessKey + ":" + signature);

        if (contentType != null) {
            requestBuilder.addHeader("Content-Type", contentType);
        }
        if (content != null) {
            requestBuilder.setEntity(new ByteArrayEntity(content));
        }

        HttpResponse response = HttpClientBuilder.create().build().execute(requestBuilder.build());
        return response;
    }

    private HttpResponse execute(String method, String uri, File file) throws IOException {

        Date now = new Date();
        RequestBuilder requestBuilder = RequestBuilder.create(method);
        RequestConfig.Builder configBuilder = RequestConfig.custom();
        configBuilder.setSocketTimeout(600000)
                .setConnectTimeout(30000)
                .setConnectionRequestTimeout(5000);
        RequestConfig config = configBuilder.build();

        requestBuilder.setConfig(config);
        requestBuilder.setUri(host + uri);
        requestBuilder.setVersion(HttpVersion.HTTP_1_1);
        if (file != null) {
            HttpEntity reqEntity = MultipartEntityBuilder.create().addPart("file", new FileBody(file)).build();
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            reqEntity.writeTo(outStream);
            byte[] bytes = outStream.toByteArray();
            requestBuilder.setEntity(reqEntity);
            // 1. 准备签名需要的数据
            List<String> itemsToBeSinged = new ArrayList<String>(6);
            // 1.1 accessKey
            itemsToBeSinged.add(accessKey);
            // 1.2 method
            itemsToBeSinged.add(method);
            // 1.3 uri
            itemsToBeSinged.add(uri);
            // 1.4 时间
            itemsToBeSinged.add(String.valueOf(now.getTime()));
            // 1.5 ContentType
            itemsToBeSinged.add(reqEntity.getContentType().getValue());
            MD5Digest digest = new MD5Digest();
            digest.update(bytes, 0, bytes.length);
            byte[] digestBytes = new byte[digest.getDigestSize()];
            digest.doFinal(digestBytes, 0);
            itemsToBeSinged.add(new String(Hex.encode(digestBytes)));
            // 2. 签名
            String stringTobeSigned = StringUtils.join(itemsToBeSinged, "\n");
            byte[] signatureBytes = signMessage(privateKey, stringTobeSigned.getBytes());
            String signature = new String(Base64.encode(signatureBytes));
            requestBuilder.addHeader("x-ub-date", String.valueOf(now.getTime()));
            requestBuilder.addHeader("x-ub-authorization", accessKey + ":" + signature);

        }
        // 3. 发起请求
        return HttpClientBuilder.create().build().execute(requestBuilder.build());
    }

    public static byte[] signMessage(PrivateKey privateKey, byte[] message) {
        Assert.notNull(privateKey);
        Assert.notNull(message);

        Signature dsa;
        try {
            dsa = Signature.getInstance("SHA1withDSA", "SUN");
            dsa.initSign(privateKey);
            dsa.update(message);
            byte[] result = dsa.sign();
            return result;
        } catch (Exception e) {
            log.error("sign error, stack trace: ",  e);
            throw new RuntimeException(e);
        }
    }

    private static PrivateKey parsePEMPrivateKey(String pemKey) {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
        PEMReader reader = new PEMReader(new StringReader(pemKey));
        Object key;
        try {
            key = reader.readObject();
            reader.close();
        } catch (IOException e) {
            log.error("read pubKey error, pemKey: {}", pemKey);
            throw new RuntimeException(e);
        }

        if (key instanceof KeyPair) {
            PrivateKey privateKey = ((KeyPair) key).getPrivate();
            return privateKey;
        }
        log.error("not a private key, pemKey: {}", pemKey);
        throw new RuntimeException("not a private key");
    }

}

