package com.coohua.ap.admin.service;

import com.coohua.ap.admin.constants.TbApReplaceAdEnum;
import com.coohua.ap.admin.mapper.ap.TbApReplaceAdMapper;
import com.coohua.ap.admin.model.TbApReplaceAd;
import com.coohua.ap.admin.model.TbApSubsidiesConfig;
import com.coohua.ap.admin.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class TbApReplaceAdService {

    @Resource
    private TbApReplaceAdMapper tbApReplaceAdMapper;

    public void insertTbApReplaceAdForSubsides(TbApReplaceAd tbApReplaceAd, TbApSubsidiesConfig vo, Integer adType, Integer ecpm) {
        tbApReplaceAd.setAdType(adType);
        tbApReplaceAd.setEcpm(ecpm);
        tbApReplaceAd.setCreateTime(new Date());
        tbApReplaceAd.setUpdateTime(new Date());

        if (!TbApReplaceAdEnum.BIDDING_LAYERS.getCode().equals(tbApReplaceAd.getLayerType())){
            tbApReplaceAd.setLayerType(TbApReplaceAdEnum.NEW_LAYER.getCode());
            String originEcpm = vo.getAutoGenerateEcpm();

            if (!StringUtils.isEmpty(originEcpm)){
                List<String> originEcpmList = Arrays.asList(originEcpm.split(","));
                if (originEcpmList.contains(ecpm)){
                    tbApReplaceAd.setLayerType(TbApReplaceAdEnum.ORIGIN_LAYERS.getCode());
                }
            }
        }
        tbApReplaceAdMapper.insert(tbApReplaceAd);
    }
}
