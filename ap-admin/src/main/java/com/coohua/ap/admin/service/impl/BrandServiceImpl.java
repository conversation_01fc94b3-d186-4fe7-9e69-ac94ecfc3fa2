package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.controller.vo.BrandTypeVO;
import com.coohua.ap.admin.mapper.ap.TbMetaBrandMapper;
import com.coohua.ap.admin.model.TbMetaBrandModel;
import com.coohua.ap.admin.service.BrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/02
 */
@Service
public class BrandServiceImpl implements BrandService {

    @Autowired
    private TbMetaBrandMapper tbMetaBrandMapper;

    @Override
    public List<BrandTypeVO> queryBrandType() {
        List<TbMetaBrandModel> models = tbMetaBrandMapper.queryAllBrandType();
        List<BrandTypeVO> ret = new ArrayList<>();

        for (TbMetaBrandModel entity : models) {
            ret.add(convertEntityToVO(entity));
        }

        return ret;
    }

    private BrandTypeVO convertEntityToVO(TbMetaBrandModel entity) {
        BrandTypeVO vo = new BrandTypeVO();
        vo.setKey(entity.getCode());
        vo.setLabel(entity.getName());
        return vo;
    }
}
