package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdTaskQueen;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description third_task_queen
 * <AUTHOR>
 * @date 2021-07-29
 */
@Mapper
@Repository
public interface ThirdTaskQueenMapper {

    int insert(ThirdTaskQueen thirdTaskQueen);

    int delete(int id);

    int update(ThirdTaskQueen thirdTaskQueen);

    ThirdTaskQueen load(int id);

    List<ThirdTaskQueen> pageList(@Param("offset") int offset,@Param("pageSize")int pagesize,@Param("batchNo")String batchNo);

    int pageListCount(@Param("offset") int offset,@Param("pageSize")int pagesize,@Param("batchNo")String batchNo);

    @Select("select * from third_task_queen where batch_no = #{batchNo}")
    List<ThirdTaskQueen> selectByBatch(@Param("batchNo")String batchNo);
}