<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdAppLoadMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdAppLoad" >
        <result column="id" property="id" />
        <result column="batch_no" property="batchNo" />
        <result column="platform_code" property="platformCode" />
        <result column="company_id" property="companyId" />
        <result column="main_body" property="mainBody" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="product" property="product" />
        <result column="app_status" property="appStatus" />
        <result column="work_lite" property="workLite" />
        <result column="os" property="os" />
        <result column="app_ext" property="appExt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                batch_no,
                platform_code,
                company_id,
                main_body,
                app_id,
                app_name,
                product,
                app_status,
                work_lite,
                os,
                app_ext,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdAppLoad">
        INSERT INTO third_app_load
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != batchNo and '' != batchNo">
                batch_no,
            </if>
            <if test="null != platformCode and '' != platformCode">
                platform_code,
            </if>
            <if test="null != companyId and '' != companyId">
                company_id,
            </if>
            <if test="null != mainBody and '' != mainBody">
                main_body,
            </if>
            <if test="null != appId and '' != appId">
                app_id,
            </if>
            <if test="null != appName and '' != appName">
                app_name,
            </if>
            <if test="null != product and '' != product">
                product,
            </if>
            <if test="null != appStatus and '' != appStatus">
                app_status,
            </if>
            <if test="null != workLite and '' != workLite">
                work_lite,
            </if>
            <if test="null != os and '' != os">
                os,
            </if>
            <if test="null != appExt and '' != appExt">
                app_ext,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != batchNo and '' != batchNo">
                #{batchNo},
            </if>
            <if test="null != platformCode and '' != platformCode">
                #{platformCode},
            </if>
            <if test="null != companyId and '' != companyId">
                #{companyId},
            </if>
            <if test="null != mainBody and '' != mainBody">
                #{mainBody},
            </if>
            <if test="null != appId and '' != appId">
                #{appId},
            </if>
            <if test="null != appName and '' != appName">
                #{appName},
            </if>
            <if test="null != product and '' != product">
                #{product},
            </if>
            <if test="null != appStatus and '' != appStatus">
                #{appStatus},
            </if>
            <if test="null != workLite and '' != workLite">
                #{workLite},
            </if>
            <if test="null != os and '' != os">
                #{os},
            </if>
            <if test="null != appExt and '' != appExt">
                #{appExt},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_app_load
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdAppLoad">
        UPDATE third_app_load
        <set>
            <if test="null != batchNo and '' != batchNo">batch_no = #{batchNo},</if>
            <if test="null != platformCode and '' != platformCode">platform_code = #{platformCode},</if>
            <if test="null != companyId and '' != companyId">company_id = #{companyId},</if>
            <if test="null != mainBody and '' != mainBody">main_body = #{mainBody},</if>
            <if test="null != appId and '' != appId">app_id = #{appId},</if>
            <if test="null != appName and '' != appName">app_name = #{appName},</if>
            <if test="null != product and '' != product">product = #{product},</if>
            <if test="null != appStatus and '' != appStatus">app_status = #{appStatus},</if>
            <if test="null != workLite and '' != workLite">work_lite = #{workLite},</if>
            <if test="null != os and '' != os">os = #{os},</if>
            <if test="null != appExt and '' != appExt">app_ext = #{appExt},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_app_load
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_app_load where 1=1
        <if test=" 0 != product">  and product = #{product}</if>
        <if test="null != batchNo and '' != batchNo"> and batch_no = #{batchNo}</if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_app_load WHERE 1=1
        <if test=" 0 != product"> and product = #{product}</if>
        <if test="null != batchNo and '' != batchNo"> and batch_no = #{batchNo}</if>
    </select>

</mapper>