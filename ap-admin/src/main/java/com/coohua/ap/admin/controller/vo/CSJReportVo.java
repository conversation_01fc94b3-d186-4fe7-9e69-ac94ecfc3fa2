package com.coohua.ap.admin.controller.vo;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * @ClassName: CSJReprotVo
 * @Description: 穿山甲请求数据
 * @Author: fan jin yang
 * @Date: 2020/4/24
 * @Version: V1.0
 **/
public class CSJReportVo extends DefaultReportVO {

    private String nonce;

    public CSJReportVo(String memberId,String secretId) {
        this.url = "https://partner.oceanengine.com/union/media/open/api/report/slot";
        this.memberId = memberId;
        this.secretId = secretId;
    }

    @Override
    void buildRequestTime(String ...date) {
        if(date!=null&&date.length==2){
            this.startTime = date[0];
            this.endTime = date[1];
        }else {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date yesterday = new Date(System.currentTimeMillis()-24*3600*1000);
            this.startTime = simpleDateFormat.format(yesterday);
            this.endTime = simpleDateFormat.format(yesterday);
        }

    }

    @Override
    void generateSign() {
        this.nonce = String.valueOf(RandomUtils.nextInt(0,100));
        String[] array = new String[]{this.secretId,this.timestamp,nonce};
        Arrays.sort(array);
        String signStr = array[0]+array[1]+array[2];
        this.sign = DigestUtils.sha1Hex(signStr);
    }

    @Override
    void generateUrl() {
        UriComponentsBuilder uriComponentsBuilder =  UriComponentsBuilder.fromUriString(this.url)
                .queryParam("user_id",memberId)
                .queryParam("timestamp",timestamp)
                .queryParam("nonce",nonce)
                .queryParam("start_date",startTime)
                .queryParam("end_date",endTime)
                .queryParam("sign",sign)
                ;
        this.requestUrl = uriComponentsBuilder.build().toUri();
    }
}
