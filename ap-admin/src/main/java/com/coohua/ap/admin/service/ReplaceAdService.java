package com.coohua.ap.admin.service;

import com.coohua.ap.admin.constants.ReplaceStatus;
import com.coohua.ap.admin.controller.vo.Ad;
import com.coohua.ap.admin.controller.vo.AdExt;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.CreateRecord;
import com.coohua.ap.admin.model.TbApBidding;
import com.coohua.ap.admin.model.ThirdAdPosEntity;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/7/18
 */
@Slf4j
@Service
public class ReplaceAdService {

    @Resource
    private CreateRecordMapper createRecordMapper;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private ThirdAdPosMapper thirdAdPosMapper;

    @Autowired
    private AdAdminService adAdminService;

    @Value("${enable.auto.replace:true}")
    private Boolean enableAutoReplace;
    @ApolloJsonValue("${join.auto.replace.list:[646]}")
    private List<Integer> joinAutoReplaceAppList;
    @Autowired
    private TbApBiddingMapper tbApBiddingMapper;
    @Autowired
    private BiddingConfigService biddingConfigService;

    public void replaceOurAd(List<CreateRecord> createRecords){
        if (!enableAutoReplace){
            return;
        }
        Date now = new Date();
        Date nowLeftOneHour = DateUtil.dateIncreaseByHour(now,-1);
        Map<String,List<CreateRecord>> createTypeMap = createRecords.stream()
                .filter(createRecord -> {
                    if (createRecord.getCreateTime().after(nowLeftOneHour)) {
                        log.info("{} CreateTime {} Too Late", createRecord.getNewPosName(), DateUtil.dateToString(createRecord.getCreateTime()));
                        return false;
                    }
                    App product = AppBuilder.getByProduct(createRecord.getProduct());
                    if (product == null){
                        log.warn("{} NotFund Product",createRecord.getProduct());
                        return false;
                    }
                    if (enableAutoReplace || joinAutoReplaceAppList.contains(product.getAppId())){
                        return true;
                    }else {
                        log.warn(">>> {} {} APP ID NotAllowed",createRecord.getOldAdId(),product.getAppId());
                        return false;
                    }
                }).collect(Collectors.groupingBy(rx-> {
                    String type = "wall";
                    if (rx.getOldPosName().contains("bidding")){
                        type = "bidding";
                    }
                    return rx.getProduct() + "_" + type;
                }));
        createTypeMap.forEach((crType,list) ->{
            if (list.size() > 10){
                list = list.stream().limit(10).collect(Collectors.toList());
            }
            log.info("{} Can Replace {}",crType,list.size());
            for (CreateRecord createRecord : list) {
                try {
                    replaceOurAd(createRecord);
                } catch (Exception e) {
                    log.error("Replace AdPos Ex:", e);
                }
            }
        });

    }

    private void replaceOurAd(CreateRecord createRecord){
        log.info(">>> Start Replace AD {}", createRecord.getOldAdId());
        // 找出我方Ad
        App product = AppBuilder.getByProduct(createRecord.getProduct());
        List<Ad> odAdList = adAdminService.selectAllAds(null,null, String.valueOf(createRecord.getOldAdId()),
                null,null,null,product.getAppId(), null,null,null,null,null);
        if (odAdList == null || odAdList.size() != 1){
            log.warn("{} getOldAd Exception",createRecord.getOldAdId());
            return;
        }
        Ad oldAd = odAdList.get(0);
        if (oldAd.getState() == 0){
            log.warn(">>> {} {} AD AlreadyClosed",createRecord.getOldAdId(),product.getAppId());
            return;
        }
        // 新建我方Ad
        List<Ad> newPreList = adAdminService.selectAllAds(createRecord.getNewPosName(),null, null,
                null,null,null,product.getAppId(), null,null,null,null,null);
        Ad newAd;
        if (newPreList == null || newPreList.size() == 0){
            convertPosId(oldAd,product,createRecord);
            newAd = adAdminService.insertAd(oldAd);
        }else {
            Ad nameAd = newPreList.get(0);
            convertPosId(nameAd,product,createRecord);
            nameAd.setName(nameAd.getName()+"_t1");
            newAd = adAdminService.insertAd(nameAd);
        }

        // 关闭我方对比
        adAdminService.changeAdInfoFlag(Math.toIntExact(createRecord.getOldAdId()),0);
        adAdminService.changeAdInfoFlag(Math.toIntExact(newAd.getId()),1);
        // 是否Bidding广告位
        if (tbApBiddingMapper.isBiddingAd(createRecord.getOldAdId()) > 0){
            replaceBiddingAd(createRecord,product,newAd);
        }
        // 更新createRecord 为已更新
        createRecord.setReplaceStatus(ReplaceStatus.ALREADY_REPLACE.getCode());
        createRecord.setUpdateTime(new Date());
        createRecordMapper.update(createRecord);
        // 切换状态为已导入
        ThirdAdPosEntity thirdAdPosEntity =  thirdAdPosMapper.queryByPosId(createRecord.getNewPosId());
        int ret = thirdAdPosMapper.updateStateToAlreadyLoadById(thirdAdPosEntity.getId());
        if (ret > 0){
            log.info("Update POS_ID {} status Succeed",thirdAdPosEntity.getPosId());
        }
        log.info(">>> Complete Replace AD {} To {}", createRecord.getOldAdId(),newAd.getId());
    }

    private void convertPosId(Ad nameAd,App product,CreateRecord createRecord){
        AdExt ext = nameAd.getExt();
        String thirdPosId = String.valueOf(createRecord.getNewPosId());
        if ("android".equals(createRecord.getOs())){
            ext.setAndroidPosId(thirdPosId);
            ext.setAndroidBakPosId(thirdPosId);
        }else {
            ext.setIosPosId(thirdPosId);
            ext.setIosBakPosId(thirdPosId);
        }
        nameAd.setName(createRecord.getNewPosName());
        nameAd.setExt(ext);
    }

    private void replaceBiddingAd(CreateRecord createRecord,App product,Ad newAd){
        log.info("{} AdIsBiddingAd",createRecord.getOldAdId());
        // 创建新的Bidding映射
        List<TbApBidding> biddingAdList = tbApBiddingMapper.queryByAdIdAndProduct(createRecord.getOldAdId(),product.getAppId());
        if (biddingAdList == null || biddingAdList.size() == 0){
            log.warn("{} Not In BiddingSystem",createRecord.getOldAdId());
            return;
        }
        TbApBidding oldBidding = biddingAdList.get(0);
        biddingConfigService.insert(product.getAppId(),oldBidding.getAdPosType(),
                (long) newAd.getId(),
                oldBidding.getStartEcpm(),
                oldBidding.getEndEcpm(),
                oldBidding.getPriority(),
                oldBidding.getBiddingType(),oldBidding.getPlayType());
        // 关闭原有Bidding映射
        biddingConfigService.switchFlag(oldBidding.getId(),0);
    }
}
