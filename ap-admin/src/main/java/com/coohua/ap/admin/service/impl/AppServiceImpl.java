package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.controller.vo.AppVO;
import com.coohua.ap.admin.mapper.ap.TbApAppMapper;
import com.coohua.ap.admin.model.AppModel;
import com.coohua.ap.admin.service.AppService;
import com.coohua.ap.base.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/03/19
 */
@Service
public class AppServiceImpl implements AppService {

    @Resource
    private TbApAppMapper tbApAppMapper;


    @Override
    public List<AppVO> list(String name) {
        List<AppModel> models = tbApAppMapper.queryByName(name);
        List<AppVO> ret = new ArrayList<>();
        for (AppModel model : models) {
            ret.add(convertModelToVO(model));
        }
        return ret;
    }

    @Override
    public int addApp(AppVO appVO) {
        AppModel model = new AppModel();
        model.setAppId(Integer.parseInt(appVO.getAppId()));
        model.setName(appVO.getAppName());
        model.setDescription(appVO.getDescription());
        return tbApAppMapper.addApp(model);
    }

    @Override
    public int updateApp(AppVO appVO) {
        AppModel model = new AppModel();
        model.setAppId(Integer.parseInt(appVO.getAppId()));
        model.setName(appVO.getAppName());
        model.setDescription(appVO.getDescription());
        return tbApAppMapper.updateApp(model);
    }

    private AppVO convertModelToVO(AppModel model) {
        AppVO vo = new AppVO();
        vo.setAppId(String.valueOf(model.getAppId()));
        vo.setAppName(model.getName());
        vo.setDescription(model.getDescription());
        vo.setUpdateTime(DateUtils.formatDateForYMDHMS(model.getUpdateTime()));
        return vo;
    }
}
