package com.coohua.ap.admin.components;

import com.coohua.ap.admin.controller.vo.Ad;
import com.coohua.ap.base.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.components
 * @create_time 2019-11-04
 */
public class DateAdValve extends AbstractAdValve {

    private String startDate;

    private String endDate;

    public DateAdValve(String startDate, String endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }

    @Override
    public List<Ad> doFilter(List<Ad> adList) {
        List<Ad> result = new ArrayList<>();

        if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            return adList;
        }

        for (Ad ad : adList) {
            if (StringUtils.isNotEmpty(ad.getStartDate()) && DateUtils.compare(ad.getStartDate(), startDate) > 0) {
                continue;
            }
            if (StringUtils.isNotEmpty(ad.getEndDate()) && DateUtils.compare(ad.getEndDate(), endDate) < 0) {
                continue;
            }
            result.add(ad);
        }

        return result;
    }
}
