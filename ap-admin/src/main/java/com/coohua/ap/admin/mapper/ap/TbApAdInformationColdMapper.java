package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.AdExtBean;
import com.coohua.ap.admin.model.AdExtEntity;
import com.coohua.ap.admin.model.TbApAdInformationCold;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface TbApAdInformationColdMapper {

    @Select("select * from tb_ap_ad_information_cold where id=#{id}")
    TbApAdInformationCold getById(Long id);

    @Select("select * from tb_ap_ad_information where id=#{id}")
    TbApAdInformationCold getByIdSource(Long id);

    @Insert("insert into tb_ap_ad_information_cold" +
            " (id,name,type,put_date_period,time_bucket,state,create_time,update_time,plan_id,ad_pos,product,state_update_time,ecp_ad_id)" +
            " values(#{id},#{name},#{type},#{putDatePeriod},#{timeBucket},#{state},#{createTime},#{updateTime},#{planId},#{adPos},#{product},#{stateUpdateTime},#{ecpAdId})")
    Integer insertToClod(TbApAdInformationCold tbApAdInformationCold);


    @Select("delete from tb_ap_ad_information where id=#{id}")
    TbApAdInformationCold deleteFormSource(Long id);


    @Select("select * from tb_ap_ad_ext where id = #{id}")
    AdExtBean queryById(Long id);

}