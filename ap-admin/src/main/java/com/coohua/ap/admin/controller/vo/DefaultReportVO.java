package com.coohua.ap.admin.controller.vo;

import java.net.URI;

/**
 * @ClassName: DefaultReportVo
 * @Description: 请求数据模板类
 * @Author: fan jin yang
 * @Date: 2020/4/24
 * @Version: V1.0
 **/
public abstract class DefaultReportVO {

    /**
     * 请求地址
     */
    protected String url;

    /**
     * 最终请求的url
     */
    protected URI requestUrl;

    /**
     * 账号id
     */
    protected String memberId;

    /**
     * 秘钥
     */
    protected String secretId;

    /**
     * 时间戳
     */
    protected String timestamp;

    /**
     * 开始时间
     */
    protected String startTime;

    /**
     *
     */
    protected String sign;

    /**
     * 结束时间
     */
    protected String endTime;

    /**
     * 构建请求时间
     * @return
     */
    abstract void buildRequestTime(String ...date);

    /**
     * 生成签名
     * @return
     */
    abstract void generateSign();

    /**
     * 构建最终请求的url
     * @return
     */
    abstract void generateUrl();

    public URI getRequestUrl(String ... date){
        this.timestamp = String.valueOf(System.currentTimeMillis());
        buildRequestTime(date);
        generateSign();
        generateUrl();
        return this.requestUrl;
    }

}
