<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdTemplateMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdTemplateEntity" >
        <result column="id" property="id" />
        <result column="model_name" property="modelName" />
        <result column="model_detail" property="modelDetail" />
        <result column="already_income_app" property="alreadyIncomeApp" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                model_name,
                model_detail,
                already_income_app,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdTemplateEntity">
        INSERT INTO third_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test ='null != modelName'>
                model_name,
            </if>
            <if test ='null != modelDetail'>
                model_detail,
            </if>
            <if test ='null != alreadyIncomeApp'>
                already_income_app,
            </if>
            <if test ='null != createTime'>
                create_time,
            </if>
            <if test ='null != updateTime'>
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test ='null != modelName'>
                #{modelName},
            </if>
            <if test ='null != modelDetail'>
                #{modelDetail},
            </if>
            <if test ='null != alreadyIncomeApp'>
                #{alreadyIncomeApp},
            </if>
            <if test ='null != createTime'>
                #{createTime},
            </if>
            <if test ='null != updateTime'>
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_template
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdTemplateEntity">
        UPDATE third_template
        <set>
            <if test ='null != modelName'>model_name = #{modelName},</if>
            <if test ='null != modelDetail'>model_detail = #{modelDetail},</if>
            <if test ='null != alreadyIncomeApp'>already_income_app = #{alreadyIncomeApp},</if>
            <if test ='null != createTime'>create_time = #{createTime},</if>
            <if test ='null != updateTime'>update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_template
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_template
        <if test=' null != name and name != ""'> WHERE model_name like concat('%',#{name},'%')</if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_template
        <if test=' null != name and name != ""'> WHERE model_name like concat('%',#{name},'%')</if>
    </select>

</mapper>