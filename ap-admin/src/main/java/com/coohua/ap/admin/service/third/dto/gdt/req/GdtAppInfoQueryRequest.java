package com.coohua.ap.admin.service.third.dto.gdt.req;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Data
public class GdtAppInfoQueryRequest {
    private Long member_id;
    private List<FilterBean> filtering;
    private Integer page;
    private Integer page_size;

    @Data
    public static class FilterBean{
        String field;
        String operator;
        String[] values;
    }
}
