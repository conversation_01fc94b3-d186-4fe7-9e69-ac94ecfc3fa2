package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.service.ExceptionUserConfigService;
import com.coohua.ap.admin.utils.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/10/28
 */
@Slf4j
@RequestMapping("exUser")
@RestController
public class ExceptionUserConfigController {

    @Autowired
    private ExceptionUserConfigService exceptionUserConfigService;

    @RequestMapping("/list")
    @ResponseBody
    public Page<ExUserConfigVo> list(Page<ExUserConfigVo> page, HttpServletRequest request) {
        exceptionUserConfigService.queryList(page, SessionUtils.getUserProduct(request));
        return page;
    }

    @RequestMapping("/addOne")
    public BaseResponse add(@RequestBody ExUserConfigRequest exUserConfigRequest,
                            HttpServletRequest request){
        exceptionUserConfigService.insert(exUserConfigRequest,SessionUtils.getUserProduct(request));
        return new BaseResponse(0);
    }

    @RequestMapping("/updateOne")
    public BaseResponse update(@RequestBody ExUserConfigRequest exUserConfigRequest,
                               HttpServletRequest request){
        exceptionUserConfigService.update(exUserConfigRequest,SessionUtils.getUserProduct(request));
        return new BaseResponse(0);
    }

    @RequestMapping("/switchFlag")
    public BaseResponse switchFlag(@RequestParam("id")Integer id,
                                   @RequestParam("switchFlag")Integer switchFlag,
                                   HttpServletRequest request){
        exceptionUserConfigService.switchFlag(id,switchFlag);
        return new BaseResponse(0);
    }

}
