package com.coohua.ap.admin.constants;

import com.coohua.ap.admin.controller.vo.CSJReportVo;
import com.coohua.ap.admin.controller.vo.DefaultReportVO;
import com.coohua.ap.admin.controller.vo.GDTReportVo;

public enum AdReportEnum {

    WALK_PAY(new CSJReportVo("19461","2ee157d157635afb71bc24c5b5b68da6"),1,"穿山甲",1,"走路宝"),
    COOHUA_NEWS(new CSJReportVo("912","e57887420e70f7617985144fe0811e29"),1,"穿山甲",2,"酷划新闻"),
    KLSJ(new GDTReportVo("************","F8kVOpvJd7tBPpUk0KRhKtFd4nupsBlr"),2,"广点通",1,"恐龙世界"),
    ;

    /**
     * 请求数据封装对象
     */
    private DefaultReportVO defaultReportVO;
    /**
     * 数据源：1-穿山甲
     */
    private int dataSource;
    /**
     * 数据源描述
     */
    private String dataSourceDesc;
    /**
     * 账户id(一个数据源下可能有多个账户)
     */
    private int accountId;
    /**
     * 账户描述
     */
    private String accountDesc;

    AdReportEnum(DefaultReportVO defaultReportVO,int dataSource,String dataSourceDesc,int accountId,String accountDesc) {
        this.defaultReportVO = defaultReportVO;
        this.dataSource = dataSource;
        this.dataSourceDesc = dataSourceDesc;
        this.accountId = accountId;
        this.accountDesc = accountDesc;
    }

    public static AdReportEnum findAdReportEnumBySourceId(int sourceId){
        if(sourceId==0){
            return null;
        }
        for(AdReportEnum adReportEnum:AdReportEnum.values()){
            if(adReportEnum.getDataSource()==sourceId){
                return adReportEnum;
            }
        }
        return null;
    }

    public DefaultReportVO getDefaultReportVO() {
        return defaultReportVO;
    }

    public int getDataSource() {
        return dataSource;
    }

    public String getDataSourceDesc() {
        return dataSourceDesc;
    }

    public int getAccountId() {
        return accountId;
    }

    public String getAccountDesc() {
        return accountDesc;
    }
}
