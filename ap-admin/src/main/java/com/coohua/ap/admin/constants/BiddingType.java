package com.coohua.ap.admin.constants;

/**
 * <AUTHOR>
 * @since 2022/8/1
 */
public enum BiddingType {
    CLIENT(1,"客户端竞价"),
    SERVER(2,"服务端竞价"),
    ;

    private Integer type;
    private String desc;


    public static BiddingType getByType(Integer code){
        for (BiddingType type : BiddingType.values()){
            if (type.type.equals(code)){
                return type;
            }
        }
        throw new RuntimeException("不存在的竞价类型..");
    }

    BiddingType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
