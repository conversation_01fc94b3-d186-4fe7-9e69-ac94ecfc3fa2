package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2021/10/28
 */
@Data
public class ExUserConfigVo {

    /**
     * 配置主键id
     */
    private Integer id;

    /**
     * 内部产品appid
     */
    private Integer product;

    /**
     * 开启状态
     */
    private Integer state;

    /**
     * 渠道
     */
    private String channel;

    /**
     * ip
     */
    private String ip;

    /**
     * 机型
     */
    private String model;

    /**
     * 手机厂商
     */
    private String phone;

    /**
     * 视频上限次数
     */
    private Integer videoLimit;

    /**
     * 提现比例
     */
    private Integer withdrawRate;

    /**
     * score_rate
     */
    private Integer scoreRate;

    private Integer ocpcType;
    private Integer ocpcChannel;
    private Integer ruleLevel;

    /**
     * create_time
     */
    private String createTime;

    /**
     * update_time
     */
    private String updateTime;
}
