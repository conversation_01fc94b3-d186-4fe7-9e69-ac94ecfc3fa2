package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.AdPosView;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.model.AdPosModel;
import com.coohua.ap.admin.service.AdPosService;
import com.coohua.ap.admin.utils.SessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.ap.admin.controller
 * @create_time 2019-11-04
 */
@RestController
@RequestMapping("adpos")
public class AdPosController {

    @Autowired
    private AdPosService adPosService;

    @RequestMapping("list")
    public BaseResponse list(@RequestParam("name") String name,
                             @RequestParam("id") String id,
                             Page<AdPosView> page, HttpServletRequest request) {

        int product = SessionUtils.getUserProduct(request);
        adPosService.pageListByProduct(name, id, product, page);

        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);

        return baseResponse;
    }


    @RequestMapping("addOne")
    public BaseResponse addOne(@RequestParam("name") String name,
                               @RequestParam("extendInfo") String extendInfo,
                               HttpServletRequest request) {
        AdPosView view = new AdPosView();
        view.setName(name);
        view.setExtendInfo(extendInfo);
        view.setProduct(SessionUtils.getUserProduct(request));
        adPosService.insertAdPos(view);
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("updateOne")
    public BaseResponse updateOne(@RequestParam("name") String name,
                                  @RequestParam("extendInfo") String extendInfo,
                                  @RequestParam("id") int id,
                                  HttpServletRequest request) {
        BaseResponse response = new BaseResponse(0);
        AdPosModel model = new AdPosModel();
        model.setName(name);
        model.setExtendInfo(extendInfo);
        model.setId(id);
        adPosService.updateAdPos(model);
        response.setData("ok");

        return response;
    }

    @RequestMapping("listByApp")
    public BaseResponse listByApp(HttpServletRequest request) {
        BaseResponse response = new BaseResponse(0);
        response.setData(adPosService.queryByProduct(SessionUtils.getUserProduct(request)));
        return response;
    }

}
