package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/6/28
 */
@Data
public class AdTypeConfigVo {
    // 1014
    private Integer targetType;
    // 1015_50|1008_50
    private String adType;
    // default 1
    private Integer repeat;
    // 1015_100
    private List<String> layer;

    // 1-基础单层 2-瀑布流 3-打底
    private Integer configType;
}
