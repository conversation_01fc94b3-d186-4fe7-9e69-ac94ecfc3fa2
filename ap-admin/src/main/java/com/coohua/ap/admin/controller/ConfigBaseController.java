package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.service.ConfigBaseService;
import com.coohua.ap.admin.utils.ParamChecker;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.base.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <pre>
 *
 * <hr/>
 * Package Name : com.coohua.nap.web
 * Project Name : nap
 * Created by <PERSON><PERSON><PERSON> on 2018/3/13 上午10:38
 * </pre>
 */
@Controller
@RequestMapping("/config/base")
public class ConfigBaseController extends BaseController {

    @Autowired
    private ConfigBaseService configBaseService;

    @RequestMapping("/list")
    @ResponseBody
    private Page<ConfigBaseVO> list(@RequestParam("type") int type,
                                    @RequestParam("name") String name,
                                    @RequestParam("state") int state,
                                    Page<ConfigBaseVO> page, HttpServletRequest request) {
        configBaseService.listConfig(page, SessionUtils.getUserProduct(request), type, name, state);
        return page;
    }


    @RequestMapping("/queryById")
    @ResponseBody
    private BaseResponse queryById(@RequestParam("id") int id,
                                    HttpServletRequest request) {
        ConfigBaseVO vo = configBaseService.queryById(id);
        return BaseResponse.build(vo);
    }

    @RequestMapping("/save")
    @ResponseBody
    private BaseResponse save(@RequestBody ConfigBaseVO configBaseVO,
                              HttpServletRequest request) {
        configBaseVO.setProduct(SessionUtils.getUserProduct(request));
        ParamChecker.isConfigBaseVoValid(configBaseVO);
        ConfigBaseVO vo = configBaseService.saveConfig(configBaseVO);
        if (vo == null) {
            throw new BusinessException(400, "配置格式有误");
        }
        return BaseResponse.build(vo);
    }

    @RequestMapping("/update")
    @ResponseBody
    private BaseResponse update(@RequestBody ConfigBaseVO configBaseVO,
                                HttpServletRequest request) {
        ParamChecker.isConfigBaseVoValid(configBaseVO);
        ConfigBaseVO vo = configBaseService.updateConfig(configBaseVO);
        if (vo == null) {
            throw new BusinessException(400, "配置格式有误");
        }
        return BaseResponse.build(vo);
    }

    @RequestMapping("/updateState")
    @ResponseBody
    private BaseResponse updateState(@RequestBody ConfigBaseVO configBaseVO, HttpServletRequest request) {
        try {
            ConfigBaseVO configBaseVO1 = configBaseService.updateConfigState(configBaseVO);
            return configBaseVO1 != null ? BaseResponse.SUCCESS : BaseResponse.build(500, "更新状态异常");
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.SYSTEM_ERROR;
        }
    }

    @RequestMapping("/configType")
    @ResponseBody
    private BaseResponse configType(){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(ConfigType.list());
        return baseResponse;
    }
}
