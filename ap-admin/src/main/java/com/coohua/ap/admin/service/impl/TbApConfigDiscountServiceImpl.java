package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.CommonStatusEnum;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.ThirdPosAdView;
import com.coohua.ap.admin.mapper.ap.TbApConfigDiscountMapper;
import com.coohua.ap.admin.mapper.ap.TbApThirdPlatformTypeMapper;
import com.coohua.ap.admin.model.TbApConfigDiscount;
import com.coohua.ap.admin.model.TbApThirdPlatformTypeConfig;
import com.coohua.ap.admin.service.TbApConfigDiscountService;
import com.coohua.ap.base.builder.RedisBuilder;
import com.coohua.ap.base.constants.RedisConstants;
import com.coohua.ap.base.domain.ConfigVersion;
import com.coohua.ap.base.exception.BusinessException;
import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.base.vo.ApThirdPlatformTypeVo;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【tb_ap_config_discount(打折系数配置)】的数据库操作Service实现
 * @createDate 2024-12-04 11:01:36
 */
@Service
@Slf4j
public class TbApConfigDiscountServiceImpl
        implements TbApConfigDiscountService {

    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    @Resource
    private TbApConfigDiscountMapper tbApConfigDiscountMapper;
    @Autowired
    private TbApThirdPlatformTypeMapper tbApThirdPlatformTypeMapper;

    @Override
    public Page<TbApConfigDiscount> listConfig(int pageNo, int pageSize, ApConfigDiscountVo discountVo, Page<TbApConfigDiscount> page) {

        int from = (pageNo - 1) * pageSize;
        List<TbApConfigDiscount> list = tbApConfigDiscountMapper.queryList(from, pageSize, discountVo.getDiscountName(), discountVo.getIsEnabled());

        page.setItems(list);
        page.setCount(tbApConfigDiscountMapper.queryCount(discountVo.getDiscountName(), discountVo.getIsEnabled()));
        return page;
    }

//    @PostConstruct
//    public void refreshRedis() {
//        List<TbApThirdPlatformTypeConfig> tbApThirdPlatformTypeConfigs = tbApThirdPlatformTypeMapper.selectAll();
//        try  {
//            Set<Integer> collect = tbApThirdPlatformTypeConfigs.stream().map(TbApThirdPlatformTypeConfig::getAdType).collect(Collectors.toSet());
//            for (Integer s : collect) {
//                apClusterRedisService.sadd(RedisConstants.CONFIG_DISCOUNT_AD_POS_TYPE, String.valueOf(s));
//            }
//        } catch (Exception e) {
//            log.error("saveToRedis error", e);
//            throw new BusinessException(500, "同步缓存失败，请重试！");
//        }
//    }

    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @Override
    public ApConfigDiscountVo saveOrUpdateConfig(ApConfigDiscountVo discountVo) {
        TbApConfigDiscount discount = convertToDiscount(discountVo);
        discount.setIsDeleted(0);
        if (discount.getId() == null) {
            discount.setCreateTime(LocalDateTime.now());
        }
        discount.setUpdateTime(LocalDateTime.now());
        if (discount.getId() != null) {
            tbApConfigDiscountMapper.updateById(discount);
        } else {
            tbApConfigDiscountMapper.insert(discount);
            discountVo.setId(discount.getId());
        }

        // 更新redis
        if (CommonStatusEnum.DISABLED.getCode() == discountVo.getIsEnabled()) {
            deleteFromRedis(discountVo.getId());
        } else {
            saveToRedis(discountVo.getId());
        }
        return discountVo;
    }

    @Transactional(value = "apdatasourceDataSourceTransactionManager")
    @Override
    public ApConfigDiscountVo deleteConfig(ApConfigDiscountVo discountVo) {
        TbApConfigDiscount discount = new TbApConfigDiscount();
        discount.setId(discountVo.getId());
        discount.setIsDeleted(1);
        discount.setUpdateTime(LocalDateTime.now());

        tbApConfigDiscountMapper.updateById(discount);

        deleteFromRedis(discountVo.getId());

        return discountVo;
    }

    private void saveToRedis(Long id) {
        ApConfigDiscountVo discount = queryById(id);
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            List<String> keys = getDiscountProductKeys(discount.getProduct());
            Pipeline pipelined = jedis.pipelined();
            for (String key : keys) {
                pipelined.hset(key, String.valueOf(discount.getId()), JSONObject.toJSONString(discount));
            }
            pipelined.sync();
        } catch (Exception e) {
            log.error("saveToRedis error", e);
            throw new BusinessException(500, "同步缓存失败，请重试！");
        }
    }

    private void deleteFromRedis(Long id) {
        ApConfigDiscountVo discountVo = queryById(id);
        try (Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION)) {
            Pipeline pipelined = jedis.pipelined();
            List<String> keys = getDiscountProductKeys(discountVo.getProduct());
            for (String key : keys) {
                pipelined.hdel(key, String.valueOf(id));
            }
            pipelined.sync();
        } catch (Exception e) {
            log.error("saveToRedis error", e);
            throw new BusinessException(500, "同步缓存失败，请重试！");
        }
    }

    /**
     * 获取打折配置的产品key
     * @param product
     * @return
     */
    private List<String> getDiscountProductKeys(String product) {
        List<String> keys = new ArrayList<>();
        if ("all".equals(product)) {
            keys.add(RedisBuilder.buildConfigDiscount(product));
        } else {
            List<String> productList = Arrays.stream(product.split(",")).collect(Collectors.toList());
            for (String s : productList) {
                keys.add(RedisBuilder.buildConfigDiscount(s));
            }
        }
        return keys;
    }

    @Override
    public ApConfigDiscountVo updateConfigState(ApConfigDiscountVo discountVo) {

        TbApConfigDiscount discount = new TbApConfigDiscount();
        discount.setId(discountVo.getId());
        discount.setIsEnabled(discountVo.getIsEnabled());
        discount.setUpdateTime(LocalDateTime.now());
        tbApConfigDiscountMapper.updateById(discount);
        if (CommonStatusEnum.DISABLED.getCode() == discountVo.getIsEnabled()) {
            deleteFromRedis(discountVo.getId());
        } else {
            saveToRedis(discountVo.getId());
        }

        return discountVo;
    }

    @Override
    public ApConfigDiscountVo queryById(Long id) {
        ApConfigDiscountVo vo = convertToDiscountVo(tbApConfigDiscountMapper.queryById(id));
        return vo;
    }

    @Override
    public List<ApThirdPlatformTypeVo> getPlatformAdPos() {
        List<TbApThirdPlatformTypeConfig> tbApThirdPlatformTypeConfigs = tbApThirdPlatformTypeMapper.selectAll();
        if (tbApThirdPlatformTypeConfigs.isEmpty()) {
            return Collections.emptyList();
        }
        List<ApThirdPlatformTypeVo> resultList = new ArrayList<>();
        for (TbApThirdPlatformTypeConfig tbApThirdPlatformTypeConfig : tbApThirdPlatformTypeConfigs) {
            ApThirdPlatformTypeVo apThirdPlatformTypeVo = new ApThirdPlatformTypeVo();
            apThirdPlatformTypeVo.setAdTypeName(tbApThirdPlatformTypeConfig.getAdTypeName());
            apThirdPlatformTypeVo.setAdTypeCode(tbApThirdPlatformTypeConfig.getAdType());
            resultList.add(apThirdPlatformTypeVo);
        }
        return resultList;
    }

    private ApConfigDiscountVo convertToDiscountVo(TbApConfigDiscount discount) {
        ApConfigDiscountVo vo = new ApConfigDiscountVo();
        vo.setId(discount.getId());
        vo.setDiscountName(discount.getDiscountName());
        vo.setWaterFallDiscountValue(discount.getWaterFallDiscountValue());
        vo.setBidDiscountValue(discount.getBidDiscountValue());
        vo.setOs(discount.getOs());
        vo.setPlatformCode(discount.getPlatformCode());
        vo.setProduct(discount.getProduct());
        vo.setAdPos(discount.getAdPos());
        vo.setBiddingType(discount.getBiddingType());
        vo.setAppVersionOrientation(JSONObject.parseObject(discount.getAppVersionOrientation(), ConfigVersion.class));
        vo.setSdkVersionOrientation(JSONObject.parseObject(discount.getSdkVersionOrientation(), ConfigVersion.class));
        vo.setChannelIdOrientation(discount.getChannelIdOrientation());
        vo.setSkipChannelOrientation(discount.getSkipChannelOrientation());
        vo.setAbTestOrientation(discount.getAbTestOrientation());
        vo.setUserSourceOrientation(discount.getUserSourceOrientation());
        vo.setPriority(discount.getPriority());
        vo.setIsEnabled(discount.getIsEnabled());

        return vo;
    }

    private TbApConfigDiscount convertToDiscount(ApConfigDiscountVo discountVo) {
        TbApConfigDiscount discount = new TbApConfigDiscount();
        discount.setId(discountVo.getId());
        discount.setDiscountName(discountVo.getDiscountName());
        discount.setWaterFallDiscountValue(discountVo.getWaterFallDiscountValue());
        discount.setBidDiscountValue(discountVo.getBidDiscountValue());
        discount.setOs(discountVo.getOs());
        discount.setPlatformCode(discountVo.getPlatformCode());
        discount.setProduct(discountVo.getProduct());
        discount.setAdPos(discountVo.getAdPos());
        discount.setBiddingType(discountVo.getBiddingType());
        if (discountVo.getAppVersionOrientation() != null) {
            discount.setAppVersionOrientation(JSONObject.toJSONString(discountVo.getAppVersionOrientation()));
        }
        if (discountVo.getSdkVersionOrientation() != null) {
            discount.setSdkVersionOrientation(JSONObject.toJSONString(discountVo.getSdkVersionOrientation()));
        }
        discount.setChannelIdOrientation(discountVo.getChannelIdOrientation());
        discount.setSkipChannelOrientation(discountVo.getSkipChannelOrientation());
        discount.setAbTestOrientation(discountVo.getAbTestOrientation());
        discount.setUserSourceOrientation(discountVo.getUserSourceOrientation());
        discount.setPriority(discountVo.getPriority());
        discount.setIsEnabled(discountVo.getIsEnabled());
        return discount;
    }
}




