package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdAppLoad;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description 应用列表
 * <AUTHOR>
 * @date 2021-08-31
 */
@Mapper
@Repository
public interface ThirdAppLoadMapper {

    /**
     * 新增
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int insert(ThirdAppLoad thirdAppLoad);

    /**
     * 刪除
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int delete(int id);

    /**
     * 更新
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int update(ThirdAppLoad thirdAppLoad);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR>
     * @date 2021/08/31
     **/
    ThirdAppLoad load(int id);

    /**
     * 查询 分页查询
     * <AUTHOR>
     * @date 2021/08/31
     **/
    List<ThirdAppLoad> pageList(@Param("offset") int offset,@Param("pageSize") int pagesize, @Param("product") Integer appId,
                                @Param("batchNo") String batchNo);

    /**
     * 查询 分页查询 count
     * <AUTHOR>
     * @date 2021/08/31
     **/
    int pageListCount(@Param("offset") int offset,@Param("pageSize") int pagesize,
                      @Param("product") Integer appId,
                      @Param("batchNo") String batchNo);

    @Select({"select count(1) from third_app_load where app_id = #{appId} and company_id = #{companyId}"})
    int existAppCheck(@Param("appId")Long appId, @Param("companyId")Integer companyId);

    @Delete({"delete from third_app_load where batch_no = #{batchNo}"})
    void deleteBatch(@Param("batchNo") String batchNo);

    @Delete({"delete from third_app_load where company_id = #{company}"})
    void deleteBatchByCpy(@Param("company") Integer company);

    @Select({"<script>",
            "select * from third_app_load where " +
                    " id in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    List<ThirdAppLoad> queryByIds(@Param("ids") List<Integer> ids);

    @Select({"<script>",
            "select * from third_app where " +
                    " id in ",
            "<foreach collection='ids' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"})
    List<ThirdAppEntity> queryCurrentByIds(@Param("ids") List<Integer> ids);

    @Delete({"truncate table third_app_load"})
    void clear();
}