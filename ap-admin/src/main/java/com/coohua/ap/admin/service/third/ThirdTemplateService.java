package com.coohua.ap.admin.service.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.ap.admin.constants.OpType;
import com.coohua.ap.admin.controller.vo.AdTemplateVo;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.ThirdTemplateView;
import com.coohua.ap.admin.mapper.ap.ThirdTemplateMapper;
import com.coohua.ap.admin.model.ThirdTemplateEntity;
import com.coohua.ap.admin.utils.third.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/1/7
 */
@Slf4j
@Service
public class ThirdTemplateService {

    @Resource
    private ThirdTemplateMapper thirdTemplateMapper;
    @Autowired
    private ThirdLogService thirdLogService;

    public void list(Integer offset, Integer limit, String name, Page<ThirdTemplateView> page){
        List<ThirdTemplateEntity> entityList = thirdTemplateMapper.pageList(offset,limit,name);
        page.setItems(entityList.stream().map(thirdTemplateEntity -> {
            ThirdTemplateView view = new ThirdTemplateView();
            view.setId(thirdTemplateEntity.getId());
            view.setModelName(thirdTemplateEntity.getModelName());
            view.setTemplateInfoList(JSONArray.parseArray(thirdTemplateEntity.getModelDetail(),AdTemplateVo.class));
            view.setCreateTime(DateUtil.dateToString(thirdTemplateEntity.getCreateTime(),DateUtil.DATETIME_PATTERN));
            view.setUpdateTime(DateUtil.dateToString(thirdTemplateEntity.getUpdateTime(),DateUtil.DATETIME_PATTERN));
            view.setAlreadyIncomeApp(thirdTemplateEntity.getAlreadyIncomeApp());
            return view;
        }).collect(Collectors.toList()));
        page.setCount(thirdTemplateMapper.pageListCount(offset,limit,name));
    }

    public void insert(String modelName,String template,String name,String account){
        ThirdTemplateEntity entity = new ThirdTemplateEntity();
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setModelName(modelName);
        entity.setModelDetail(template);

        int count = thirdTemplateMapper.countExist(entity.getModelName());
        if (count > 0){
            throw new RuntimeException("模板名称已存在，请重新填写");
        }

        thirdTemplateMapper.insert(entity);
        // 保存日志
        thirdLogService.saveLog(account,name, OpType.ADD_TEMPLATE,entity.getId(),null, JSON.toJSONString(entity));
    }

    public void update(Integer id,String modelName,String template,String name,String account){
        ThirdTemplateEntity entity = thirdTemplateMapper.load(id);

        Date now = new Date();
        ThirdTemplateEntity record = new ThirdTemplateEntity();
        record.setId(entity.getId());
        record.setModelName(modelName);
        record.setModelDetail(template);
        record.setUpdateTime(now);

        thirdTemplateMapper.update(record);

        thirdLogService.saveLog(account,name,OpType.UPDATE_TEMPLATE,entity.getId(),JSON.toJSONString(entity),JSON.toJSONString(record));
    }

    public void copyOne(Integer id,String name,String account){
        ThirdTemplateEntity entity = thirdTemplateMapper.load(id);
        Optional.ofNullable(entity).orElseThrow(() -> new RuntimeException("模板不存在"));
        ThirdTemplateEntity newEntity = new ThirdTemplateEntity();
        BeanUtils.copyProperties(entity,newEntity);
        newEntity.setModelName(entity.getModelName() + "-副本");
        Date now = new Date();
        newEntity.setCreateTime(now);
        newEntity.setUpdateTime(now);
        thirdTemplateMapper.insert(newEntity);
        thirdLogService.saveLog(account,name,OpType.ADD_TEMPLATE,entity.getId(),null,JSON.toJSONString(entity));
    }

    public void delete(Integer id,String name,String account){
        ThirdTemplateEntity entity = thirdTemplateMapper.load(id);
        Optional.ofNullable(entity).orElseThrow(() -> new RuntimeException("模板不存在"));
        thirdTemplateMapper.delete(entity.getId());
        thirdLogService.saveLog(account,name,OpType.UPDATE_TEMPLATE,entity.getId(),JSON.toJSONString(entity),null);
    }
}
