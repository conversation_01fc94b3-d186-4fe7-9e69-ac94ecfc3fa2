package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.AdInfo;
import com.coohua.ap.admin.model.TbApBidding;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description tb_ap_bidding
 * <AUTHOR> @date 2021-10-13
 */
@Mapper
@Repository
public interface TbApBiddingMapper {

    /**
     * 新增
     * <AUTHOR> @date 2021/10/13
     **/
    int insert(TbApBidding tbApBidding);

    @Insert("insert into tb_ap_bidding_clod(id,product,ad_pos_type,ad_id,switch_flag,del_flag,create_time,update_time,start_ecpm,end_ecpm,priority,bidding_type,play_type) " +
            " values (#{id},#{product},#{adPosType},#{adId},#{switchFlag},#{delFlag},#{createTime},#{updateTime},#{startEcpm},#{endEcpm},#{priority},#{biddingType},#{playType})")
    int insertCold(TbApBidding tbApBidding);

    /**
     * 刪除
     * <AUTHOR> @date 2021/10/13
     **/
    int delete(int id);

    /**
     * 更新
     * <AUTHOR> @date 2021/10/13
     **/
    int update(TbApBidding tbApBidding);


    /**
     * 批量更新
     * <AUTHOR> @date 2021/10/13
     **/
    int batchUpdate(List<TbApBidding> tbApBiddingList);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR> @date 2021/10/13
     **/
    TbApBidding load(int id);

    /**
     * 查询 分页查询
     * <AUTHOR> @date 2021/10/13
     **/
    List<TbApBidding> pageList(@Param("offset") int offset,
                               @Param("pageSize") int pagesize,
                               @Param("product")Integer product,
                               @Param("adId") String adId);

    /**
     * 查询 分页查询 count
     * <AUTHOR> @date 2021/10/13
     **/
    int pageListCount(@Param("offset") int offset,
                      @Param("pageSize") int pagesize,
                      @Param("product")Integer product,
                      @Param("adId") String adId);


    @Select("select count(1) from tb_ap_bidding where  del_flag = 1 and ad_id = #{adId}")
    Integer isBiddingAd(@Param("adId")Long adId);

    @Select("select * from tb_ap_bidding where ad_id = #{adId} and product = #{product}")
    List<TbApBidding> queryByAdIdAndProduct(@Param("adId")Long adId,@Param("product") Integer product);

    @Select({"<script>",
            "SELECT * FROM tb_ap_bidding WHERE product = #{product} and ad_id in ",
            "<foreach collection='adIds' index='index' item='item' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"
    })
    List<TbApBidding> queryAdIdListAndProduct(@Param("adIds")List<Integer> adId,@Param("product") Integer product);

    @Select("select * from `bp-ap`.tb_ap_bidding bid left join `bp-ap`.ad_info info on bid.ad_id = info.ad_id " +
            "where bid.product = #{product} and os = #{os} and lock_area != 1 and switch_flag = #{switchFlag} and del_flag = 1 and ad_type like concat('%',#{adType},'%') and ad_name like '%perk%';")
    List<TbApBidding> queryReplaceBidding(@Param("product")Integer product, @Param("os")Integer os, @Param("adType") Integer adType, @Param("switchFlag") Integer switchFlag);


    @Select("select * from `bp-ap`.tb_ap_bidding bid left join `bp-ap`.ad_info info on bid.ad_id = info.ad_id " +
            "where bid.product = #{product} and os = #{os} and lock_area != 1 and switch_flag = #{switchFlag} and del_flag = 1 and ad_type like concat('%',#{adType},'%') and ad_name like '%perk%';")
    List<AdInfo> queryAdInfo(@Param("product")Integer product, @Param("os")Integer os, @Param("adType") Integer adType, @Param("switchFlag") Integer switchFlag);

}