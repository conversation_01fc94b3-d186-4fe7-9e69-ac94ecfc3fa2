package com.coohua.ap.admin.controller.vo;

import lombok.Data;

/**
 * <pre>
 *  用户标签具体信息
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/12/31
 */
@Data
public class TagInfoVO {
    /**
     * <pre>
     * 版本定向：可指定为单个版本，也可以是一个区间，也可以是多个区间及单个版本的组合。
     * 格式：
     *      单个版本：1.0.0.0
     *      单个区间：1.0.0.0-2.0.0.0
     *      组合：1.0.0.0-2.0.0.0,2.2.0.0,3.0.0.0-3.3.0.0
     * 规则：不填视为不限制，否则按以上规则匹配
     * </pre>
     */
    private String version;
    /**
     * 尾号定向：多个尾号之间用半角逗号隔开，逻辑上采用UserID的末尾匹配，如果配置了多个，则他们之间的关系是"或"的关系
     * 格式：1,2,34,123,9
     * 规则：不填视为不限制，否则按以上逻辑匹配
     */
    private String tailNumber;
    /**
     * 平台定向
     * 规则：0-不限制，1-Android，2-iOS
     */
    private Integer os;
    /**
     * 是否适配受限区
     * 规则：0-不限制，1-只适配受限区，2-只适配非受限区
     */
    private Integer filterRegion;
    /**
     * 匿名用户定向
     * 规则：0-不限制，1-只适配匿名用户，2-只适配非匿名用户
     */
    private Integer anonymous;
    /**
     * 用户马甲包名定向：多个包名之前用逗号隔开，必须填完整的包名，填写多个包名为或的关系
     * 规则：不填视为不限制，否则按上述逻辑匹配
     */
    private String userPackage;
    /**
     * 用户渠道定向：多个渠道用逗号隔开，填写定义的渠道的开头，匹配时按开头匹配
     * 规则：不填视为不限制，否则按上述逻辑匹配
     */
    private String userChannel;
}
