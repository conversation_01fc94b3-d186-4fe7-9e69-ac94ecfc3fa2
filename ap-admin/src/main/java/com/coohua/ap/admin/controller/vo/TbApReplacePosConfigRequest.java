package com.coohua.ap.admin.controller.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TbApReplacePosConfigRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String strategyName;

    private String os;

    private String product;

    private String adTypeName;

    private String dsp;

    private List<TbApReplacePosPlatformConfigVO> platformConfig;

    private String extend1;

    private String extend2;

    private Byte isEnabled;

    private Byte isDeleted;

}
