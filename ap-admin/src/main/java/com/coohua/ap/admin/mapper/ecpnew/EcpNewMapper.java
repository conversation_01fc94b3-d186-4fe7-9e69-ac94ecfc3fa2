package com.coohua.ap.admin.mapper.ecpnew;

import com.coohua.ap.admin.model.EcpLinkEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName: EcpNewMapper
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/8/12
 * @Version: 1.0.0
 **/
public interface EcpNewMapper {

    @Select("select id ,pc_customer_name as customerName,link, chose_app_list as appStr,create_name as createName " +
            "from pc_ad_link " +
            "where audit_type = 1 and ad_up_status =1")
    List<EcpLinkEntity> getOpenLink();
}
