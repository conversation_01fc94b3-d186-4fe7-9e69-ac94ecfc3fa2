<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ThirdAdPosLoadMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.ThirdAdPosLoad" >
        <result column="id" property="id" />
        <result column="batch_no" property="batchNo" />
        <result column="platform_code" property="platformCode" />
        <result column="company_id" property="companyId" />
        <result column="main_body" property="mainBody" />
        <result column="application_id" property="applicationId" />
        <result column="app_id" property="appId" />
        <result column="app_name" property="appName" />
        <result column="pos_id" property="posId" />
        <result column="pos_name" property="posName" />
        <result column="pos_status" property="posStatus" />
        <result column="ad_type" property="adType" />
        <result column="income_status" property="incomeStatus" />
        <result column="ad_price" property="adPrice" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                batch_no,
                platform_code,
                company_id,
                main_body,
                application_id,
                app_id,
                app_name,
                pos_id,
                pos_name,
                pos_status,
                ad_type,
                income_status,
                ad_price,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.ThirdAdPosLoad">
        INSERT INTO third_ad_pos_load
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != batchNo and '' != batchNo">
                batch_no,
            </if>
            <if test="null != platformCode and '' != platformCode">
                platform_code,
            </if>
            <if test="null != companyId and '' != companyId">
                company_id,
            </if>
            <if test="null != mainBody and '' != mainBody">
                main_body,
            </if>
            <if test="null != applicationId and '' != applicationId">
                application_id,
            </if>
            <if test="null != appId and '' != appId">
                app_id,
            </if>
            <if test="null != appName and '' != appName">
                app_name,
            </if>
            <if test="null != posId and '' != posId">
                pos_id,
            </if>
            <if test="null != posName and '' != posName">
                pos_name,
            </if>
            <if test="null != posStatus and '' != posStatus">
                pos_status,
            </if>
            <if test="null != adType and '' != adType">
                ad_type,
            </if>
            <if test="null != incomeStatus and '' != incomeStatus">
                income_status,
            </if>
            <if test="null != adPrice and '' != adPrice">
                ad_price,
            </if>
            <if test="null != createTime and '' != createTime">
                create_time,
            </if>
            <if test="null != updateTime and '' != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != batchNo and '' != batchNo">
                #{batchNo},
            </if>
            <if test="null != platformCode and '' != platformCode">
                #{platformCode},
            </if>
            <if test="null != companyId and '' != companyId">
                #{companyId},
            </if>
            <if test="null != mainBody and '' != mainBody">
                #{mainBody},
            </if>
            <if test="null != applicationId and '' != applicationId">
                #{applicationId},
            </if>
            <if test="null != appId and '' != appId">
                #{appId},
            </if>
            <if test="null != appName and '' != appName">
                #{appName},
            </if>
            <if test="null != posId and '' != posId">
                #{posId},
            </if>
            <if test="null != posName and '' != posName">
                #{posName},
            </if>
            <if test="null != posStatus and '' != posStatus">
                #{posStatus},
            </if>
            <if test="null != adType and '' != adType">
                #{adType},
            </if>
            <if test="null != incomeStatus and '' != incomeStatus">
                #{incomeStatus},
            </if>
            <if test="null != adPrice and '' != adPrice">
                #{adPrice},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM third_ad_pos_load
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.ThirdAdPosLoad">
        UPDATE third_ad_pos_load
        <set>
            <if test="null != batchNo and '' != batchNo">batch_no = #{batchNo},</if>
            <if test="null != platformCode and '' != platformCode">platform_code = #{platformCode},</if>
            <if test="null != companyId and '' != companyId">company_id = #{companyId},</if>
            <if test="null != mainBody and '' != mainBody">main_body = #{mainBody},</if>
            <if test="null != applicationId and '' != applicationId">application_id = #{applicationId},</if>
            <if test="null != appId and '' != appId">app_id = #{appId},</if>
            <if test="null != appName and '' != appName">app_name = #{appName},</if>
            <if test="null != posId and '' != posId">pos_id = #{posId},</if>
            <if test="null != posName and '' != posName">pos_name = #{posName},</if>
            <if test="null != posStatus and '' != posStatus">pos_status = #{posStatus},</if>
            <if test="null != adType and '' != adType">ad_type = #{adType},</if>
            <if test="null != incomeStatus and '' != incomeStatus">income_status = #{incomeStatus},</if>
            <if test="null != adPrice and '' != adPrice">ad_price = #{adPrice},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_ad_pos_load
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM third_ad_pos_load
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM third_ad_pos_load
    </select>

</mapper>