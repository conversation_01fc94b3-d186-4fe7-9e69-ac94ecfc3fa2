package com.coohua.ap.admin.controller.third;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdAppLoad;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.service.third.RefreshThirdInfoService;
import com.coohua.ap.admin.service.third.ThirdAppService;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.admin.utils.third.Strings;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

import static com.coohua.ap.admin.controller.vo.BaseResponse.CODE_SYS_ERROR;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@RestController("thirdAppController")
@RequestMapping("third/app")
public class AppController {


    @Autowired
    private ThirdAppService thirdAppService;
    @Autowired
    private RefreshThirdInfoService refreshThirdInfoService;

    @RequestMapping("list")
    @ResponseBody
    public BaseResponse list(@RequestParam("name") String appName,
                             @RequestParam(value = "os",required = false) Integer os,
                             @RequestParam(value = "thirdAppId",required = false) Long thirdAppId,
                             Page<ThirdAppEntity> page, HttpServletRequest request) {
        // 获取APP
        int product = SessionUtils.getUserProduct(request);
        page.setAppId(product);
        int from = (page.getPageNo() - 1) * page.getPageSize();
        thirdAppService.queryList(from,page.getPageSize(),appName,os,thirdAppId,page);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping("addOne")
    public BaseResponse addOne(@RequestBody ThirdAppEntity entityRequest, HttpServletRequest request) {
        try {
            thirdAppService.insert(entityRequest, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }

        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("updateOne")
    public BaseResponse updateOne(@RequestBody ThirdAppEntity entityRequest, HttpServletRequest request) {
        try {
            thirdAppService.update(entityRequest, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }

        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    /**
     * 修改APP状态
     * @param requestMap
     * @param request
     * @return
     */
    @RequestMapping(value = "switchFlag")
    public BaseResponse switchFlag(@RequestBody Map<String,String> requestMap, HttpServletRequest request){
        Integer id = Integer.valueOf(requestMap.get("id"));
        Integer state = Integer.valueOf(requestMap.get("state"));
        try {
            thirdAppService.updateSwitch(id,state, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }

        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping(value = "pullAll")
    public BaseResponse pullAllAppList(Integer companyId){
        BaseResponse response = new BaseResponse(0);
        response.setData(refreshThirdInfoService.synchronizeAppList(companyId));
        return response;
    }

    @RequestMapping(value = "copyThoseToNewApp")
    public BaseResponse copyAppList(@RequestBody List<Integer> ids,@Param("product")Integer product,HttpServletRequest request){
        BaseResponse response = new BaseResponse(0);
        log.info("Copy {} to Product {}",JSON.toJSONString(ids),product);
        thirdAppService.copyToNewRecord(ids,product);
        response.setData("Ok");
        return response;
    }

    @RequestMapping(value = "loadToSystemReal")
    public BaseResponse loadTempToSystem(@RequestBody List<Integer> ids,HttpServletRequest request){
        BaseResponse baseResponse = new BaseResponse(0);
        refreshThirdInfoService.synchTempToRealApp(ids);
        baseResponse.setData("ok");
        return baseResponse;
    }

    @RequestMapping(value = "loadTempToSystem")
    public BaseResponse loadTempToSystem(Integer companyId){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(refreshThirdInfoService.synchronizeAppListTemp(companyId));
        return baseResponse;
    }

    @RequestMapping(value = "removeTempFormSystem")
    public BaseResponse removeFromOurSystem(@Param("batchNo") String batchNo){
        BaseResponse baseResponse = new BaseResponse(0);
        refreshThirdInfoService.stopOrRemoveTempList(batchNo);
        baseResponse.setData("ok");
        return baseResponse;
    }

    @RequestMapping(value = "queryTempList")
    public BaseResponse queryTempList(@Param("batchNo")String batchNo,Page<ThirdAppLoad> page,HttpServletRequest request){
        int productId = SessionUtils.getUserProduct(request);
        page.setAppId(productId);
        int from = (page.getPageNo() - 1) * page.getPageSize();
        thirdAppService.queryListLoad(batchNo,from,page.getPageSize(),page);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping(value = "delete")
    public BaseResponse deleteThisApp(@Param("id")Integer id,HttpServletRequest request){
        thirdAppService.deleteApp(id,SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData("ok");
        return baseResponse;
    }
}
