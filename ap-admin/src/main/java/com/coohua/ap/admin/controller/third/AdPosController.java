package com.coohua.ap.admin.controller.third;

import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.service.third.RefreshThirdInfoService;
import com.coohua.ap.admin.service.third.ThirdAdPosService;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.admin.utils.third.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.coohua.ap.admin.controller.vo.BaseResponse.CODE_SYS_ERROR;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Slf4j
@RestController("thirdAdPosController")
@RequestMapping("third/adPos")
public class AdPosController {

    @Autowired
    private ThirdAdPosService thirdAdPosService;
    @Autowired
    private RefreshThirdInfoService refreshThirdInfoService;

    @RequestMapping("list")
    @ResponseBody
    public BaseResponse list(@RequestParam(value = "name",required = false) String appName,
                             @RequestParam(value = "thirdAppId",required = false) String appId,
                             @RequestParam(value = "posId" ,required = false) Long posId,
                             @RequestParam(value = "importStatus",required = false) Integer importStatus,
                             String posName, Integer adType,String queryDay, Page<ThirdPosAdView> page,
                             HttpServletRequest request) {
        // 获取产品
        int productId = SessionUtils.getUserProduct(request);
        page.setAppId(productId);
        int from = (page.getPageNo() - 1) * page.getPageSize();
        thirdAdPosService.list(from,page.getPageSize(),appName,appId,posName,adType,queryDay,posId,importStatus,page);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping("addOne")
    public BaseResponse addOne(@RequestBody ThirdPosAdRequest thirdPosAdRequest, HttpServletRequest request) {
        try {
            int product = SessionUtils.getUserProduct(request);
            thirdAdPosService.insert(thirdPosAdRequest, SessionUtils.getAccount(request),SessionUtils.getAccountName(request),product);
        }catch (Exception e){
            log.error("创建第三方广告位失败 thirdPosAdRequest : {} e : {}" , thirdPosAdRequest, e);
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }

        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping("queryAndCheck")
    public BaseResponse queryAndCheckPrice(@RequestBody List<Integer> ids,HttpServletRequest request){
        if (Lists.isEmpty(ids)){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData("请选择要导入的广告位！！");
            return response;
        }
        BaseResponse response = new BaseResponse(BaseResponse.CODE_OK);
        response.setData(thirdAdPosService.queryAdLoadPreList(ids));
        return response;
    }

    @RequestMapping("addBatch")
    public BaseResponse addBatch(@RequestBody ThirdPosAdBatchRequest thirdPosAdBatchRequest, HttpServletRequest request) {
        try {
            String batchNo = thirdAdPosService.insertBatch(thirdPosAdBatchRequest, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
            BaseResponse response = new BaseResponse(0);
            response.setData(batchNo);
            return response;
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }
    }

    @RequestMapping("updateOne")
    public BaseResponse updateOne(@RequestBody ThirdPosAdRequest thirdPosAdRequest, HttpServletRequest request) {
        try {
            thirdAdPosService.update(thirdPosAdRequest, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    /**
     * 修改APP状态
     * @param requestMap
     * @param request
     * @return
     */
    @RequestMapping(value = "switchFlag")
    public BaseResponse switchFlag(@RequestBody Map<String,String> requestMap, HttpServletRequest request){
        Integer id = Integer.valueOf(requestMap.get("id"));
        Integer state = Integer.valueOf(requestMap.get("state"));
        try {
            thirdAdPosService.updateSwitch(id,state, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData(e.getMessage());
            return response;
        }

        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }

    @RequestMapping(value = "loadToSystem")
    public BaseResponse loadToOurSystem(@RequestBody List<Integer> ids,
                                        @RequestParam("importType") Integer importType,
                                        @RequestParam("biddingImportType") Integer biddingImportType,
                                        HttpServletRequest request){
        if (Lists.isEmpty(ids)){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            response.setData("请选择要导入的广告位！！");
            return response;
        }
        try {
            thirdAdPosService.loadAdToOurSystem(ids,importType,biddingImportType, SessionUtils.getAccount(request),SessionUtils.getAccountName(request));
        }catch (Exception e){
            BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
            if (Strings.isNotBlank(e.getMessage())) {
                response.setData(e.getMessage());
            }else {
                response.setData("批量导入广告异常！");
            }
            return response;
        }
        BaseResponse response = new BaseResponse(0);
        response.setData("ok");
        return response;
    }


    @RequestMapping(value = "pullAll")
    public BaseResponse pullAllAppList(Integer appId, HttpServletRequest request){
        BaseResponse response = new BaseResponse(0);
        response.setData(refreshThirdInfoService.synchronizeAdPosList(appId,SessionUtils.getAccount(request),SessionUtils.getAccountName(request)));
        return response;
    }

    @RequestMapping(value = "queryTaskList")
    public BaseResponse queryTaskList(@Param("batchNo")String batchNo,Page<ThirdTaskQueenVo> page){
        int from = (page.getPageNo() - 1) * page.getPageSize();
        thirdAdPosService.taskList(from,page.getPageSize(),batchNo,page);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping(value = "retryTask")
    public BaseResponse retryTask(@Param("taskId")Integer taskId){
        Optional.ofNullable(taskId).orElseThrow(() -> new RuntimeException("任务ID不能为空"));
        thirdAdPosService.retry(taskId);
        return new BaseResponse(0);
    }

    @RequestMapping(value = "queryThirdAdCurrent")
    public BaseResponse queryThirdAdCurrent(@Param("code")Integer code,
                                            @Param("os")Integer os,
                                            Page<ThirdAdLowPriceVo> page,
                                            HttpServletRequest request){
        int product = SessionUtils.getUserProduct(request);
        Platform platform = Platform.getPlatform(code);
        thirdAdPosService.queryThirdList(page,platform,os,product);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

    @RequestMapping(value = "loadToSystemReal")
    public BaseResponse loadTempToSystem(@RequestBody List<Integer> ids,HttpServletRequest request){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData("ok");
        return baseResponse;
    }

    @RequestMapping(value = "loadTempToSystem")
    public BaseResponse loadTempToSystem(){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData("ok");
        return baseResponse;
    }

    @RequestMapping(value = "removeTempFormSystem")
    public BaseResponse removeFromOurSystem(){
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData("ok");
        return baseResponse;
    }


    @RequestMapping(value = "queryTempList")
    public BaseResponse queryTempList(@Param("code")Integer code,
                                            @Param("os")Integer os,
                                            Page<ThirdPosAdView> page,
                                            HttpServletRequest request){
        int product = SessionUtils.getUserProduct(request);
        Platform platform = Platform.getPlatform(code);
        BaseResponse baseResponse = new BaseResponse(0);
        baseResponse.setData(page);
        return baseResponse;
    }

}
