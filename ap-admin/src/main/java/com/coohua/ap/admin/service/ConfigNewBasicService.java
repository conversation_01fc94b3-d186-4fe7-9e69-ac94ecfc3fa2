package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mapper.ap.ConfigBaseMapper;
import com.coohua.ap.admin.mapper.ap.TbConfigNewFilterMapper;
import com.coohua.ap.admin.model.BaseConfigHistoryEntity;
import com.coohua.ap.admin.model.ConfigBaseEntity;
import com.coohua.ap.admin.model.TbConfigNewFilter;
import com.coohua.ap.admin.utils.third.Lists;
import com.coohua.ap.base.constants.AdConfigType;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/6/23
 */
@Slf4j
@Service
public class ConfigNewBasicService {

    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Resource
    private ConfigBaseMapper configBaseMapper;
    @Resource
    private TbConfigNewFilterMapper tbConfigNewFilterMapper;
    @Resource
    private AdInfoMapper adInfoMapper;

    private final static Integer ON = 1;
    private final static Integer OFF = 0;

    public void queryPosList(Integer product,String name,Integer switchFlag,Integer id,Page<ConfigBaseVO> page,Integer type){

        ConfigBaseEntity entity = new ConfigBaseEntity();
        entity.setId(Optional.ofNullable(id).orElse(0));
        entity.setName(name);
        entity.setProduct(product);
        entity.setType(ConfigType.PLACEMENT.code());
        entity.setState(Optional.ofNullable(switchFlag).orElse(-1));
        entity.setPosType(Optional.ofNullable(type).orElse(0));
        int from = (page.getPageNo() - 1) * page.getPageSize();
        List<ConfigBaseEntity> baseEntityList = configBaseMapper.queryAllByProduct(entity,from,page.getPageSize(),2);
        Long count = configBaseMapper.countAllByProduct(entity,2);
        page.setCount(count.intValue());
        List<ConfigBaseVO> baseVOList = baseEntityList.stream().map(configBaseEntity -> {
            ConfigBaseVO vo = new ConfigBaseVO();
            vo.setId(configBaseEntity.getId());
            vo.setProduct(configBaseEntity.getProduct());
            vo.setComment(configBaseEntity.getComment());
            vo.setConfig(configBaseEntity.getConfig());
            vo.setCreateTime(configBaseEntity.getCreateTime());
            vo.setUpdateTime(configBaseEntity.getUpdateTime());
            vo.setName(configBaseEntity.getName());
            vo.setState(configBaseEntity.getState() == 1);
            vo.setDelFlag(configBaseEntity.getDelFlag());
            vo.setPosType(configBaseEntity.getPosType());
            vo.setType(configBaseEntity.getType());
            return vo;
        }).collect(Collectors.toList());

        page.setItems(baseVOList);
    }

    public void addPosEntity(ConfigBaseVO vo,Integer product){
        Optional.ofNullable(vo.getName()).orElseThrow(throwException("配置名称不能为空"));
        Optional.ofNullable(vo.getPosType()).orElseThrow(throwException("配置类型不能为空"));
        int count = configBaseMapper.countByName(vo.getName(),product);
        if (count > 0){
            throw throwException("配置名称不能重复!请重新填写").get();
        }

        ConfigBaseEntity entity = new ConfigBaseEntity();

        entity.setName(vo.getName());
        entity.setProduct(product);
        entity.setState(ON);
        entity.setDelFlag(ON);
        entity.setPosType(vo.getPosType());
        entity.setConfig("[]");
        entity.setComment("创建合并配置");
        entity.setType(ConfigType.PLACEMENT.code());
        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        configBaseMapper.insertConfig(entity);
    }

    public void editPosName(Integer id,Integer product,String name){
        ConfigBaseEntity entity = configBaseMapper.queryOneById(id);
        Optional.ofNullable(entity).orElseThrow(throwException("未查询到基础配置"));

        int count = configBaseMapper.countByName(name,product);
        if (count > 0){
            throw throwException("已存在该配置名称..请修改后再提交").get();
        }
        entity.setName(name);
        configBaseMapper.updateConfig(entity);
    }

    public void copyPosEntity(Integer id){
        ConfigBaseEntity entity = configBaseMapper.queryOneById(id);
        Optional.ofNullable(entity).orElseThrow(throwException("未查询到该配置！"));

        ConfigBaseEntity entityRecord = new ConfigBaseEntity();
        BeanUtils.copyProperties(entity,entityRecord);

        int count = configBaseMapper.countByNameLike(entity.getName().split("_")[0],entity.getProduct());
        entityRecord.setId(null);
        entityRecord.setName(entity.getName().split("_")[0]+"_副本_" + entity.getProduct() + "_" + count);

        Date now = new Date();
        entityRecord.setCreateTime(now);
        entityRecord.setUpdateTime(now);
        configBaseMapper.insertConfig(entityRecord);

        List<TbConfigNewFilter> tbConfigNewFilters = tbConfigNewFilterMapper.selectListByConfigId(entity.getId());
        tbConfigNewFilters.forEach(tbConfigNewFilter -> {
           TbConfigNewFilter filter = new TbConfigNewFilter();
           BeanUtils.copyProperties(tbConfigNewFilter,filter);
           filter.setId(null);
           filter.setConfigId(entityRecord.getId());
           filter.setCreateTime(now);
           filter.setUpdateTime(now);

           tbConfigNewFilterMapper.insert(filter);
        });
    }

    public void switchPosState(Integer id,Integer state){
        ConfigBaseEntity entity = configBaseMapper.queryOneById(id);
        Optional.ofNullable(entity).orElseThrow(throwException("未查询到该配置！"));

        entity.setState(state);
        entity.setUpdateTime(new Date());
        configBaseMapper.updateConfig(entity);
    }

    public void delPosEntity(Integer id){
        ConfigBaseEntity entity = configBaseMapper.queryOneById(id);
        Optional.ofNullable(entity).orElseThrow(throwException("未查询到该配置！"));

        if (entity.getState() == 1){
            throw throwException("开关未关闭，无法删除..").get();
        }
        entity.setDelFlag(OFF);
        entity.setUpdateTime(new Date());
        configBaseMapper.updateConfig(entity);
    }

    public AdPullResponse queryConfigList(Integer configId){
        AdPullResponse response = new AdPullResponse();
        response.setDefaultAdTypeList(Collections.emptyList());
        response.setConfigNewFilterVoList(Collections.emptyList());
        List<TbConfigNewFilter> tbConfigNewFilters = tbConfigNewFilterMapper.selectListByConfigId(configId);

        ConfigBaseEntity configBaseEntity = configBaseMapper.queryOneById(configId);
        Optional.ofNullable(configBaseEntity).orElseThrow(throwException("未查询到对应的基础配置."));
        List<AdTypeConfigVo> configVos = JSON.parseArray(configBaseEntity.getConfig(),AdTypeConfigVo.class);

        Map<Integer,List<AdTypeConfigVo>> configMap = configVos.parallelStream().collect(Collectors.groupingBy(AdTypeConfigVo::getConfigType));
        List<AdConfigDefaultFilterRequest.DefaultAdType> subFilterVoList = configMap.getOrDefault(AdConfigType.BASE_CONFIG.getCode(), Collections.emptyList())
                .stream()
                .map(adTypeConfigVo -> {
                    AdConfigDefaultFilterRequest.DefaultAdType vo = new AdConfigDefaultFilterRequest.DefaultAdType();
                    vo.setAdType(adTypeConfigVo.getTargetType());
                    vo.setDefaultAdType(Integer.valueOf(adTypeConfigVo.getLayer().get(0).split("_")[0]));
                    return vo;
                }).collect(Collectors.toList());
        response.setDefaultAdTypeList(subFilterVoList);

        if (Lists.isEmpty(tbConfigNewFilters)){
            return response;
        }

        if (tbConfigNewFilters.size() == 1){
            TbConfigNewFilter tbConfigNewFilter = tbConfigNewFilters.get(0);
            TbConfigNewFilterVo vo = new TbConfigNewFilterVo();
            transferVo(tbConfigNewFilter,vo);
            vo.setDisableSort(true);
            // 单层 无需排序
            response.setConfigNewFilterVoList(Collections.singletonList(vo));
        }else {
            Map<Integer,List<TbConfigNewFilter>> tbMap =  tbConfigNewFilters.stream().collect(Collectors.groupingBy(TbConfigNewFilter::getPrice));
            Map<Integer,Boolean> tbSortMap = new HashMap<>();
            tbMap.forEach((price,listFilter) -> tbSortMap.put(price,listFilter.size() <= 1));
            List<TbConfigNewFilterVo> tbConfigNewFilterVos = tbConfigNewFilters.stream()
                    .map(tbConfigNewFilter -> {
                        TbConfigNewFilterVo vo = new TbConfigNewFilterVo();
                        transferVo(tbConfigNewFilter,vo);
                        vo.setDisableSort(tbSortMap.get(vo.getPrice()));
                        return vo;
                    }).sorted(Comparator.comparing(TbConfigNewFilterVo::getIndexIx))
                    .collect(Collectors.toList());
            response.setConfigNewFilterVoList(tbConfigNewFilterVos);
        }
        return response;
    }

    private void transferVo(TbConfigNewFilter tbConfigNewFilter,TbConfigNewFilterVo vo){
        BeanUtils.copyProperties(tbConfigNewFilter,vo);
        List<String> adTypeList = JSON.parseArray(tbConfigNewFilter.getAdType(),String.class);
        List<TbConfigNewFilterVo.SubFilterVo> subFilterVos = adTypeList.stream()
                .map(str->{
                    TbConfigNewFilterVo.SubFilterVo subFilterVo = new TbConfigNewFilterVo.SubFilterVo();
                    String[] spiltStr = str.split("_");
                    AdTypeSub adType = AdTypeSub.get(Integer.parseInt(spiltStr[0]));
                    subFilterVo.setAdType(adType.type);
                    subFilterVo.setAdTypeName(adType.getTypeDesc());
                    subFilterVo.setRet(Integer.parseInt(spiltStr[1]));
                    return subFilterVo;
                })
                .collect(Collectors.toList());
        vo.setAdTypeList(subFilterVos);
    }

    /**
     * 新增广告类型列表 单列插入
     */
    public void addAdTypeList(AdConfigFilterRequest request,Integer product){
        // 生成单条记录
        TbConfigNewFilter tbConfigNewFilter = new TbConfigNewFilter();
        tbConfigNewFilter.setConfigId(request.getConfigId());
        tbConfigNewFilter.setIndexIx(request.getIndex());
        tbConfigNewFilter.setProduct(product);
        tbConfigNewFilter.setPrice(request.getPrice());
        tbConfigNewFilter.setDelFlag(ON);
        tbConfigNewFilter.setSwitchFlag(request.getState());
        List<String> adTypeList = request.getSubFilterVoList().stream()
                .map(subFilterVo -> subFilterVo.getAdType() + "_" + subFilterVo.getRet())
                .collect(Collectors.toList());
        tbConfigNewFilter.setAdType(JSON.toJSONString(adTypeList));
        Date now = new Date();
        tbConfigNewFilter.setCreateTime(now);
        tbConfigNewFilter.setUpdateTime(now);

        tbConfigNewFilterMapper.insert(tbConfigNewFilter);
        // 压缩JSON存储
        updateJsonConfigSync(tbConfigNewFilter.getConfigId());
    }

    /**
     * 新增打底配置
     */
    public void saveAdTypeDefaultList(AdConfigDefaultFilterRequest request){
        ConfigBaseEntity configBaseEntity = configBaseMapper.queryOneById(request.getConfigId());
        Optional.ofNullable(configBaseEntity).orElseThrow(throwException("未查询到基础配置"));
        List<AdTypeConfigVo> configVos = JSON.parseArray(configBaseEntity.getConfig(),AdTypeConfigVo.class);

        List<AdTypeConfigVo> notAdDefaultList = configVos.stream()
                .filter(adTypeConfigVo -> !adTypeConfigVo.getConfigType().equals(AdConfigType.BASE_CONFIG.getCode()))
                .collect(Collectors.toList());
        List<AdTypeConfigVo> defaultAdList = request.getDefaultAdTypeList().stream()
                .map(defaultAdType -> {
                    AdTypeConfigVo vo = new AdTypeConfigVo();
                    vo.setTargetType(defaultAdType.getAdType());
                    vo.setLayer(Collections.singletonList(defaultAdType.getDefaultAdType() + "_100"));
                    vo.setConfigType(AdConfigType.BASE_CONFIG.getCode());
                    return vo;
                })
                .collect(Collectors.toList());
        notAdDefaultList.addAll(defaultAdList);
        configBaseEntity.setConfig(JSON.toJSONString(notAdDefaultList));
        // 更新
        configBaseMapper.updateConfig(configBaseEntity);
    }

    private Supplier<? extends RuntimeException> throwException(String desc){
        return () -> new RuntimeException(desc);
    }

    public void sortNode(AdConfigSortRequest request,Integer product){
        List<TbConfigNewFilter> tbConfigNewFilters = tbConfigNewFilterMapper.selectListByConfigId(request.getConfigId());
        Map<Integer,Integer> afterSortMap = request.getAdConfigCardList().stream()
                .collect(Collectors.toMap(
                        AdConfigSortRequest.AdConfigCard::getId,
                        AdConfigSortRequest.AdConfigCard::getIndex,(r1,r2) -> r1));
        tbConfigNewFilters.forEach(tbConfigNewFilter -> {
            tbConfigNewFilter.setIndexIx(afterSortMap.get(tbConfigNewFilter.getId()));
            tbConfigNewFilterMapper.updateById(tbConfigNewFilter.getId(),tbConfigNewFilter.getIndexIx());
        });
        log.info("更新完成...同步到配置.");
        // 压缩JSON存储
        updateJsonConfigSync(request.getConfigId());
    }

    /**
     * 重排序.编辑触发
     */
    public void editAdTypeList(AdConfigFilterRequest request){
        // 单条记录下线
        TbConfigNewFilter tbConfigNewFilter = tbConfigNewFilterMapper.load(request.getId());
        Optional.ofNullable(tbConfigNewFilter).orElseThrow(throwException("未查询到分层记录"));

        List<String> adTypeList = request.getSubFilterVoList().stream()
                .map(subFilterVo -> subFilterVo.getAdType() + "_" + subFilterVo.getRet())
                .collect(Collectors.toList());
        tbConfigNewFilter.setAdType(JSON.toJSONString(adTypeList));
        tbConfigNewFilter.setSwitchFlag(request.getState());
        tbConfigNewFilter.setPrice(request.getPrice());
        tbConfigNewFilter.setIndexIx(request.getIndex());
        Date now = new Date();
        tbConfigNewFilter.setUpdateTime(now);

        tbConfigNewFilterMapper.update(tbConfigNewFilter);
        // 压缩JSON存储
        updateJsonConfig(tbConfigNewFilter.getConfigId());
    }

    /**
     * 分层开关修改...
     */
    public void switchAdTypeFilter(Integer id,Integer state){
        // 单条记录下线
        TbConfigNewFilter tbConfigNewFilter = tbConfigNewFilterMapper.load(id);
        Optional.ofNullable(tbConfigNewFilter).orElseThrow(throwException("未查询到分层记录"));

        tbConfigNewFilter.setSwitchFlag(state);
        Date now = new Date();
        tbConfigNewFilter.setUpdateTime(now);
        tbConfigNewFilterMapper.update(tbConfigNewFilter);

        // JSON 剔除
        updateJsonConfigSync(tbConfigNewFilter.getConfigId());
    }

    /**
     * 删除分层节点
     * @param id 分层id
     */
    public void delNode(Integer id){
        TbConfigNewFilter tbConfigNewFilter = tbConfigNewFilterMapper.load(id);
        Optional.ofNullable(tbConfigNewFilter).orElseThrow(throwException("未查询到分层记录"));

        if (tbConfigNewFilter.getSwitchFlag().equals(ON)){
            throw throwException("开关未关闭,无法删除...").get();
        }

        tbConfigNewFilter.setDelFlag(OFF);
        tbConfigNewFilter.setUpdateTime(new Date());
        tbConfigNewFilterMapper.update(tbConfigNewFilter);
        updateJsonConfigSync(tbConfigNewFilter.getConfigId());
    }

    private void updateJsonConfigSync(Integer configId){
        CompletableFuture.runAsync(() -> this.updateJsonConfig(configId),executor);
    }

    private void updateJsonConfig(Integer configId){
        List<TbConfigNewFilter> tbConfigNewFilters = tbConfigNewFilterMapper.selectListByConfigId(configId);
        // 对新相同价格的分层进行排序
        tbConfigNewFilters.sort((c1,c2) ->{
            if (c1.getPrice().equals(c2.getPrice())){
                return c1.getIndexIx().compareTo(c2.getIndexIx());
            }
            return c2.getPrice().compareTo(c1.getPrice());
        });
        for(int i = 0;i < tbConfigNewFilters.size();i++){
            TbConfigNewFilter tbConfigNewFilter = tbConfigNewFilters.get(i);
            tbConfigNewFilter.setIndexIx(i);
            tbConfigNewFilterMapper.updateById(tbConfigNewFilter.getId(),tbConfigNewFilter.getIndexIx());
        }
        ConfigBaseEntity configBaseEntity = configBaseMapper.queryOneById(configId);
        List<AdTypeConfigVo> configVos = JSON.parseArray(configBaseEntity.getConfig(),AdTypeConfigVo.class);
        Map<Integer,List<AdTypeConfigVo>> configMap = configVos.parallelStream().collect(Collectors.groupingBy(AdTypeConfigVo::getConfigType));
        String jsonConfig = parseJsonConfig(tbConfigNewFilters,configMap.get(AdConfigType.BASE_CONFIG.getCode()));

        configBaseEntity.setConfig(jsonConfig);
        configBaseEntity.setUpdateTime(new Date());
        configBaseMapper.updateConfig(configBaseEntity);
    }

    /**
     * 转换为单层总JSON
     * @param tbConfigNewFilterList 分层配置
     * @param typeConfigVos 打底配置
     * @return 配置JSON
     */
    private String parseJsonConfig(List<TbConfigNewFilter> tbConfigNewFilterList,List<AdTypeConfigVo> typeConfigVos){
        List<AdTypeConfigVo> adTypeConfigVos = new ArrayList<>();
        // 排序
        List<TbConfigNewFilter> sortNewFilter = tbConfigNewFilterList.stream()
                .filter(tbConfigNewFilter -> tbConfigNewFilter.getSwitchFlag().equals(ON) && tbConfigNewFilter.getDelFlag().equals(ON))
                .sorted(Comparator.comparing(TbConfigNewFilter::getIndexIx))
                .collect(Collectors.toList());

        if (sortNewFilter.size() > 0) {
            // 设置头层
            TbConfigNewFilter tbConfigNewFilter = sortNewFilter.get(0);
            List<String> adConfigList = JSON.parseArray(tbConfigNewFilter.getAdType(), String.class);
            adTypeConfigVos.add(new AdTypeConfigVo() {{
                setConfigType(AdConfigType.TOP_CONFIG.getCode());
                setAdType(String.join("|", adConfigList));
                setRepeat(1);
            }});
            if (sortNewFilter.size() > 1) {
                // 设置瀑布流
                List<String> waterStream = sortNewFilter.stream()
                        .skip(1)
                        .map(configFilter -> {
                            List<String> rx = JSON.parseArray(configFilter.getAdType(), String.class);
                            return String.join("|", rx);
                        }).collect(Collectors.toList());
                AdTypeConfigVo adTypeConfigVo = new AdTypeConfigVo();
                adTypeConfigVo.setTargetType(Integer.valueOf(adConfigList.get(0).split("_")[0]));
                adTypeConfigVo.setLayer(waterStream);
                adTypeConfigVo.setConfigType(AdConfigType.WATERFALLS_CONFIG.getCode());
                adTypeConfigVos.add(adTypeConfigVo);
            }

        }
        // 设置打底
        if (typeConfigVos != null && typeConfigVos.size() > 0) {
            adTypeConfigVos.addAll(typeConfigVos);
        }
        return JSON.toJSONString(adTypeConfigVos);
    }

    // 查询分层列表
    public List<AdFilterBean> queryFilterList(Integer configId,Integer product){
        // 查询关联的adType
        ConfigBaseEntity configBaseEntity = configBaseMapper.queryOneById(configId);
        Optional.ofNullable(configBaseEntity).orElseThrow(throwException("未查询到对应配置"));
        // AdType 映射到广告
        ConfigType configType = ConfigType.find(configBaseEntity.getType());
        Optional.ofNullable(configType).orElseThrow(throwException("未查询到对应的配置类型"));
        List<AdTypeConfigVo> adTypeConfigVoList = JSONArray.parseArray(configBaseEntity.getConfig(),AdTypeConfigVo.class);
        return convertToAdFilter(adTypeConfigVoList,configType,product);
    }

    private List<AdFilterBean> convertToAdFilter(List<AdTypeConfigVo> adTypeConfigVoList,ConfigType configType,Integer product){
        List<AdFilterBean> adFilterBeanList = new ArrayList<>();
        switch (configType){
            case AD_CONFIG:
                break;
            case AD_DEFAULT_CONFIG:
            case APP_DEFAULT_CONFIG:
            case APP_DEFAULT_GOLD_CONFIG:

                break;
        }
        return adFilterBeanList;
    }

    // 拖拽更新广告分层记录保存
    public void updateFilterList(List<AdFilterBean> updateFilterList){

    }
}
