package com.coohua.ap.admin.service.third.dto.ks.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/8/13
 */
@Data
public class KsAppResponse {
    private String appId;
    private String name;
    private Integer mediumType;
    private Integer cooperationMode;
    private String packageName;
    private Long industryId;
    private List<FileInfo> files;
    private Long subIndustryId;
    private Integer auditStatus;
    private Integer cooperationContent;
    private Integer shadowAuditStatus;

    @Data
    public static class FileInfo{
        private Long expireTime;
        private Integer auditStatus;
        private Long id;
        private String auditDetail;
        private String url;
    }
}
