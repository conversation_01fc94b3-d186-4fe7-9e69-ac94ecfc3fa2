package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.ThirdPosAdView;
import com.coohua.ap.admin.model.TbApConfigDiscount;
import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.admin.service.TbApConfigDiscountService;
import com.coohua.ap.admin.utils.ParamChecker;
import com.coohua.ap.base.exception.BusinessException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/config/discount")
public class ConfigDiscountController {

    @Resource
    private TbApConfigDiscountService configDiscountService;

    @PostMapping("/list")
    private BaseResponse list(Page<TbApConfigDiscount> page,
                              @RequestParam("discountName") String discountName,
                              @RequestBody ApConfigDiscountVo discountVo,
                              HttpServletRequest request) {
        if (discountVo == null) {
            discountVo = new ApConfigDiscountVo();
            discountVo.setDiscountName(discountName);
        } else {
            discountVo.setDiscountName(discountName);
        }

        return BaseResponse.build(configDiscountService.listConfig(page.getPageNo(), page.getPageSize(), discountVo, page));
    }


    @GetMapping("/queryById")
    private BaseResponse queryById(@RequestParam("id") Long id,
                                   HttpServletRequest request) {
        ApConfigDiscountVo vo = configDiscountService.queryById(id);
        return BaseResponse.build(vo);
    }

    @PostMapping("/saveOrUpdate")
    private BaseResponse saveOrUpdate(@RequestBody ApConfigDiscountVo discountVo,
                                      HttpServletRequest request) {
        ParamChecker.isConfigDiscountVoValid(discountVo);
        ApConfigDiscountVo vo = configDiscountService.saveOrUpdateConfig(discountVo);
        if (vo == null) {
            throw new BusinessException(400, "配置格式有误");
        }
        return BaseResponse.build(vo);
    }

    @PutMapping("/delete")
    private BaseResponse deleteConfig(@RequestBody ApConfigDiscountVo discountVo,
                                      HttpServletRequest request) {
        ApConfigDiscountVo vo = configDiscountService.deleteConfig(discountVo);
        if (vo == null) {
            throw new BusinessException(400, "配置格式有误");
        }
        return BaseResponse.build(vo);
    }

    @PutMapping("/updateState")
    private BaseResponse updateState(@RequestBody ApConfigDiscountVo discountVo, HttpServletRequest request) {
        try {
            ApConfigDiscountVo discountVo1 = configDiscountService.updateConfigState(discountVo);
            return discountVo1 != null ? BaseResponse.SUCCESS : BaseResponse.build(500, "更新状态异常");
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.SYSTEM_ERROR;
        }
    }

    @GetMapping("/getPlatformAdPos")
    private BaseResponse getPlatformAdPos(HttpServletRequest request) {
        return BaseResponse.build(configDiscountService.getPlatformAdPos());
    }
}
