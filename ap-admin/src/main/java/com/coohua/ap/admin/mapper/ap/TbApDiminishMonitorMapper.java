package com.coohua.ap.admin.mapper.ap;


import com.coohua.ap.admin.model.TbApDiminishMonitor;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TbApDiminishMonitorMapper {

    @Select("select * from tb_ap_diminish_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '1:30' HOUR_MINUTE) and " +
            " arpu_step1 < 0.00 and dau_3b >= 200 " +
            " group by app_id,os,platform,category_id having count(*) >= 2 ")
    List<TbApDiminishMonitor> queryDiminishAdPosForStep1();

    @Select("select * from tb_ap_diminish_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '30' MINUTE) and " +
            " ((arpu_step3 < 0.00 and arpu_step1 < 0.00 and dau_3b >= 200) or (arpu_step3 < 0.05 and arpu_step6 < 0.05)) ")
    List<TbApDiminishMonitor> queryDiminishAdPos();

    @Select("select * from tb_ap_diminish_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '1:30' HOUR_MINUTE) and arpu_step6 < -0.05 " +
            " group by app_id,os,platform,category_id having count(*) >= 2 ")
    List<TbApDiminishMonitor> queryDiminishAdPosForNight();

    @Select("select distinct config_name from tb_ap_diminish_monitor where logday <= #{logday} and logday >= #{startLogday} ")
    List<String> queryByLogDay(@Param("logday") String logday, @Param("startLogday") String startLogday);


}




