<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApBiddingMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbApBidding" >
        <result column="id" property="id" />
        <result column="product" property="product" />
        <result column="ad_pos_type" property="adPosType" />
        <result column="ad_id" property="adId" />
        <result column="switch_flag" property="switchFlag" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="start_ecpm" property="startEcpm" />
        <result column="end_ecpm" property="endEcpm" />
        <result column="priority" property="priority" />
        <result column="bidding_type" property="biddingType" />
        <result column="play_type" property="playType" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                product,
                ad_pos_type,
                ad_id,
                switch_flag,
                del_flag,
                create_time,
                update_time,
                start_ecpm,
                end_ecpm,
                priority,
                bidding_type,
                play_type
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.TbApBidding">
        INSERT INTO tb_ap_bidding
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != product">
                product,
            </if>
            <if test="null != adPosType">
                ad_pos_type,
            </if>
            <if test="null != adId">
                ad_id,
            </if>
            <if test="null != switchFlag">
                switch_flag,
            </if>
            <if test="null != delFlag">
                del_flag,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != startEcpm">
                start_ecpm,
            </if>
            <if test="null != endEcpm">
                end_ecpm,
            </if>
            <if test="null != priority">
                priority,
            </if>
            <if test="null != biddingType">
                bidding_type,
            </if>
            <if test="null != playType">
                play_type
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != product">
                #{product},
            </if>
            <if test="null != adPosType">
                #{adPosType},
            </if>
            <if test="null != adId">
                #{adId},
            </if>
            <if test="null != switchFlag">
                #{switchFlag},
            </if>
            <if test="null != delFlag">
                #{delFlag},
            </if>
            <if test="null != createTime ">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != startEcpm">
                #{startEcpm},
            </if>
            <if test="null != endEcpm">
                #{endEcpm},
            </if>
            <if test="null != priority">
                #{priority},
            </if>
            <if test="null != biddingType">
                #{biddingType},
            </if>
            <if test="null != playType">
                #{playType}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM tb_ap_bidding
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.TbApBidding">
        UPDATE tb_ap_bidding
        <set>
            <if test="null != product">product = #{product},</if>
            <if test="null != adPosType">ad_pos_type = #{adPosType},</if>
            <if test="null != adId">ad_id = #{adId},</if>
            <if test="null != switchFlag">switch_flag = #{switchFlag},</if>
            <if test="null != delFlag">del_flag = #{delFlag},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != startEcpm">start_ecpm = #{startEcpm},</if>
            <if test="null != endEcpm">end_ecpm = #{endEcpm},</if>
            <if test="null != priority">priority = #{priority},</if>
            <if test="null != biddingType">bidding_type = #{biddingType},</if>
            <if test="null != playType">play_type = #{playType}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdate" parameterType="java.util.List">
        UPDATE tb_ap_bidding
        <trim prefix="SET" suffixOverrides=",">
            <foreach collection="list" item="item" separator="">
                <if test="item.product != null">
                    product = CASE WHEN id = #{item.id} THEN #{item.product} ELSE product END,
                </if>
                <if test="item.adPosType != null">
                    ad_pos_type = CASE WHEN id = #{item.id} THEN #{item.adPosType} ELSE ad_pos_type END,
                </if>
                <if test="item.adId != null">
                    ad_id = CASE WHEN id = #{item.id} THEN #{item.adId} ELSE ad_id END,
                </if>
                <if test="item.switchFlag != null">
                    switch_flag = CASE WHEN id = #{item.id} THEN #{item.switchFlag} ELSE switch_flag END,
                </if>
                <if test="item.delFlag != null">
                    del_flag = CASE WHEN id = #{item.id} THEN #{item.delFlag} ELSE del_flag END,
                </if>
                <if test="item.createTime != null">
                    create_time = CASE WHEN id = #{item.id} THEN #{item.createTime} ELSE create_time END,
                </if>
                <if test="item.updateTime != null">
                    update_time = CASE WHEN id = #{item.id} THEN #{item.updateTime} ELSE update_time END,
                </if>
                <if test="item.startEcpm != null">
                    start_ecpm = CASE WHEN id = #{item.id} THEN #{item.startEcpm} ELSE start_ecpm END,
                </if>
                <if test="item.endEcpm != null">
                    end_ecpm = CASE WHEN id = #{item.id} THEN #{item.endEcpm} ELSE end_ecpm END,
                </if>
                <if test="item.priority != null">
                    priority = CASE WHEN id = #{item.id} THEN #{item.priority} ELSE priority END,
                </if>
                <if test="item.biddingType != null">
                    bidding_type = CASE WHEN id = #{item.id} THEN #{item.biddingType} ELSE bidding_type END,
                </if>
                <if test="item.playType != null">
                    play_type = CASE WHEN id = #{item.id} THEN #{item.playType} ELSE play_type END,
                </if>
            </foreach>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>



    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_bidding
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_bidding
        where del_flag = 1
        <if test=' null != product'> AND product =#{product} </if>
        <if test=' null != adId and adId != "" '> AND ad_id =#{adId} </if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_ap_bidding
        where del_flag = 1
        <if test=' null != product'> AND product =#{product} </if>
        <if test=' null != adId and adId != ""'> AND ad_id =#{adId} </if>
    </select>

</mapper>