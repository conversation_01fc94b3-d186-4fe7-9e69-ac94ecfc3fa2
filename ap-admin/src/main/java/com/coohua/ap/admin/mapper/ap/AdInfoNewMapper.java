package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.AdInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Mapper
@Repository
public interface AdInfoNewMapper {
    /**
     * 新增
     **/
    int insert(AdInfo adInfo);

    /**
     * 刪除
     **/
    int delete(Long id);

    /**
     * 更新
     **/
    int update(AdInfo adInfo);

    /**
     * 查询 根据主键 id 查询
     **/
    AdInfo load(Long id);

    /**
     * 查询 分页查询
     **/
    List<AdInfo> pageList(int offset, int pagesize);

    /**
     * 查询 分页查询 count
     **/
    int pageListCount(int offset,int pagesize);


    @Select("select * from ad_info where product = #{product}")
    List<AdInfo> queryByProduct(@Param("product")Integer product);


    @Select("<script>" +
            "select * from ad_info " +
            "where product = #{product} and state = 1 and os = #{os} and ad_type in " +
            "<foreach item='item' index='index' collection='typeResults' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    List<AdInfo> queryByProductAndPosId(@Param("product") Integer product, @Param("typeResults") List<Integer> typeResults, @Param("os") Integer os);


}
