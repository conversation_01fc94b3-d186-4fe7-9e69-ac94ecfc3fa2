package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbApConfigDiscount;
import com.coohua.ap.base.vo.ApConfigDiscountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_ap_config_discount(打折系数配置)】的数据库操作Mapper
* @createDate 2024-12-04 11:01:36
* @Entity com.coohua.ap.admin.model.TbApConfigDiscount
*/
public interface TbApConfigDiscountMapper {

    TbApConfigDiscount queryById(Long id);

    void updateById(TbApConfigDiscount discount);

    void insert(TbApConfigDiscount discount);

    List<TbApConfigDiscount> queryList(@Param("from") int from, @Param("pageSize") int pageSize, @Param("discountName") String discountName, @Param("isEnabled") Integer isEnabled);

    int queryCount(@Param("discountName") String discountName, @Param("isEnabled") Integer isEnabled);
}




