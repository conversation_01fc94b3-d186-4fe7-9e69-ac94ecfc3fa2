package com.coohua.ap.admin.service.replace;

import com.coohua.ap.admin.constants.AdReplaceType;
import com.coohua.ap.admin.mapper.ap.TbApDiminishBiddingMonitorMapper;
import com.coohua.ap.admin.model.TbApBidding;
import com.coohua.ap.admin.model.TbApDiminishBiddingMonitor;
import com.coohua.ap.admin.model.TbApSubsidiesConfig;
import com.coohua.ap.admin.service.BiddingConfigService;
import com.coohua.ap.admin.service.TbApDiminishBiddingMonitorService;
import com.coohua.ap.admin.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class EntireBiddingReplaceProcessor {

    @Resource
    private TbApDiminishBiddingMonitorMapper tbApDiminishBiddingMonitorMapper;
    @Autowired
    private TbApDiminishBiddingMonitorService tbApDiminishBiddingMonitorService;
    @Autowired
    private BiddingConfigService biddingConfigService;

    public AdReplaceType process(TbApSubsidiesConfig config, Integer adType) {
        if (config == null){
            return AdReplaceType.NO_REPLACE;
        }
        TbApDiminishBiddingMonitor tbApDiminishBiddingMonitor = tbApDiminishBiddingMonitorMapper.entireBiddingMonitorByProduct(config.getOs(), config.getProduct());
        List<TbApBidding> validPerkBiddingList = biddingConfigService.findPerkBiddingList(config, 1, adType);

        if (CollectionUtils.isNotEmpty(validPerkBiddingList) && (!StringUtils.isEmpty(config.getAutoGenerateEcpm()) || !StringUtils.isEmpty(config.getLayerEcpm()))){
            if (tbApDiminishBiddingMonitor.getOriBiddingEcpm3() >= tbApDiminishBiddingMonitor.getWaterfallOriBiddingNcsjEcpm3() * 1.5){
                tbApDiminishBiddingMonitorService.replaceBidding(config);
                return AdReplaceType.REPLACE_BOTH;
            }else {
                tbApDiminishBiddingMonitorService.pauseBidding(config);
                return AdReplaceType.REPLACE_WATERFALL;
            }
        }

        if (!StringUtils.isEmpty(config.getAutoGenerateEcpm()) || !StringUtils.isEmpty(config.getLayerEcpm())){
            if (tbApDiminishBiddingMonitor.getOriBiddingEcpm3() >= tbApDiminishBiddingMonitor.getWaterfallOriBiddingNcsjEcpm3() * 2){
                tbApDiminishBiddingMonitorService.appendBidding(config);
                return AdReplaceType.REPLACE_WATERFALL_ADD_BIDDING;
            }

            if (tbApDiminishBiddingMonitor.getOriBiddingEcpm3() < tbApDiminishBiddingMonitor.getWaterfallOriBiddingNcsjEcpm3() * 1.5){
                return AdReplaceType.REPLACE_WATERFALL;
            }

        }

        if (CollectionUtils.isNotEmpty(validPerkBiddingList)){
            if (tbApDiminishBiddingMonitor.getOriBiddingEcpm3() >= tbApDiminishBiddingMonitor.getWaterfallOriBiddingNcsjEcpm3() * 1.5){
                tbApDiminishBiddingMonitorService.replaceBidding(config);
                return AdReplaceType.REPLACE_BIDDING;
            }else {
                tbApDiminishBiddingMonitorService.pauseBidding(config);
                return AdReplaceType.NO_REPLACE;
            }

        }
        if (tbApDiminishBiddingMonitor.getOriBiddingEcpm3() >= tbApDiminishBiddingMonitor.getWaterfallOriBiddingNcsjEcpm3() * 2){
            tbApDiminishBiddingMonitorService.appendBidding(config);
            return AdReplaceType.ADD_BIDDING;
        }

        return AdReplaceType.NO_REPLACE;
    }

}
