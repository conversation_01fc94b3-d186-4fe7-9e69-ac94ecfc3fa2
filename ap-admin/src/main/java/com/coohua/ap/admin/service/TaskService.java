package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.TaskVO;
import com.coohua.ap.admin.model.TbApTaskModel;

import java.util.List;

/**
 * <pre>
 *  任务中心
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/01/20
 */
public interface TaskService {
    /**
     * 新增任务
     * @param taskVO
     * @return
     */
    TbApTaskModel addNewTask(TaskVO taskVO);

    /**
     * 修改任务
     * @param taskVO
     * @return
     */
    TbApTaskModel updateTask(TaskVO taskVO);

    /**
     * 查询任务
     * @param taskType
     * @param appId
     * @return
     */
    TaskVO queryByAppAndTaskType(int taskType, int appId);

    /**
     * 查询任务列表
     * @param appId
     * @return
     */
    List<TaskVO> queryList(int appId, String name);
}
