package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.controller.vo.EmptyAdInfoVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.domain.EmptyAdCountDomain;
import com.coohua.ap.admin.mapper.ap.EmptyAdInfoMapper;
import com.coohua.ap.admin.service.EmptyAdService;
import com.coohua.ap.base.domain.EmptyAdDomain;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @ClassName: EmptyAdServiceImpl
 * @Description: 空广告记录服务
 * @Author: fan jin yang
 * @Date: 2020/3/30
 * @Version: V1.0
 **/
@Service
public class EmptyAdServiceImpl implements EmptyAdService {


    @Autowired
    private EmptyAdInfoMapper emptyAdInfoMapper;

    @Override
    public List<EmptyAdCountDomain> queryEmptyCount(int appId) {
        List<EmptyAdCountDomain> emptyAdCountDomains = emptyAdInfoMapper.queryCountByProduct(appId);
        EmptyAdCountDomain countDomain = new EmptyAdCountDomain();
        countDomain.setAppId(0);
        countDomain.setProduct("全部");
        countDomain.setCount(0);
        for(EmptyAdCountDomain emptyAdCountDomain:emptyAdCountDomains){
            countDomain.setCount(countDomain.getCount()+emptyAdCountDomain.getCount());
        }
        emptyAdCountDomains.add(countDomain);
        emptyAdCountDomains.sort(new Comparator<EmptyAdCountDomain>() {
            @Override
            public int compare(EmptyAdCountDomain o1, EmptyAdCountDomain o2) {
                return o1.getAppId()-o2.getAppId();
            }
        });
        return emptyAdCountDomains;
    }


    @Override
    public Page<EmptyAdInfoVO> queryEmptyAdInfo(Page<EmptyAdInfoVO> page) {

        List<EmptyAdDomain> emptyAdDomains = emptyAdInfoMapper.queryEmptyAd(page.getAppId(), page.getPageNo(), page.getPageSize());

        List<EmptyAdInfoVO> emptyAdInfoVOS = new ArrayList<>();

        for(EmptyAdDomain emptyAdDomain:emptyAdDomains){
            EmptyAdInfoVO emptyAdInfoVO = new EmptyAdInfoVO();
            emptyAdInfoVO.setId(emptyAdDomain.getId());
            emptyAdInfoVO.setUserId(emptyAdDomain.getUserId());
            emptyAdInfoVO.setAppId(emptyAdDomain.getAppId());
            emptyAdInfoVO.setProduct(emptyAdDomain.getProduct());
            emptyAdInfoVO.setStrategyId(emptyAdDomain.getStrategyId());
            emptyAdInfoVO.setOs(emptyAdDomain.getOs());
            emptyAdInfoVO.setRegion(emptyAdDomain.getRegion()==1?"受限":emptyAdDomain.getRegion()==2?"非受限":"未知");
            emptyAdInfoVO.setAnonymous(emptyAdDomain.getAnonymous()==1?"匿名":emptyAdDomain.getAnonymous()==2?"非匿名":"未知");
            emptyAdInfoVO.setRegistTime(new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(emptyAdDomain.getRegistTime()));
            emptyAdInfoVO.setIncome(emptyAdDomain.getIncome());
            emptyAdInfoVO.setVersion(emptyAdDomain.getVersion());
            emptyAdInfoVO.setPkgName(emptyAdDomain.getPkgName());
            emptyAdInfoVO.setChannelId(emptyAdDomain.getChannelId());
            emptyAdInfoVO.setCreateTime(emptyAdDomain.getCreateTime().substring(0,emptyAdDomain.getCreateTime().length()-2));
            emptyAdInfoVOS.add(emptyAdInfoVO);
        }
        page.setItems(emptyAdInfoVOS);
        return page;
    }
}
