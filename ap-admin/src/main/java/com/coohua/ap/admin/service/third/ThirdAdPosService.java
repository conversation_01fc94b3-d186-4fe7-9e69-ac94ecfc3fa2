package com.coohua.ap.admin.service.third;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.*;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.domain.CreateExceptionAdPosDto;
import com.coohua.ap.admin.mapper.ap.*;
import com.coohua.ap.admin.model.*;
import com.coohua.ap.admin.service.AdAdminService;
import com.coohua.ap.admin.service.BiddingConfigService;
import com.coohua.ap.admin.service.ExcelConfigService;
import com.coohua.ap.admin.service.TbApReplaceAdService;
import com.coohua.ap.admin.service.third.dto.AdTypeCreateVo;
import com.coohua.ap.admin.service.third.dto.LoadAdPreVo;
import com.coohua.ap.admin.service.third.dto.bd.req.BdAdPosCreateRequest;
import com.coohua.ap.admin.service.third.dto.bd.req.BdAdQueryRequest;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdAdDetailResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdAdQueryResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.CratePosResponse;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosCreateRequest;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosQueryRequest;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosUpdateRequest;
import com.coohua.ap.admin.service.third.dto.csj.rsp.*;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosCreateRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosUpdateRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAppInfoQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosInfo;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosPageResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAddAdposResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtResponse;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionQueryRequest;
import com.coohua.ap.admin.service.third.dto.ks.request.KsPositionRequest;
import com.coohua.ap.admin.service.third.dto.ks.request.KsSetPriceRequest;
import com.coohua.ap.admin.service.third.dto.ks.response.KsPositionResponse;
import com.coohua.ap.admin.service.third.dto.ks.response.KsResponse;
import com.coohua.ap.admin.service.third.dto.oppo.req.OppoBaseReq;
import com.coohua.ap.admin.service.third.dto.oppo.req.OppoCreatePosRequest;
import com.coohua.ap.admin.service.third.dto.oppo.req.OppoQueryListRequest;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoAdResp;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoCreatePosRsp;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoPageRsp;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoResponse;
import com.coohua.ap.admin.service.third.service.bd.BdBaseService;
import com.coohua.ap.admin.service.third.service.csj.CsjBaseService;
import com.coohua.ap.admin.service.third.service.gdt.GdtBaseService;
import com.coohua.ap.admin.service.third.service.ks.KsBaseService;
import com.coohua.ap.admin.service.third.service.oppo.OppoBaseService;
import com.coohua.ap.admin.utils.DingTalkRobotUtils;
import com.coohua.ap.admin.utils.StringUtils;
import com.coohua.ap.admin.utils.third.CallBackUtils;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.admin.utils.third.Lists;
import com.coohua.ap.admin.utils.third.Strings;
import com.coohua.ap.base.constants.*;
import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.support.core.container.AppBuilder;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.jboss.netty.util.internal.ThreadLocalBoolean;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.coohua.ap.admin.task.PerkAdPosTask.DING_KEY_WORD;
import static com.coohua.ap.admin.task.PerkAdPosTask.DING_TALK_URL;

/**
 * <AUTHOR>
 * @since 2021/1/7
 */
@Slf4j
@Service
public class ThirdAdPosService {
    @Autowired
    private CsjBaseService csjBaseService;
    @Autowired
    private GdtBaseService gdtBaseService;
    @Autowired
    private KsBaseService ksBaseService;
    @Resource
    private ThirdTemplateMapper thirdTemplateMapper;
    @Autowired
    private ThirdLogService thirdLogService;
    @Resource
    private ThirdAdPosMapper thirdAdPosMapper;
    @Resource
    private ThirdCompanyMapper thirdCompanyMapper;
    @Resource
    private ThirdAppMapper thirdAppMapper;
    @Resource
    private ThirdTaskQueenMapper thirdTaskQueenMapper;
    @Autowired
    private ExcelConfigService excelConfigService;
    @Autowired
    private AdAdminService adAdminService;
    @Autowired
    private ThirdCallBackService thirdCallBackService;
    @Resource
    private CreateRecordMapper createRecordMapper;
    @Autowired
    private BiddingConfigService biddingConfigService;
    @Autowired
    private BdBaseService bdBaseService;
    @Autowired
    private OppoBaseService oppoBaseService;
    @Autowired
    private TbApReplaceAdService tbApReplaceAdService;
    @Resource
    private AdPosMapper adPosMapper;

    private final ThreadLocalBoolean threadLocalBoolean = new ThreadLocalBoolean();

    private static AtomicBoolean autoFlag = new AtomicBoolean(Boolean.TRUE);

    public void list(Integer offset, Integer limit, String appName,String appId, String posName,
                     Integer adType,String queryDate,Long posId,Integer importStatus,
                     Page<ThirdPosAdView> page){
        List<String> appIdList = new ArrayList<>();
        if (page.getAppId() != 0){
            List<ThirdAppEntity> appEntityList = thirdAppMapper.queryByProduct(page.getAppId());
            appIdList = appEntityList.stream().map(ThirdAppEntity::getAppId).collect(Collectors.toList());
            if (appIdList.size() == 0){
                appIdList.add("0");
            }
        }

        Date stTime = null,edTime = null;
        if (Strings.noEmpty(queryDate)){
            stTime = DateUtil.stringToDateWithTime(queryDate + " 00:00:00");
            edTime = DateUtil.stringToDateWithTime(queryDate + " 23:59:59");
        }
        List<ThirdAdPosEntity> thirdAdPosEntityList = thirdAdPosMapper.pageList(offset, limit,appName,appId,posName,posId,importStatus,
                adType,stTime,edTime,appIdList);
        List<ThirdAppEntity> thirdAppEntities = thirdAppMapper.queryAll();
        Map<Integer,ThirdAppEntity> thirdAppEntityMap = thirdAppEntities.stream().collect(Collectors.toMap(ThirdAppEntity::getId,k->k));
        page.setItems(thirdAdPosEntityList.stream().map(t ->{
            ThirdPosAdView view = new ThirdPosAdView();
            BeanUtils.copyProperties(t,view);
            view.setAppId(t.getAppId().toString());
            view.setPosId(t.getPosId().toString());

            AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(t.getAdType());
            view.setAdType(adTypeCreateVo.getAdType());
            view.setAdTypeOwn(adTypeCreateVo.getAdTypeOwn());
            view.setAdSite(adTypeCreateVo.getAdSite());
            view.setAdSubType(adTypeCreateVo.getAdSubType());

            ThirdAppEntity entity = thirdAppEntityMap.get(t.getApplicationId());
            if (entity != null){
                view.setOs(entity.getOs());
            }
            view.setCreateTime(DateUtil.dateToString(t.getCreateTime(),DateUtil.DATETIME_PATTERN));
            view.setUpdateTime(DateUtil.dateToString(t.getUpdateTime(),DateUtil.DATETIME_PATTERN));
            return view;
        }).collect(Collectors.toList()));
        page.setCount(thirdAdPosMapper.pageListCount(offset,limit,appName,appId,posName,posId,importStatus,adType,stTime,edTime,appIdList));
    }

    public ThirdAdPosEntity insert(ThirdPosAdRequest thirdPosAdRequest,ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,String account,String name){
        if (!appEntity.getPlatformCode().equals(thirdPosAdRequest.getPlatformCode())){
            throw new RuntimeException("请选择正确的平台应用！");
        }
        if (!companyEntity.getPlatformCode().equals(thirdPosAdRequest.getPlatformCode())){
            throw new RuntimeException("请选择正确的主体！");
        }

        ThirdAdPosEntity entity = new ThirdAdPosEntity();
        if (thirdPosAdRequest.getInnerType() == null){
            thirdPosAdRequest.setInnerType(0);
        }
        Platform platform = Platform.getPlatform(thirdPosAdRequest.getPlatformCode());
        if (Platform.CSJ.equals(platform)){
            AdPosCreateResponse response = createCsjPos(companyEntity,appEntity,thirdPosAdRequest);
            entity.setPosId(response.getAd_slot_id());
            entity.setCallBackKey(response.getReward_security_key());
        }else if (Platform.GDT.equals(platform)){
            thirdPosAdRequest.setSecret(CallBackUtils.createSecret());
            GdtAddAdposResponse gdtAddAdposResponse = createGdtPos(companyEntity,appEntity,thirdPosAdRequest);
            entity.setPosId(gdtAddAdposResponse.getPlacement_id());
            entity.setCallBackKey(thirdPosAdRequest.getSecret());
        }else if (Platform.KS.equals(platform)){
            sleep(500L);
            Long posId = createKsPos(companyEntity,appEntity,thirdPosAdRequest);
            // 设置价格
            if (thirdPosAdRequest.getAdPrice() != null && thirdPosAdRequest.getAdPrice() > 0) {
                if (PriceType.BIDDING.getCode().equals(thirdPosAdRequest.getIsBidding())
                        && Integer.valueOf(2).equals(thirdPosAdRequest.getIsSetPrice())) {
                    sleep(1000L);
                    setKsPrice(companyEntity, appEntity, thirdPosAdRequest, posId);
                    KsPositionResponse response = queryKsSingleAdPos(companyEntity, appEntity, posId);
                    entity.setCallBackKey(response.getCallback_sk());
                }else if (PriceType.NORMAL.getCode().equals(thirdPosAdRequest.getIsBidding())){
                    sleep(1000L);
                    setKsPrice(companyEntity, appEntity, thirdPosAdRequest, posId);
                    KsPositionResponse response = queryKsSingleAdPos(companyEntity, appEntity, posId);
                    entity.setCallBackKey(response.getCallback_sk());
                }
            }
            entity.setPosId(posId);
        } else if (Platform.BD.equals(platform)){
            // 百度平台的创建-不接入服务端回调
            CratePosResponse cratePosResponse = createBdPos(companyEntity, appEntity, thirdPosAdRequest);
            entity.setPosId(Long.valueOf(cratePosResponse.getTu_id()));
            CratePosResponse.AdInfo adInfo = cratePosResponse.getAd_info();
            if(adInfo != null){
                entity.setCallBackKey(adInfo.getReward_video_return_token());
            }
        } else if (Platform.OPPO.equals(platform)){
            // OPPO的创建-不接入服务端回调
            OppoAdResp oppoCreatePosRsp = createPos(companyEntity, appEntity, thirdPosAdRequest);
            entity.setPosId(Long.valueOf(oppoCreatePosRsp.getPosId()));
        }

        AdTypeCreateVo adTypeCreateVo = new AdTypeCreateVo();
        adTypeCreateVo.setAdType(thirdPosAdRequest.getAdType());
        adTypeCreateVo.setAdSubType(thirdPosAdRequest.getAdSubType());
        adTypeCreateVo.setAdTypeOwn(thirdPosAdRequest.getAdTypeOwn());
        adTypeCreateVo.setAdSite(thirdPosAdRequest.getAdSite());
        adTypeCreateVo.setIsBidding(thirdPosAdRequest.getIsBidding() == 1);
        entity.setAdType(adTypeCreateVo.toString());
        entity.setPlatformCode(companyEntity.getPlatformCode());
        entity.setSwitchFlag(1);
        entity.setAppId(appEntity.getAppId());
        entity.setAppName(appEntity.getAppName());
        entity.setCompanyId(companyEntity.getId());
        entity.setMainBody(companyEntity.getMainBody());
        entity.setIncomeStatus(0);
        entity.setPosName(thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName()));
        entity.setPosStatus(1);
        entity.setAdPrice(BigDecimal.valueOf(thirdPosAdRequest.getAdPrice()));
        entity.setApplicationId(appEntity.getId());

        Date now = new Date();
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        thirdAdPosMapper.insert(entity);

        thirdLogService.saveLog(account,name,OpType.ADD_ADPOS,entity.getId(),null, JSON.toJSONString(entity));
        return entity;
    }

    public void insert(ThirdPosAdRequest thirdPosAdRequest,String account,String name,Integer product){
        Optional.ofNullable(thirdPosAdRequest.getOs()).orElseThrow(() -> new RuntimeException("所选系统不能为空"));
        ThirdAppEntity appEntity = thirdAppMapper.queryByProductAndOs(product,thirdPosAdRequest.getOs(),thirdPosAdRequest.getPlatformCode());
        Optional.ofNullable(appEntity).orElseThrow(() -> new RuntimeException("未匹配到应用，请查看应用配置！"));
        ThirdCompanyEntity companyEntity = thirdCompanyMapper.load(appEntity.getCompanyId());

        DspEnum dspEnum = DspEnum.fromCode(thirdPosAdRequest.getPosName());
        if (dspEnum != null) {
            // 手动创建三方广告，识别dsp，在后续创建过程中统一名称增加dsp
            thirdPosAdRequest.setPosName(thirdPosAdRequest.getPosName().replace("-" + dspEnum.getCode(),""));
            thirdPosAdRequest.setDsp(dspEnum.getCode());
        }

        insert(thirdPosAdRequest,companyEntity,appEntity,account,name);
    }

    private AdPosCreateResponse createCsjPos(ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,ThirdPosAdRequest thirdPosAdRequest){
        AdPosCreateRequest request = new AdPosCreateRequest();
        request.setUser_id(Integer.valueOf(companyEntity.getUserId()));
        request.setRole_id(companyEntity.getRoleId());
//        request.setMask_rule_id(companyEntity.getMaskRuleId());
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(companyEntity.getSecurityKey(),timestamp,nonce));
        request.setVersion("1.0");
        request.setApp_id(Math.toIntExact(Long.parseLong(appEntity.getAppId())));
        request.setAd_slot_type(convertToCsjType(thirdPosAdRequest.getAdType()));
        request.setAd_slot_name(thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName()));
        request.setBidding_type(0);
        request.setUse_mediation("0");
        // 信息流
        if (request.getAd_slot_type() ==1){
            if (Strings.isEmpty(thirdPosAdRequest.getRenderType())){
                throw new RuntimeException("渲染类型异常!请重新填写");
            }
            int renderType = Integer.parseInt(thirdPosAdRequest.getRenderType());
            request.setRender_type(renderType);
            if (renderType == 1) {
                request.setAccept_material_type(3);
                request.setTpl_list(new AdPosCreateRequest.Tpl[]{new AdPosCreateRequest.Tpl() {{
                    setTpl_id(1459);
                }}});
            }else {
                request.setAd_categories(convertFlushCsj(thirdPosAdRequest.getAdSubType()));
            }
        }else if (request.getAd_slot_type() == 3 || request.getAd_slot_type() ==4){
            request.setRender_type(3);
        }else if (request.getAd_slot_type() == 5){
            request.setRender_type(1);
            request.setOrientation(1);
            request.setReward_name("翻倍");
            request.setReward_count(100);
            request.setReward_is_callback(1);
            request.setReward_back_type(1);
            request.setReward_callback_url(CallBackUtils.getCallBackUrl(Platform.CSJ,thirdPosAdRequest.getAdPrice().intValue()));
            PriceType biddingType = PriceType.getByCode(thirdPosAdRequest.getIsBidding());
            request.setBidding_type(biddingType.getCode());
        }else if (request.getAd_slot_type() == 9){
            request.setRender_type(1);
            request.setAd_rollout_size(2);
            request.setAccept_material_type(1);
            request.setOrientation(1);
            request.setSkip_duration(0);
            request.setUse_endcard(2);
            if (thirdPosAdRequest.getAdTypeOwn().toString().startsWith(String.valueOf(AdType.CSJ_NEW_FULL_CHAPIN.type))){
                log.info("穿山甲新插屏,调整创建参数...");
                request.setAccept_material_type(2);
                request.setAd_rollout_size(1);
                request.setSkip_duration(5);
            }
            PriceType biddingType = PriceType.getByCode(thirdPosAdRequest.getIsBidding());
            request.setBidding_type(biddingType.getCode());
        }else if (request.getAd_slot_type() == 2){
            request.setRender_type(1);
            request.setSlide_banner(2);
            request.setWidth(640);
            request.setHeight(100);
        }
        if (thirdPosAdRequest.getAdPrice() != null && thirdPosAdRequest.getAdPrice() > 0) {
            request.setCpm(String.valueOf(thirdPosAdRequest.getAdPrice().intValue()));
        }
        if (request.getBidding_type().equals(PriceType.BIDDING.getCode())){
            // 设置为客户端竞价
            BiddingType biddingType = BiddingType.getByType(thirdPosAdRequest.getBiddingType());
            if (BiddingType.CLIENT.equals(biddingType)){
                request.setBidding_type(2);
            }else if (BiddingType.SERVER.equals(biddingType)){
                request.setBidding_type(1);
            }else {
                throw new RuntimeException("不存在的竞价类型..");
            }
            request.setCpm(null);
        }

        // 插屏重新调整
        if (request.getAd_slot_type() == 4 || request.getAd_slot_type() == 9){
            if (thirdPosAdRequest.getInnerType() == 0){
                log.info("插屏样式默认 啥也不干....");
            }else if (thirdPosAdRequest.getInnerType() == 1){
                request.setAd_rollout_size(2);
                request.setRender_type(1);
                request.setAccept_material_type(3);
            }else if (thirdPosAdRequest.getInnerType() == 2){
                request.setAd_rollout_size(3);
                request.setRender_type(1);
                request.setAccept_material_type(3);
            }
        }

        Response<AdPosCreateResponse> responseResponse =  csjBaseService.createAdPos(request);
        if (responseResponse.isSuccess()){
            return responseResponse.getData();
        }else {
            throw new RuntimeException(responseResponse.getMessage());
        }
    }

    private Integer convertToCsjType(Integer adType){
        switch (adType){
            case 1:return 3;
            case 2:return 1;
            case 3:return 4;
            case 4:return 5;
            case 5:return 9;
            case 6:return 2;
            default:throw new RuntimeException("填写正确的AdType");
        }
    }

    private String convertScene(Integer adType){
        switch (adType){
            case 1:return "FLASH";
            case 2:return "FLOW";
            case 3:
            case 5:
                return "INSERTION";
            case 4:
                return "REWARDED_VIDEO";
            case 6:
                return "BANNER";
            default:throw new RuntimeException("填写正确的AdType");
        }
    }

    private String[] convertFlush(List<String> adSubType){
        return adSubType.stream().map(this::convertFlush).collect(Collectors.toSet()).toArray(new String[adSubType.size()]);
    }

    private Integer convertFlushType(List<String> adSubType){
        if (adSubType.contains("16:9图片") && adSubType.contains("16:9视频") && adSubType.size() == 2){
            return 1;
        }
        if (adSubType.contains("9:16图片") && adSubType.contains("9:16视频") && adSubType.size() == 2){
            return 2;
        }
        if (adSubType.contains("16:9图片")&& adSubType.contains("16:9视频") && adSubType.contains("9:16视频") && adSubType.size() == 3){
            return 3;
        }
        if (adSubType.contains("16:9图片")&& adSubType.contains("16:9视频") && adSubType.contains("3:2图片") && adSubType.size() == 3){
            return 4;
        }
        if (adSubType.contains("16:9图片") && adSubType.size() == 1){
            return 5;
        }
        if (adSubType.contains("16:9图片")&&  adSubType.contains("3:2图片") && adSubType.size() == 2){
            return 6;
        }
        if (adSubType.contains("9:16图片") && adSubType.size() ==1){
            return 7;
        }
        if (adSubType.contains("16:9视频") && adSubType.size() ==1){
            return 8;
        }
        if (adSubType.contains("9:16视频") && adSubType.size() ==1){
            return 9;
        }
        if (adSubType.contains("16:9视频") && adSubType.contains("9:16视频") && adSubType.size() == 2){
            return 10;
        }
        if (adSubType.contains("16:9图片") && adSubType.contains("9:16图片") && adSubType.size() == 2){
            return 11;
        }
        if (adSubType.size() == 4 && adSubType.contains("16:9图片") && adSubType.contains("9:16图片")
        && adSubType.contains("16:9视频") && adSubType.contains("9:16视频")){
            return 14;
        }
        throw new RuntimeException("不支持的广告类型组和");
    }

    private String convertFlush(String adSubType){
        switch (adSubType){
            case "上文下图":return "TT_BP";
            case "左图右文":return "RP_LT";
            case "横版视频模板":return "H_IMG";
            default:throw new RuntimeException("填写正确的AdSubType");
        }
    }

    private Integer[] convertFlushCsj(List<String> adSubType){
        return adSubType.stream().map(this::convertFlushCsj).collect(Collectors.toList()).toArray(new Integer[adSubType.size()]);
    }

    private Integer convertFlushCsj(String adSubType){
        switch (adSubType){
            case "大图":return 1;
            case "组图":return 2;
            case "单图":return 3;
            case "横板视频":return 4;
            case "竖图":return 6;
            case "竖版视频":return 5;
            default:throw new RuntimeException("填写正确的AdSubType");
        }
    }

    private void setKsPrice(ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,ThirdPosAdRequest thirdPosAdRequest,Long posId){
        KsSetPriceRequest request = new KsSetPriceRequest();
        request.setAk(String.valueOf(companyEntity.getUserId()));
        request.setSk(companyEntity.getSecurityKey());
        request.setAppId(String.valueOf(appEntity.getAppId()));
        request.setPositionId(posId);
        request.setCpmFloor(thirdPosAdRequest.getAdPrice());

        KsResponse<Boolean> responseKsResponse = ksBaseService.setPositionPrice(request);
        if (!responseKsResponse.isSuccess()){
            throw new RuntimeException(responseKsResponse.getError_msg());
        }
    }
    //创建快手广告位 处理入参
    private Long createKsPos(ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,ThirdPosAdRequest thirdPosAdRequest){
        KsPositionRequest request = new KsPositionRequest();
        request.setAk(String.valueOf(companyEntity.getUserId()));
        request.setSk(companyEntity.getSecurityKey());
        request.setAppId(String.valueOf(appEntity.getAppId()));
        request.setName(thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName()));
        setKsBean(thirdPosAdRequest,request);

        KsResponse<String> responseKsResponse =  ksBaseService.createPosition(request);
        if (responseKsResponse.isSuccess()){
            return Long.valueOf(responseKsResponse.getData());
        }else {
            throw new RuntimeException(responseKsResponse.getError_msg());
        }
    }

    private OppoAdResp createPos(ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,ThirdPosAdRequest thirdPosAdRequest){

        OppoCreatePosRequest request = new OppoCreatePosRequest();
        OppoBaseReq baseReq = new OppoBaseReq();
        baseReq.setClientId(companyEntity.getUserId());
        baseReq.setClientSecret(companyEntity.getSecurityKey());

        request.setAppId(appEntity.getAppId());
        request.setPosName(thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName()));

        if (thirdPosAdRequest.getAdType() == 4){
            request.setVideoPlayDirection(2);
            request.setDevCrtType("45");
            request.setPosScene(5);
            request.setMultiEndPageStyles(1);
            request.setOpenVoiceStyle(1);
        }else {
            throw new RuntimeException("暂不支持创建的类型");
        }

        PriceType priceType = PriceType.getByCode(thirdPosAdRequest.getIsBidding());
        if (PriceType.BIDDING.equals(priceType)){
            request.setBiddingPattern(1);
        }else {
            request.setBiddingPattern(0);
            request.setTargetPriceOpen(1);
            request.setTargetPrice(thirdPosAdRequest.getAdPrice().intValue());
        }
        OppoResponse<OppoCreatePosRsp> response = oppoBaseService.createPos(request,baseReq);
        if (response.isSuccess()){
            // 查询
            OppoQueryListRequest queryRequest = new OppoQueryListRequest();
            queryRequest.setPage(1);
            queryRequest.setRows(200);
            queryRequest.setAppId(Integer.valueOf(appEntity.getAppId()));
            queryRequest.setStatus(1);
            queryRequest.setAuditStatus(2);
            queryRequest.setSortMode(0);
            queryRequest.setPosId(response.getData().getPosId());
            OppoResponse<OppoPageRsp> oppoResponse = oppoBaseService.queryPosList(queryRequest,baseReq);
            if (oppoResponse.isSuccess()){
                return oppoResponse.getData().getItems()
                        .stream()
                        .filter(r->r.getPosId().equals(response.getData().getPosId()))
                        .findFirst()
                        .get();
            }else {
                throw new RuntimeException(oppoResponse.getMsg());
            }
        }else {
            throw new RuntimeException(response.getMsg());
        }
    }

    // 百度广告位创建
    private CratePosResponse createBdPos(ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,ThirdPosAdRequest thirdPosAdRequest){
        BdAdPosCreateRequest request = new BdAdPosCreateRequest();
        request.setApp_sid(appEntity.getAppId());
        request.setAd_name(thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName()));
        BdAdPosCreateRequest.AdInfo adInfo = new BdAdPosCreateRequest.AdInfo();
        if (thirdPosAdRequest.getAdType() == 3 || thirdPosAdRequest.getAdType() == 5 ){
            request.setAd_type(34);
            adInfo.setInterstitial_ad_scene(1);
            adInfo.setInterstitial_material_types(Arrays.asList(1));
            adInfo.setInterstitial_style_types(Arrays.asList(1,2));
            if (thirdPosAdRequest.getAdTypeOwn().toString().startsWith(String.valueOf(AdType.BAIDU_FULL_CP.type))){
                adInfo.setInterstitial_material_types(Collections.singletonList(7));
                adInfo.setInterstitial_style_types(Collections.singletonList(4));
            }
            // 混排插屏加入
            if (thirdPosAdRequest.getInnerType() == 1){
                adInfo.setInterstitial_ad_scene(1);
                adInfo.setInterstitial_style_types(Arrays.asList(1,2,5));
                adInfo.setInterstitial_material_types(Arrays.asList(1,7));
            }else if (thirdPosAdRequest.getInnerType() == 2){
                adInfo.setInterstitial_ad_scene(1);
                adInfo.setInterstitial_style_types(Arrays.asList(1,2,4,5));
                adInfo.setInterstitial_material_types(Arrays.asList(1,7));
            }
        }else if (thirdPosAdRequest.getAdType() == 4){//激励
            request.setAd_type(44);
            adInfo.setReward_video_return_control(1);
            adInfo.setReward_video_return_url(CallBackUtils.getCallBackUrl(Platform.BD, thirdPosAdRequest.getAdPrice().intValue()));
            adInfo.setReward_video_voice_control(0);
            adInfo.setReward_video_ad_num(1);
            adInfo.setReward_video_multi_rewards(1);
        }else {
            throw new RuntimeException("暂不支持创建的类型");
        }
        request.setAd_info(adInfo);
        PriceType priceType = PriceType.getByCode(thirdPosAdRequest.getIsBidding());
        if (PriceType.BIDDING.equals(priceType)){
            request.setPrice_type(2);
        }else {
            request.setPrice_type(1);
            request.setCpm(thirdPosAdRequest.getAdPrice());
        }
        BdResponse<CratePosResponse> response = bdBaseService.createPos(request,companyEntity.getUserId(),companyEntity.getSecurityKey());
        if (response.isSuccess()){
            return response.getData();
        }else {
            throw new RuntimeException(response.getMessage());
        }
    }

    private GdtAddAdposResponse createGdtPos(ThirdCompanyEntity companyEntity,ThirdAppEntity appEntity,ThirdPosAdRequest thirdPosAdRequest){
        GdtAdPosCreateRequest request = new GdtAdPosCreateRequest();
        request.setMember_id(Long.valueOf(companyEntity.getUserId()));
        request.setApp_id(Long.valueOf(appEntity.getAppId()));
        request.setPlacement_name(thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName()));
        request.setScene(convertScene(thirdPosAdRequest.getAdType()));
        request.setRender_type("TEMPLATE");
        if (request.getScene().equals("FLASH")){
            request.setFlash_crt_type("FLASH");
            request.setRender_type("NORMAL");
            request.setAd_crt_type_list(new String[]{"FLASH_IMAGE_V"});
        }

        if (request.getScene().equals("REWARDED_VIDEO")){
            request.setRewarded_video_scene("LOGIN_REWARD");
            request.setRewarded_video_crt_type("ALL_DIRECTION");
            request.setAd_crt_type_list(new String[]{"VIDEO","IMAGE"});
            request.setAd_crt_normal_type(0);
            request.setRender_type("NORMAL");
            request.setNeed_server_verify("NeedServerVerify");
            request.setTransfer_url(CallBackUtils.getCallBackUrl(Platform.GDT,thirdPosAdRequest.getAdPrice().intValue()));
            request.setSecret(thirdPosAdRequest.getSecret());
        }
        request.setAd_pull_mode("SDK");



        if (request.getScene().equals("FLOW")) {
            if (Strings.isEmpty(thirdPosAdRequest.getRenderType())){
                throw new RuntimeException("渲染类型异常!请重新填写");
            }
            String render = "NORMAL";
            if ("1".equals(thirdPosAdRequest.getRenderType())){
                render = "TEMPLATE";
            }
            request.setRender_type(render);
            if ("TEMPLATE".equals(render)) {
                request.setAd_crt_template_type(convertFlush(thirdPosAdRequest.getAdSubType()));
                for (String type : request.getAd_crt_template_type()) {
                    if ("H_IMG".equals(type)) {
                        request.setAd_crt_type_list(new String[]{"VIDEO"});
                    } else {
                        request.setAd_crt_type_list(new String[]{"IMAGE"});
                    }
                    break;
                }
            }else {
                request.setAd_crt_normal_type(convertFlushType(thirdPosAdRequest.getAdSubType()));
                request.setCustom_position_scene_level3("FLOW_NORMAL_1");
                request.setScene(null);
            }
        }

        if ("INSERTION".equals(request.getScene())){
            request.setAd_crt_template_type(new String[]{"INLINE_VH"});
            request.setAd_crt_type_list(new String[]{"IMAGE","VIDEO"});

            if (thirdPosAdRequest.getAdTypeOwn().toString().startsWith(String.valueOf(AdType.GDT_NEW_FULL_CHAPIN.type))){
                request.setAd_crt_template_type(new String[]{"INLINE_FULL"});
                request.setAd_crt_type_list(new String[]{"VIDEO"});
            }

            // 插屏样式判断
            if (thirdPosAdRequest.getInnerType() == 1){
                request.setAd_crt_template_type(new String[]{"INLINE_VHS"});
                request.setAd_crt_type_list(new String[]{"IMAGE","VIDEO"});
            }else if (thirdPosAdRequest.getInnerType() == 2){
                request.setAd_crt_template_type(new String[]{"INLINE_FULL"});
                request.setAd_crt_type_list(new String[]{"IMAGE","VIDEO"});
            }
        }

        if ("BANNER".equals(request.getScene())){
            request.setRender_type("TEMPLATE");
            request.setAd_crt_type_list(new String[]{"IMAGE"});
            request.setAd_crt_template_type(new String[]{"BANNER_DP"});
        }

        if (thirdPosAdRequest.getAdPrice() != null && thirdPosAdRequest.getAdPrice() > 0) {
            request.setEcpm_price(thirdPosAdRequest.getAdPrice().intValue() * 100);
            request.setPrice_strategy_type("TargetPrice");
        }

        PriceType priceType = PriceType.getByCode(thirdPosAdRequest.getIsBidding());
        if (PriceType.BIDDING.equals(priceType)){
            request.setPrice_strategy_type("BiddingPrice");
            BiddingType biddingType = BiddingType.getByType(thirdPosAdRequest.getBiddingType());
            if (BiddingType.CLIENT.equals(biddingType)){
                request.setReal_time_bidding_type("Client_Bidding");
            }else if (BiddingType.SERVER.equals(biddingType)){
                request.setReal_time_bidding_type("Server_Bidding");
            }
            request.setEcpm_price(null);
        }

        GdtResponse<GdtAddAdposResponse> responseGdtResponse =  gdtBaseService.createAdPos(request,companyEntity.getSecurityKey());
        if (responseGdtResponse.isSuccess()){
            return responseGdtResponse.getData();
        }else {
            throw new RuntimeException(Optional.ofNullable(responseGdtResponse.getMsg())
                    .orElse(responseGdtResponse.getMessage()));
        }
    }

    public void taskList(Integer offset,Integer limit,String batchNo,Page<ThirdTaskQueenVo> page){

        page.setItems(thirdTaskQueenMapper.pageList(offset,limit,batchNo).stream().map(task ->{
            ThirdTaskQueenVo vo = new ThirdTaskQueenVo();
            vo.setTaskId(task.getTaskId());
            vo.setRemark(task.getRemark());
            vo.setCreateTime(DateUtil.dateToString(task.getCreateTime()));
            vo.setRetryTimes(task.getRetryTimes());
            vo.setTaskStatus(task.getTaskStatus());
            vo.setPosName(JSONObject.parseObject(task.getTaskParam(),ThirdPosAdRequest.class).buildThirdPosName("App"));
            return vo;
        }).collect(Collectors.toList()));
        page.setCount(thirdTaskQueenMapper.pageListCount(offset,limit,batchNo));
    }

    public void queryThirdList(Page<ThirdAdLowPriceVo> page,Platform platform,Integer os,Integer product){
        ThirdAppEntity thirdAppEntity = thirdAppMapper.queryByProductAndOs(product,os,platform.getCode());
        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
        switch (platform){
            case CSJ:
                queryCsjAdPosList(page,thirdAppEntity,thirdCompanyEntity,platform);
                break;
            case GDT:
                queryGdtAdPosList(page,thirdAppEntity,thirdCompanyEntity,platform);
                break;
            case KS:
                queryKsAdPosList(page,thirdAppEntity,thirdCompanyEntity,platform);
                break;
        }
    }

    private void queryCsjAdPosList(Page<ThirdAdLowPriceVo> page,ThirdAppEntity thirdAppEntity,ThirdCompanyEntity thirdCompanyEntity,Platform platform){
        AdPosQueryRequest request = new AdPosQueryRequest();
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setUser_id(Math.toIntExact(Long.parseLong(thirdCompanyEntity.getUserId())));
        request.setRole_id(thirdCompanyEntity.getRoleId());
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),timestamp,nonce));
        request.setPage(page.getPageNo());
        request.setPage_size(page.getPageSize());
        request.setVersion("1.0");
        request.setApp_id(new Integer[]{Math.toIntExact(Long.parseLong(thirdAppEntity.getAppId()))});
        request.setAd_slot_type(new Integer[]{4,5,9});
        Response<AdPosPageResponse> responseResponse = csjBaseService.queryAdPosList(request);
        if (responseResponse.isSuccess()){
            page.setCount(responseResponse.getData().getPageInfo().getTotal_number());
            page.setItems(responseResponse.getData().getAd_slot_list().stream().map( adPosInfo -> {
                ThirdAdLowPriceVo vo = new ThirdAdLowPriceVo();
                vo.setCodeStr(platform.getDesc());
                vo.setOsStr(thirdAppEntity.getOs() == 1?"android":"ios");
                vo.setPrice(String.valueOf(adPosInfo.getCpm()));
                vo.setPosId(String.valueOf(adPosInfo.getAd_slot_id()));
                vo.setPosName(adPosInfo.getAd_slot_name());
                return vo;
            }).collect(Collectors.toList()));
        }else {
            throw new RuntimeException(responseResponse.getMessage());
        }
    }

    private void queryGdtAdPosList(Page<ThirdAdLowPriceVo> page,ThirdAppEntity thirdAppEntity,ThirdCompanyEntity thirdCompanyEntity,Platform platform){
        GdtAdPosQueryRequest request = new GdtAdPosQueryRequest();
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
        request.setPage(page.getPageNo());
        request.setPage_size(page.getPageSize());
        List<GdtAppInfoQueryRequest.FilterBean> filterBeanList = new ArrayList<>();
        GdtAppInfoQueryRequest.FilterBean filterBean = new GdtAppInfoQueryRequest.FilterBean();
        filterBean.setField("app_id");
        filterBean.setOperator("EQUALS");
        filterBean.setValues(new String[]{String.valueOf(thirdAppEntity.getAppId())});
        filterBeanList.add(filterBean);
        request.setFiltering(filterBeanList);
        GdtResponse<GdtAdPosPageResponse> responseGdtResponse = gdtBaseService.queryAdPos(request,thirdCompanyEntity.getSecurityKey());
        if (responseGdtResponse.isSuccess()){
            page.setCount(responseGdtResponse.getData().getPage_info().getTotal_number());
            page.setItems(responseGdtResponse.getData().getList().stream()
                    .filter(gdtAdPosInfo -> gdtAdPosInfo.getScene().equals("REWARDED_VIDEO") ||
                            gdtAdPosInfo.getScene().equals("INSERTION"))
                    .map(adPosInfo ->{
                ThirdAdLowPriceVo vo = new ThirdAdLowPriceVo();
                vo.setCodeStr(platform.getDesc());
                vo.setOsStr(thirdAppEntity.getOs() == 1?"android":"ios");
                vo.setPrice(String.valueOf(adPosInfo.getEcpm_price()/100));
                vo.setPosId(String.valueOf(adPosInfo.getPlacement_id()));
                vo.setPosName(adPosInfo.getPlacement_name());
                return vo;
            }).collect(Collectors.toList()));
        }else {
            throw new RuntimeException(Optional.ofNullable(responseGdtResponse.getMsg())
                    .orElse(responseGdtResponse.getMessage()));
        }
    }

    private void queryKsAdPosList(Page<ThirdAdLowPriceVo> page,ThirdAppEntity thirdAppEntity,ThirdCompanyEntity thirdCompanyEntity,Platform platform){

        KsPositionQueryRequest request = new KsPositionQueryRequest();
        request.setAk(String.valueOf(Long.valueOf(thirdCompanyEntity.getUserId())));
        request.setSk(thirdCompanyEntity.getSecurityKey());
        request.setAppId(String.valueOf(thirdAppEntity.getAppId()));
        request.setPage(page.getPageNo());
        request.setPageSize(page.getPageSize());
        KsResponse<List<KsPositionResponse>> response = ksBaseService.queryPositionInfo(request);
        if (response.isSuccess()){
            List<ThirdAdLowPriceVo> results = response.getData().stream()
                    .filter(adPosInfo -> adPosInfo.getAd_style() == 2 || adPosInfo.getAd_style() == 3)
                    .map(adPosInfo->{
                ThirdAdLowPriceVo vo = new ThirdAdLowPriceVo();
                vo.setCodeStr(platform.getDesc());
                vo.setOsStr(thirdAppEntity.getOs() == 1?"android":"ios");
                vo.setPrice(String.valueOf(adPosInfo.getCpm_floor()));
                vo.setPosId(String.valueOf(adPosInfo.getPosition_id()));
                vo.setPosName(adPosInfo.getName());
                return vo;
            }).collect(Collectors.toList());
            page.setCount(response.getPage_info().getTotal_count());
            page.setItems(results);
        }else {
            throw new RuntimeException(response.getError_msg());
        }
    }

    public void retry(Integer taskId){
        ThirdTaskQueen thirdTaskQueen = thirdTaskQueenMapper.load(taskId);
        Optional.ofNullable(thirdTaskQueen).orElseThrow(() -> new RuntimeException("未找到该任务，请重试"));
        thirdTaskQueen.setRetryTimes(thirdTaskQueen.getRetryTimes() + 1);
        try {
            taskQueenRunner(thirdTaskQueen);
            thirdTaskQueen.setTaskStatus(TaskStatus.SUCCEED.getStatus());
            thirdTaskQueen.setRemark(TaskStatus.SUCCEED.getDesc());
        }catch (Exception e){
            log.error("重试创建第三方广告位失败 thirdTaskQueen : {} e : " , thirdTaskQueen , e);
            thirdTaskQueen.setTaskStatus(TaskStatus.FAILED.getStatus());
            thirdTaskQueen.setRemark(e.getMessage());
        }
        thirdTaskQueen.setUpdateTime(new Date());
        thirdTaskQueenMapper.update(thirdTaskQueen);
    }

    public String insertBatch(ThirdPosAdBatchRequest thirdPosAdBatchRequest, String account, String name){
        ThirdTemplateEntity thirdTemplateEntity = thirdTemplateMapper.load(thirdPosAdBatchRequest.getTemplateId());
        if (thirdTemplateEntity == null){
            throw new RuntimeException("获取模板失败");
        }

        List<AdTemplateVo> templateVoList = JSONArray.parseArray(thirdTemplateEntity.getModelDetail(),AdTemplateVo.class);
        if (templateVoList.size() == 0){
            throw new RuntimeException("模板数据错误");
        }

        String batchNo = UUID.randomUUID().toString();
        Date now = new Date();
        // 默认8件套的广告
        if (60319 == thirdPosAdBatchRequest.getTemplateId()){
            templateVoList.forEach(templateVo -> {
                // 开屏
                specialAdPosCreateDefaultConfig(thirdPosAdBatchRequest,account,name,now,batchNo,templateVo);
            });
        }else {
            templateVoList.forEach(templateVo -> {
                ThirdPosAdRequest thirdPosAdRequest = new ThirdPosAdRequest();
                thirdPosAdRequest.setAdPrice(templateVo.getAdPrice());
                thirdPosAdRequest.setAdSubType(templateVo.getAdTypeSub());
                thirdPosAdRequest.setAdType(templateVo.getAdType());
                thirdPosAdRequest.setAdSite(templateVo.getAdSite());
                thirdPosAdRequest.setAdTypeOwn(templateVo.getAdTypeOwn());
                thirdPosAdRequest.setInnerType(templateVo.getInnerType());

                DspEnum dspEnum = DspEnum.fromName(thirdTemplateEntity.getModelName());
                if (dspEnum != null) {
                    thirdPosAdRequest.setDsp(dspEnum.getCode());
                }
                setPub(thirdPosAdRequest,thirdPosAdBatchRequest,account,name,templateVo);

                initThirdTask(batchNo,thirdPosAdRequest,now);
            });
        }

        CompletableFuture.runAsync(() ->solveTaskJob(batchNo));
        return batchNo;
    }

    private void specialAdPosCreateDefaultConfig(ThirdPosAdBatchRequest thirdPosAdBatchRequest,
                                                 String account, String name,Date now,String batchNo,
                                                 AdTemplateVo templateVo){
        ThirdPosAdRequest thirdPosAdRequest = new ThirdPosAdRequest();
        setPub(thirdPosAdRequest, thirdPosAdBatchRequest, account, name, templateVo);
        if (templateVo.getAdName().contains("开屏")) {
            thirdPosAdRequest.setAdType(1);
            thirdPosAdRequest.setAdPrice(0d);
            thirdPosAdRequest.setAdSubType(templateVo.getAdTypeSub());
            thirdPosAdRequest.setAdSite(templateVo.getAdSite());
            if (Platform.CSJ.getCode().equals(templateVo.getAdPlatform())) {
                thirdPosAdRequest.setAdTypeOwn(AdType.TOUTIAO_OPENSCREEN.type);
            }else {
                thirdPosAdRequest.setAdTypeOwn(AdType.GDT_OPENSCREEN.type);
            }
            thirdPosAdRequest.setPosName("");
        }else if (templateVo.getAdName().contains("固定位") || templateVo.getAdName().contains("弹窗")){
            thirdPosAdRequest.setAdType(2);
            thirdPosAdRequest.setAdPrice(0d);
            thirdPosAdRequest.setAdSubType(templateVo.getAdTypeSub());
            thirdPosAdRequest.setAdSite(templateVo.getAdSite());
            thirdPosAdRequest.setRenderType("1");
            if (Platform.CSJ.getCode().equals(templateVo.getAdPlatform())) {
                thirdPosAdRequest.setAdTypeOwn(AdType.TOUTIAO_STATIC_TEMPLATE.type);
            }else {
                thirdPosAdRequest.setAdSubType(Collections.singletonList("上文下图"));
                thirdPosAdRequest.setAdTypeOwn(AdType.GDT_STATIC_TEMPLATE_2.type);
            }
            if (templateVo.getAdName().contains("弹窗")) {
                thirdPosAdRequest.setPosName("-弹窗");
            }
        }else {
            thirdPosAdRequest.setAdType(6);
            thirdPosAdRequest.setAdPrice(0d);
            thirdPosAdRequest.setAdSubType(templateVo.getAdTypeSub());
            thirdPosAdRequest.setAdSite(templateVo.getAdSite());
            if (Platform.CSJ.getCode().equals(templateVo.getAdPlatform())) {
                thirdPosAdRequest.setAdTypeOwn(AdType.TT_BANNER.type);
            }else {
                thirdPosAdRequest.setAdTypeOwn(AdType.GDT_BANNER.type);
            }
            thirdPosAdRequest.setPosName("");
        }

        initThirdTask(batchNo, thirdPosAdRequest, now);
    }

    private void setPub(ThirdPosAdRequest thirdPosAdRequest,ThirdPosAdBatchRequest thirdPosAdBatchRequest,String account,String name,AdTemplateVo templateVo){
        thirdPosAdRequest.setPosName(templateVo.getAdName());
        thirdPosAdRequest.setPlatformCode(templateVo.getAdPlatform());
        thirdPosAdRequest.setCsjCompanyId(thirdPosAdBatchRequest.getCsjCompanyId());
        thirdPosAdRequest.setCsjAppId(thirdPosAdBatchRequest.getCsjAppId());
        thirdPosAdRequest.setGdtCompanyId(thirdPosAdBatchRequest.getGdtCompanyId());
        thirdPosAdRequest.setGdtAppId(thirdPosAdBatchRequest.getGdtAppId());
        thirdPosAdRequest.setKsAppId(thirdPosAdBatchRequest.getKsAppId());
        thirdPosAdRequest.setKsCompanyId(thirdPosAdBatchRequest.getKsCompanyId());
        thirdPosAdRequest.setBdAppId(thirdPosAdBatchRequest.getBdAppId());
        thirdPosAdRequest.setBdCompanyId(thirdPosAdBatchRequest.getBdCompanyId());
        thirdPosAdRequest.setOppoCompanyId(thirdPosAdBatchRequest.getOppoCompanyId());
        thirdPosAdRequest.setOppoAppId(thirdPosAdBatchRequest.getOppoAppId());
        thirdPosAdRequest.setAccount(account);
        thirdPosAdRequest.setName(name);
        thirdPosAdRequest.setRenderType(templateVo.getRenderType());
        thirdPosAdRequest.setIsBidding(Optional.ofNullable(templateVo.getIsBidding()).orElse(PriceType.NORMAL.getCode()));
        if (thirdPosAdRequest.getIsBidding().equals(PriceType.BIDDING.getCode())) {
            thirdPosAdRequest.setBiddingType(templateVo.getBiddingType());
        }
    }

    private void initThirdTask(String batchNo,ThirdPosAdRequest thirdPosAdRequest,Date now){
        ThirdTaskQueen thirdTaskQueen = new ThirdTaskQueen();
        thirdTaskQueen.setBatchNo(batchNo);
        thirdTaskQueen.setTaskParam(JSON.toJSONString(thirdPosAdRequest));
        thirdTaskQueen.setTaskStatus(TaskStatus.INIT.getStatus());
        thirdTaskQueen.setRetryTimes(0);
        thirdTaskQueen.setCreateTime(now);
        thirdTaskQueen.setUpdateTime(now);
        thirdTaskQueenMapper.insert(thirdTaskQueen);
    }

    private void taskQueenRunner(ThirdTaskQueen thirdTaskQueen){
        ThirdPosAdRequest thirdPosAdRequest = JSONObject.parseObject(thirdTaskQueen.getTaskParam(),ThirdPosAdRequest.class);
        Platform platform = Platform.getPlatform(thirdPosAdRequest.getPlatformCode());
        if (Platform.CSJ.equals(platform)){
            ThirdCompanyEntity csjCompanyEntity = thirdCompanyMapper.load(thirdPosAdRequest.getCsjCompanyId());
            ThirdAppEntity csjAppEntity = thirdAppMapper.load(thirdPosAdRequest.getCsjAppId());
            thirdPosAdRequest.setCompanyId(csjCompanyEntity.getId());
            thirdPosAdRequest.setAppId(csjAppEntity.getId());
            insert(thirdPosAdRequest,csjCompanyEntity,csjAppEntity,thirdPosAdRequest.getAccount(),thirdPosAdRequest.getName());
        }else if (Platform.GDT.equals(platform)){
            ThirdCompanyEntity gdtCompanyEntity = thirdCompanyMapper.load(thirdPosAdRequest.getGdtCompanyId());
            ThirdAppEntity gdtAppEntity = thirdAppMapper.load(thirdPosAdRequest.getGdtAppId());
            thirdPosAdRequest.setCompanyId(gdtCompanyEntity.getId());
            thirdPosAdRequest.setAppId(gdtAppEntity.getId());
            insert(thirdPosAdRequest,gdtCompanyEntity,gdtAppEntity,thirdPosAdRequest.getAccount(),thirdPosAdRequest.getName());
        } else if (Platform.KS.equals(platform)){
            ThirdCompanyEntity ksCompanyEntity = thirdCompanyMapper.load(thirdPosAdRequest.getKsCompanyId());
            ThirdAppEntity ksAppEntity = thirdAppMapper.load(thirdPosAdRequest.getKsAppId());
            thirdPosAdRequest.setCompanyId(ksCompanyEntity.getId());
            thirdPosAdRequest.setAppId(ksAppEntity.getId());
            insert(thirdPosAdRequest,ksCompanyEntity,ksAppEntity,thirdPosAdRequest.getAccount(),thirdPosAdRequest.getName());
        } else if (Platform.BD.equals(platform)){
            ThirdCompanyEntity bdCompanyEntity = thirdCompanyMapper.load(thirdPosAdRequest.getBdCompanyId());
            ThirdAppEntity bdAppEntity = thirdAppMapper.load(thirdPosAdRequest.getBdAppId());
            thirdPosAdRequest.setCompanyId(bdCompanyEntity.getId());
            thirdPosAdRequest.setAppId(bdAppEntity.getId());
            insert(thirdPosAdRequest,bdCompanyEntity,bdAppEntity,thirdPosAdRequest.getAccount(),thirdPosAdRequest.getName());
        } else if (Platform.OPPO.equals(platform)){
            ThirdCompanyEntity bdCompanyEntity = thirdCompanyMapper.load(thirdPosAdRequest.getOppoCompanyId());
            ThirdAppEntity bdAppEntity = thirdAppMapper.load(thirdPosAdRequest.getOppoAppId());
            thirdPosAdRequest.setCompanyId(bdCompanyEntity.getId());
            thirdPosAdRequest.setAppId(bdAppEntity.getId());
            insert(thirdPosAdRequest,bdCompanyEntity,bdAppEntity,thirdPosAdRequest.getAccount(),thirdPosAdRequest.getName());
        }
    }

    private void sleep(Long mic){
        try {
            Thread.sleep(mic);
        } catch (InterruptedException e) {
            log.error("SleepError:",e);
        }
    }

    private void solveTaskJob(String batchNo){
        List<ThirdTaskQueen> thirdTaskQueens = thirdTaskQueenMapper.selectByBatch(batchNo);
        thirdTaskQueens.forEach(thirdTaskQueen -> {
            try{
                taskQueenRunner(thirdTaskQueen);
                thirdTaskQueen.setTaskStatus(TaskStatus.SUCCEED.getStatus());
                thirdTaskQueen.setRemark(TaskStatus.SUCCEED.getDesc());
            }catch (Exception e){
                log.error("创建第三方广告位失败 thirdTaskQueen : {} e : " , thirdTaskQueen , e);
                thirdTaskQueen.setTaskStatus(TaskStatus.FAILED.getStatus());
                thirdTaskQueen.setRemark(e.getMessage());
            }
            thirdTaskQueen.setUpdateTime(new Date());
            thirdTaskQueenMapper.update(thirdTaskQueen);
        });
    }

    public void update(ThirdPosAdRequest thirdPosAdRequest, String account, String name){
        ThirdAdPosEntity entity = thirdAdPosMapper.load(thirdPosAdRequest.getId());
        threadLocalBoolean.set(Boolean.FALSE);
        ThirdCompanyEntity companyEntity = thirdCompanyMapper.load(thirdPosAdRequest.getCompanyId());

        Platform platform = Platform.getPlatform(entity.getPlatformCode());
        if (Platform.CSJ.equals(platform)){
            updateCsjAdPos(companyEntity,thirdPosAdRequest,entity);
        }else if (Platform.GDT.equals(platform)){
            updateGdtAdPos(companyEntity,thirdPosAdRequest,entity);
        }else if (Platform.KS.equals(platform)){
            updateKsAdPos(companyEntity,thirdPosAdRequest,entity);
        }

        ThirdAdPosEntity record = new ThirdAdPosEntity();
        record.setId(thirdPosAdRequest.getId());
        record.setPosName(thirdPosAdRequest.getPosName());
        if (threadLocalBoolean.get()) {
            record.setAdPrice(BigDecimal.valueOf(thirdPosAdRequest.getAdPrice()));
        }
        AdTypeCreateVo adTypeCreateVo = new AdTypeCreateVo();
        adTypeCreateVo.setAdType(thirdPosAdRequest.getAdType());
        adTypeCreateVo.setAdSubType(thirdPosAdRequest.getAdSubType());
        adTypeCreateVo.setAdTypeOwn(thirdPosAdRequest.getAdTypeOwn());
        adTypeCreateVo.setAdSite(thirdPosAdRequest.getAdSite());
        record.setAdType(adTypeCreateVo.toString());
        record.setUpdateTime(new Date());
        thirdAdPosMapper.update(record);
        thirdLogService.saveLog(account,name,OpType.UPDATE_ADPOS,entity.getId(),JSON.toJSONString(entity),JSON.toJSONString(record));
        threadLocalBoolean.remove();
    }

    private void updateCsjAdPos(ThirdCompanyEntity companyEntity,ThirdPosAdRequest thirdPosAdRequest,ThirdAdPosEntity entity){
        AdPosUpdateRequest request = new AdPosUpdateRequest();
        request.setUser_id(Integer.valueOf(companyEntity.getUserId()));
        request.setRole_id(companyEntity.getRoleId());
        request.setMask_rule_id(companyEntity.getMaskRuleId());
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(companyEntity.getSecurityKey(),timestamp,nonce));
        request.setVersion("1.0");
        request.setAd_slot_id(Math.toIntExact(entity.getPosId()));
        request.setAd_slot_name(thirdPosAdRequest.getPosName());
        if (entity.getAdPrice().doubleValue() != thirdPosAdRequest.getAdPrice()){
            int day = DateUtil.daysBetween(entity.getCreateTime(),new Date());
            if (thirdPosAdRequest.getAdPrice() != null && thirdPosAdRequest.getAdPrice() > 0 && day >= 1) {
                request.setCpm(String.valueOf(thirdPosAdRequest.getAdPrice().intValue()));
                threadLocalBoolean.set(Boolean.TRUE);
            }
        }

        Response<AdPosUpdateResponse> responseResponse = csjBaseService.updateAdPos(request);
        if (!responseResponse.isSuccess()){
            throw new RuntimeException(responseResponse.getMessage());
        }
    }

    private void updateGdtAdPos(ThirdCompanyEntity companyEntity,ThirdPosAdRequest thirdPosAdRequest,ThirdAdPosEntity entity){
        GdtAdPosUpdateRequest request = new GdtAdPosUpdateRequest();
        request.setMember_id(Long.valueOf(companyEntity.getUserId()));
        request.setPlacement_id(entity.getPosId());
        request.setPlacement_name(thirdPosAdRequest.getPosName());

        if (entity.getAdPrice().doubleValue() != thirdPosAdRequest.getAdPrice()){
            int day = DateUtil.daysBetween(entity.getCreateTime(),new Date());
            if (thirdPosAdRequest.getAdPrice() != null && thirdPosAdRequest.getAdPrice() > 0 && day >= 1) {
                request.setEcpm_price((long) (thirdPosAdRequest.getAdPrice().intValue() * 100));
                request.setPrice_strategy_type("TargetPrice");
                threadLocalBoolean.set(Boolean.TRUE);
            }
        }

        GdtResponse<Void> gdtResponse = gdtBaseService.updateAdPos(request,companyEntity.getSecurityKey());
        if (!gdtResponse.isSuccess()){
            throw new RuntimeException(gdtResponse.getMsg());
        }
    }

    private void setKsBean(ThirdPosAdRequest thirdPosAdRequest,KsPositionRequest ksPositionRequest){
        if (PriceType.NORMAL.getCode().equals(thirdPosAdRequest.getIsBidding())){
            ksPositionRequest.setPriceStrategy(1);
        }else if (PriceType.BIDDING.getCode().equals(thirdPosAdRequest.getIsBidding())){
            ksPositionRequest.setPriceStrategy(2);
            if (BiddingType.CLIENT.getType().equals(thirdPosAdRequest.getBiddingType())){
                ksPositionRequest.setBiddingStrategy(1);
            }else {
                ksPositionRequest.setBiddingStrategy(2);
            }
        }

        if (thirdPosAdRequest.getAdTypeOwn().toString().startsWith(String.valueOf(AdType.KS_FULLSCREEN.type))){
            // 快手全屏视频
            ksPositionRequest.setRenderType(1);
            ksPositionRequest.setAdStyle(3);
            ksPositionRequest.setMaterialTypeList(Collections.singletonList(1));
            // 设置完就结束
            return;
        }

        if (thirdPosAdRequest.getAdType() == 1){
            ksPositionRequest.setAdStyle(4);
            ksPositionRequest.setSkipAdMode(-1);
            ksPositionRequest.setRenderType(2);
            ksPositionRequest.setTemplateId(1000);
            ksPositionRequest.setMaterialTypeList(new ArrayList<Integer>(){{
                add(1);
                add(3);
            }});
            ksPositionRequest.setCountdownShow(1);
            ksPositionRequest.setVoice(1);
        }else if (thirdPosAdRequest.getAdType() == 2){
            ksPositionRequest.setMaterialTypeList(new ArrayList<Integer>(){{
                add(2);
                add(6);
            }});
            ksPositionRequest.setAdStyle(1);
            ksPositionRequest.setRenderType(2);
            ksPositionRequest.setTemplateId(7);
        }else if (thirdPosAdRequest.getAdType() == 3){//插屏
            ksPositionRequest.setRenderType(2);
            ksPositionRequest.setTemplateId(9);
            ksPositionRequest.setAdStyle(13);
            ksPositionRequest.setMaterialTypeList(new ArrayList<Integer>(){{
                add(1);
                add(2);
                add(5);
                add(6);
            }});
        }else if (thirdPosAdRequest.getAdType() == 4){
            ksPositionRequest.setMaterialTypeList(new ArrayList<Integer>(){{
                add(1);
            }});
            ksPositionRequest.setRenderType(1);
            ksPositionRequest.setAdStyle(2);
            ksPositionRequest.setRewardedType(11);
            ksPositionRequest.setRewardedNum(100);
            ksPositionRequest.setCallbackStatus(1);
            ksPositionRequest.setCallbackUrl(CallBackUtils.getCallBackUrl(Platform.KS, thirdPosAdRequest.getAdPrice().intValue()));
        }else if(thirdPosAdRequest.getAdType() == 5){//5新插屏
            ksPositionRequest.setRenderType(2);
            ksPositionRequest.setTemplateId(9);
            ksPositionRequest.setAdStyle(23);

            Integer innerType = thirdPosAdRequest.getInnerType();
            handleKsPositionRequest(ksPositionRequest, innerType);
        }
    }

    private static void handleKsPositionRequest(KsPositionRequest ksPositionRequest, Integer innerType) {
        LinkedList<Integer> materialTypeList = new LinkedList<>();
        materialTypeList.add(5);
        materialTypeList.add(6);

        if(AdInnerType.AUTO_ALL.check(innerType)){
            ksPositionRequest.setAdRolloutSize(AdRolloutSize.AUTO.getType());
            materialTypeList.addFirst(2);
            materialTypeList.addFirst(1);
        }else if(AdInnerType.HALF_IMG_VIDEO.check(innerType)){
            ksPositionRequest.setAdRolloutSize(AdRolloutSize.HALF.getType());
            materialTypeList.addFirst(2);
            materialTypeList.addFirst(1);
        }else if(AdInnerType.HALF_IMG.check(innerType)){
            ksPositionRequest.setAdRolloutSize(AdRolloutSize.HALF.getType());
        }else{
            throw new IllegalArgumentException("innerType illegal");
        }
        ksPositionRequest.setMaterialTypeList(materialTypeList);
    }

    private void updateKsAdPos(ThirdCompanyEntity companyEntity,ThirdPosAdRequest thirdPosAdRequest,ThirdAdPosEntity entity){
        KsPositionRequest ksPositionRequest =  new KsPositionRequest();
        ksPositionRequest.setAk(String.valueOf(companyEntity.getUserId()));
        ksPositionRequest.setSk(companyEntity.getSecurityKey());
        ksPositionRequest.setAppId(String.valueOf(entity.getAppId()));
        ksPositionRequest.setName(thirdPosAdRequest.getPosName());
        ksPositionRequest.setPositionId(entity.getPosId());

        setKsBean(thirdPosAdRequest,ksPositionRequest);
        KsResponse<Boolean> response = ksBaseService.updatePosition(ksPositionRequest);
        if (!response.isSuccess() || !response.getData()){
            throw new RuntimeException(String.format("修改快手广告位失败:%s", response.getError_msg()));
        }
        if (entity.getAdPrice().doubleValue() != thirdPosAdRequest.getAdPrice()){
            int day = DateUtil.daysBetween(entity.getCreateTime(),new Date());
            if (thirdPosAdRequest.getAdPrice() == null || thirdPosAdRequest.getAdPrice() <= 0 || day < 1) {
                return;
            }
        }
        if (entity.getAdPrice().doubleValue() != thirdPosAdRequest.getAdPrice()){
            if (thirdPosAdRequest.getAdType() == 3 || thirdPosAdRequest.getAdType() == 4) {
                sleep(1000L);
                threadLocalBoolean.set(Boolean.TRUE);
                KsSetPriceRequest ksSetPriceRequest = new KsSetPriceRequest();
                ksSetPriceRequest.setAppId(String.valueOf(entity.getAppId()));
                ksSetPriceRequest.setCpmFloor(thirdPosAdRequest.getAdPrice());
                ksSetPriceRequest.setPositionId(entity.getPosId());
                ksSetPriceRequest.setAk(String.valueOf(companyEntity.getUserId()));
                ksSetPriceRequest.setSk(companyEntity.getSecurityKey());
                KsResponse<Boolean> booleanKsResponse = ksBaseService.setPositionPrice(ksSetPriceRequest);
                if (!booleanKsResponse.isSuccess() || !booleanKsResponse.getData()){
                    throw new RuntimeException(String.format("修改快手广告位价格失败:%s", booleanKsResponse.getError_msg()));
                }
            }
        }
    }

    /**
     * 更新开关状态
     * @param id
     * @param state
     * @param account
     * @param name
     */
    public void updateSwitch(Integer id,Integer state,String account,String name){
        ThirdAdPosEntity entity = thirdAdPosMapper.load(id);

        if (entity == null){
            throw new RuntimeException("未找到对应的广告位");
        }

        ThirdAdPosEntity record = new ThirdAdPosEntity();
        record.setId(entity.getId());
        record.setSwitchFlag(state);
        record.setUpdateTime(new Date());

        thirdAdPosMapper.update(record);

        thirdLogService.saveLog(account,name,OpType.UPDATE_ADPOS,entity.getId(),JSON.toJSONString(entity),JSON.toJSONString(record));
    }


    public void loadAdToOurSystem(List<Integer> ids,Integer importType,Integer biddingImportType,String account,String name) {
        if (Boolean.FALSE.equals(autoFlag.get())){
            throw new RuntimeException("任务在后台执行中...请勿重新提交!");
        }
        List<ThirdAdPosEntity> thirdAdPosEntityList = thirdAdPosMapper.queryList(ids);
        if (thirdAdPosEntityList == null || thirdAdPosEntityList.size() == 0) {
            throw new RuntimeException("查询失败...");
        }

        CompletableFuture.runAsync(()-> {
            autoFlag.set(Boolean.FALSE);
            try {
                // 我方创建广告
                List<Ad> adList = loadAdToOurSystem(thirdAdPosEntityList,ids,account,name);
                // 老广告关闭
                closeOldAd(adList,importType);
                // 补充回调URL
                adList.forEach(ad -> thirdCallBackService.setSingleUrl((long) ad.getId(),ad.getProduct()));
                // 创建Bidding
                biddingDefaultCreate(biddingImportType,adList);
            }catch (Exception e){
                log.error("Error:",e);
            }
            autoFlag.set(Boolean.TRUE);
        });
    }

    private void closeOldAd(List<Ad> adList,Integer importType){
        if (importType == 2){
            log.info("需要对老广告进行关闭...");
            Map<Integer,Ad> adTypeQueryMap = adList.stream()
                    .sorted(Comparator.comparingInt(Ad::getId))
                    .collect(Collectors.toMap(Ad::getType,ad->ad,(a1,a2) -> a2));
            adTypeQueryMap.forEach((adType,ad) ->{
                List<Ad> needShutDownList = adAdminService.selectAllAds(null,null,null,
                        String.valueOf(adType),null,null,ad.getProduct(), null,null,null,null,null);

                String appId = Strings.noEmpty(ad.getExt().getAndroidAppId()) ? ad.getExt().getAndroidAppId():ad.getExt().getIosAppId();
                if (Strings.isEmpty(appId)){
                    log.error("未获取到APP_ID..,跳过广告：{}-{} 的同类型关停",ad.getId(),ad.getType());
                    return;
                }
                for (Ad adNeedShutDown : needShutDownList){
                    if (adNeedShutDown.getId() == ad.getId()){
                        log.info("正在导入的广告不执行关停....");
                        continue;
                    }
                    String appIdSt = Strings.noEmpty(adNeedShutDown.getExt().getAndroidAppId()) ? adNeedShutDown.getExt().getAndroidAppId():adNeedShutDown.getExt().getIosAppId();
                    if (appId.equals(appIdSt)){
                        // 关闭老玩意
                        Integer count = adAdminService.changeAdInfoFlag(adNeedShutDown.getId(),0);
                        if (count > 0){
                            log.info("广告-{} 已被关闭..",adNeedShutDown.getId());
                        }else {
                            log.info("广告关闭更新条数为0，id：{}",adNeedShutDown.getId());
                        }
                    }
                }
            });
        }
    }

    private void biddingDefaultCreate(Integer biddingImportType,List<Ad> adList){
        if (biddingImportType == 1){
            List<Ad> needToSetBidPriceAdList = adList.stream()
                    .filter(ad -> ad.getName().toLowerCase().contains("bidding"))
                    .collect(Collectors.toList());
            if (needToSetBidPriceAdList.size() > 0){
                needToSetBidPriceAdList.forEach(r -> {
                    // 获取广告位类型
                    AdTypeSub typeSub = AdTypeSub.get(r.getType());
                    if (typeSub == null){
                        return;
                    }
                    Integer posType = null;
                    if (typeSub.getTypeDesc().contains("全屏")){
                        posType = AdPosType.Full_screen_video.getCode();
                    }else if (typeSub.getTypeDesc().contains("视频")){
                        posType = AdPosType.Rewarded_video.getCode();
                    }else if (typeSub.getTypeDesc().contains("插屏")){
                        posType = AdPosType. Table_plaque.getCode();
                    }
                    if (posType == null){
                        return;
                    }
                    // 默认设置为 Bidding 实时竞价广告
                    biddingConfigService.insert(r.getProduct(), posType, (long) r.getId(), 0, 3000, 0, 1, 1);
                });
            }
        }
    }

    public List<Ad> loadAdToOurSystem(List<ThirdAdPosEntity> thirdAdPosEntityList,List<Integer> ids,String account,String name){
        List<Ad> createAdIdList = new ArrayList<>();
        List<Integer> applicationList = thirdAdPosEntityList.stream().map(ThirdAdPosEntity::getApplicationId).collect(Collectors.toList());
        // 查询广告基础类型
        List<ThirdAppEntity> thirdAppEntities = thirdAppMapper.queryList(applicationList);
        Map<Integer,ThirdAppEntity> thirdAppEntityMap = thirdAppEntities.stream().collect(Collectors.toMap(ThirdAppEntity::getId,k->k));
        Map<Integer,List<ThirdAdPosEntity>> adPosMap = thirdAdPosEntityList.stream().collect(Collectors.groupingBy(ThirdAdPosEntity::getApplicationId));

        adPosMap.forEach((product,list) ->{
            ThirdAppEntity appEntity = thirdAppEntityMap.get(product);
            if (appEntity == null || appEntity.getProduct() == null){
                throw new RuntimeException("应用配置异常，无法导入");
            }
            // 导入广告系统
            List<ExcelFileAdConfVo> excelFileAdConfVoList = list.stream().map(entity -> {
                        String os = appEntity.getOs() == 1 ? "android" : "ios";
                        Platform platformEnum = Platform.getPlatform(entity.getPlatformCode());
                        String platform = platformEnum.getDesc().toLowerCase();
                        AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
                        Integer adIntTypeOwn = adTypeCreateVo.getAdTypeOwn();
                        Integer adIntType = adTypeCreateVo.getAdType();
                        List<Integer> adSiteIntType = null;
                        if (adIntType == 2){
                            adSiteIntType = adTypeCreateVo.getAdSite();
                        }
                        String adType = convertToAdTypeSting(adIntType,adSiteIntType);
                        String adTypeOwn;
                        // 记得开屏没层
                        if (adIntType == 1) {
                            adTypeOwn = chooseAdType(adIntType,entity.getPlatformCode()).desc();
                        }else {
                            if (adIntTypeOwn == null){
                                throw new RuntimeException("自家广告类型未填写！请填写广告类型");
                            }
                            adTypeOwn = AdTypeSub.get(adIntTypeOwn).getTypeDesc();
                        }
                        // 绑定兼容
                        if (adIntTypeOwn.toString().startsWith(String.valueOf(AdType.CSJ_NEW_FULL_CHAPIN.type))
                                || adIntTypeOwn.toString().startsWith(String.valueOf(AdType.GDT_NEW_FULL_CHAPIN.type))
                                || adIntTypeOwn.toString().startsWith(String.valueOf(AdType.KS_FULLSCREEN.type))){
                            adType = "全屏视频";
                        }else if (adIntTypeOwn.toString().startsWith(String.valueOf(AdType.TT_BANNER.type))
                                ||adIntTypeOwn.toString().startsWith(String.valueOf(AdType.GDT_BANNER.type))){
                            adType = "banner";
                        }else if (entity.getPosName().contains("-弹窗")){
                            adType = "弹窗";
                        } else if (adIntTypeOwn.toString().startsWith(String.valueOf(AdType.TOUTIAO_STATIC_TEMPLATE.type))
                                ||adIntTypeOwn.toString().startsWith(String.valueOf(AdType.GDT_STATIC_TEMPLATE_2.type))){
                            adType = "固定位";
                        }
                        // 特殊模板更
                        if (entity.getPosName().contains("-pslj-")){
                            adType = "激励视频";
                        }
                        if (entity.getPosName().contains("-bphc-")){
                            adType = "插屏";
                        }

                        entity.setBasePrice(adType, os);

                        return new ExcelFileAdConfVo(new String[]{os,
                                entity.getPosName(),adType,platform,
                                adTypeOwn,
                                String.valueOf(entity.getPosId()),String.valueOf(entity.getAppId()),"",
                                String.valueOf(entity.getAdPrice().intValue() == 0?10:entity.getAdPrice().intValue()),""
                        });
            }).collect(Collectors.toList());

            List<Ad> adList = excelConfigService.convertToAd(excelFileAdConfVoList,appEntity.getProduct());
            adList.forEach(adAdminService::insertAd);
            // Bidding
            createAdIdList.addAll(adList);
        });
        // 变更状态为已导入
        int updateCount = thirdAdPosMapper.updateStateToAlreadyLoad(ids);
        log.info("本次提交导入成功{}条",updateCount);
        //记录变更日志
        ids.forEach(id->{
            thirdLogService.saveLog(account,name,OpType.LOAD_ADPOS,id,null,null);
        });
        return createAdIdList;
    }

    private String convertToAdTypeSting(Integer adType,List<Integer> adSite){
        switch (adType){
            case 1:return "开屏";
            case 2:
                return "固定位";
            case 3:
            case 5:
                return "插屏";
            case 4:return "激励视频";
            case 6:return "banner";
            default:throw new RuntimeException("广告类型异常！无法继续导入");
        }
    }

    private AdType chooseAdType(Integer adType,Integer platform){
        Platform platformCode = Platform.getPlatform(platform);
        switch (platformCode){
            case CSJ:
                switch (adType){
                    case 1:return AdType.TOUTIAO_OPENSCREEN;
                    case 2:return AdType.TOUTIAO_STATIC_TEMPLATE;
                    case 3:return AdType.CSJ_CHAPING;
                    case 4:return AdType.TOUTIAO_VIDEO_TEMPLATE;
                    default:throw new RuntimeException("广告类型异常！无法继续导入");
                }
            case GDT:
                switch (adType){
                    case 1:return AdType.GDT_OPENSCREEN;
                    case 2:return AdType.GDT_TEMPLATE;
                    case 3:return AdType.GDT_CHAPING;
                    case 4:return AdType.GDT_VIDEO;
                    default:throw new RuntimeException("广告类型异常！无法继续导入");
                }
            case KS:
                switch (adType){
                    case 1:return AdType.KS_SPLASH;
                    case 2:return AdType.KS_STATIC;
                    case 3:return AdType.KS_CHAPIN;
                    case 4:return AdType.KS_VIDEO;
                    default:throw new RuntimeException("广告类型异常！无法继续导入");
                }
            default:
                throw new RuntimeException("广告平台填写异常");
        }
    }

    /**
     * 查询今日创建的异常广告位
     * @return
     */
    public List<ThirdAdPosEntity> queryTodayCreatedPos(){
        Date now = new Date();
        Date crTime = DateUtil.stringToDateWithTime(DateUtil.dateToString(now) + " 00:00:00");
        String likeCr = "%" + DateUtil.dateToString(now,DateUtil.COMMON_DATE_FORMAT) +"%";

        return thirdAdPosMapper.queryCreateExPos(crTime,likeCr);
    }

    public void insertOrUpdate(List<ThirdAdPosEntity> thirdAdPosEntities){
        thirdAdPosEntities.forEach(entity -> {
            Integer count = thirdAdPosMapper.existCount(entity.getPosId());
            if (count == 0){
                thirdAdPosMapper.insert(entity);
            }
        });
    }

    public List<ThirdAdPosEntity> queryByPosId(List<Long> posIds){
        return thirdAdPosMapper.queryListByPosId(posIds);
    }

    public BigDecimal queryThirdAdInfo(ThirdPlatformType thirdPlatformType,ThirdAppEntity thirdAppEntity,Long posId){
        // 分平台查询单个广告位
        ThirdCompanyEntity companyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
        switch (thirdPlatformType){
            case KS:
                Double cpm = queryKsSingleAdPos(companyEntity,thirdAppEntity,posId).getCpm_floor();
                return BigDecimal.valueOf(cpm == null?0:cpm);
            case CSJ:return queryCsjSingleAdPos(companyEntity,thirdAppEntity,posId);
            case GDT:return queryGdtSingleAdPos(companyEntity,thirdAppEntity,posId);
            case BD:
            case NONE:
            default:
                throw new RuntimeException("不支持的广告位平台...");
        }
    }

    /**
     * 查询穿山甲广告
     * @param thirdCompanyEntity CompanyEntity
     * @param thirdAppEntity AppEntity
     * @param posId 广告位Id
     * @return
     */
    private BigDecimal queryCsjSingleAdPos(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,Long posId){
        AdPosQueryRequest request = new AdPosQueryRequest();
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setUser_id(Math.toIntExact(Long.valueOf(thirdCompanyEntity.getUserId())));
        request.setRole_id(thirdCompanyEntity.getRoleId());
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),timestamp,nonce));
        request.setPage(1);
        request.setPage_size(10);
        request.setVersion("1.0");
        request.setApp_id(new Integer[]{Math.toIntExact(Long.parseLong(thirdAppEntity.getAppId()))});
        request.setAd_slot_id(new Integer[]{Math.toIntExact(posId)});

        Response<AdPosPageResponse> responseResponse = csjBaseService.queryAdPosList(request);
        if (responseResponse.isSuccess()){
            Double cpm = responseResponse.getData().getAd_slot_list().get(0).getCpm();
            return BigDecimal.valueOf(cpm == null?0:cpm);
        }else {
            throw new RuntimeException("[CSJ]-三方价格获取失败....");
        }
    }

    private List<AdPosInfo> queryCsjBatchAdPos(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,List<Long> posIds){
        AdPosQueryRequest request = new AdPosQueryRequest();
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setUser_id(Math.toIntExact(Long.parseLong(thirdCompanyEntity.getUserId())));
        request.setRole_id(thirdCompanyEntity.getRoleId());
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),timestamp,nonce));
        request.setPage(1);
        request.setPage_size(100);
        request.setVersion("1.0");
        request.setApp_id(new Integer[]{Math.toIntExact(Long.parseLong(thirdAppEntity.getAppId()))});
        request.setAd_slot_id(posIds.stream().map(Math::toIntExact).collect(Collectors.toList()).toArray(new Integer[posIds.size()]));

        Response<AdPosPageResponse> responseResponse = csjBaseService.queryAdPosList(request);
        if (responseResponse.isSuccess()){
            return responseResponse.getData().getAd_slot_list();
        }else {
            throw new RuntimeException("[CSJ]-三方价格获取失败....");
        }
    }

    public GdtAdPosPageResponse queryGdtSingleAdPosModel(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,Long posId){
        GdtAdPosQueryRequest request = new GdtAdPosQueryRequest();
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
        request.setPage(1);
        request.setPage_size(10);
        GdtAppInfoQueryRequest.FilterBean filterBean = new GdtAppInfoQueryRequest.FilterBean();
        filterBean.setField("app_id");
        filterBean.setOperator("EQUALS");
        filterBean.setValues(new String[]{String.valueOf(thirdAppEntity.getAppId())});

        GdtAppInfoQueryRequest.FilterBean filterBeanPlacement = new GdtAppInfoQueryRequest.FilterBean();
        filterBeanPlacement.setField("placement_id");
        filterBeanPlacement.setOperator("EQUALS");
        filterBeanPlacement.setValues(new String[]{String.valueOf(posId)});
        request.setFiltering(Arrays.asList(filterBeanPlacement,filterBean));
        GdtResponse<GdtAdPosPageResponse> gdtResponse = gdtBaseService.queryAdPos(request,thirdCompanyEntity.getSecurityKey());
        if (gdtResponse.isSuccess()){
            return gdtResponse.getData();
        }else {
            throw new RuntimeException("[GDT]-三方价格获取失败....");
        }
    }

    private BigDecimal queryGdtSingleAdPos(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,Long posId){
        GdtAdPosPageResponse gdtResponse = queryGdtSingleAdPosModel(thirdCompanyEntity,thirdAppEntity,posId);
        Integer ecpm = gdtResponse.getList().get(0).getEcpm_price();
        return BigDecimal.valueOf(ecpm == null?0:ecpm/100);
    }

    private List<GdtAdPosInfo> queryGdtBatchList(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,List<Long> posIds){
        GdtAdPosQueryRequest request = new GdtAdPosQueryRequest();
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
        request.setPage(1);
        request.setPage_size(100);
        GdtAppInfoQueryRequest.FilterBean filterBean = new GdtAppInfoQueryRequest.FilterBean();
        filterBean.setField("app_id");
        filterBean.setOperator("EQUALS");
        filterBean.setValues(new String[]{String.valueOf(thirdAppEntity.getAppId())});

        GdtAppInfoQueryRequest.FilterBean filterBeanPlacement = new GdtAppInfoQueryRequest.FilterBean();
        filterBeanPlacement.setField("placement_id");
        filterBeanPlacement.setOperator("IN");
        filterBeanPlacement.setValues(posIds.stream().map(String::valueOf).collect(Collectors.toList()).toArray(new String[posIds.size()]));
        request.setFiltering(Arrays.asList(filterBeanPlacement,filterBean));
        GdtResponse<GdtAdPosPageResponse> gdtResponse = gdtBaseService.queryAdPos(request,thirdCompanyEntity.getSecurityKey());
        if (gdtResponse.isSuccess()){
            return gdtResponse.getData().getList();
        }else {
            throw new RuntimeException("[GDT]-三方价格获取失败....");
        }
    }

    private List<LoadAdPreVo> convertLoadAdGdt(List<GdtAdPosInfo> adPosInfos,Map<Long,ThirdAdPosEntity> adPosEntityMap){
        return adPosInfos.stream().map(ad ->{
            LoadAdPreVo loadAdPreVo =  new LoadAdPreVo();
            loadAdPreVo.setPosId(ad.getPlacement_id());
            loadAdPreVo.setPosName(ad.getPlacement_name());
            Integer ecpm = ad.getEcpm_price() == null ? 0:ad.getEcpm_price()/100;
            loadAdPreVo.setAdPrice(ecpm);

            ThirdAdPosEntity entity = adPosEntityMap.get(ad.getPlacement_id());
            AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
            Integer type = adTypeCreateVo.getAdType();
            loadAdPreVo.setAppName(entity.getAppName());
            loadAdPreVo.setAdType(type);
            if (entity.getAdPrice().intValue() == ecpm){
                loadAdPreVo.setIsSamePrice(0);
            }else if (entity.getAdPrice().intValue() > ecpm){
                loadAdPreVo.setIsSamePrice(1);
            }else {
                loadAdPreVo.setIsSamePrice(-1);
            }
            int status = 0;
            if (!"Normal".equals(ad.getPause_status())){
                status = 1;
            }
            loadAdPreVo.setAdStatus(status);
            return loadAdPreVo;
        }).collect(Collectors.toList());
    }

    private List<LoadAdPreVo> convertLoadAdCsj(List<AdPosInfo> adPosInfos,Map<Long,ThirdAdPosEntity> adPosEntityMap){
        return adPosInfos.stream().map(ad ->{
            LoadAdPreVo loadAdPreVo =  new LoadAdPreVo();
            loadAdPreVo.setPosId(Long.valueOf(ad.getAd_slot_id()));
            loadAdPreVo.setPosName(ad.getAd_slot_name());
            Integer ecpm = ad.getCpm() == null ? 0 : ad.getCpm().intValue();
            loadAdPreVo.setAdPrice(ecpm);

            ThirdAdPosEntity entity = adPosEntityMap.get(Long.valueOf(ad.getAd_slot_id()));
            AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
            Integer type = adTypeCreateVo.getAdType();
            loadAdPreVo.setAppName(entity.getAppName());
            loadAdPreVo.setAdType(type);
            if (entity.getAdPrice().intValue() == ecpm){
                loadAdPreVo.setIsSamePrice(0);
            }else if (entity.getAdPrice().intValue() > ecpm){
                loadAdPreVo.setIsSamePrice(1);
            }else {
                loadAdPreVo.setIsSamePrice(-1);
            }
            int status = 0;
            if (ad.getStatus()  == 6 || ad.getStatus()  == 3){
                status = 1;
            }
            loadAdPreVo.setAdStatus(status);
            return loadAdPreVo;
        }).collect(Collectors.toList());
    }

    private List<LoadAdPreVo> convertLoadAdOppo(List<OppoAdResp> oppoPositionResponses, Map<Long,ThirdAdPosEntity> adPosEntityMap){
        return oppoPositionResponses.stream()
                .filter(r -> adPosEntityMap.containsKey(Long.valueOf(r.getPosId())))
                .map(ad ->{
            LoadAdPreVo loadAdPreVo =  new LoadAdPreVo();
            loadAdPreVo.setPosId(Long.valueOf(ad.getPosId()));
            loadAdPreVo.setPosName(ad.getPosName());
            Integer ecpm = ad.getTargetPrice() == null? 0 : ad.getTargetPrice();
            loadAdPreVo.setAdPrice(ecpm);

            ThirdAdPosEntity entity = adPosEntityMap.get(Long.valueOf(ad.getPosId()));
            AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
            Integer type = adTypeCreateVo.getAdType();
            loadAdPreVo.setAppName(entity.getAppName());
            loadAdPreVo.setAdType(type);
            if (entity.getAdPrice().intValue() == ecpm){
                loadAdPreVo.setIsSamePrice(0);
            }else if (entity.getAdPrice().intValue() > ecpm){
                loadAdPreVo.setIsSamePrice(1);
            }else {
                loadAdPreVo.setIsSamePrice(-1);
            }
            loadAdPreVo.setAdStatus(0);
            return loadAdPreVo;
        }).collect(Collectors.toList());
    }

    private List<LoadAdPreVo> convertLoadAdBd(List<BdAdDetailResponse> bdPositionResponses,Map<Long,ThirdAdPosEntity> adPosEntityMap){
        return bdPositionResponses.stream().map(ad ->{
            LoadAdPreVo loadAdPreVo =  new LoadAdPreVo();
            loadAdPreVo.setPosId(ad.getTu_id());
            loadAdPreVo.setPosName(ad.getAd_name());
            Integer ecpm = ad.getCpm() == null? 0 : ad.getCpm().intValue();
            loadAdPreVo.setAdPrice(ecpm);

            ThirdAdPosEntity entity = adPosEntityMap.get(ad.getTu_id());
            AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
            Integer type = adTypeCreateVo.getAdType();
            loadAdPreVo.setAppName(entity.getAppName());
            loadAdPreVo.setAdType(type);
            if (entity.getAdPrice().intValue() == ecpm){
                loadAdPreVo.setIsSamePrice(0);
            }else if (entity.getAdPrice().intValue() > ecpm){
                loadAdPreVo.setIsSamePrice(1);
            }else {
                loadAdPreVo.setIsSamePrice(-1);
            }
            loadAdPreVo.setAdStatus(0);
            return loadAdPreVo;
        }).collect(Collectors.toList());
    }

    private List<OppoAdResp> queryOppoBatchList(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,List<Long> posIds){
        OppoQueryListRequest request = new OppoQueryListRequest();
        request.setPage(1);
        request.setRows(200);
        request.setAppId(Integer.valueOf(thirdAppEntity.getAppId()));
        request.setStatus(1);
        request.setAuditStatus(2);
        request.setSortMode(0);

        OppoBaseReq baseReq = new OppoBaseReq();
        baseReq.setClientId(thirdCompanyEntity.getUserId());
        baseReq.setClientSecret(thirdCompanyEntity.getSecurityKey());

        OppoResponse<OppoPageRsp> response = oppoBaseService.queryPosList(request,baseReq);
        if (response.isSuccess()){
            return response.getData().getItems();
        }else {
            throw new RuntimeException(response.getMsg());
        }
    }

    private List<BdAdDetailResponse> queryBdBatchList(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,List<Long> posIds){
        BdAdQueryRequest request = new BdAdQueryRequest();
        request.setPage_no(1);
        request.setPage_size(posIds.size() >10 && posIds.size() <=100 ? posIds.size():100);
        request.setTu_ids(posIds);
        request.setApp_sids(Collections.singletonList(thirdAppEntity.getAppId()));

        BdResponse<BdAdQueryResponse> response = bdBaseService.queryPosList(request,thirdCompanyEntity.getUserId(),thirdCompanyEntity.getSecurityKey());
        if (response.isSuccess()){
            return response.getData().getData();
        }else {
            throw new RuntimeException(response.getMessage());
        }
    }

    private List<LoadAdPreVo> convertLoadAdKs(List<KsPositionResponse> ksPositionResponses,Map<Long,ThirdAdPosEntity> adPosEntityMap){
        return ksPositionResponses.stream().map(ad ->{
            LoadAdPreVo loadAdPreVo =  new LoadAdPreVo();
            loadAdPreVo.setPosId(ad.getPosition_id());
            loadAdPreVo.setPosName(ad.getName());
            Integer ecpm = ad.getCpm_floor() == null? 0 : ad.getCpm_floor().intValue();
            loadAdPreVo.setAdPrice(ecpm);

            ThirdAdPosEntity entity = adPosEntityMap.get(ad.getPosition_id());
            AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
            Integer type = adTypeCreateVo.getAdType();
            loadAdPreVo.setAppName(entity.getAppName());
            loadAdPreVo.setAdType(type);
            if (entity.getAdPrice().intValue() == ecpm){
                loadAdPreVo.setIsSamePrice(0);
            }else if (entity.getAdPrice().intValue() > ecpm){
                loadAdPreVo.setIsSamePrice(1);
            }else {
                loadAdPreVo.setIsSamePrice(-1);
            }
            loadAdPreVo.setAdStatus(0);
            return loadAdPreVo;
        }).collect(Collectors.toList());
    }


    private List<KsPositionResponse> queryKsBatchList(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,List<Long> posIds){
        List<CompletableFuture<KsPositionResponse>> completableFutures = new ArrayList<>();
        for (Long posId :posIds){
            CompletableFuture<KsPositionResponse> completableFuture = CompletableFuture.supplyAsync(()->{
                KsPositionQueryRequest request = new KsPositionQueryRequest();
                request.setAk(String.valueOf(Long.valueOf(thirdCompanyEntity.getUserId())));
                request.setSk(thirdCompanyEntity.getSecurityKey());
                request.setAppId(String.valueOf(thirdAppEntity.getAppId()));
                request.setPage(1);
                request.setPageSize(5);
                request.setPositionId(posId);
                KsResponse<List<KsPositionResponse>> tempResponse = ksBaseService.queryPositionInfo(request);
                if (tempResponse.isSuccess()){
                    return tempResponse.getData().get(0);
                }else {
                    throw new RuntimeException("[KS]-三方广告获取失败....");
                }
            });
            completableFutures.add(completableFuture);
        }
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[completableFutures.size()]));

        return completableFutures.stream().map(future ->{
            try {
                return future.get(5, TimeUnit.SECONDS);
            }catch (Exception e){
                log.error("query err:",e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private KsPositionResponse queryKsSingleAdPos(ThirdCompanyEntity thirdCompanyEntity,ThirdAppEntity thirdAppEntity,Long posId){
        KsPositionQueryRequest request = new KsPositionQueryRequest();
        request.setAk(String.valueOf(Long.valueOf(thirdCompanyEntity.getUserId())));
        request.setSk(thirdCompanyEntity.getSecurityKey());
        request.setAppId(String.valueOf(thirdAppEntity.getAppId()));
        request.setPage(1);
        request.setPageSize(10);
        request.setPositionId(posId);
        KsResponse<List<KsPositionResponse>> tempResponse = ksBaseService.queryPositionInfo(request);
        if (tempResponse.isSuccess()){
//            Double cpm = tempResponse.getData().get(0).getCpm_floor();
            return tempResponse.getData().get(0);
        }else {
            throw new RuntimeException("[KS]-三方价格获取失败....");
        }
    }

    @Resource
    private ChuanShanJiaTempReplaceMapper chuanShanJiaTempReplaceMapper;

    public Integer createExceptionAdPos(List<ThirdAdPosEntity> adPosEntities, AdPosCreateType adPosType,Map<Long, CreateExceptionAdPosDto> createReasonMap){
        Integer success = 0;
        for (ThirdAdPosEntity thirdAdPosEntity : adPosEntities) {
            try {
                ThirdPosAdRequest thirdPosAdRequest = new ThirdPosAdRequest();
                thirdPosAdRequest.setIsBidding(PriceType.NORMAL.getCode());
                if (adPosType.equals(AdPosCreateType.JLSP_BIDDING)){
                    thirdPosAdRequest.setIsBidding(PriceType.BIDDING.getCode());
                }
                thirdPosAdRequest.setSystemCreate(true);
                thirdPosAdRequest.setPlatformCode(thirdAdPosEntity.getPlatformCode());
                thirdPosAdRequest.setCompanyId(thirdAdPosEntity.getCompanyId());
                thirdPosAdRequest.setPosName(thirdAdPosEntity.getPosName());
                ThirdAppEntity appEntity = thirdAppMapper.load(thirdAdPosEntity.getApplicationId());
                if (appEntity == null){
                    log.error("未匹配到应用，请查看应用配置！");
                    continue;
                }

                DspEnum dspEnum = DspEnum.fromCode(thirdAdPosEntity.getPosName());
                if (dspEnum != null) {
                    thirdPosAdRequest.setDsp(dspEnum.getCode());
                }

                thirdPosAdRequest.setOs(appEntity.getOs());
                thirdPosAdRequest.setAppId(thirdAdPosEntity.getApplicationId());
                AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(thirdAdPosEntity.getAdType());
                thirdPosAdRequest.setAdType(adTypeCreateVo.getAdType());
                if (AdPosCreateType.JLSP_BIDDING.equals(adPosType)){
                    thirdPosAdRequest.setAdType(4);
                }
                if (adTypeCreateVo.getAdTypeOwn() == null) {
                    log.error("{} 广告类型异常..", thirdAdPosEntity.getPosId());
                    if (Platform.CSJ.getCode().equals(thirdAdPosEntity.getPlatformCode())){
                        if (AdPosCreateType.JLSP.equals(adPosType)) {
                            thirdPosAdRequest.setAdTypeOwn(AdType.TOUTIAO_VIDEO_TEMPLATE.type);
                        }else if (AdPosCreateType.JLSP_BIDDING.equals(adPosType)) {
                            thirdPosAdRequest.setAdTypeOwn(1015201);
                        }else {
                            thirdPosAdRequest.setAdTypeOwn(AdType.CSJ_NEW_CHAPIN.type);
                        }
                    }else if(Platform.GDT.getCode().equals(thirdAdPosEntity.getPlatformCode())){
                        if (AdPosCreateType.JLSP.equals(adPosType)) {
                            thirdPosAdRequest.setAdTypeOwn(AdType.GDT_VIDEO.type);
                        }else if (AdPosCreateType.JLSP_BIDDING.equals(adPosType)) {
                            thirdPosAdRequest.setAdTypeOwn(1008201);
                        }else {
                            thirdPosAdRequest.setAdTypeOwn(AdType.GDT_CHAPING_2_0.type);
                        }
                    }else if(Platform.KS.getCode().equals(thirdAdPosEntity.getPlatformCode())){
                        if (AdPosCreateType.JLSP.equals(adPosType)) {
                            thirdPosAdRequest.setAdTypeOwn(AdType.KS_VIDEO.type);
                        }else if (AdPosCreateType.JLSP_BIDDING.equals(adPosType)) {
                            thirdPosAdRequest.setAdTypeOwn(1018201);
                        }else if (AdPosCreateType.CP.equals(adPosType)){
                            thirdPosAdRequest.setAdTypeOwn(AdType.KS_CHAPIN.type);
                        }else if (AdPosCreateType.NCP.equals(adPosType)){
                            thirdPosAdRequest.setAdTypeOwn(AdType.KS_CHAPIN.type);
                        }else{
                            log.warn(" 没有对应的类型 adPosType : {}", adPosType);
                            continue;
                        }
                    }
                }else {
                    thirdPosAdRequest.setAdTypeOwn(adTypeCreateVo.getAdTypeOwn());
                }
                thirdPosAdRequest.setAdSubType(adTypeCreateVo.getAdSubType());
                thirdPosAdRequest.setAdSite(adTypeCreateVo.getAdSite());
                thirdPosAdRequest.setAdPrice(thirdAdPosEntity.getAdPrice().doubleValue());
                // 插屏混出
                if (thirdAdPosEntity.getPosName().contains("-cphc-")){
                    thirdPosAdRequest.setInnerType(1);
                }else if (thirdAdPosEntity.getPosName().contains("-bqhc-")){
                    thirdPosAdRequest.setInnerType(2);
                }
                ThirdPlatformType platformType = AdTypeSub.getPlatform(thirdPosAdRequest.getAdTypeOwn());
                if ((thirdAdPosEntity.getAdPrice().doubleValue() < 1d ||
                        !thirdAdPosEntity.getPosName().endsWith(thirdAdPosEntity.getAdPrice().intValue()+"")
                ) && !AdPosCreateType.JLSP_BIDDING.equals(adPosType)) {
                    // 查询三方报价
                    ThirdAppEntity thirdAppEntity = thirdAppMapper.load(thirdAdPosEntity.getApplicationId());
                    BigDecimal thirdECPM = queryThirdAdInfo(platformType,thirdAppEntity,thirdAdPosEntity.getPosId());
                    thirdPosAdRequest.setAdPrice(thirdECPM.doubleValue());
                }
                ThirdCompanyEntity companyEntity = thirdCompanyMapper.load(appEntity.getCompanyId());
                String posName = thirdPosAdRequest.buildThirdPosName(AppBuilder.getById(appEntity.getProduct()).getProductName());

                if (thirdAdPosMapper.queryCountAdPos(posName) > 0){
                    log.info("{} Already Create...",posName);
                    success++;
                    continue;
                }

                if (AdPosCreateType.JLSP_BIDDING.equals(adPosType)){
                    posName = posName +"_bidding";
                    thirdPosAdRequest.setIsBidding(PriceType.BIDDING.getCode());
                    thirdPosAdRequest.setBiddingType(BiddingType.CLIENT.getType());
                    //兼容广点通的bidding类型
                    if (ThirdPlatformType.GDT.equals(platformType)){
                        GdtAdPosPageResponse response = queryGdtSingleAdPosModel(companyEntity,appEntity,thirdAdPosEntity.getPosId());
                        if (!"Client_Bidding".equalsIgnoreCase(response.getList().get(0).getReal_time_bidding_type())){
                            thirdPosAdRequest.setBiddingType(BiddingType.SERVER.getType());
                        }
                    }
                }

                ThirdAdPosEntity entity = null;
                if (AdPosCreateType.JLSP_BIDDING.equals(adPosType) && ThirdPlatformType.CSJ.equals(platformType)){
                    Integer createType = typeMap.getOrDefault(appEntity.getProduct(),1015201);
                    if (createType.equals(thirdPosAdRequest.getAdTypeOwn())) {
                        List<ChuanShanJiaTempReplace> replaces = chuanShanJiaTempReplaceMapper.queryByProduct(appEntity.getProduct());
                        if (replaces.size() > 0){
                            for (ChuanShanJiaTempReplace chuanShanJiaTempReplace :replaces) {
                                List<Ad> ads = adAdminService.selectAllAds(null,null,null,
                                        null,null,null,appEntity.getProduct(), null,null,
                                        null, String.valueOf(chuanShanJiaTempReplace.getPosId()),null);

                                if (ads.size() > 0){
                                    continue;
                                }
                                // 再看posId是否在广告系统存在
                                // 若都已经导入了 重新新建
                                entity = new ThirdAdPosEntity();
                                entity.setPosId(chuanShanJiaTempReplace.getPosId());
                                entity.setPosName(posName);
                                chuanShanJiaTempReplaceMapper.updateToUsed(chuanShanJiaTempReplace.getId());
                                break;
                            }
                        }else {
                            entity = insert(thirdPosAdRequest, companyEntity, appEntity, "SYSTEM", "SYSTEM");
                        }
                    }else {
                        entity = insert(thirdPosAdRequest, companyEntity, appEntity, "SYSTEM", "SYSTEM");
                    }
                }else {
                    entity = insert(thirdPosAdRequest, companyEntity, appEntity, "SYSTEM", "SYSTEM");
                }
                // 写入记录表
                insertCreateRecord(entity,createReasonMap.get(thirdAdPosEntity.getPosId()));
                log.info("Create Success..");
                success++;
            } catch (Exception e) {
                log.error("Create Error:", e);
            }
        }
        return success;
    }

    private static final Map<Integer,Integer> typeMap = new HashMap<Integer,Integer>(){{
        put(652,1015100);
        put(659,1015111);
        put(651,1015211);
        put(633,1015102);
    }};

    private void insertCreateRecord(ThirdAdPosEntity entity,CreateExceptionAdPosDto createExceptionAdPosDto){
        if (entity == null || createExceptionAdPosDto == null){
            log.info("Create Dto null... Skip Record");
            return;
        }
        Date now = new Date();
        CreateRecord createRecord = new CreateRecord();
        createRecord.setLogday(now);
        createRecord.setProduct(createExceptionAdPosDto.getProduct());
        createRecord.setProductName(createExceptionAdPosDto.getProductName());
        createRecord.setOs(createExceptionAdPosDto.getOs());
        createRecord.setAdSource(createExceptionAdPosDto.getSourceName());
        createRecord.setOldAdId(createExceptionAdPosDto.getAdId());
        createRecord.setOldPosId(createExceptionAdPosDto.getPosId());
        createRecord.setOldPosName(createExceptionAdPosDto.getPosName());
        createRecord.setNewPosId(entity.getPosId());
        createRecord.setNewPosName(entity.getPosName());
        createRecord.setCreateType(createExceptionAdPosDto.getCreateType());
        createRecord.setCreateTypeName(createExceptionAdPosDto.getCreateDesc());
        createRecord.setPvRate(createExceptionAdPosDto.getRate());
        createRecord.setSumIncome(createExceptionAdPosDto.getIncome());
        createRecord.setReplaceStatus(ReplaceStatus.NO_REPLACE.getCode());
        createRecord.setUpdateTime(now);
        createRecord.setCreateTime(now);

        createRecordMapper.insert(createRecord);
    }

    /**
     * 查询三日之前未导入的广告位
     * @return 广告位
     */
    public List<Long> queryNoLoadToOwnSystemAds(Integer platform,Integer product,String adType,Long appId,Integer os,Boolean auto){
        Date day3Ago = DateUtil.dateIncreaseByDay(new Date(),-2);
        List<ThirdAdPosEntity> list  = thirdAdPosMapper.queryNoLoadAds(day3Ago,platform,appId);
        if (Lists.noEmpty(list)){
            List<Long> posIdList = new ArrayList<>();
            Date n24 = DateUtil.dateIncreaseByDay(new Date(),-1);
            for (ThirdAdPosEntity thirdAdPosEntity : list){
                if (Strings.noEmpty(adType)){
                    AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(thirdAdPosEntity.getAdType());
                    Integer type = adTypeCreateVo.getAdType();
                    if (type == null){
                        continue;
                    }
                    AdTypeConvertVo adTypeConvertVo = AdTypeSub.adTypeMapFl.get(type);
                    if (!adType.equals(adTypeConvertVo.getSubTypeName())){
                        continue;
                    }
                }
                if (auto){
                    if (thirdAdPosEntity.getCreateTime().after(n24)){
                        continue;
                    }
                }
                posIdList.add(thirdAdPosEntity.getPosId());
            }
            return posIdList;
        }
        return Collections.emptyList();
    }


    public List<LoadAdPreVo> queryAdLoadPreList(List<Integer> ids){
        List<LoadAdPreVo> adPreVos = new ArrayList<>();
        List<ThirdAdPosEntity> thirdAdPosEntities = thirdAdPosMapper.queryList(ids);

        Map<Long,ThirdAdPosEntity> posEntityMap = thirdAdPosEntities.stream().collect(Collectors.toMap(ThirdAdPosEntity::getPosId,
                k->k,(k1,k2) -> k2));
        Map<Integer,List<ThirdAdPosEntity>> platformMap = thirdAdPosEntities.stream().collect(Collectors.groupingBy(ThirdAdPosEntity::getPlatformCode));

        for (Map.Entry<Integer, List<ThirdAdPosEntity>> entry : platformMap.entrySet()) {
            Integer platform = entry.getKey();
            List<ThirdAdPosEntity> entityList = entry.getValue();
            Platform platformE = Platform.getPlatform(platform);

            ThirdAdPosEntity entity = entityList.get(0);
            ThirdAppEntity thirdAppEntity = thirdAppMapper.load(entity.getApplicationId());
            ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(entity.getCompanyId());
            List<Long> list = entityList.stream().map(ThirdAdPosEntity::getPosId).collect(Collectors.toList());

            switch (platformE) {
                case CSJ: adPreVos.addAll(convertLoadAdCsj(queryCsjBatchAdPos(thirdCompanyEntity,thirdAppEntity,list),posEntityMap)); break;
                case GDT: adPreVos.addAll(convertLoadAdGdt(queryGdtBatchList(thirdCompanyEntity,thirdAppEntity,list),posEntityMap)); break;
                case KS:  adPreVos.addAll(convertLoadAdKs(queryKsBatchList(thirdCompanyEntity,thirdAppEntity,list),posEntityMap)); break;
                case BD:  adPreVos.addAll(convertLoadAdBd(queryBdBatchList(thirdCompanyEntity,thirdAppEntity,list),posEntityMap)); break;
                case OPPO:  adPreVos.addAll(convertLoadAdOppo(queryOppoBatchList(thirdCompanyEntity,thirdAppEntity,list),posEntityMap)); break;
                default: throw  new RuntimeException("Noop This Plat!");
            }
        }

        Map<Long,LoadAdPreVo>  resMap = adPreVos.stream().collect(Collectors.toMap(LoadAdPreVo::getPosId,r->r,(r1,r2) -> r1));

        return thirdAdPosEntities.stream().map(entity -> {
            LoadAdPreVo loadAdPreVo = resMap.get(entity.getPosId());
            if (loadAdPreVo == null){
                loadAdPreVo = new LoadAdPreVo();
                loadAdPreVo.setPosId(entity.getPosId());
                loadAdPreVo.setAdPrice(entity.getAdPrice().intValue());
                loadAdPreVo.setAppName(entity.getAppName());
                loadAdPreVo.setPosName(entity.getPosName());
                loadAdPreVo.setIsSamePrice(2);
                AdTypeCreateVo adTypeCreateVo = AdTypeCreateVo.fromString(entity.getAdType());
                Integer type = adTypeCreateVo.getAdType();
                loadAdPreVo.setAdType(type);
                loadAdPreVo.setAdStatus(entity.getPosStatus());
            }
            return loadAdPreVo;
        }).collect(Collectors.toList());
    }

    public void closeByPosId(Long posId){
        Integer count = thirdAdPosMapper.closeByPosId(posId);
        log.info("Close {} .",count);
    }


    /**
     * 创建三方广告位
     *
     * @param vo                补贴配置类
     * @param adTypeSubPriceMap 广告位类型-价格
     * @param createAdType      创建广告位类型
     * @param posName           广告位名称
     * @param priceTypeCode     竞价类型
     */
    public void autoCreateThirdAdPos(TbApSubsidiesConfig vo, Map<Integer, Integer> adTypeSubPriceMap, int createAdType, String posName, Integer priceTypeCode) {
        XxlJobLogger.log("autoCreateThirdAdPos start......:{}", JSON.toJSONString(adTypeSubPriceMap));
        log.info("autoCreateThirdAdPos start......");
        // 创建广告位
        ThirdPosAdRequest thirdPosAdRequest = new ThirdPosAdRequest();
        TbApReplaceAd tbApReplaceAd = new TbApReplaceAd();
        BeanUtils.copyProperties(vo, tbApReplaceAd);
        tbApReplaceAd.setStrategyId(vo.getId());
        // 系统自动创建
        thirdPosAdRequest.setSystemCreate(Boolean.TRUE);
        thirdPosAdRequest.setOs(OSType.find(vo.getOs()).getCode());
        thirdPosAdRequest.setPlatformCode(ThirdPlatformType.getByCode(vo.getPlatform()).getCode());
        // perk-补贴
        thirdPosAdRequest.setPosName(posName);
        thirdPosAdRequest.setAdType(createAdType);
        thirdPosAdRequest.setIsBidding(priceTypeCode);
        if(priceTypeCode.equals(PriceType.BIDDING.getCode())){
            tbApReplaceAd.setLayerType(TbApReplaceAdEnum.BIDDING_LAYERS.getCode());
            thirdPosAdRequest.setBiddingType(BiddingType.CLIENT.getType());
        }

        if (vo.getAdTypeName().contains(AdPosCreateType.CP.getDesc())) {
            // 插屏默认配置1
            thirdPosAdRequest.setInnerType(1);
        }
        adTypeSubPriceMap.forEach((key, value) -> {
            thirdPosAdRequest.setAdTypeOwn(key);
            thirdPosAdRequest.setAdPrice(Double.valueOf(value));
            insert(thirdPosAdRequest, "系统自动生成", "系统自动生成", vo.getAppId());
            //保存替换的广告位
            tbApReplaceAdService.insertTbApReplaceAdForSubsides(tbApReplaceAd,vo,key,value);
        });
        int addSize = modifyLayerSize(vo);
        String msg = "瀑布流替换&加层";
        if (PriceType.BIDDING.getCode().equals(thirdPosAdRequest.getIsBidding())){
            addSize = adTypeSubPriceMap.size();
            msg = "Bidding新增";
        }
        sendCreatePosMsg(vo.getProductName(), vo.getAdTypeName(), addSize, adTypeSubPriceMap.size(),
                msg, ThirdPlatformType.getByCode(vo.getPlatform()).getDesc(), vo.getOs());
        log.info("adTypeSubPriceMap:{}", JSON.toJSONString(adTypeSubPriceMap));
        log.info("autoCreateThirdAdPos end......");
        XxlJobLogger.log("autoCreateThirdAdPos end......");
    }

    public void sendCreatePosMsg(String productName, String adTypeName, int needCreateAdPosCount, int actualCreateAdPosCount, String operationType, String platform, String os) {
        String[] mobiles = new String[]{"18328115421", "15175062197", "18811603713"};
        Map<String,Object> atMobiles = new HashMap<>();
        atMobiles.put("atMobiles", mobiles);
        StringBuilder message = new StringBuilder(String.format(
                "### %s %n%n **产品：** %s %n%n **广告位类型：** %s  %n%n **平台：** %s %n%n **系统：** %s %n%n **操作时间：** %s %n%n **应创建广告位个数：** <font color=red> %d </font> %n%n **实际创建广告位个数：** <font color=red> %d </font> %n%n",
                operationType, productName, adTypeName, platform, os, DateUtils.formatDate(new Date(), DateUtils.PATTERN_YMDHMS), needCreateAdPosCount, actualCreateAdPosCount
        ));

        if (!MapUtils.isEmpty(atMobiles)) {
            String[] atMobilesArr = (String[]) atMobiles.get("atMobiles");
            Arrays.asList(atMobilesArr).forEach(atMobile -> {
                message.append(String.format("%n @%s", atMobile));
            });
        }
        DingTalkRobotUtils.sendMarkdownMsg(DING_TALK_URL, DING_KEY_WORD, message.toString(), atMobiles);
    }

    public void sendCreatePosFailMsg(String warnType,String jobName,String detailUrl) {
        String[] mobiles = new String[]{"18328115421", "15175062197", "18811603713"};
        Map<String,Object> atMobiles = new HashMap<>();
        StringBuilder message = new StringBuilder(String.format("## <font color=red>%s</font> %n **任务名称:** %s %n%n **排查路径：** [点击查看详情](%s) %n%n **请尽快处理！！！** ",
                warnType,jobName,detailUrl));
        if (!MapUtils.isEmpty(atMobiles)) {
            atMobiles.put("atMobiles", mobiles);
            Arrays.asList(mobiles).forEach(mobile -> {
                message.append(String.format("%n%n @%s", mobile));
            });
        }
        DingTalkRobotUtils.sendMarkdownMsg(DING_TALK_URL, DING_KEY_WORD, message.toString(), atMobiles);
    }

    private int modifyLayerSize(TbApSubsidiesConfig vo) {
        String layerEcpm = vo.getLayerEcpm();
        String autoGenerateEcpm = vo.getAutoGenerateEcpm();
        int res = 0;
        if (!StringUtils.isEmpty(layerEcpm)){
            res += layerEcpm.split(",").length;
        }

        if (!StringUtils.isEmpty(autoGenerateEcpm)){
            res += autoGenerateEcpm.split(",").length;
        }

        return res;
    }

    /**
     * 处理广告位
     *
     * @param vo 补贴配置
     * @return adPos
     */
    public Integer preAdPos(TbApSubsidiesConfig vo) {
        List<AdPosModel> adPosList = adPosMapper.queryAllByProduct(vo.getAppId());
        if (CollectionUtils.isEmpty(adPosList)) {
            XxlJobLogger.log("not find ad pos");
            return null;
        }

        Integer adPos = adPosList.stream()
                .filter(adPosModel -> vo.getAdTypeName().equals(adPosModel.getName()))
                .findFirst()
                .map(AdPosModel::getId)
                .orElse(null);

        if (adPos == null) {
            XxlJobLogger.log("no ad pos exist:" + vo.getAdTypeName());
            log.info("no ad pos exist:{}", vo.getAdTypeName());
            return null;
        }
        return adPos;
    }

}
