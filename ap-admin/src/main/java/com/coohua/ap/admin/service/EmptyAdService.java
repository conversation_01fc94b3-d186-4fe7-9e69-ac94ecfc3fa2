package com.coohua.ap.admin.service;


import com.coohua.ap.admin.controller.vo.EmptyAdInfoVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.domain.EmptyAdCountDomain;

import java.util.List;

/**
 * @ClassName: EmptyAdService
 * @Description: TODO
 * @Author: fan jin yang
 * @Date: 2020/3/30
 * @Version: V1.0
 **/
public interface EmptyAdService {

    List<EmptyAdCountDomain> queryEmptyCount(int appId);

    Page<EmptyAdInfoVO> queryEmptyAdInfo(Page<EmptyAdInfoVO> page);
}
