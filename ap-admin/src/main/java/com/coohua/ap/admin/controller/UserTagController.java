package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.UserTagVO;
import com.coohua.ap.admin.model.UserTagEntity;
import com.coohua.ap.admin.service.UserTagService;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName: UserTagController
 * @Description: 用户标签管理
 * @Author: fan jin yang
 * @Date: 2020/5/6
 * @Version: V1.0
 **/

@RestController
@RequestMapping("/userTag")
@Slf4j
public class UserTagController {


    @Autowired
    private UserTagService userTagService;

    @RequestMapping("/list")
    public BaseResponse list(HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse(0);
        List<UserTagVO> voList = userTagService.queryUserTag(SessionUtils.getUserProduct(request));
        baseResponse.setData(voList);
        return baseResponse;
    }

    @RequestMapping("/add")
    public BaseResponse add(@RequestBody UserTagVO userTagVO, HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse(0);
        UserTagEntity result = userTagService.addUserTag(userTagVO, SessionUtils.getUserProduct(request));
        if (result == null) {
            throw new BusinessException(500, "add user tag error.");
        }
        baseResponse.setData("OK");
        return baseResponse;
    }

    @RequestMapping("/update")
    public BaseResponse update(@RequestBody UserTagVO userTagVO, HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse(0);
        UserTagEntity result = userTagService.updateUserTag(userTagVO, SessionUtils.getUserProduct(request));
        if (result == null) {
            throw new BusinessException(500, "update user tag error.");
        }
        baseResponse.setData("OK");
        return baseResponse;
    }

}
