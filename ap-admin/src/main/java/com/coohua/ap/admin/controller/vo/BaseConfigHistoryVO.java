package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.BaseConfigHistoryEntity;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * @ClassName: BaseConfigHistoryVO
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/7/31
 * @Version: 1.0.0
 **/
@Data
public class BaseConfigHistoryVO implements Serializable {

    private int id;

    private int configId;

    private int appId;

    private String appName;

    private String configOld;

    private String configNew;

    private String createTime;

    private String updateTime;

    public BaseConfigHistoryVO() {
    }

    public BaseConfigHistoryVO(BaseConfigHistoryEntity baseConfigHistoryEntity, Map<Integer,String> appMap) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.id = baseConfigHistoryEntity.getId();
        this.configId = baseConfigHistoryEntity.getConfigId();
        this.appId = baseConfigHistoryEntity.getAppId();
        this.configOld = baseConfigHistoryEntity.getConfigOld();
        this.configNew = baseConfigHistoryEntity.getConfigNew();
        this.createTime = simpleDateFormat.format(baseConfigHistoryEntity.getCreateTime());
        this.updateTime = simpleDateFormat.format(baseConfigHistoryEntity.getUpdateTime());
        this.appName = appMap.get(appId);
    }


}
