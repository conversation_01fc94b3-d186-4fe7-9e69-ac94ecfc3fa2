package com.coohua.ap.admin.service.replace;

import com.coohua.ap.admin.model.TbApDiminishBiddingMonitor;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;

public abstract class BiddingProcessor {

    @ApolloJsonValue("${ad.replace.bidding.product.list:[]}")
    private List<String> biddingProductList;

    public void process(){
        List<TbApDiminishBiddingMonitor> tbApDiminishBiddingMonitors = fetchData();
        //限制产品
        if (CollectionUtils.isNotEmpty(biddingProductList)) {
            Iterator<TbApDiminishBiddingMonitor> iterator = tbApDiminishBiddingMonitors.iterator();
            if (iterator.hasNext() && !biddingProductList.contains(iterator.next().getProduct())) {
                iterator.remove();
            }
        }

        if (CollectionUtils.isNotEmpty(tbApDiminishBiddingMonitors)){
            handle(tbApDiminishBiddingMonitors);
        }
    }

    protected abstract List<TbApDiminishBiddingMonitor> fetchData();

    protected abstract void handle(List<TbApDiminishBiddingMonitor> tbApDiminishBiddingMonitors);
}
