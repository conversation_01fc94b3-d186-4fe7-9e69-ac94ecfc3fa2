package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.ConfigVO;
import com.coohua.ap.admin.model.Config;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @package com.coohua.nap.service
 * @create_time 2019-08-20
 */
public interface ConfigService {

    String getConfigValue(String name,int product);

    int insert(String name, String value);

    int insert(Config config);

    ConfigVO update(ConfigVO configVO);

    List<Config> getAll();

    List<Config> getAllByProduct(int product);

    String get(String name,int product);

    Config getConfig(String name,int product);

    List<String> getJsAdChannelSourceList(int product);
}
