package com.coohua.ap.admin.service.third.service.csj;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.admin.service.third.dto.csj.req.*;
import com.coohua.ap.admin.service.third.dto.csj.rsp.*;
import com.coohua.ap.admin.utils.Env;
import com.coohua.ap.admin.utils.third.HttpClients;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2021/1/5
 * 穿山甲应用代码位管理API文档
 * @link https://bytedance.feishu.cn/docs/doccnA6h4DyERjfiLDJHHz5Zrke
 * @link https://lf3-plat.pglstatp-toutiao.com/obj/union-platform/93e01bec503c9f1f874b86f7bf0a6277.pdf
 * @link https://lf3-plat.pglstatp-toutiao.com/obj/union-platform/e21326dee85ab382b4ae7a943f1f82cd.pdf
 */
@Slf4j
@Service
public class CsjBaseService {


    @Autowired
    private Env env;

    private static final String URL = "https://open-api.csjplatform.com";

    private static final String TEST_URL = "http://sandbox-callback.bytedance.com";

    private static final Map<String,Object> publicHeader = new HashMap<String,Object>(){{
        put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        put("Accept",MediaType.APPLICATION_JSON_VALUE);
    }};

    public static Integer invokeTime(){
        return (int) (System.currentTimeMillis() / 1000);
    }

    public static Integer invokeNonce(){
        return (int) (Math.random() * 100);
    }

    private Map<String,Object> headerInvoke(){
        Map<String, Object> header = new HashMap<>(publicHeader);
        if (env.isTest()){
            header.put("X-Tt-Env","open_api_sandbox");
        }
        return header;
    }
    
    private String invokeUrl(){
        return env.isTest()? TEST_URL: URL;
    }

    public static String invokeSign(String securityKey,long timestamp,Integer nonce){
        String beforeSign = Stream.of(Long.toString(timestamp), Integer.toString(nonce),securityKey).sorted().collect(Collectors.joining());
        String sign = DigestUtils.sha1Hex(beforeSign);
        log.info("{} --> SIGN:{}",beforeSign,sign);
        return sign;
    }

    public Response<AppCreateResponse> createApplication(AppCreateRequest request){
        String baseUrl = invokeUrl() + "/union/media/open_api/site/create";
        return post(baseUrl,request,new TypeReference<Response<AppCreateResponse>>(){});
    }

    public Response<AppUpdateResponse> updateApplication(AppUpdateRequest request){
        String baseUrl = invokeUrl() + "/union/media/open_api/site/update";
        return post(baseUrl,request,new TypeReference<Response<AppUpdateResponse>>(){});
    }

    public Response<AppPageResponse> queryAppList(AppQueryRequest request){
        String baseUrl = invokeUrl() + "/union/media/open_api/site/query";
        return post(baseUrl,request,new TypeReference<Response<AppPageResponse>>(){});
    }

    public Response<AdPosCreateResponse> createAdPos(AdPosCreateRequest request){
        String baseUrl = invokeUrl() + "/union/media/open_api/code/create";
        return post(baseUrl,request,new TypeReference<Response<AdPosCreateResponse>>(){});
    }

    public Response<AdPosUpdateResponse> updateAdPos(AdPosUpdateRequest request){
        String baseUrl = invokeUrl() + "/union/media/open_api/code/update";
        return post(baseUrl,request,new TypeReference<Response<AdPosUpdateResponse>>(){});
    }

    public Response<AdPosPageResponse> queryAdPosList(AdPosQueryRequest request){
        String baseUrl = invokeUrl() + "/union/media/open_api/code/query";
        return post(baseUrl,request,new TypeReference<Response<AdPosPageResponse>>(){});
    }

    /**
     * 基础请求
     * @param url URL
     * @param request REQUEST
     * @param pClass  Class
     * @param <T> TypeReference
     * @return TypeReference <T>
     */
    private <T> T post(String url,Object request,TypeReference<T> pClass){
        String text = HttpClients.jsonPost(url,headerInvoke(),JSON.toJSONString(request));
        return JSON.parseObject(text,pClass);
    }


    public Integer convertToCsjType(Integer adType){
        switch (adType){
            case 3:return 1;
            case 1:return 2;
            case 4:return 3;
            case 5:return 4;
            case 9:return 5;
            default:return 0;
        }
    }
}
