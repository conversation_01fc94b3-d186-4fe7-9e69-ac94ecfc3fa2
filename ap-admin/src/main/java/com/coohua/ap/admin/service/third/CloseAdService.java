package com.coohua.ap.admin.service.third;

import com.coohua.ap.admin.constants.Platform;
import com.coohua.ap.admin.controller.vo.NeedCloseAdVo;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mapper.ap.ThirdAppMapper;
import com.coohua.ap.admin.mapper.ap.ThirdCompanyMapper;
import com.coohua.ap.admin.model.AdInfoEntity;
import com.coohua.ap.admin.model.TbApThirdConfig;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosQueryRequest;
import com.coohua.ap.admin.service.third.dto.csj.req.AdPosUpdateRequest;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AdPosInfo;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AdPosPageResponse;
import com.coohua.ap.admin.service.third.dto.csj.rsp.AdPosUpdateResponse;
import com.coohua.ap.admin.service.third.dto.csj.rsp.Response;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosPauseOrStartRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAdPosQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.req.GdtAppInfoQueryRequest;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosInfo;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosPageResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtResponse;
import com.coohua.ap.admin.service.third.service.csj.CsjBaseService;
import com.coohua.ap.admin.service.third.service.gdt.GdtBaseService;
import com.coohua.ap.admin.utils.third.AdUtils;
import com.coohua.ap.admin.utils.third.DateUtil;
import com.coohua.ap.admin.utils.third.Lists;
import com.coohua.ap.admin.utils.third.Strings;
import com.coohua.ap.base.constants.AdTypeConvertVo;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.ThirdPlatformType;
import com.coohua.ap.support.core.spring.model.ThirdAdModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jboss.netty.util.internal.ThreadLocalBoolean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/12/2
 */
@Slf4j
@Service
public class CloseAdService {

    private static final List<Integer> SUPPORT_PLATFORM = new ArrayList<Integer>(){{
        add(Platform.CSJ.getCode());
        add(Platform.GDT.getCode());
    }};

    private static final ThreadLocalBoolean threadLocalBoolean = new ThreadLocalBoolean(Boolean.TRUE);

    @Data
    private static class CloseAdBean{
        private Long posId;
        private ThirdAppEntity thirdAppEntity;
        private ThirdCompanyEntity thirdCompanyEntity;
    }

    @Autowired
    private GdtBaseService gdtBaseService;
    @Autowired
    private CsjBaseService csjBaseService;
    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Autowired
    private ThirdConfigService thirdConfigService;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Autowired
    private ThirdAdPosService thirdAdPosService;
    @Resource
    private ThirdCompanyMapper thirdCompanyMapper;
    @Resource
    private ThirdAppMapper thirdAppMapper;

    public List<NeedCloseAdVo> queryNeedCloseAd(Integer platform,Integer product,String adType,Long appId,Integer os,Boolean auto){
        Platform platformEnum = Platform.getPlatform(platform);
        List<Long> posIdList = new ArrayList<>();
        // 查询系统中未运行的广告
        List<Long> noRunAds = queryNoRunAds(platform,product,adType,appId,os,auto);
        if (Lists.noEmpty(noRunAds)){
            posIdList.addAll(noRunAds);
        }
        // 查询未导入广告
        List<Long> thirdAdPosEntities = thirdAdPosService.queryNoLoadToOwnSystemAds(platform,product,adType,appId,os,auto);
        if (Lists.noEmpty(thirdAdPosEntities)){
            posIdList.addAll(thirdAdPosEntities);
        }
        // 查询低收入广告
        // 从三方查询可运行的广告列表
        if (Lists.isEmpty(posIdList)){
            return Collections.emptyList();
        }
        if (Platform.CSJ.equals(platformEnum)){
            return queryAllFormCsj(posIdList,product,appId,os,platform);
        }else if (Platform.GDT.equals(platformEnum)){
            List<NeedCloseAdVo> adVos = new ArrayList<>();
            for (int i = 0 ; i <= posIdList.size() / 100; i++){
                List<Long> needQueryList = posIdList.stream().skip(i * 100).limit(100).collect(Collectors.toList());
                if (needQueryList.size() > 0) {
                    List<NeedCloseAdVo> gdtList = queryAllFromGdt(needQueryList, product, appId, os, platform);
                    if (gdtList.size() > 0) {
                        adVos.addAll(gdtList);
                    }
                }
            }
            return adVos;
        }
        return Collections.emptyList();
    }

    private List<Long> queryNoRunAds(Integer platform,Integer product,String adType,Long appId,Integer os,Boolean auto){
        List<Long> posIdList = new ArrayList<>();
        List<AdInfoEntity> list = adInfoMapper.selectAllProductAd(product,0);
        Date n24 = DateUtil.dateIncreaseByDay(new Date(),-2);
        for (AdInfoEntity adInfoEntity : list){
            ThirdPlatformType thirdPlatformType = AdTypeSub.getPlatform(adInfoEntity.getType());
            if (!platform.equals(thirdPlatformType.getCode())){
                continue;
            }
            if (Strings.noEmpty(adType)){
                AdTypeConvertVo adTypeConvertVo = AdTypeSub.adTypeMapFl.get(adInfoEntity.getType());
                if (!adType.contains(adTypeConvertVo.getSubTypeName())){
                    continue;
                }
            }
            if (!appId.equals(AdUtils.getAppId(adInfoEntity))){
                continue;
            }
            if (auto){
                if (adInfoEntity.getCreateTime().after(n24)){
                    continue;
                }
            }
            posIdList.add(AdUtils.getPosId(adInfoEntity));
        }
        return posIdList;
    }

    private List<NeedCloseAdVo> queryAllFromGdt(List<Long> posId,Integer product,Long appId,Integer os,Integer plat){
        ThirdAppEntity thirdAppEntity = thirdAppMapper.queryByProductAndOsAndAppId(product,os,appId.toString(),plat);
        if (thirdAppEntity == null){
            return new ArrayList<>();
        }
        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
        if (thirdCompanyEntity == null){
            return new ArrayList<>();
        }
        GdtAdPosQueryRequest request = new GdtAdPosQueryRequest();
        request.setMember_id(Long.valueOf(thirdCompanyEntity.getUserId()));
        request.setPage(1);
        request.setPage_size(posId.size());
        GdtAppInfoQueryRequest.FilterBean filterBean = new GdtAppInfoQueryRequest.FilterBean();
        filterBean.setField("app_id");
        filterBean.setOperator("EQUALS");
        filterBean.setValues(new String[]{String.valueOf(thirdAppEntity.getAppId())});

        GdtAppInfoQueryRequest.FilterBean filterBeanPlacement = new GdtAppInfoQueryRequest.FilterBean();
        filterBeanPlacement.setField("placement_id");
        filterBeanPlacement.setOperator("IN");
        filterBeanPlacement.setValues(
                posId.stream()
                        .map(String::valueOf)
                        .collect(Collectors.toList())
                        .toArray(new String[posId.size()])
        );

        GdtAppInfoQueryRequest.FilterBean filterBeanStatue = new GdtAppInfoQueryRequest.FilterBean();
        filterBeanStatue.setField("status");
        filterBeanStatue.setOperator("EQUALS");
        filterBeanStatue.setValues(new String[]{"Normal"});
        request.setFiltering(Arrays.asList(filterBeanPlacement,filterBean,filterBeanStatue));

        GdtResponse<GdtAdPosPageResponse> gdtResponse = gdtBaseService.queryAdPos(request,thirdCompanyEntity.getSecurityKey());
        if (gdtResponse.isSuccess()){
            if (Lists.noEmpty(gdtResponse.getData().getList())) {
                return gdtResponse.getData().getList().stream()
                        .map(this::convertToOwn)
                        .collect(Collectors.toList());
            }
            return new ArrayList<>();
        }else {
            throw new RuntimeException(gdtResponse.getMessage());
        }
    }

    private List<NeedCloseAdVo> queryAllFormCsj(List<Long> posId,Integer product,Long appId,Integer os,Integer plat){

        ThirdAppEntity thirdAppEntity = thirdAppMapper.queryByProductAndOsAndAppId(product,os,appId.toString(),plat);

        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyMapper.load(thirdAppEntity.getCompanyId());
        AdPosQueryRequest request = new AdPosQueryRequest();
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setUser_id(Math.toIntExact(Long.valueOf(thirdCompanyEntity.getUserId())));
        request.setRole_id(thirdCompanyEntity.getRoleId());
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(thirdCompanyEntity.getSecurityKey(),timestamp,nonce));
        request.setPage(1);
        request.setPage_size(posId.size());
        request.setVersion("1.0");
        request.setApp_id(new Integer[]{Math.toIntExact(Long.parseLong(thirdAppEntity.getAppId()))});
        request.setAd_slot_id(
                posId.stream()
                .map(Math::toIntExact)
                .collect(Collectors.toList())
                .toArray(new Integer[posId.size()])
        );
        Response<AdPosPageResponse> responseResponse = csjBaseService.queryAdPosList(request);
        if (responseResponse.isSuccess()){
            return responseResponse.getData().getAd_slot_list().stream()
                    .map(this::convertToOwn)
                    .collect(Collectors.toList());
        }else {
            throw new RuntimeException(responseResponse.getMessage());
        }
    }

    private NeedCloseAdVo convertToOwn(AdPosInfo adPosInfo){
        NeedCloseAdVo needCloseAdVo = new NeedCloseAdVo();
        needCloseAdVo.setPosId(Long.valueOf(adPosInfo.getAd_slot_id()));
        needCloseAdVo.setPlatform(Platform.CSJ.getDesc());
        needCloseAdVo.setPosName(adPosInfo.getAd_slot_name());
        int status = 1;
        if (adPosInfo.getStatus()  == 6 || adPosInfo.getStatus()  == 3){
            status = 0;
        }
        needCloseAdVo.setDesc(status == 1?"开启":"暂停");
        needCloseAdVo.setStatus(status);
        needCloseAdVo.setAdType(csjBaseService.convertToCsjType(adPosInfo.getAd_slot_type()));
        return needCloseAdVo;
    }

    private NeedCloseAdVo convertToOwn(GdtAdPosInfo adPosInfo){
        NeedCloseAdVo needCloseAdVo = new NeedCloseAdVo();
        needCloseAdVo.setPosId(adPosInfo.getPlacement_id());
        needCloseAdVo.setPlatform(Platform.GDT.getDesc());
        needCloseAdVo.setPosName(adPosInfo.getPlacement_name());
        int status = 1;
        if (!"Normal".equals(adPosInfo.getPause_status())){
            status = 0;
        }
        needCloseAdVo.setDesc(status == 1?"开启":"暂停");
        needCloseAdVo.setStatus(status);
        needCloseAdVo.setAdType(gdtBaseService.getScene(adPosInfo.getScene()));
        return needCloseAdVo;
    }


    public void closeAd(List<Long> posIds,Boolean freeze){
        CompletableFuture.runAsync(() ->{
            Boolean config = threadLocalBoolean.get();
            if (!config){
                log.info("任务未结束...");
                return;
            }
            threadLocalBoolean.set(Boolean.FALSE);
            doClose(posIds,freeze);
            threadLocalBoolean.set(Boolean.TRUE);
        },executor);
    }

    public void doClose(List<Long> posIds,Boolean freeze){
        try {
            // 从配置拉取对应 APPId
            Map<Long,ThirdAppEntity> appEntityMap = thirdConfigService.queryThirdApp(posIds);
            Map<Long,ThirdCompanyEntity> thirdCompanyEntityMap = thirdConfigService.queryThirdCompany(appEntityMap);
            // closeBeanList
            List<CloseAdBean> closeAdBeans =  posIds.stream()
                    .map(id ->{
                        CloseAdBean closeAdBean = new CloseAdBean();
                        closeAdBean.setPosId(id);
                        ThirdAppEntity thirdAppEntity = appEntityMap.get(id);
                        if (Objects.isNull(thirdAppEntity)  || thirdAppEntity.getId() == null){
                            log.warn("广告位 {} 未找到 APP...",id);
                            return null;
                        }
                        ThirdCompanyEntity thirdCompanyEntity = thirdCompanyEntityMap.get(id);
                        if (Objects.isNull(thirdCompanyEntity) || thirdCompanyEntity.getId() == null){
                            log.warn("广告位 {} 未找到 Company...",id);
                            return null;
                        }
                        closeAdBean.setThirdAppEntity(thirdAppEntity);
                        closeAdBean.setThirdCompanyEntity(thirdCompanyEntity);
                        return closeAdBean;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 执行
            closeAdNow(closeAdBeans,freeze);
        }catch (Exception e){
            log.error("Re:",e);
        }

    }


    private void closeAdNow(List<CloseAdBean> closeAdBeanList,boolean freeze){
        closeAdBeanList.forEach(r -> this.closeAdNow(r,freeze));
    }

    private void closeAdNow(CloseAdBean closeAdBean,boolean freeze){
        try {
            log.info("===>> 开始对广告 {} 进行关停", closeAdBean.getPosId());
            if (!canClose(closeAdBean.getThirdAppEntity())){
                log.info("===>> 广告 {} 平台不符合..拒绝关停", closeAdBean.getPosId());
                return;
            }
            if (!checkPosIdIsOpen(closeAdBean.getPosId())){
                log.info("===>> 广告 {} 在运行中..拒绝关停", closeAdBean.getPosId());
                return;
            }
            if (Platform.CSJ.getCode().equals(closeAdBean.getThirdAppEntity().getPlatformCode())){
                closeCsjAd(closeAdBean);
            }else if (Platform.GDT.getCode().equals(closeAdBean.getThirdAppEntity().getPlatformCode())){
                closeGdtAd(closeAdBean);
                if (freeze) {
                    freezeGdtAd(closeAdBean);
                }
                thirdAdPosService.closeByPosId(closeAdBean.getPosId());

            }
            log.info("===>> 完成对广告 {} 进行关停", closeAdBean.getPosId());
        }catch (Exception e){
            log.error("关停异常:",e);
        }
    }

    /**
     * 查看当前PosId是否在广告系统中使用
     * @param posId 三方id
     * @return TRUE - 在使用 FALSE -未在使用
     */
    private boolean checkPosIdIsOpen(Long posId){
        List<TbApThirdConfig> adIdList = thirdConfigService.queryAdId(posId);
        if (Lists.noEmpty(adIdList)){
            for (TbApThirdConfig tbApThirdConfig : adIdList){
                AdInfoEntity adInfoEntity = adInfoMapper.selectOneAd(tbApThirdConfig.getAdId(),tbApThirdConfig.getProduct());
                if (adInfoEntity != null && adInfoEntity.getState() == 1){
                    return false;
                }
            }
        }else {
            List<Long> adIdLists = adInfoMapper.adIdQueryByPosId(posId);
            if (adIdLists != null && adIdLists.size() > 0) {
                for (Long adId : adIdLists) {
                    ThirdAdModel model = adInfoMapper.getById(adId);
                    if (model != null && model.getState() == 1) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 当前广告APP 是否支持关停
     * @param thirdAppEntity APP实体
     * @return TRUE - 支持 FALSE -不支持
     */
    private boolean canClose(ThirdAppEntity thirdAppEntity){
        return SUPPORT_PLATFORM.contains(thirdAppEntity.getPlatformCode());
    }

    /**
     * 关停广点通广告
     * @param closeAdBean 请求实体
     */
    private void closeGdtAd(CloseAdBean closeAdBean){
        GdtAdPosPauseOrStartRequest request = new GdtAdPosPauseOrStartRequest();
        request.setPlacement_id(closeAdBean.getPosId());
        request.setMember_id(Long.valueOf(closeAdBean.getThirdCompanyEntity().getUserId()));

        GdtResponse<Void> gdtResponse = gdtBaseService.pauseAdPos(request,closeAdBean.getThirdCompanyEntity().getSecurityKey());
        if (!gdtResponse.isSuccess()){
            throw new RuntimeException(gdtResponse.getMessage());
        }
    }

    private void freezeGdtAd(CloseAdBean closeAdBean){
        GdtAdPosPauseOrStartRequest request = new GdtAdPosPauseOrStartRequest();
        request.setPlacement_id(closeAdBean.getPosId());
        request.setMember_id(Long.valueOf(closeAdBean.getThirdCompanyEntity().getUserId()));

        GdtResponse<Void> gdtResponse = gdtBaseService.freezeAdPos(request,closeAdBean.getThirdCompanyEntity().getSecurityKey());
        if (!gdtResponse.isSuccess()){
            throw new RuntimeException(gdtResponse.getMessage());
        }
    }

    /**
     * 删除广点通广告
     * @param closeAdBean 请求实体
     */
    private void deleteGdtAd(CloseAdBean closeAdBean){
        GdtAdPosPauseOrStartRequest request = new GdtAdPosPauseOrStartRequest();
        request.setPlacement_id(closeAdBean.getPosId());
        request.setMember_id(Long.valueOf(closeAdBean.getThirdCompanyEntity().getUserId()));
        GdtResponse<Void> gdtResponse = gdtBaseService.deleteAdPos(request,closeAdBean.getThirdCompanyEntity().getSecurityKey());
        if (!gdtResponse.isSuccess()){
            throw new RuntimeException(gdtResponse.getMessage());
        }
    }

    /**
     * 关停穿山甲广告
     * @param closeAdBean 请求实体
     */
    private void closeCsjAd(CloseAdBean closeAdBean){
        AdPosUpdateRequest request = new AdPosUpdateRequest();
        request.setUser_id(Math.toIntExact(Long.valueOf(closeAdBean.getThirdCompanyEntity().getUserId())));
        request.setRole_id(closeAdBean.getThirdCompanyEntity().getRoleId());
        request.setMask_rule_id(closeAdBean.getThirdCompanyEntity().getMaskRuleId());
        int timestamp = CsjBaseService.invokeTime();
        int nonce = CsjBaseService.invokeTime();
        request.setTimestamp(timestamp);
        request.setNonce(nonce);
        request.setSign(CsjBaseService.invokeSign(closeAdBean.getThirdCompanyEntity().getSecurityKey(),timestamp,nonce));
        request.setVersion("1.0");
        request.setAd_slot_id(Math.toIntExact(closeAdBean.getPosId()));
        request.setStatus(3);

        Response<AdPosUpdateResponse> response = csjBaseService.updateAdPos(request);
        if (!response.isSuccess()){
            throw new RuntimeException(response.getMessage());
        }
    }
}
