package com.coohua.ap.admin.controller.vo;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaseResponse {
    public static final int CODE_OK = 0;

    public static final int CODE_SYS_ERROR = 500;

    public static final String MESSAGE_EMPTY = "";

    public static final String MESSAGE_OK = "SUCCESS";

    public static final String MESSAGE_ERROR = "System Error";

    public static final BaseResponse SUCCESS = new BaseResponse(CODE_OK, MESSAGE_OK);

    public static final BaseResponse SYSTEM_ERROR = new BaseResponse(CODE_SYS_ERROR, MESSAGE_ERROR);

    private int ret;
    private Object data;

    public BaseResponse(int ret) {
        this.ret = ret;
    }

    private BaseResponse(int ret, Object data) {
        this.ret = ret;
        this.data = data;
    }

    public static BaseResponse build(int code, Object data) {
        return new BaseResponse(code, data);
    }

    public static BaseResponse build(Object data) {
        return new BaseResponse(CODE_OK, data);
    }
}
