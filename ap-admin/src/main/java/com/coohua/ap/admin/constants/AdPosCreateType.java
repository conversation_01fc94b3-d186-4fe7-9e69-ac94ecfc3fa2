package com.coohua.ap.admin.constants;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2021/11/16
 */
public enum  AdPosCreateType {
    KP(1,"开屏"),
    X<PERSON>(2,"信息流"),
    <PERSON>(3,"插屏"),
    <PERSON><PERSON><PERSON>(4,"激励视频"),
    NCP(5,"新插屏"),
    JLSP_BIDDING(6,"激励视频-BIDDING"),
    ;

    public Integer code;
    public String desc;

    AdPosCreateType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(int code) {
        return Arrays.stream(AdPosCreateType.values())
                .filter(status -> status.getCode() == code)
                .findFirst()
                .map(AdPosCreateType::getDesc)
                .orElse(null);
    }

    public static AdPosCreateType getByDesc(String desc) {
        for (AdPosCreateType type : AdPosCreateType.values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
