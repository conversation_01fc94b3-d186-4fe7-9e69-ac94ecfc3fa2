<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbConfigNewFilterMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbConfigNewFilter" >
        <result column="id" property="id" />
        <result column="index_ix" property="indexIx" />
        <result column="config_id" property="configId" />
        <result column="product" property="product" />
        <result column="switch_flag" property="switchFlag" />
        <result column="del_flag" property="delFlag" />
        <result column="ad_type" property="adType" />
        <result column="price" property="price" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                index_ix,
                config_id,
                product,
                switch_flag,
                del_flag,
                ad_type,
                price,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.TbConfigNewFilter">
        INSERT INTO tb_config_new_filter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != indexIx">
                index_ix,
            </if>
            <if test="null != configId">
                config_id,
            </if>
            <if test="null != product">
                product,
            </if>
            <if test="null != switchFlag">
                switch_flag,
            </if>
            <if test="null != delFlag">
                del_flag,
            </if>
            <if test="null != adType and '' != adType">
                ad_type,
            </if>
            <if test="null != price ">
                price,
            </if>
            <if test="null != createTime ">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != indexIx">
                #{indexIx},
            </if>
            <if test="null != configId ">
                #{configId},
            </if>
            <if test="null != product">
                #{product},
            </if>
            <if test="null != switchFlag">
                #{switchFlag},
            </if>
            <if test="null != delFlag">
                #{delFlag},
            </if>
            <if test="null != adType and '' != adType">
                #{adType},
            </if>
            <if test="null != price">
                #{price},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <delete id="delete" >
        DELETE FROM tb_config_new_filter
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.coohua.ap.admin.model.TbConfigNewFilter">
        UPDATE tb_config_new_filter
        <set>
            <if test="null != indexIx">index_ix = #{indexIx},</if>
            <if test="null != configId">config_id = #{configId},</if>
            <if test="null != product">product = #{product},</if>
            <if test="null != switchFlag">switch_flag = #{switchFlag},</if>
            <if test="null != delFlag">del_flag = #{delFlag},</if>
            <if test="null != adType and '' != adType">ad_type = #{adType},</if>
            <if test="null != price ">price = #{price},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_config_new_filter
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_config_new_filter
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_config_new_filter
    </select>

</mapper>