package com.coohua.ap.admin.constants;

/**
 * <AUTHOR>
 * @since 2022/7/12
 */
public enum  CreateReason {
    GAP(1,"PV_GAP"),
    INCOME_OVER(2,"INCOME_OVER"),
    INCOME_GAP(3,"INCOME_GAP"),
    LAYER_INSERT(4,"LAYER_INSERT"),
    ;
    private Integer type;
    private String desc;


    public static CreateReason getByType(Integer type){
        for (CreateReason createReason: CreateReason.values()){
            if (createReason.getType().equals(type)){
                return createReason;
            }
        }
        return CreateReason.GAP;
    }

    CreateReason(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
