package com.coohua.ap.admin.service.impl;

import com.coohua.ap.admin.controller.vo.UserVO;
import com.coohua.ap.admin.mapper.ap.UserMapper;
import com.coohua.ap.admin.model.UserModel;
import com.coohua.ap.admin.service.UserService;
import com.coohua.ap.base.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/03/19
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<UserVO> listUser(String username) {
        List<UserModel> models = userMapper.queryAll(username);
        List<UserVO> ret = new ArrayList<>();
        for (UserModel model : models) {
            ret.add(convertModelToVO(model));
        }
        return ret;
    }

    @Override
    public int addUser(UserVO userVO) {
        UserModel model = new UserModel();
        model.setUsername(userVO.getUsername());
        model.setPassword(userVO.getPassword());
        model.setName(userVO.getName());
        model.setProduct(userVO.getAppId());
        model.setProductName(userVO.getAppName());
        model.setState(1);
        return userMapper.addUser(model);
    }

    @Override
    public int updateUser(UserVO userVO) {
        UserModel model = new UserModel();
        model.setId(userVO.getId());
        model.setUsername(userVO.getUsername());
        model.setPassword(userVO.getPassword());
        model.setName(userVO.getName());
        return userMapper.updateUser(model);
    }

    private UserVO convertModelToVO(UserModel model) {
        UserVO vo = new UserVO();
        vo.setId(model.getId());
        vo.setUsername(model.getUsername());
        vo.setPassword(model.getPassword());
        vo.setName(model.getName());
        vo.setAppId(model.getProduct());
        vo.setAppName(model.getProductName());
        vo.setState(model.getState());
        vo.setUpdateTime(DateUtils.formatDateForYMDHMS(model.getUpdateTime()));
        return vo;
    }
}
