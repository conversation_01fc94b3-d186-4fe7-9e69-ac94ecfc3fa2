package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.ApExceptionAdPos;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ApAdIncomeMapper {
    /**
     * 查询截止至#{yesterday}广告位收入
     *
     * @param product       产品线
     * @param os            操作系统
     * @param adTypeName    广告类型名称
     * @param dsp           广告主
     * @param yesterday      昨天日期
     * @return List<ApExceptionAdPos>
     */
    List<ApExceptionAdPos> queryTotalAdIncome(@Param("product") String product, @Param("os") String os, @Param("adTypeName") String adTypeName, @Param("dsp") String dsp, @Param("yesterday") String yesterday);

    /**
     * 查询昨日广告位收入
     * @param product
     * @param os
     * @param adTypeName
     * @param dsp
     * @param yesterday
     * @return
     */
    List<ApExceptionAdPos> queryYesterdayAdIncome(@Param("product") String product, @Param("os") String os, @Param("adTypeName") String adTypeName, @Param("dsp") String dsp, @Param("yesterday") String yesterday);
}
