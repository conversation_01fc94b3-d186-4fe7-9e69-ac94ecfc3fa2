package com.coohua.ap.admin.controller.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ToString
public class AdSearchTestVO {

    // 是否作弊用户
    private boolean cheatUser;

    private String gdtAppId; // 广点通账号
    private String baiduAppId; // 百度账号
    private String toutiaoAppId; // 今日头条账号
    private String appId360; // 360APPID
    private String mintegralAppId;
    private String oppoAppId;
    private String sougouAppId;
    private String inmPicPlaId; // Inmobi静态图广告PlantID
    private String inmPicActId; // Inmobi静态图AccountID
    private String inmVideoPlaId; // Inmobi视频广告PlantID
    private String inmVideoActId; // Inmobi视频广告AccountID
    private String inmActId; // InMobi广告AccountID

    // 可投放的广告列表<AdType, List<AdID>>
    private List<AdSearchTestOKVO> okList = new ArrayList<>();

    // 被过滤的广告列表
    private List<AdSearchTestNotOKVO> notOkList = new ArrayList<>();

}
