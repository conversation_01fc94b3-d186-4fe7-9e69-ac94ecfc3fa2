package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.service.ConfigNewBasicService;
import com.coohua.ap.admin.utils.SessionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2021/6/23
 */
@Slf4j
@Controller
@RequestMapping("/config/new")
public class ConfigNewPosController {

    @Autowired
    private ConfigNewBasicService configNewBasicService;

    @RequestMapping("/queryList")
    @ResponseBody
    public BaseResponse queryPosList(@RequestParam(value = "switchT",required = false) Integer state,
                                     @RequestParam(value = "configType" ,required = false) Integer type,
                                     @RequestParam(value = "configName",required = false) String name,
                                     @RequestParam(value = "id",required = false) Integer id,
                                     Page<ConfigBaseVO> page, HttpServletRequest request){
        int product = SessionUtils.getUserProduct(request);
        configNewBasicService.queryPosList(product,name,state,id,page,type);
        return BaseResponse.build(page);
    }

    @RequestMapping("/addNew")
    @ResponseBody
    public BaseResponse addNewPos(@RequestBody ConfigBaseVO configBaseVO,HttpServletRequest request){
        int product = SessionUtils.getUserProduct(request);
        configNewBasicService.addPosEntity(configBaseVO,product);
        return new BaseResponse(0);
    }

    @RequestMapping("/editName")
    @ResponseBody
    public BaseResponse editName(@Param("name")String name,
                                  @Param("id")Integer id,
                                  HttpServletRequest request){
        int product = SessionUtils.getUserProduct(request);
        configNewBasicService.editPosName(id,product,name);
        return new BaseResponse(0);
    }

    @RequestMapping("/copyNew")
    @ResponseBody
    public BaseResponse copyNewPos(@RequestParam("id") Integer id,HttpServletRequest request){
        configNewBasicService.copyPosEntity(id);
        return new BaseResponse(0);
    }

    @RequestMapping("/switchPos")
    @ResponseBody
    public BaseResponse switchPos(@RequestParam("id") int id,
                                  @RequestParam("switchT") int state,
                                  HttpServletRequest request){
        configNewBasicService.switchPosState(id,state);
        return new BaseResponse(0);
    }

    @RequestMapping("/delPos")
    @ResponseBody
    public BaseResponse delPosConfig(@RequestParam("id") int id,HttpServletRequest request){
        configNewBasicService.delPosEntity(id);
        return new BaseResponse(0);
    }
}
