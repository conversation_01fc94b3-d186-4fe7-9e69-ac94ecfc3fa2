package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbApThirdConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description tb_ap_third_config
 * <AUTHOR>
 * @date 2021-12-02
 */
@Mapper
@Repository
public interface TbApThirdConfigMapper {

    int insert(TbApThirdConfig tbApThirdConfig);

    int delete(int id);

    int update(TbApThirdConfig tbApThirdConfig);

    TbApThirdConfig load(Long id);

    List<TbApThirdConfig> pageList(int offset, int pagesize);

    int pageListCount(int offset,int pagesize);

    @Select({"<script>",
            "select * from tb_ap_third_config where pos_id in ",
            "<foreach collection='posIds' index='index' item='item' open='(' separator=',' close=')'> #{item} </foreach>",
            "</script>",
    })
    List<TbApThirdConfig> queryByPosId(@Param("posIds") List<Long> posId);

    @Select("select * from tb_ap_third_config where pos_id =#{posId}")
    List<TbApThirdConfig> queryByPosIdSingle(@Param("posId")Long posId);

}
