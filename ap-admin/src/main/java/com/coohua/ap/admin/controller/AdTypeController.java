package com.coohua.ap.admin.controller;

import com.coohua.ap.admin.controller.vo.AdTypeVO;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019-11-26
 */
@RestController
@RequestMapping("adType")
public class AdTypeController {

    @PostMapping("list")
    public BaseResponse list() {

        BaseResponse baseResponse = new BaseResponse(0);
        List<AdTypeVO> vos = new ArrayList<>();

        for (AdTypeSub adType : AdTypeSub.adTypeList) {
            AdTypeVO vo = new AdTypeVO();
            vo.setCode(adType.getType());
            vo.setName(adType.getTypeDesc());
            vos.add(vo);
        }
        baseResponse.setData(vos);
        return baseResponse;
    }
}
