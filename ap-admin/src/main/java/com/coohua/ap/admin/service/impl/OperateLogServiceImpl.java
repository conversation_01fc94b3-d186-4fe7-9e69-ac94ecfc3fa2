package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.aop.OperateType;
import com.coohua.ap.admin.constants.OperateLogType;
import com.coohua.ap.admin.controller.vo.OperateLogVO;
import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.mapper.ap.OperateLogMapper;
import com.coohua.ap.admin.model.OperateLogEntity;
import com.coohua.ap.admin.service.OperateLogService;
import com.coohua.ap.base.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 *
 * </pre>
 *
 */
@Service
public class OperateLogServiceImpl implements OperateLogService {

    @Autowired
    private OperateLogMapper operateLogMapper;


    @Override
    public void queryPageList(Integer itemId, Integer userId, String userName, Integer operateType, Integer operateLogType, int userProduct, Page<OperateLogVO> page) {
        int startIndex = (page.getPageNo() - 1) * page.getPageSize();
        List<OperateLogEntity> retList = operateLogMapper.queryByApp(userProduct, startIndex, page.getPageSize(), itemId, userId, userName, operateType, operateLogType);
        long count = operateLogMapper.queryCount(userProduct, itemId, userId, userName, operateType, operateLogType);
        List<OperateLogVO> result = new ArrayList<>();
        for (OperateLogEntity entity : retList) {
            result.add(entityToVo(entity));
        }
        page.setCount((int) count);
        page.setItems(result);
    }


    private OperateLogVO entityToVo(OperateLogEntity entity) {
        OperateLogVO vo = new OperateLogVO();
        vo.setId(entity.getId());
        vo.setItemId(entity.getItemId());
        vo.setOperateLogType(OperateLogType.find(entity.getOperateLogType()).desc());
        vo.setIp(entity.getIp());
        vo.setUserId(entity.getUserId());
        vo.setUserName(entity.getUserName());
        vo.setOperateType(OperateType.find(entity.getOperateType()).desc());
        vo.setOldValue(JSONObject.toJSONString(entity.getOldValue()));
        vo.setNewValue(JSONObject.toJSONString(entity.getNewValue()));
        vo.setOperateTime(DateUtils.formatDateForYMDHMS(entity.getOperateTime()));
        return vo;
    }
}
