package com.coohua.ap.admin.controller.vo;

/**
 * <pre>
 * Comments:
 *
 *
 * <pre/>
 * <hr/>
 * Created by <PERSON><PERSON><PERSON> on 2017/6/14.
 */
public class AdPtype implements Comparable<AdPtype> {
    private int ptype; // 广告父类型ID
    private String name; // 广告子类型

    public AdPtype() {
    }

    public AdPtype(int ptype, String name) {
        this.ptype = ptype;
        this.name = name;
    }

    public int getPtype() {
        return ptype;
    }

    public void setPtype(int ptype) {
        this.ptype = ptype;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "AdPtype{" +
                "ptype=" + ptype +
                ", name='" + name + '\'' +
                '}';
    }

    @Override
    public int compareTo(AdPtype other) {
        return this.ptype > other.ptype ? 1 : this.ptype == other.ptype ? 0 : -1;
    }
}
