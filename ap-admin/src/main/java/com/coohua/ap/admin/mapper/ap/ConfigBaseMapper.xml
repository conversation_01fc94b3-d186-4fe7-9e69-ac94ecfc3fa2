<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.ConfigBaseMapper">

	<resultMap type="com.coohua.ap.admin.model.ConfigBaseEntity" id="configBaseResult">
		<id column="id" property="id" />
		<result column="name" property="name" />
		<result column="product" property="product" />
		<result column="type" property="type" />
		<result column="config" property="config"/>
		<result column="comment" property="comment"/>
		<result column="state" property="state"/>
		<result column="pos_type" property="posType"/>
		<result column="del_flag" property="delFlag"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
	</resultMap>

	<sql id="base_column">
		`name`, `product`, `type`, `config`, `comment`, `state`,`pos_type`,`del_flag`, `create_time`, `update_time`
	</sql>

	<select id="queryAll" resultMap="configBaseResult">
		SELECT * FROM tb_ap_config_base WHERE `state` = 1
	</select>

	<select id="queryOneById" resultMap="configBaseResult">
		SELECT * FROM tb_ap_config_base WHERE `id` = #{id}
	</select>

	<select id="queryAllByProduct" resultMap="configBaseResult">
		<![CDATA[
			SELECT *
			  FROM tb_ap_config_base
			 WHERE `product` = #{configBase.product} and del_flag = 1
		]]>
		<if test="configBase.type!=0">
			and `type` = #{configBase.type}
		</if>
		<if test="configBase.posType!=0">
			and `pos_type` = #{configBase.posType}
		</if>
		<if test="configBase.id!=0">
			and `id` = #{configBase.id}
		</if>
		<if test="configBase.state!=-1">
			and `state` = #{configBase.state}
		</if>
		<if test="queryType==1">
			and `type` != 5
		</if>
		<if test="queryType==2">
			and `type` = 5
		</if>
		<if test="configBase.name!=null and configBase.name!=''">
			and `name` like concat('%',#{configBase.name},'%')
		</if>
		LIMIT #{from}, #{offset}
	</select>

	<select id="countAllByProduct" resultType="Long">
		<![CDATA[
			SELECT COUNT(*)
			  FROM tb_ap_config_base
			 WHERE `product`= #{configBase.product} and del_flag = 1
		]]>
		<if test="configBase.type!=0">
			and `type` = #{configBase.type}
		</if>
		<if test="configBase.posType!=0">
			and `pos_type` = #{configBase.posType}
		</if>
		<if test="configBase.id!=0">
			and `id` = #{configBase.id}
		</if>
		<if test="configBase.state!=-1">
			and `state` = #{configBase.state}
		</if>
		<if test="queryType==1">
			and `type` != 5
		</if>
		<if test="queryType==2">
			and `type` = 5
		</if>
		<if test="configBase.name!=null and configBase.name!=''">
			and `name` like concat('%',#{configBase.name},'%')
		</if>
	</select>

	<insert id="insertConfig" parameterType="com.coohua.ap.admin.model.ConfigBaseEntity" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO tb_ap_config_base
		(
			<include refid="base_column"/>
		)
		VALUES
		(
			#{name}, #{product}, #{type}, #{config}, #{comment}, #{state},#{posType},#{delFlag}, now(), now()
		)
	</insert>

	<update id="updateConfig" parameterType="com.coohua.ap.admin.model.ConfigBaseEntity">
		UPDATE tb_ap_config_base
		   SET `name` = #{name},
		       `config` = #{config},
		       `comment` = #{comment},
		       `state` = #{state},
		       `del_flag` = #{delFlag},
		       `type` = #{type},
		       `update_time` = now()
		 WHERE `id` = #{id}
	</update>

	<update id="updateConfigState" parameterType="com.coohua.ap.admin.model.ConfigBaseEntity">
		UPDATE tb_ap_config_base
		   SET `state` = #{state},
		       `update_time` = now()
		 WHERE `id` = #{id}
	</update>

	<update id="updateConfigDelFlag" parameterType="com.coohua.ap.admin.model.ConfigBaseEntity">
		UPDATE tb_ap_config_base
		   SET `del_flag` = #{delFlag},
		       `update_time` = now()
		 WHERE `id` = #{id}
	</update>

	<select id="queryIdAndNameList" resultMap="configBaseResult">
		<![CDATA[
			SELECT *
			  FROM tb_ap_config_base
			 WHERE `product` = #{product}
			   AND `state` = 1
		]]>
	</select>

</mapper>