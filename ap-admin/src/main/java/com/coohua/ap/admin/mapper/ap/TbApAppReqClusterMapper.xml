<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApAppReqClusterMapper">

    <sql id="Base_Column_List">
        id,app_id,redis_hash_key,
        os,belong_cluster,app_version,
        sdk_version,req_time,create_time,
        update_time
    </sql>
    <insert id="saveBatch">
        insert into ${table} (app_id,redis_hash_key,
        os,belong_cluster,app_version,
        sdk_version,req_time,create_time,
        update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.appId},#{item.redisHashKey},
            #{item.os},#{item.belongCluster},#{item.appVersion},#{item.sdkVersion},#{item.reqTime},#{item.createTime},#{item.updateTime}
            )
        </foreach>
    </insert>
    <select id="selectAllHashKeysFromMysql" resultType="java.lang.String" parameterType="java.lang.String">
        select redis_hash_key from ${table}
    </select>
</mapper>
