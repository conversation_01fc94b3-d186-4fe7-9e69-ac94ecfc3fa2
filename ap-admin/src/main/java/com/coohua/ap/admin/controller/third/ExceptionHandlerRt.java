package com.coohua.ap.admin.controller.third;

import com.coohua.ap.admin.controller.vo.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import static com.coohua.ap.admin.controller.vo.BaseResponse.CODE_SYS_ERROR;

/**
 * <AUTHOR>
 * @since 2020/7/28
 */
@Slf4j
@ControllerAdvice
public class ExceptionHandlerRt {

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public BaseResponse exception(Exception e){
        BaseResponse response = new BaseResponse(CODE_SYS_ERROR);
        response.setData(e.getMessage());
        return response;
    }

}
