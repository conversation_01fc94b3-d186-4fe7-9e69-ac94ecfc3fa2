package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.TbApDiminishBiddingMonitor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface TbApDiminishBiddingMonitorMapper {

    /**
     * 细分监控
     */
    @Select("select * from `bp-ap`.tb_ap_diminish_bidding_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '30' MINUTE) and " +
            " ori_bidding_ecpm1 >= waterfall_ecpm1 * 1.5 and ori_bidding_ecpm3 >= waterfall_ecpm3 * 1.5 and new_bidding_pv3 = 0")
    List<TbApDiminishBiddingMonitor> segementNoLayersBidding();

    @Select("select * from `bp-ap`.tb_ap_diminish_bidding_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '30' MINUTE) and " +
            " ori_bidding_ecpm3 < waterfall_ecpm3  * 1.2 and ori_bidding_ecpm1 < waterfall_ecpm1 * 1.2 ")
    List<TbApDiminishBiddingMonitor> segementLowLayersBidding();

    @Select("select * from `bp-ap`.tb_ap_diminish_bidding_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '30' MINUTE) and " +
            " new_bidding_ecpm1 < waterfall_ori_bidding_ecpm1 * 1.2 and new_bidding_ecpm3 < waterfall_ori_bidding_ecpm3 * 1.2 ")
    List<TbApDiminishBiddingMonitor> segementHighLayersBidding();


    /**
     * 整体监控
     */

    @Select("select * from `bp-ap`.tb_ap_diminish_bidding_monitor where " +
            " create_time >= DATE_SUB(NOW(), INTERVAL '30' MINUTE) and os = #{os} and product = #{product} and " +
            " order by create_time desc limit 1")
    TbApDiminishBiddingMonitor entireBiddingMonitorByProduct(@Param("os") String os, @Param("product") String product);

}
