package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.domain.EmptyAdCountDomain;
import com.coohua.ap.base.domain.EmptyAdDomain;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;
import java.util.Map;

public interface EmptyAdInfoMapper {


    @SelectProvider(type = EmptyAdInfoProvider.class, method = "queryEmptyAdInfoList")
    List<EmptyAdDomain> queryEmptyAd(@Param("appId") int appId, @Param("pageNum") int pageNum,@Param("limitNum") int limitNum);



    @SelectProvider(type = EmptyAdInfoProvider.class, method = "queryCountByProduct")
    List<EmptyAdCountDomain> queryCountByProduct(@Param("appId")int appId);





    class EmptyAdInfoProvider{

        public String queryCountByProduct(Map<String,Object> param){
            int appId = (int) param.get("appId");
            String sql = "SELECT a.app_id as appId,b.name as product,count(a.id) as count FROM `bp_ap_ad_empty_log` as a left join " +
                    "tb_ap_app as b on a.app_id=b.app_id";
            if(appId==0){
                sql = sql + " group by a.app_id";
            }else {
                sql = sql + " where a.app_id = #{appId}";
            }
            return sql;
        }

        public String queryEmptyAdInfoList(Map<String,Object> param){
            String sql = "select a.id as id,a.app_id as appId,a.user_id as userId,a.strategy_id as strategyId,a.os as os," +
                    "a.region as region,a.anonymous as anonymous,a.regist_time as registTime,a.income as income,a.version" +
                    " as version,a.pkg_name as pkgName,a.channel_id as channelId,a.create_time as createTime," +
                    " b.name as product "+
                    "from bp_ap_ad_empty_log as a left join tb_ap_app as b on a.app_id=b.app_id";
            int appId = (int) param.get("appId");
            int pageNum = (int) param.get("pageNum");
            int limitNum = (int) param.get("limitNum");
            if(appId==0){
                sql = sql + " order by a.create_time desc limit "+(pageNum-1)*limitNum+",#{limitNum}";
            }else {
                sql = sql + " where a.app_id=#{appId} order by a.create_time desc limit "+(pageNum-1)*limitNum+",#{limitNum}";
            }
            return sql;
        }
    }
}
