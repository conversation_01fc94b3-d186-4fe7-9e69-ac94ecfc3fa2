package com.coohua.ap.admin.service.third;

import com.coohua.ap.admin.constants.OpType;
import com.coohua.ap.admin.model.ThirdAdPosEntity;
import com.coohua.ap.admin.model.ThirdAppEntity;
import com.coohua.ap.admin.model.ThirdAppLoad;
import com.coohua.ap.admin.model.ThirdCompanyEntity;
import com.coohua.ap.admin.utils.third.Lists;
import com.coohua.ap.admin.utils.third.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/1/19
 */
@Slf4j
@Service
public class RefreshThirdInfoService {


    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private ThirdAppService thirdAppService;
    @Autowired
    private ThirdCompanyService thirdCompanyService;
    @Autowired
    private ThirdAdPosService thirdAdPosService;
    @Autowired
    private ThirdLogService thirdLogService;

    private final AtomicBoolean flag = new AtomicBoolean(Boolean.TRUE);
    private final AtomicBoolean flagPos = new AtomicBoolean(Boolean.TRUE);
    private final AtomicBoolean flagTempApp = new AtomicBoolean(Boolean.TRUE);
    private final AtomicReference<String> tempAppIdUId = new AtomicReference<> ();
    private final AtomicReference<CompletableFuture> tempAppFuture = new AtomicReference<> ();

    private AtomicInteger companyIdTemp = new AtomicInteger();

    @Scheduled(cron = "0 0 * * * ?")
    public void refreshAppList(){
        log.info("开始同步应用数量....");
        List<Integer> companyIdList = thirdCompanyService.getAllCompanyId();
        companyIdList.forEach(id ->{
            try {
                thirdCompanyService.refreshCompanyApp(id);
            }catch (Exception e){
                log.error("更新应用数量异常...",e);
            }
        });
        log.info("结束同步应用数量....");
    }

    /**
     * 同步所有的产品信息
     */
    public boolean synchronizeAppList(Integer companyId){
        if (flag.getAndSet(Boolean.FALSE)) {
            log.info("开始任务....");
            CompletableFuture.runAsync(() -> {
                List<ThirdAppEntity> thirdAppEntities = new ArrayList<>();
                if (companyId == null) {
                    List<ThirdCompanyEntity> companyEntities = thirdCompanyService.queryAll();
                    companyEntities.parallelStream().forEach(thirdCompanyEntity -> {
                        List<ThirdAppEntity> thirdAppEntityList = thirdCompanyService.queryAllThisCompany(thirdCompanyEntity);
                        thirdAppEntities.addAll(thirdAppEntityList);
                    });
                }else {
                    ThirdCompanyEntity companyEntity = thirdCompanyService.load(companyId);
                    List<ThirdAppEntity> thirdAppEntityList = thirdCompanyService.queryAllThisCompany(companyEntity);
                    thirdAppEntities.addAll(thirdAppEntityList);
                }
                // 有则更新 无则插入
                thirdAppService.insertOrUpdate(thirdAppEntities);
                // 指标设置为可刷新
                flag.set(Boolean.TRUE);
            }, threadPoolTaskExecutor);
            log.info("更新App任务已提交");
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public String synchronizeAppListTemp(Integer companyId){
        companyIdTemp.set(companyId);
        stopOrRemoveTempList(null);
        companyIdTemp = new AtomicInteger();
        if (flagTempApp.getAndSet(Boolean.FALSE)) {
            log.info("开始任务....");
            companyIdTemp.set(companyId);
            String batchId = UUID.randomUUID().toString();
            tempAppIdUId.set(batchId);
            CompletableFuture future = CompletableFuture.runAsync(() -> {
                try {
                    List<ThirdAppEntity> thirdAppEntities = new ArrayList<>();
                    ThirdCompanyEntity companyEntity = thirdCompanyService.load(companyId);
                    List<ThirdAppEntity> thirdAppEntityList = thirdCompanyService.queryAllThisCompany(companyEntity);
                    if (thirdAppEntityList.size() > 0) {
                        thirdAppEntities.addAll(thirdAppEntityList);
                    }
                    List<ThirdAppLoad> thirdAppLoadList = thirdAppEntities.stream()
                            .map(thirdAppEntity -> {
                                ThirdAppLoad load = new ThirdAppLoad();
                                BeanUtils.copyProperties(thirdAppEntity,load);
                                load.setBatchNo(batchId);
                                return load;
                            })
                            .collect(Collectors.toList());
                    // 有则更新 无则插入
                    thirdAppService.insertOrUpdateTemp(thirdAppLoadList);
                }catch (Exception e){
                    log.error("出现异常：",e);
                }
                // 指标设置为可刷新
                flagTempApp.set(Boolean.TRUE);
                tempAppIdUId.set(null);
                tempAppFuture.set(null);
            }, threadPoolTaskExecutor);
            tempAppFuture.set(future);
            log.info("更新App任务已提交");
            return batchId;
        }
        return null;
    }

    public void stopOrRemoveTempList(String batch){
        if (Strings.noEmpty(batch)){
            thirdAppService.deleteBatch(batch);
        }else {
            if (Strings.noEmpty(tempAppIdUId.get())) {
                CompletableFuture future = tempAppFuture.get();
                if (future != null) {
                    future.cancel(true);
                }
                thirdAppService.deleteBatch(tempAppIdUId.get());
            }else {
                log.info("很不巧任务结束了,清空本产品下所有....");
                thirdAppService.deleteBatchByCpy(companyIdTemp.get());
            }
        }
    }

    /**
     * 同步所有应用的广告位到系统
     */
    public boolean synchronizeAdPosList(Integer id,String account,String name){
        if (flagPos.getAndSet(Boolean.FALSE)) {

            CompletableFuture.runAsync(() -> {
                // 查询App
                ThirdAppEntity thirdAppEntity = thirdAppService.loadSingle(id);
                if (thirdAppEntity == null){
                    log.error("同步所有应用的广告位到系统 未查询到 {} ...",id);
                    flagPos.set(Boolean.TRUE);
                    return;
                }
                List<ThirdAdPosEntity> thirdAdPosEntities = new ArrayList<>(thirdAppService.queryAllPosList(thirdAppEntity));
                // 有则更新 无则插入
                if (thirdAdPosEntities.size() > 0) {
                    log.info("同步所有应用的广告位到系统 thirdAdPosEntities : {}", thirdAdPosEntities);
                    thirdAdPosService.insertOrUpdate(thirdAdPosEntities);
                }
                thirdLogService.saveLog(account, name, OpType.SYCN_ADPOS, id, null, null);
                flagPos.set(Boolean.TRUE);
            }, threadPoolTaskExecutor);

            log.info("同步所有应用的广告位到系统 更新广告位任务已提交");
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public void synchTempToRealApp(List<Integer> ids){
        List<ThirdAppLoad> thirdAppLoadList = thirdAppService.queryTempByIds(ids);
        if (Lists.noEmpty(thirdAppLoadList)){
            CompletableFuture.runAsync(() ->{
                List<ThirdAppEntity> thirdAppEntities = thirdAppLoadList.stream().map(thirdAppLoad -> {
                    ThirdAppEntity entity = new ThirdAppEntity();
                    BeanUtils.copyProperties(thirdAppLoad,entity);
                    entity.setSwitchFlag(1);
                    return entity;
                }).collect(Collectors.toList());
                // 有则更新 无则插入
                thirdAppService.insertOrUpdate(thirdAppEntities);
            },threadPoolTaskExecutor);
        }
    }
}
