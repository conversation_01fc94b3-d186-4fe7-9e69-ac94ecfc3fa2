<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- 命名空间对应 Mapper 接口路径 -->
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApReplaceAdMapper">

    <!-- 基础结果集映射：数据库字段与实体类属性对应 -->
    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbApReplaceAd">
        <result column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_name" property="strategyName"/>
        <result column="os" property="os"/>
        <result column="app_id" property="appId"/>
        <result column="product" property="product"/>
        <result column="product_name" property="productName"/>
        <result column="ad_type_name" property="adTypeName"/>
        <result column="platform" property="platform"/>
        <result column="ad_type" property="adType"/>
        <result column="ecpm" property="ecpm"/>
        <result column="ab_test" property="abTest"/>
        <result column="layer_type" property="layerType"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础字段列表：复用查询字段 -->
    <sql id="Base_Column_List">
        id,
        strategy_id,
        strategy_name,
        os,
        app_id,
        product,
        product_name,
        ad_type_name,
        platform,
        ad_type,
        ecpm,
        ab_test,
        layer_type,
        is_enabled,
        create_time,
        update_time
    </sql>

    <!-- 插入方法 -->
    <insert id="insert"
            useGeneratedKeys="true"
            keyColumn="id"
            keyProperty="id"
            parameterType="com.coohua.ap.admin.model.TbApReplaceAd">
        INSERT INTO tb_ap_replace_ad
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != strategyId">strategy_id,</if>
            <if test="null != strategyName">strategy_name,</if>
            <if test="null != os">os,</if>
            <if test="null != appId">app_id,</if>
            <if test="null != product">product,</if>
            <if test="null != productName">product_name,</if>
            <if test="null != adTypeName">ad_type_name,</if>
            <if test="null != platform">platform,</if>
            <if test="null != adType">ad_type,</if>
            <if test="null != ecpm">ecpm,</if>
            <if test="null != abTest">ab_test,</if>
            <if test="null != layerType">layer_type,</if>
            <if test="null != isEnabled">is_enabled,</if>
            <if test="null != createTime">create_time,</if>
            <if test="null != updateTime">update_time</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != strategyId">#{strategyId},</if>
            <if test="null != strategyName">#{strategyName},</if>
            <if test="null != os">#{os},</if>
            <if test="null != appId">#{appId},</if>
            <if test="null != product">#{product},</if>
            <if test="null != productName">#{productName},</if>
            <if test="null != adTypeName">#{adTypeName},</if>
            <if test="null != platform">#{platform},</if>
            <if test="null != adType">#{adType},</if>
            <if test="null != ecpm">#{ecpm},</if>
            <if test="null != abTest">#{abTest},</if>
            <if test="null != layerType">#{layerType},</if>
            <if test="null != isEnabled">#{isEnabled},</if>
            <if test="null != createTime">#{createTime},</if>
            <if test="null != updateTime">#{updateTime}</if>
        </trim>
    </insert>

    <!-- 删除方法 -->
    <delete id="delete">
        DELETE FROM tb_ap_replace_ad
        WHERE id = #{id}
    </delete>

    <!-- 更新方法 -->
    <update id="update" parameterType="com.coohua.ap.admin.model.TbApReplaceAd">
        UPDATE tb_ap_replace_ad
        <set>
            <if test="null != strategyId">strategy_id = #{strategyId},</if>
            <if test="null != strategyName">strategy_name = #{strategyName},</if>
            <if test="null != os">os = #{os},</if>
            <if test="null != appId">app_id = #{appId},</if>
            <if test="null != product">product = #{product},</if>
            <if test="null != productName">product_name = #{productName},</if>
            <if test="null != adTypeName">ad_type_name = #{adTypeName},</if>
            <if test="null != platform">platform = #{platform},</if>
            <if test="null != adType">ad_type = #{adType},</if>
            <if test="null != ecpm">ecpm = #{ecpm},</if>
            <if test="null != abTest">ab_test = #{abTest},</if>
            <if test="null != layerType">layer_type = #{layerType},</if>
            <if test="null != isEnabled">is_enabled = #{isEnabled},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 批量更新方法 -->
    <update id="batchUpdate" parameterType="java.util.List">
        UPDATE tb_ap_replace_ad
        <trim prefix="SET" suffixOverrides=",">
            <foreach collection="list" item="item" separator="">
                <if test="item.strategyId != null">
                    strategy_id = CASE WHEN id = #{item.id} THEN #{item.strategyId} ELSE strategy_id END,
                </if>
                <if test="item.strategyName != null">
                    strategy_name = CASE WHEN id = #{item.id} THEN #{item.strategyName} ELSE strategy_name END,
                </if>
                <if test="item.os != null">
                    os = CASE WHEN id = #{item.id} THEN #{item.os} ELSE os END,
                </if>
                <if test="item.appId != null">
                    app_id = CASE WHEN id = #{item.id} THEN #{item.appId} ELSE app_id END,
                </if>
                <if test="item.product != null">
                    product = CASE WHEN id = #{item.id} THEN #{item.product} ELSE product END,
                </if>
                <if test="item.productName != null">
                    product_name = CASE WHEN id = #{item.id} THEN #{item.productName} ELSE product_name END,
                </if>
                <if test="item.adTypeName != null">
                    ad_type_name = CASE WHEN id = #{item.id} THEN #{item.adTypeName} ELSE ad_type_name END,
                </if>
                <if test="item.platform != null">
                    platform = CASE WHEN id = #{item.id} THEN #{item.platform} ELSE platform END,
                </if>
                <if test="item.adType != null">
                    ad_type = CASE WHEN id = #{item.id} THEN #{item.adType} ELSE ad_type END,
                </if>
                <if test="item.ecpm != null">
                    ecpm = CASE WHEN id = #{item.id} THEN #{item.ecpm} ELSE ecpm END,
                </if>
                <if test="item.abTest != null">
                    ab_test = CASE WHEN id = #{item.id} THEN #{item.abTest} ELSE ab_test END,
                </if>
                <if test="item.layerType != null">
                    layer_type = CASE WHEN id = #{item.id} THEN #{item.layerType} ELSE layer_type END,
                </if>
                <if test="item.isEnabled != null">
                    is_enabled = CASE WHEN id = #{item.id} THEN #{item.isEnabled} ELSE is_enabled END,
                </if>
                <if test="item.createTime != null">
                    create_time = CASE WHEN id = #{item.id} THEN #{item.createTime} ELSE create_time END,
                </if>
                <if test="item.updateTime != null">
                    update_time = CASE WHEN id = #{item.id} THEN #{item.updateTime} ELSE update_time END,
                </if>
            </foreach>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 根据ID查询单条记录 -->
    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_ap_replace_ad
        WHERE id = #{id}
    </select>

    <!-- 分页查询列表 -->
    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_ap_replace_ad
        WHERE is_enabled = 1
        <if test="null != product">AND product = #{product}</if>
        <if test="null != adType and adType != ''">AND ad_type = #{adType}</if>
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 分页查询总数 -->
    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tb_ap_replace_ad
        WHERE is_enabled = 1
        <if test="null != product">AND product = #{product}</if>
        <if test="null != adType and adType != ''">AND ad_type = #{adType}</if>
    </select>

</mapper>