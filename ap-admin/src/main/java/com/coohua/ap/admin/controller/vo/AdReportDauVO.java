package com.coohua.ap.admin.controller.vo;

import com.coohua.ap.admin.model.AdReportDauEntity;
import com.coohua.ap.base.utils.DateUtils;
import lombok.Data;

/**
 * @ClassName: AdReportDauVO
 * @Description: 产品DAU
 * @Author: fan jin yang
 * @Date: 2020/4/27
 * @Version: V1.0
 **/
@Data
public class AdReportDauVO {

    /**
     * 产品dau
     */
    private long dau_device;

    /**
     * 日期
     */
    private String logday;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 产品名称
     */
    private String product_name;

    public AdReportDauEntity convert2Entity(){
        AdReportDauEntity adReportDauEntity =  new AdReportDauEntity();
        adReportDauEntity.setDau(this.dau_device);
        adReportDauEntity.setOs(this.os);
        adReportDauEntity.setProductName(this.product_name);
        adReportDauEntity.setLogday(DateUtils.parseDate(this.logday,"yyyyMMdd"));
        return adReportDauEntity;
    }
}
