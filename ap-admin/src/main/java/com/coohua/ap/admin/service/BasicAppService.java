package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.mapper.ap.AppConfigMapper;
import com.coohua.ap.support.core.container.AppBuilder;
import com.coohua.ap.support.core.domain.App;
import com.coohua.user.event.api.dto.ProductEntity;
import com.coohua.user.event.api.remote.rpc.AdPosRpc;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/10
 */
@Slf4j
@Service
public class BasicAppService {

    @MotanReferer(basicReferer = "user-eventBasicRefererConfigBean", application = "user-event",version = "2.0.0")
    private AdPosRpc adPosRpc;

    @Resource
    private AppConfigMapper appConfigMapper;
    @Value("${env}")
    private String env;

    public List<App> queryBasicList(){
        return appConfigMapper.queryAppList();
    }

    public App queryAppByProductCn(String productCn){
        return appConfigMapper.queryAppByProductCn(productCn);
    }

    public App queryAppByProductEn(String productEn){
        return appConfigMapper.queryAppByProductEn(productEn);
    }

    @PostConstruct
    public void refreshApp(){
        log.info("开始更新 APP...");
        List<App> appList = appConfigMapper.queryAppList();
        AppBuilder.refreshBasicApp(appList);
        if ("fat".equals(env)){
            log.info(" 测试 不同步");
            return;
        }
        List<ProductEntity> productEntityList = adPosRpc.queryProductDict();
        // 同步到表，若变更一起同步，关注 product 和 appId 的关联关系
        Map<Integer,App> appMap = appList.stream().collect(Collectors.toMap(App::getAppId, r->r,(r1, r2)->r1));

        for (ProductEntity productEntity : productEntityList){
            App app = appMap.get(productEntity.getId());
            if (app == null){
                App record = new App();
                record.setAppId(productEntity.getId());
                record.setProduct(productEntity.getProduct());
                record.setProductName(productEntity.getProductName());
                record.setProductGroup(productEntity.getProductGroup());
                int count = appConfigMapper.addNewApp(record);
                if (count > 0){
                    log.info("==> 新增 App:{}", JSON.toJSONString(record));
                }
            }else {
                if (!app.getProduct().equals(productEntity.getProduct())){
                    app.setAppId(productEntity.getId());
                    app.setProduct(productEntity.getProduct());
                    int count = appConfigMapper.updateApp(app);
                    if (count > 0){
                        log.info("==> 更新 App:{}", JSON.toJSONString(app));
                    }
                }
                if (app.getProductGroup() == null || !app.getProductGroup().equals(productEntity.getProductGroup())) {
                    app.setProductGroup(productEntity.getProductGroup());
                    app.setAppId(productEntity.getId());
                    int count = appConfigMapper.updateAppProductGroup(app);
                    if (count > 0){
                        log.info("==> 更新 App:{}", JSON.toJSONString(app));
                    }
                }
            }
        }
        log.info("结束更新 APP...");
    }
}
