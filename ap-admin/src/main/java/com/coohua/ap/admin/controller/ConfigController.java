package com.coohua.ap.admin.controller;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.controller.vo.BaseResponse;
import com.coohua.ap.admin.controller.vo.BrandTypeVO;
import com.coohua.ap.admin.controller.vo.ConfigBaseVO;
import com.coohua.ap.admin.controller.vo.ConfigVO;
import com.coohua.ap.admin.model.Config;
import com.coohua.ap.admin.service.ConfigService;
import com.coohua.ap.admin.utils.SessionUtils;
import com.coohua.ap.base.constants.ConfigEnum;
import com.coohua.ap.base.constants.ConfigType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 * Comments:
 *  少林寺驻武当山办事处大神父王喇嘛↓
 * <pre/>
 * <hr/>
 * Created by Zhimin Xu on 2017/3/7.
 */

//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                            O\ = /O
//                        ____/`---'\____
//                      .   ' \\| |// `.
//                       / \\||| : |||// \
//                     / _||||| -:- |||||- \
//                       | | \\\ - /// | |
//                     | \_| ''\---/'' | |
//                      \ .-\__ `-` ___/-. /
//                   ___`. .' /--.--\ `. . __
//                ."" '< `.___\_<|>_/___.' >'"".
//               | | : `- \`.;`\ _ /`;.`/ - ` : | |
//                 \ \ `-. \_ __\ /__ _/ .-` / /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//
//         .............................................
//                  佛祖镇楼                  BUG辟易
//          佛曰:
//                  写字楼里写字间，写字间里程序员；
//                  程序人员写程序，又拿程序换酒钱。
//                  酒醒只在网上坐，酒醉还来网下眠；
//                  酒醉酒醒日复日，网上网下年复年。
//                  但愿老死电脑间，不愿鞠躬老板前；
//                  奔驰宝马贵者趣，公交自行程序员。
//                  别人笑我忒疯癫，我笑自己命太贱；
//                  不见满街漂亮妹，哪个归得程序员？
@Slf4j
@Controller
@RequestMapping("/config")
public class ConfigController extends BaseController {

    @Autowired
    private ConfigService configService;

    @Autowired
    private ApplicationContext applicationContext;

    @RequestMapping("/getAllConfig")
    @ResponseBody
    public Object getAllConfig(HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        List<Config> configs = configService.getAllByProduct(SessionUtils.getUserProduct(request));
        List<ConfigVO> configList = new ArrayList<>();
        if (configs != null && !configs.isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            for (Config config : configs) {
                ConfigVO cf = new ConfigVO();
                cf.setId(config.getId());
                cf.setName(config.getName());
                cf.setValue(config.getValue());
                cf.setTime(sdf.format(config.getUpdateTime()));
                cf.setComment(config.getComment());
                configList.add(cf);
            }
            ret.setRet(0);
            ret.setData(configList);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/getConfigEnumList")
    @ResponseBody
    public BaseResponse getCanAddConfig(){
        BaseResponse baseResponse = new BaseResponse(0);
        List<BrandTypeVO> configList=  Arrays.stream(ConfigEnum.values())
                .filter(r -> "广告中台——各业务线配置".equals(r.getConfigPos()))
                .map(r ->{
                    BrandTypeVO brandTypeVO = new BrandTypeVO();
                    brandTypeVO.setKey(r.getConfigKey());
                    brandTypeVO.setLabel(r.getDesc());
                    return brandTypeVO;
                }).collect(Collectors.toList());
        baseResponse.setData(configList);
        return baseResponse;
    }

    @RequestMapping("/addNewConfig")
    @ResponseBody
    public BaseResponse addNewConfig(String name,String value,HttpServletRequest request){
        BaseResponse baseResponse = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(request);
        Config config = configService.getConfig(name,product);
        if (config == null) {
            log.info("当前无配置..插入");
            configService.insert(new Config() {{
                setName(name);
                setValue(value);
                setComment(ConfigEnum.getByKey(name).getDesc());
                setCreateTime(new Date());
                setUpdateTime(new Date());
                setProduct(product);
            }});
        }
        return baseResponse;
    }

    @RequestMapping("/getAdGroupConfig")
    @ResponseBody
    public Object getAdGroupConfig(@RequestParam("key") String key,HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(request);
        String value = configService.get(key,product);
        if (value != null) {
            ret.setRet(0);
            ret.setData(JSON.parseObject(value));
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/updateAdGroupConfig")
    @ResponseBody
    public Object updateAdGroupConfig(@RequestBody Map<String, String> params,HttpServletRequest request) {
        System.out.println("========= params ============");
        String key = params.get("key");
        String value = params.get("value");
        BaseResponse ret = new BaseResponse(0);
        ConfigVO configVO = new ConfigVO(key,value);
        configVO.setProduct(SessionUtils.getUserProduct(request));
        int a = configService.update(configVO).getId();
        if (a != 0) {
            ret.setRet(0);
            Config config = configService.getConfig(key,configVO.getProduct());
            ConfigVO cf = new ConfigVO();
            cf.setName(config.getName());
            cf.setValue(config.getValue());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            cf.setTime(sdf.format(config.getUpdateTime()));
            ret.setData(cf);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/getAdCreditConfig")
    @ResponseBody
    public Object getAdCreditConfig(HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(request);
        String value = configService.get("news.group.ad.credit",product);
        if (value != null) {
            ret.setRet(0);
            ret.setData(JSON.parseObject(value));
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/updateAdCreditConfig")
    @ResponseBody
    public Object updateAdCreditConfig(@RequestParam("value") String value) {
        BaseResponse ret = new BaseResponse(0);
        ConfigVO configVO = new ConfigVO("news.group.ad.credit", value);
        int a = configService.update(configVO).getId();
        if (a != 0) {
            ret.setRet(0);
        } else {
            ret.setRet(1);
        }
        return ret;
    }

    @RequestMapping("/getJsAdChannelSourceList")
    @ResponseBody
    public Object getJsAdChannelSourceList(HttpServletRequest request) {
        BaseResponse ret = new BaseResponse(0);
        int product = SessionUtils.getUserProduct(request);
        List<String> list = configService.getJsAdChannelSourceList(product);
        // js渠道源校验
        /*if (CollectionUtils.isEmpty(list)) {
            ret.setRet(1);
            return ret;
        }*/

        ret.setData(list);

        return ret;
    }



    @RequestMapping("/getCacheStatus")
    @ResponseBody
    private BaseResponse getCacheStatus(HttpServletRequest request) {
        try {
            String name = "ad.app.strategy.cache";
            int product = SessionUtils.getUserProduct(request);

            Config config = configService.getConfig(name,product);
            boolean cache = false;
            if (config != null && config.getValue() != null){
                cache = Boolean.parseBoolean(config.getValue());
            }

            return BaseResponse.build(cache);
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.SYSTEM_ERROR;
        }
    }

    @RequestMapping("/changeCacheStatus")
    @ResponseBody
    private BaseResponse updateState(Boolean cache, HttpServletRequest request) {
        try {
            String name = "ad.app.strategy.cache";
            int product = SessionUtils.getUserProduct(request);
            log.info("product:{},cache:{}", product,cache);

            Config config = configService.getConfig(name,product);
            if (config == null){
                log.info("当前无配置..插入");
                configService.insert(new Config(){{
                    setName(name);
                    setValue(cache.toString());
                    setComment("客户端策略缓存-是否开启强制刷新");
                    setCreateTime(new Date());
                    setUpdateTime(new Date());
                    setProduct(product);
                }});
            }else {
                ConfigVO configVO = new ConfigVO();
                configVO.setId(config.getId());
                configVO.setName(config.getName());
                configVO.setValue(cache.toString());
                configVO.setComment(config.getComment());
                configVO.setProduct(config.getProduct());
                configService.update(configVO);
            }
            return BaseResponse.SUCCESS;
        } catch (Exception e) {
            e.printStackTrace();
            return BaseResponse.SYSTEM_ERROR;
        }
    }
}
