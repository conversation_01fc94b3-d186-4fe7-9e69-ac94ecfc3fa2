package com.coohua.ap.admin.mapper.ap;

import com.coohua.ap.admin.model.CreateRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description create_record
 * <AUTHOR>
 * @date 2022-07-13
 */
@Mapper
@Repository
public interface CreateRecordMapper {

    /**
     * 新增
     * <AUTHOR>
     * @date 2022/07/13
     **/
    int insert(CreateRecord createRecord);

    /**
     * 刪除
     * <AUTHOR>
     * @date 2022/07/13
     **/
    int delete(int id);

    /**
     * 更新
     * <AUTHOR>
     * @date 2022/07/13
     **/
    int update(CreateRecord createRecord);

    /**
     * 查询 根据主键 id 查询
     * <AUTHOR>
     * @date 2022/07/13
     **/
    CreateRecord load(int id);

    /**
     * 查询 分页查询
     * <AUTHOR>
     * @date 2022/07/13
     **/
    List<CreateRecord> pageList(int offset, int pagesize);

    /**
     * 查询 分页查询 count
     * <AUTHOR>
     * @date 2022/07/13
     **/
    int pageListCount(int offset,int pagesize);


    @Select("select * from create_record where logday = #{today} and replace_status = 0")
    List<CreateRecord> queryTodayCreated(@Param("today") String logday);

}
