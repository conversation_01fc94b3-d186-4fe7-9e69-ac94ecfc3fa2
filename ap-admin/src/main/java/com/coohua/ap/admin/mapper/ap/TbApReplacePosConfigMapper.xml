<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApReplacePosConfigMapper">

    <resultMap id="BaseResultMap" type="com.coohua.ap.admin.model.TbApReplacePosConfig" >
        <result column="id" property="id" />
        <result column="strategy_name" property="strategyName" />
        <result column="os" property="os" />
        <result column="product" property="product" />
        <result column="ad_type_name" property="adTypeName" />
        <result column="dsp" property="dsp" />
        <result column="platform_config" property="platformConfig" />
        <result column="extend1" property="extend1" />
        <result column="extend2" property="extend2" />
        <result column="is_enabled" property="isEnabled" />
        <result column="is_deleted" property="isDeleted" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                strategy_name,
                os,
                product,
                ad_type_name,
                dsp,
                platform_config,
                extend1,
                extend2,
                is_enabled,
                is_deleted,
                create_time,
                update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.coohua.ap.admin.model.TbApReplacePosConfig">
        INSERT INTO tb_ap_replace_pos_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != strategyName">
                strategy_name,
            </if>
            <if test="null != os">
                os,
            </if>
            <if test="null != product">
                product,
            </if>
            <if test="null != adTypeName">
                ad_type_name,
            </if>
            <if test="null != dsp">
                dsp,
            </if>
            <if test="null != platformConfig">
                platform_config,
            </if>
            <if test="null != extend1">
                extend1,
            </if>
            <if test="null != extend2">
                extend2,
            </if>
            <if test="null != isEnabled">
                is_enabled,
            </if>
            <if test="null != isDeleted">
                is_deleted,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != strategyName">
                #{strategyName},
            </if>
            <if test="null != os">
                #{os},
            </if>
            <if test="null != product">
                #{product},
            </if>
            <if test="null != adTypeName">
                #{adTypeName},
            </if>
            <if test="null != dsp">
                #{dsp},
            </if>
            <if test="null != platformConfig">
                #{platformConfig},
            </if>
            <if test="null != extend1">
                #{extend1},
            </if>
            <if test="null != extend2">
                #{extend2},
            </if>
            <if test="null != isEnabled">
                #{isEnabled},
            </if>
            <if test="null != isDeleted">
                #{isDeleted},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime}
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.coohua.ap.admin.model.TbApReplacePosConfig">
        UPDATE tb_ap_replace_pos_config
        <set>
            <if test="null != strategyName">strategy_name = #{strategyName},</if>
            <if test="null != os">os = #{os},</if>
            <if test="null != product">product = #{product},</if>
            <if test="null != adTypeName">ad_type_name = #{adTypeName},</if>
            <if test="null != dsp">dsp = #{dsp},</if>
            <if test="null != platformConfig">platform_config = #{platformConfig},</if>
            <if test="null != extend1">extend1 = #{extend1},</if>
            <if test="null != extend2">extend2 = #{extend2},</if>
            <if test="null != isEnabled">is_enabled = #{isEnabled},</if>
            <if test="null != isDeleted">is_deleted = #{isDeleted}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_replace_pos_config
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM tb_ap_replace_pos_config
        where is_deleted = 0
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tb_ap_replace_pos_config
    </select>

</mapper>