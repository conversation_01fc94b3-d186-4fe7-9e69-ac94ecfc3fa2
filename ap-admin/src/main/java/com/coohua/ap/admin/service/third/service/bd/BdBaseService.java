package com.coohua.ap.admin.service.third.service.bd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.admin.service.third.dto.bd.req.BdAdPosCreateRequest;
import com.coohua.ap.admin.service.third.dto.bd.req.BdAdQueryRequest;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdAdQueryResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.BdResponse;
import com.coohua.ap.admin.service.third.dto.bd.rsp.CratePosResponse;
import com.coohua.ap.admin.utils.Env;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/5/26
 * @link: https://baidu-ssp.gz.bcebos.com/mssp/api/%E7%99%BE%E5%BA%A6%E8%81%94%E7%9B%9F-%E5%AA%92%E4%BD%93%E7%AE%A1%E7%90%86API-%E6%8E%A5%E5%85%A5%E6%96%87%E6%A1%A3-v1.0.pdf
 */
@Slf4j
@Service
public class BdBaseService {

    @Autowired
    private Env env;

    private final static String TEST_URL = "https://ubapidev.baidu.com";

    private final static String URL = "https://ubapi.baidu.com";

    private String invokeUrl(){
        return env.isTest() ? TEST_URL:URL;
    }

    public BdResponse<CratePosResponse> createPos(BdAdPosCreateRequest request,String accessKey,String privateKey){
        UbapiClient ubapiClient = new UbapiClient(invokeUrl(),accessKey,privateKey);
        return ubapiClient.post("/ssp/1/sspservice/appadpos/app/adpos/create",
                JSON.toJSONBytes(request),
                new TypeReference<BdResponse<CratePosResponse>>(){});
    }

    public BdResponse<BdAdQueryResponse> queryPosList(BdAdQueryRequest request, String accessKey, String privateKey){
        UbapiClient ubapiClient = new UbapiClient(invokeUrl(),accessKey,privateKey);
        return ubapiClient.post("/ssp/1/sspservice/appadpos/app/adpos/page-query",
                JSON.toJSONBytes(request),
                new TypeReference<BdResponse<BdAdQueryResponse>>(){});
    }

    public BdResponse<CratePosResponse> updatePos(CratePosResponse request, String accessKey, String privateKey){
        UbapiClient ubapiClient = new UbapiClient(invokeUrl(),accessKey,privateKey);
        return ubapiClient.post("/ssp/1/sspservice/appadpos/app/adpos/update",
                JSON.toJSONBytes(request),
                new TypeReference<BdResponse<CratePosResponse>>(){});
    }

}
