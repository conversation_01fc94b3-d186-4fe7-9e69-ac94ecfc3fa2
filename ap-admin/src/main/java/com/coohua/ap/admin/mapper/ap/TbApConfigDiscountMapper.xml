<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.ap.admin.mapper.ap.TbApConfigDiscountMapper">
    <insert id="insert" parameterType="com.coohua.ap.admin.model.TbApConfigDiscount" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_ap_config_discount (
        <if test="discountName != null">discount_name,</if>
        <if test="bidDiscountValue != null">bid_discount_value,</if>
        <if test="waterFallDiscountValue != null">water_fall_discount_value,</if>
        <if test="os != null">os,</if>
        <if test="platformCode != null">platform_code,</if>
        <if test="product != null">product,</if>
        <if test="adPos != null">ad_pos,</if>
        <if test="biddingType != null">bidding_type,</if>
        <if test="appVersionOrientation != null">app_version_orientation,</if>
        <if test="sdkVersionOrientation != null">sdk_version_orientation,</if>
        <if test="channelIdOrientation != null">channel_id_orientation,</if>
        <if test="skipChannelOrientation != null">skip_channel_orientation,</if>
        <if test="abTestOrientation != null">ab_test_orientation,</if>
        <if test="userSourceOrientation != null">user_source_orientation,</if>
        <if test="priority != null">priority,</if>
        <if test="isEnabled != null">is_enabled,</if>
        <if test="isDeleted != null">is_deleted,</if>
        <if test="createTime != null">create_time,</if>
        <if test="updateTime != null">update_time</if>
        ) VALUES (
        <if test="discountName != null">#{discountName},</if>
        <if test="bidDiscountValue != null">#{bidDiscountValue},</if>
        <if test="waterFallDiscountValue != null">#{waterFallDiscountValue},</if>
        <if test="os != null">#{os},</if>
        <if test="platformCode != null">#{platformCode},</if>
        <if test="product != null">#{product},</if>
        <if test="adPos != null">#{adPos},</if>
        <if test="biddingType != null">#{biddingType},</if>
        <if test="appVersionOrientation != null">#{appVersionOrientation},</if>
        <if test="sdkVersionOrientation != null">#{sdkVersionOrientation},</if>
        <if test="channelIdOrientation != null">#{channelIdOrientation},</if>
        <if test="skipChannelOrientation != null">#{skipChannelOrientation},</if>
        <if test="abTestOrientation != null">#{abTestOrientation},</if>
        <if test="userSourceOrientation != null">#{userSourceOrientation},</if>
        <if test="priority != null">#{priority},</if>
        <if test="isEnabled != null">#{isEnabled},</if>
        <if test="isDeleted != null">#{isDeleted},</if>
        <if test="createTime != null">#{createTime},</if>
        <if test="updateTime != null">#{updateTime}</if>
        )
    </insert>
    <update id="updateById" parameterType="com.coohua.ap.admin.model.TbApConfigDiscount">
        UPDATE tb_ap_config_discount
        <set>
            <if test="discountName != null">discount_name = #{discountName},</if>
            <if test="bidDiscountValue != null">bid_discount_value = #{bidDiscountValue},</if>
            <if test="waterFallDiscountValue != null">water_fall_discount_value = #{waterFallDiscountValue},</if>
            <if test="os != null">os = #{os},</if>
            <if test="platformCode != null">platform_code = #{platformCode},</if>
            <if test="product != null">product = #{product},</if>
            <if test="adPos != null">ad_pos = #{adPos},</if>
            <if test="biddingType != null">bidding_type = #{biddingType},</if>
            <if test="appVersionOrientation != null">app_version_orientation = #{appVersionOrientation},</if>
            <if test="sdkVersionOrientation != null">sdk_version_orientation = #{sdkVersionOrientation},</if>
            <if test="channelIdOrientation != null">channel_id_orientation = #{channelIdOrientation},</if>
            <if test="skipChannelOrientation != null">skip_channel_orientation = #{skipChannelOrientation},</if>
            <if test="abTestOrientation != null">ab_test_orientation = #{abTestOrientation},</if>
            <if test="userSourceOrientation != null">user_source_orientation = #{userSourceOrientation},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="isEnabled != null">is_enabled = #{isEnabled},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="updateTime != null">update_time = #{updateTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="queryById" resultType="com.coohua.ap.admin.model.TbApConfigDiscount"
            parameterType="java.lang.Long">
        SELECT * FROM tb_ap_config_discount WHERE id = #{id}
    </select>
    <select id="queryList" resultType="com.coohua.ap.admin.model.TbApConfigDiscount"
            parameterType="com.coohua.ap.base.vo.ApConfigDiscountVo">
        SELECT
        id,
        discount_name,
        bid_discount_value,
        water_fall_discount_value,
        os,
        platform_code,
        product,
        ad_pos,
        bidding_type,
        app_version_orientation,
        sdk_version_orientation,
        channel_id_orientation,
        skip_channel_orientation,
        ab_test_orientation,
        user_source_orientation,
        priority,
        is_enabled,
        is_deleted,
        create_time,
        update_time
        FROM tb_ap_config_discount
        <where>
            and is_deleted = 0
            <if test="discountName != null and discountName != ''">
                AND discount_name LIKE CONCAT('%', #{discountName}, '%')
            </if>
            <if test="isEnabled != null">
                AND is_enabled = #{isEnabled}
            </if>
        </where>
        ORDER BY update_time DESC
        <if test="from != null and pageSize != null">
            LIMIT #{from}, #{pageSize}
        </if>
    </select>
    <select id="queryCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM tb_ap_config_discount
        <where>
            and is_deleted = 0
            <if test="discountName != null and discountName != ''">
                AND discount_name LIKE CONCAT('%', #{discountName}, '%')
            </if>
            <if test="isEnabled != null">
                AND is_enabled = #{isEnabled}
            </if>
        </where>
    </select>
</mapper>