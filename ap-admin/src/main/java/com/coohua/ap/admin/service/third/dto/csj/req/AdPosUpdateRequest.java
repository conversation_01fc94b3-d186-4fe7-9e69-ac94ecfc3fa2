package com.coohua.ap.admin.service.third.dto.csj.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/6
 */
@Data
public class AdPosUpdateRequest {
    private Integer user_id;
    private Integer role_id;
    private Integer timestamp;
    private Integer nonce;
    private String sign;
    private String version;
    private Integer app_id;
    private Integer ad_slot_type;
    private String ad_slot_name;
    private Integer ad_slot_id;
    private Integer mask_rule_id;
    private String cpm;

    private Integer render_type;
    private Integer slide_banner;
    private Integer width;
    private Integer height;
    private Integer[] ad_categroies;


    private Integer reward_is_callback;
    private String reward_callback_url;
    private Integer update_security_key;

    private Integer status;

}
