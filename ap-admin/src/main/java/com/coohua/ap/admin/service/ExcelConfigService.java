package com.coohua.ap.admin.service;

import com.alibaba.fastjson.JSON;
import com.coohua.ap.admin.constants.CommonField;
import com.coohua.ap.admin.controller.vo.*;
import com.coohua.ap.admin.mapper.ap.AdInfoMapper;
import com.coohua.ap.admin.mapper.ap.AdPosMapper;
import com.coohua.ap.admin.mapper.ap.ConfigMapper;
import com.coohua.ap.admin.model.AdPlanEntity;
import com.coohua.ap.admin.model.AdPosModel;
import com.coohua.ap.admin.model.Config;
import com.coohua.ap.admin.utils.StringUtils;
import com.coohua.ap.base.constants.AdType;
import com.coohua.ap.base.constants.AdTypeSub;
import com.coohua.ap.base.constants.DspEnum;
import com.coohua.ap.base.utils.DateUtils;
import com.coohua.ap.base.vo.WebMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/10/27
 */
@Slf4j
@Service
public class ExcelConfigService {

    @Autowired
    private AdAdminService adAdminService;
    @Resource(name = "configTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Resource
    private AdPosMapper adPosMapper;
    @Resource
    private AdInfoMapper adInfoMapper;
    @Resource
    private ConfigMapper configMapper;

    private final List<Integer> filterVideoList = new ArrayList<Integer>(){{
        add(AdType.GDT_VIDEO.type);
        add(AdType.TOUTIAO_VIDEO_TEMPLATE.type);
        add(AdType.KS_VIDEO.type);
        for (int i = 1; i<= 15; i++){
            String ks = String.valueOf(AdType.KS_VIDEO.type) + i;
            String tt = String.valueOf(AdType.TOUTIAO_VIDEO_TEMPLATE.type) + i;
            String gdt = String.valueOf(AdType.GDT_VIDEO.type) + i;
            add(Integer.valueOf(ks));
            add(Integer.valueOf(tt));
            add(Integer.valueOf(gdt));
        }
    }} ;


    public WebMessage initAdPlanConfig(MultipartFile file, String fileType, int product){
        try {
            // 读取excel
            List<ExcelFileAdConfVo> excelFileAdConfVoList = convertExcel(file.getInputStream(),fileType);

            log.info(JSON.toJSONString(excelFileAdConfVoList));

            if (excelFileAdConfVoList.size() == 0){
                return WebMessage.build(WebMessage.CODE_FAILED,"Excel解析为空，请检查Excel内容");
            }
            List<AdPosModel> adPosModelList = adPosMapper.queryAllByProduct(product);
            Map<String,AdPosModel> adPosModelMap = adPosModelList.stream().collect(Collectors.toMap(AdPosModel::getName,k->k));
            List<AdPlanEntity> adPlanEntityList = adInfoMapper.selectAllPlans(product,-1,-1,DefaultConfigService.EMPTY);
            Map<String,AdPlanEntity> adPlanEntityMap = adPlanEntityList.stream().collect(Collectors.toMap(AdPlanEntity::getName,k->k));
            for (ExcelFileAdConfVo vo : excelFileAdConfVoList){
                if (AdType.DEFAULT.desc().equals(AdTypeSub.getByCnDesc(vo.getAdTypeName()).getTypeDesc())){
                    return WebMessage.build(WebMessage.CODE_FAILED,"Excel字段 广告类型 [" + vo.getAdTypeName() + "] 填写异常！请检查");
                }
                for (String posName : vo.getAdPosName()){
                    if (adPosModelMap.get(posName) == null){
                        return WebMessage.build(WebMessage.CODE_FAILED,"Excel字段 投放位置 ["+posName+"] 填写异常！请检查");
                    }
                }
                if (adPlanEntityMap.get(vo.getAdPlanName()) == null){
                    return WebMessage.build(WebMessage.CODE_FAILED,"Excel字段 广告计划 [ " +vo.getAdPlanName()+ "] 填写异常！请检查");
                }

                if(!StringUtils.isEmpty(vo.getEcpm())){
                    try {
                        Integer.valueOf(vo.getEcpm());
                    }catch (Exception e){
                        return WebMessage.build(WebMessage.CODE_FAILED,"Excel字段 [ECPM] 填写异常！请检查");
                    }
                }

                if(!StringUtils.isEmpty(vo.getAbPoint())){
                    try {
                        Integer.valueOf(vo.getAbPoint());
                    }catch (Exception e){
                        return WebMessage.build(WebMessage.CODE_FAILED,"Excel字段 [AB定向] 填写异常！请检查");
                    }
                }
            }

            CompletableFuture.runAsync(() -> {
                List<Ad> adList = convertToAd(excelFileAdConfVoList,product);
                List<Ad> afterInsertAdList = adList.stream().map(adAdminService::insertAd).collect(Collectors.toList());
                // Android 须载入缓存广告
                Map<String,AdCacheConfVo> adCacheConfVoMap = queryCacheMapAndroid(afterInsertAdList);
                // Ios
                Map<String,AdCacheConfVo> adCacheConfVoMapIos = queryCacheMapIos(afterInsertAdList);
                // 筛选广告里的静态图
                Map<String,AdCacheConfVo> adCacheConfStatic = queryCacheMapStatic(afterInsertAdList);
                // 筛选广告里的Banner
                Map<String,AdCacheConfVo> adCacheConfBanner = queryCacheMapBanner(afterInsertAdList);
                // 更新缓存
                updateConfigMapper(adCacheConfVoMap,"ad.third.cache",product);
                updateConfigMapper(adCacheConfVoMapIos,"ad.third.cache.ios",product);
                updateConfigMapper(adCacheConfStatic,"ad.third.static.cache",product);
                updateConfigMapper(adCacheConfBanner,"ad.third.banner.cache",product);
            }, executor);

            return WebMessage.build(WebMessage.CODE_OK,"配置加载成功！");
        } catch (Exception e) {
            log.error("excel convert error! e = ",e);
            return WebMessage.build(WebMessage.CODE_FAILED,"Excel解析出错！请检查Excel内容");
        }
    }

    private Map<String,AdCacheConfVo> queryCacheMapStatic(List<Ad> afterInsertAdList){
        List<Integer> filterAdType = new ArrayList<>();
        filterAdType.add(AdType.TOUTIAO_STATIC_TEMPLATE.type);
        filterAdType.add(AdType.GDT_TEMPLATE.type);
        filterAdType.add(AdType.JU_HE_CHAPING.type);
        filterAdType.add(AdType.CSJ_CHAPING.type);
        filterAdType.add(AdType.GDT_CHAPING.type);
        for (int i = 1;i <=4; i++){
            String tt = String.valueOf(AdType.TOUTIAO_STATIC_TEMPLATE.type) + i;
            String gdt = String.valueOf(AdType.GDT_TEMPLATE.type) + i;
            filterAdType.add(Integer.valueOf(tt));
            filterAdType.add(Integer.valueOf(gdt));
        }

        for (int i = 1;i <=8; i++){
            String tt = String.valueOf(AdType.CSJ_CHAPING.type) + i;
            String gdt = String.valueOf(AdType.GDT_CHAPING.type) + i;
            filterAdType.add(Integer.valueOf(tt));
            filterAdType.add(Integer.valueOf(gdt));
        }
        filterAdType.add(AdType.TT_JU_HE_INSERT.type);
        return afterInsertAdList.stream()
                .filter(ad -> ad.isCache() && filterAdType.contains(ad.getType()))
                .collect(Collectors.toMap(ad -> String.valueOf(ad.getType()),ad -> {
                    AdCacheConfVo vo = new AdCacheConfVo();
                    vo.setAdId(ad.getId());
                    vo.setPosId(ad.getPosIdTemp());
                    return vo;
                },(k1,k2) -> k1));
    }

    private Map<String,AdCacheConfVo> queryCacheMapBanner(List<Ad> afterInsertAdList){
        List<Integer> filterList = new ArrayList<>();
        filterList.add(AdType.TT_BANNER.type);
        filterList.add(AdType.GDT_BANNER.type);
        for (int i = 1;i <=4; i++){
            String tt = String.valueOf(AdType.TT_BANNER.type) + i;
            String gdt = String.valueOf(AdType.GDT_BANNER.type) + i;
            filterList.add(Integer.valueOf(tt));
            filterList.add(Integer.valueOf(gdt));
        }

        return afterInsertAdList.stream()
                .filter(ad -> ad.isCache() && filterList.contains(ad.getType()))
                .collect(Collectors.toMap(ad -> String.valueOf(ad.getType()),ad -> {
                    AdCacheConfVo vo = new AdCacheConfVo();
                    vo.setAdId(ad.getId());
                    vo.setPosId(ad.getPosIdTemp());
                    return vo;
                },(k1,k2) -> k1));
    }

    private Map<String,AdCacheConfVo> queryCacheMapIos(List<Ad> afterInsertAdList){
        return afterInsertAdList.stream()
                .filter(ad -> ad.isCache() && !ad.getOrientation().isNoIos() && filterVideoList.contains(ad.getType()))
                .collect(Collectors.toMap(ad -> String.valueOf(ad.getType()),ad -> {
                    AdCacheConfVo vo = new AdCacheConfVo();
                    vo.setAdId(ad.getId());
                    vo.setPosId(ad.getPosIdTemp());
                    return vo;
                },(k1,k2) -> k1));
    }

    private Map<String,AdCacheConfVo> queryCacheMapAndroid(List<Ad> afterInsertAdList){
        return afterInsertAdList.stream()
                .filter(ad -> ad.isCache() && ad.getOrientation().isNoIos() && filterVideoList.contains(ad.getType()))
                .collect(Collectors.toMap(ad -> String.valueOf(ad.getType()),ad -> {
                    AdCacheConfVo vo = new AdCacheConfVo();
                    vo.setAdId(ad.getId());
                    vo.setPosId(ad.getPosIdTemp());
                    return vo;
                },(k1,k2) -> k1));
    }

    private void updateConfigMapper(Map<String,AdCacheConfVo> mapVo,String baseKey,Integer product){
        Config configIos = configMapper.selectOne(baseKey,product);
        if (configIos == null || mapVo.keySet().size() == 0){
            log.info("获取产品{}的缓存默认值异常... 不处理",product);
        }else {
            String valueIos = configIos.getValue();
            log.info("获取到配置为：{}",valueIos);
            Map objectMap = JSON.parseObject(valueIos, Map.class);
            Object key = objectMap.keySet().iterator().next();
            Map valueIndex = (Map) objectMap.get(key);
            if (valueIndex == null){
                valueIndex = new HashMap();
            }
            valueIndex.putAll(mapVo);
            objectMap.put(key,valueIndex);
            valueIos = JSON.toJSONString(objectMap);
            log.info("配置更新为：{}",valueIos);
            configIos.setValue(valueIos);
            configMapper.updateOne(configIos);
        }
    }

    public List<Ad> convertToAd(List<ExcelFileAdConfVo> excelFileAdConfVoList,int product){
        return excelFileAdConfVoList.stream().map(excelFileAdConfVo -> convertToAd(excelFileAdConfVo,product)).collect(Collectors.toList());
    }

    private Ad convertToAd(ExcelFileAdConfVo excelFileAdConfVo,int product){
        Ad ad = new Ad();
        // 广告位获取产品相关信息..
        // 获取广告位
        List<AdPosModel> adPosModelList = adPosMapper.queryAllByProduct(product);
        Map<String,AdPosModel> adPosModelMap = adPosModelList.stream().collect(Collectors.toMap(AdPosModel::getName,k->k));
        List<AdPlanEntity> adPlanEntityList = adInfoMapper.selectAllPlans(product,-1,-1,DefaultConfigService.EMPTY);
        Map<String,AdPlanEntity> adPlanEntityMap = adPlanEntityList.stream().collect(Collectors.toMap(AdPlanEntity::getName,k->k));

        List<Integer> adPosIdList = excelFileAdConfVo.getAdPosName().stream()
                .map(posName -> {
                    AdPosModel adPosModel = adPosModelMap.get(posName);
                    if (adPosModel != null){
                        return adPosModel.getId();
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());
        Date now = new Date();
        // 补充计划相关
        AdPlan adPlan = new AdPlan();
        AdPlanEntity adPlanEntity = adPlanEntityMap.get(excelFileAdConfVo.getAdPlanName());
        if (adPlanEntity != null){
            adPlan.setId(adPlanEntity.getId().intValue());
            adPlan.setName(adPlanEntity.getName());
            adPlan.setProductName(adPlanEntity.getProductName());
            adPlan.setOpen(adPlanEntity.getState() == 1);
            adPlan.setSalesman(adPlanEntity.getSalesman());
        }
        ad.setPlan(adPlan);
        // 补充限制相关信息
        AdOrientation adOrientation = new AdOrientation();
        if ("android".equals(excelFileAdConfVo.getOs())) {
            adOrientation.setNoAndroid(false);
            adOrientation.setNoIos(true);
        }else if ("ios".equals(excelFileAdConfVo.getOs())){
            adOrientation.setNoAndroid(true);
            adOrientation.setNoIos(false);
        }

        adOrientation.setNoRegion(false);
        adOrientation.setNoRegistLongerLimit(true);
        adOrientation.setNoRegistTimeLimit(true);
        adOrientation.setNoIncomeLimit(true);
        adOrientation.setBrandLimit(false);
        adOrientation.setBrandLimitList(new ArrayList<>());
        adOrientation.setFilterRegion(0);
        adOrientation.setLockArea(0);
        if (excelFileAdConfVo.getName().toLowerCase().contains("bidding")){
            adOrientation.setLockArea(2);

            DspEnum dspEnum = DspEnum.fromCode(excelFileAdConfVo.getName());
            if (dspEnum != null) {
                adOrientation.setDsp(dspEnum.getCode());
            }
        }
        if(!StringUtils.isEmpty(excelFileAdConfVo.getAbPoint())) {
            adOrientation.setAbPoint(excelFileAdConfVo.getAbPoint());
        }
        ad.setOrientation(adOrientation);

        // 创建默认的投放时间
        List<TimeBucketVO> timeBucketVOList = new ArrayList<>();
        Map<Integer,String> weekMap = new HashMap<Integer,String>(){{
            put(0, "星期一");
            put(1, "星期二");
            put(2, "星期三");
            put(3, "星期四");
            put(4, "星期五");
            put(5, "星期六");
            put(6, "星期日");

        }};
        for (int week =0 ;week < 7; week ++) {
            TimeBucketVO vo = new TimeBucketVO();
            List<Bucket> defaultBucket = new ArrayList<>();
            for (int i = 0; i < 48; i++) {
                Bucket bucket = new Bucket();
                bucket.setShow(true);
                bucket.setTime(i);
                defaultBucket.add(bucket);
            }

            vo.setWeek(week);
            vo.setName(weekMap.get(week));
            vo.setTime(defaultBucket);
            timeBucketVOList.add(vo);
        }
        ad.setTimeBucketVOList(timeBucketVOList);
        AdBudget adBudget = new AdBudget();
        adBudget.setBudget(1000);
        adBudget.setEnableBudget(false);
        adBudget.setNoIdleTime(false);
        adBudget.setNoPerMaxTimes(false);
        adBudget.setNoExposureIdleTime(false);
        adBudget.setExposureIdleTime(-1);
        adBudget.setPerMaxTimes(-1);
        adBudget.setIdleTime(-1);
        adBudget.setExposureMaxTimes(-1);
        if (!StringUtils.isEmpty(excelFileAdConfVo.getEcpm())) {
            adBudget.setEcpm(Integer.valueOf(excelFileAdConfVo.getEcpm()));
        }
        ad.setBudget(adBudget);

        // 补充扩展信息
        AdExt adExt = new AdExt();
        adExt.setSelfEndCard(0);
        adExt.setAndroidPosId("");
        adExt.setAndroidBakPosId("");
        adExt.setIosBakPosId("");
        adExt.setIosPosId("");
        adExt.setAndroidAppId("");
        if ("android".equals(excelFileAdConfVo.getOs())){
            adExt.setAndroidAppId(excelFileAdConfVo.getAppId());
            adExt.setAndroidPosId(excelFileAdConfVo.getAdThirdPosId());
            adExt.setAndroidBakPosId(excelFileAdConfVo.getAdThirdPosId());
        }else if ("ios".equals(excelFileAdConfVo.getOs())){
            adExt.setIosAppId(excelFileAdConfVo.getAppId());
            adExt.setIosPosId(excelFileAdConfVo.getAdThirdPosId());
            adExt.setIosBakPosId(excelFileAdConfVo.getAdThirdPosId());
        }
        adExt.setPreType(0);
        adExt.setDownloadDrive(false);
        adExt.setExtendType(0);
        adExt.setTemplate(false);
        adExt.setTemplateImgSize(0);
        adExt.setMintegralType(0);
        adExt.setStyle(101);
        adExt.setTitle(DefaultConfigService.EMPTY);
        adExt.setContent(DefaultConfigService.EMPTY);
        adExt.setClkUrl(DefaultConfigService.EMPTY);
        adExt.setImgUrls(new ArrayList<>());
        adExt.setImpTrackUrl(DefaultConfigService.EMPTY);
        adExt.setClkTrackUrl(DefaultConfigService.EMPTY);
        adExt.setAppPkgName(DefaultConfigService.EMPTY);
        adExt.setDownloadUrl(DefaultConfigService.EMPTY);
        adExt.setAdm(DefaultConfigService.EMPTY);
        adExt.setVideoStartTrackUrl(DefaultConfigService.EMPTY);
        adExt.setVideoFinishTrackUrl(DefaultConfigService.EMPTY);
        adExt.setVideoPauseTrackUrl(DefaultConfigService.EMPTY);
        adExt.setItid(DefaultConfigService.EMPTY);
        adExt.setWidth(0);
        adExt.setHeight(0);
        adExt.setEndCardReward(0);
        adExt.setDuration(0);
        adExt.setCountDownGift(0);
        adExt.setShareImgUrl(DefaultConfigService.EMPTY);
        adExt.setShareType(1);
        adExt.setStyleType(1);
        adExt.setWidth(0);
        adExt.setNoAppId(true);
        adExt.setIndustry(0);
        adExt.setSharePrice(0);
        adExt.setDetailPageShare(false);
        adExt.setNewuserShare(false);
        adExt.setReplaceShareDomain(DefaultConfigService.EMPTY);
        adExt.setWord(DefaultConfigService.EMPTY);
        adExt.setPr(0);
        adExt.setTwiceJumpTimes(2);
        adExt.setUrl(DefaultConfigService.EMPTY);
        adExt.setSourceFrom(0);
        adExt.setFetchStrategy(1);
        adExt.setHotWordAdUrl(DefaultConfigService.EMPTY);
        adExt.setFetchUrl(DefaultConfigService.EMPTY);
        adExt.setDeeplinkUrl(DefaultConfigService.EMPTY);
        adExt.setDeeplinkOpenType(0);
        adExt.setDeeplinkAppType(1);
        adExt.setDeeplinkPkgName(DefaultConfigService.EMPTY);
        adExt.setMiniProgramName(DefaultConfigService.EMPTY);
        adExt.setMiniIcon(DefaultConfigService.EMPTY);
        adExt.setMiniShareTitle(DefaultConfigService.EMPTY);
        adExt.setMiniShareImage(DefaultConfigService.EMPTY);
        adExt.setMiniMiddleBtnText(DefaultConfigService.EMPTY);
        adExt.setMiniMiddleImage(DefaultConfigService.EMPTY);
        adExt.setAdAppId(DefaultConfigService.EMPTY);
        adExt.setAdOriginalId(DefaultConfigService.EMPTY);
        adExt.setAdMiniPath(DefaultConfigService.EMPTY);
        adExt.setAdPackageName(DefaultConfigService.EMPTY);
        adExt.setOwnAppId(DefaultConfigService.EMPTY);
        adExt.setOwnOriginalId(DefaultConfigService.EMPTY);
        adExt.setOwnMiniPath(DefaultConfigService.EMPTY);
        adExt.setOwnMiniAppId(DefaultConfigService.EMPTY);
        adExt.setOwnPackageName(DefaultConfigService.EMPTY);
        adExt.setMiniStartWay(0);
        adExt.setOtherBrowser(0);
        adExt.setGdtUnitId(DefaultConfigService.EMPTY);
        adExt.setAdTag(DefaultConfigService.EMPTY);
        adExt.setAdTagRatio(DefaultConfigService.EMPTY);
        adExt.setShowAdLabel(false);
        adExt.setAdLabel(DefaultConfigService.EMPTY);
        adExt.setDefaultAdTwiceJump(false);
        adExt.setJsDomainName(DefaultConfigService.EMPTY);
        adExt.setJsAdPosClose(new JsAdPosConfig(){{
            setChannelId(DefaultConfigService.EMPTY);
            setChannelSource(DefaultConfigService.EMPTY);
            setJsStyle(101);
        }});
        adExt.setJsAdPosOpen(new JsAdPosConfig(){{
            setChannelId(DefaultConfigService.EMPTY);
            setChannelSource(DefaultConfigService.EMPTY);
            setJsStyle(201);
        }});
        adExt.setJsAdPosRecommend(new ArrayList<JsAdPosConfig>(){{
            add(new JsAdPosConfig(){{
                setChannelId(DefaultConfigService.EMPTY);
                setChannelSource(DefaultConfigService.EMPTY);
                setJsStyle(301);
            }});
        }});
        adExt.setUrl(DefaultConfigService.EMPTY);
        adExt.setUrl(DefaultConfigService.EMPTY);

        ad.setExt(adExt);
        // 补充广告基本信息
        ad.setName(excelFileAdConfVo.getName());
        ad.setState(DefaultConfigService.SATE_ON);
        ad.setType(AdTypeSub.getByCnDesc(excelFileAdConfVo.getAdTypeName()).type);
        ad.setNoEndDate(true);
        ad.setEndDate("");
        ad.setStartDate(DateUtils.formatDateForYMD(now));
        ad.setProduct(product);
        ad.setOpen(true);
        ad.setAdPos(adPosIdList);

        ad.setPosIdTemp(excelFileAdConfVo.getAdThirdPosId());
        ad.setCache(excelFileAdConfVo.isCache());
        return ad;
    }

    private List<ExcelFileAdConfVo>  convertExcel(InputStream inputStream, String fileType) throws IOException {
        List<ExcelFileAdConfVo> excelFileAdConfVoList = new ArrayList<>();
        Workbook workbook = null;
        if(fileType.equalsIgnoreCase(CommonField.XLS)){
            workbook = new HSSFWorkbook(inputStream);
        }else if(fileType.equalsIgnoreCase(CommonField.XLSX)){
            workbook = new XSSFWorkbook(inputStream);
        }

        if (workbook == null){
            return excelFileAdConfVoList;
        }

        Sheet sheetAt = workbook.getSheetAt(BigDecimal.ZERO.intValue());
        if(sheetAt == null){
            return excelFileAdConfVoList;
        }
        int firstRowNum = sheetAt.getFirstRowNum();
        Row firstRow = sheetAt.getRow(firstRowNum);
        if(firstRow == null){
            return excelFileAdConfVoList;
        }
        int rowStart = firstRowNum+1;
        int rowEnd = sheetAt.getPhysicalNumberOfRows();
        for(int rowNum = rowStart;rowNum<rowEnd;rowNum++){
            Row row = sheetAt.getRow(rowNum);
            if(row == null){
                return excelFileAdConfVoList;
            }
            String[] currArray = new String[10];
            for(int i = 0; i<10;i++){
                String cellValue = getCellValue(row,i);
                currArray[i] = cellValue;
            }
            // 数据转成对象
            ExcelFileAdConfVo excelFileVo = new ExcelFileAdConfVo(currArray);
            excelFileAdConfVoList.add(excelFileVo);
        }

        return excelFileAdConfVoList;
    }

    private String getCellValue(Row row, int column) {
        String val = "";
        try {
            Cell cell = row.getCell(column);
            if (cell != null) {
                cell.setCellType(Cell.CELL_TYPE_STRING);
                val = cell.getStringCellValue();
            }
        } catch (Exception e) {
            return val;
        }
        return val;
    }
}
