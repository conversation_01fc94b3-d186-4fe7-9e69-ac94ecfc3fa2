package com.coohua.ap.admin.service.third.service.oppo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtAdPosPageResponse;
import com.coohua.ap.admin.service.third.dto.gdt.rsp.GdtResponse;
import com.coohua.ap.admin.service.third.dto.oppo.req.OppoBaseReq;
import com.coohua.ap.admin.service.third.dto.oppo.req.OppoCreatePosRequest;
import com.coohua.ap.admin.service.third.dto.oppo.req.OppoQueryListRequest;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoCreatePosRsp;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoPageRsp;
import com.coohua.ap.admin.service.third.dto.oppo.rsp.OppoResponse;
import com.coohua.ap.admin.utils.third.HttpClients;
import com.coohua.ap.admin.utils.third.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.message.BasicHeader;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @link: https://toufang.shinet.cn/oppo_v13.pdf
 * @since 2023/8/25
 * oppoAPI 对接
 */
@Slf4j
@Service
public class OppoBaseService {


    @JedisClusterClientRefer(namespace = "ap-cluster")
    private JedisClusterClient apClusterRedisService;

    private static final String URL_BASE = "https://openapi.heytapmobi.com/";

    private Map<String, Object> invokeHeader(String accessToken, String clientSecret, Map<String, Object> paramMap) {
        long time = System.currentTimeMillis();
        String timeMillis = String.valueOf(time);
        Random random = new Random();
        int nonce = random.nextInt(20000);
        String sign = sign(accessToken, timeMillis, String.valueOf(nonce), clientSecret, paramMap);
        log.info("X-Api-Sign: {}", sign);
        Map<String, Object> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        headers.put("Authorization", accessToken);
        headers.put("X-Client-Send-Utc-Ms", timeMillis);
        headers.put("X-Nonce", String.valueOf(nonce));
        headers.put("X-Api-Sign", sign);
        return headers;
    }


    public OppoResponse<OppoCreatePosRsp> createPos(OppoCreatePosRequest request, OppoBaseReq req){
        try {
            String access = getAccessToken(req.getClientId(),req.getClientSecret(),req.getGrantType());
            Map<String,Object> header = invokeHeader(access,req.getClientSecret(),toMap(request));
            String result = HttpClients.fromPost(URL_BASE+"union/v1/order/create",header,toMap(request));
            return JSON.parseObject(result,new TypeReference<OppoResponse<OppoCreatePosRsp>>(){});
        }catch (Exception e){
            log.error("create oppo posEr:",e);
        }

        OppoResponse<OppoCreatePosRsp> response = new OppoResponse<>();
        response.setCode(-1);
        response.setMsg("创建广告位失败..");
        return response;
    }

    public OppoResponse<OppoPageRsp> queryPosList(OppoQueryListRequest request, OppoBaseReq req){
        String access = getAccessToken(req.getClientId(),req.getClientSecret(),req.getGrantType());
        Map<String,Object> header = invokeHeader(access,req.getClientSecret(),toMap(request));
        String result = HttpClients.fromPost(URL_BASE+"union/v1/order/list",header,toMap(request));
        return JSON.parseObject(result,new TypeReference<OppoResponse<OppoPageRsp>>(){});
    }


    private String getAccessToken(String clientId, String clientSecret,String grantType){
        String url = URL_BASE + "oauth2/v1/token";
        // 实现调用获取 AccessToken 接口的功能，在有效期内可以不重复获取。
        // 该 AccessToken 的有效期由返回参数 expires_in 指定，如果 AccessToken 失效，
        String result = apClusterRedisService.get("oppo:token:"+clientId);
        if (Strings.noEmpty(result)){
            return result;
        }
        String token = getToken(url,clientId,clientSecret,grantType);
        apClusterRedisService.set("oppo:token:"+clientId,token);
        apClusterRedisService.expire("oppo:token:"+clientId,60 * 5);
        return token;
    }


    private static String getToken(String url,String clientId,String clientSecret,String grantType ) {
        // 获取失败重试
        String doGetUrl = url + "?client_id=" + clientId + "&client_secret=" + clientSecret + "&grant_type=" + grantType;
        String accessToken = null;
        try {
            String body = HttpClients.GET(doGetUrl);
            JSONObject jsonObject = JSONObject.parseObject(body).getJSONObject("data");
            accessToken = jsonObject.getString("access_token");
        }catch (Exception e){
            try {
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException interruptedException) {
                interruptedException.printStackTrace();
            }
            String body = HttpClients.GET(doGetUrl);
            JSONObject jsonObject = JSONObject.parseObject(body).getJSONObject("data");
            accessToken = jsonObject.getString("access_token");
        }
        return accessToken;
    }

    private static String sign(String accessToken, String timestamp, String nonce, String secret, Map<String, Object> paramsMap) {
        List<String> keysList = new ArrayList<>(paramsMap.keySet());
        Collections.sort(keysList);
        List<String> paramList = new ArrayList<>();
        for (String key : keysList) {
            Object object = paramsMap.get(key);
            if (object == null) {
                continue;
            }
            String value = key + "=" + object;
            paramList.add(value);
        }
        String s = "access_token=" + accessToken + "&timestamp=" + timestamp + "&nonce=" + nonce;
        if (!paramList.isEmpty()) {
            String signStr = String.join("&", paramList);
            s = s + "&" + signStr;
        }
        return hmacSHA256(s, secret);
    }

    /**
     * HMAC_SHA256 计算签名
     *
     * @param data 需要加密的参数
     * @param key  签名密钥
     * @return String 返回加密后字符串
     */
    public static String hmacSHA256(String data, String key) {
        try {
            byte[] secretByte = key.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec signingKey = new SecretKeySpec(secretByte,
                    "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(signingKey);
            byte[] dataByte = data.getBytes(StandardCharsets.UTF_8);
            byte[] by = mac.doFinal(dataByte);
            return byteArr2HexStr(by);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 字节数组转换为十六进制
     *
     * @param bytes
     * @return String
     */
    private static String byteArr2HexStr(byte[] bytes) {
        int length = bytes.length;
        // 每个 byte 用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuilder sb = new StringBuilder(length * 2);
        for (int i = 0; i < length; i++) {
            // 将得到的字节转 16 进制
            String strHex = Integer.toHexString(bytes[i] & 0xFF);
            // 每个字节由两个字符表示，位数不够，高位补 0
            sb.append((strHex.length() == 1) ? "0" + strHex : strHex);
        }
        return sb.toString();
    }

    private static Map<String, Object> toMap(Object request) {
        return JSONObject.parseObject(JSON.toJSONString(request));
    }

}
