package com.coohua.ap.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.ap.admin.constants.CommonField;
import com.coohua.ap.admin.controller.vo.BaseConfigVO;
import com.coohua.ap.admin.controller.vo.ExcelFileVo;
import com.coohua.ap.admin.mapper.ap.BaseConfigHistoryMapper;
import com.coohua.ap.admin.mapper.ap.ConfigBaseMapper;
import com.coohua.ap.admin.model.BaseConfigHistoryEntity;
import com.coohua.ap.admin.model.ConfigBaseEntity;
import com.coohua.ap.admin.service.FileUploadService;
import com.coohua.ap.base.constants.ConfigType;
import com.coohua.ap.support.core.factory.StrategyConfigFactory;
import com.coohua.ap.support.core.spring.model.StrategyBaseConfigModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: FileUploadServiceImpl
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/7/30
 * @Version: 1.0.0
 **/
@Slf4j
@Service
public class FileUploadServiceImpl implements FileUploadService {


    @Autowired
    private ConfigBaseMapper configBaseMapper;

    @Autowired
    private BaseConfigHistoryMapper baseConfigHistoryMapper;

    @Override
    public boolean changeAdOutput(MultipartFile file,String fileType) {

        try {
            // 读取excel
            convertExcel(file.getInputStream(),fileType);
            // 获取配置
            StrategyConfigFactory strategyConfigFactory = StrategyConfigFactory.getInstance();
            List<StrategyBaseConfigModel> allBaseConfig = strategyConfigFactory.getAllBaseConfig();

            Map<Integer,List<StrategyBaseConfigModel>> baseConfigMap = new ConcurrentHashMap<>();
            // 填充Map
            fillBaseConfigMap(baseConfigMap,allBaseConfig);

            // 遍历依据不同应用调整配置
            for(int appId:CommonField.getExcelContainer().keySet()){
                Map<Integer, ExcelFileVo> adType2ConfigMap = CommonField.getExcelContainer().get(appId);
                List<StrategyBaseConfigModel> strategyBaseConfigModels = baseConfigMap.get(appId);
                // 对配置处理
                calculateConfig(adType2ConfigMap,strategyBaseConfigModels);
            }

            return true;
        } catch (Exception e) {
            log.error("excel convert error! e = ",e);
            return false;
        }

    }

    private void calculateConfig(Map<Integer, ExcelFileVo> adType2ConfigMap,List<StrategyBaseConfigModel> strategyBaseConfigModels){
        for(StrategyBaseConfigModel strategyBaseConfigModel:strategyBaseConfigModels){
            String config = strategyBaseConfigModel.getConfig();
            BaseConfigHistoryEntity baseConfigHistoryEntity = new BaseConfigHistoryEntity();
            baseConfigHistoryEntity.setAppId(strategyBaseConfigModel.getProduct());
            baseConfigHistoryEntity.setConfigId(strategyBaseConfigModel.getId());
            baseConfigHistoryEntity.setConfigOld(config);
            List<BaseConfigVO> baseConfigVOS = JSONObject.parseArray(config, BaseConfigVO.class);
            List<BaseConfigVO> adjustedBaseConfigVOS = new ArrayList<>();

            // 本次配置是否进行过更改
            boolean changed = false;
            // ecpm最大的广告类型
            int maxEcpmAdType = 0;
            // 最大的ecpm
            int maxEcpm = 0;

            for(BaseConfigVO baseConfigVO:baseConfigVOS){
                // 当前配置单元的所有广告类型
                List<Integer> adTypeList = new ArrayList<>();
                // 当前配置单元的广告相关配置信息
                String adTypeStr = baseConfigVO.getAdType();
                // 调整之后的配置
                StringBuilder judgedStr = null;
                // 当前总量（最大100）
                int currentCount = 0;
                String[] configUnit = adTypeStr.split("\\|");
                // 找出包含的广告类型和找出ecpm最大的广告类型
                for(String type2Percent: configUnit){
                    int adType = Integer.valueOf(type2Percent.split("_")[0]);
                    // excel配置文件中不包含当前的广告类型
                    if(adType2ConfigMap.get(adType)==null){
                        currentCount+=Integer.valueOf(type2Percent.split("_")[1]);
                        if(judgedStr==null){
                            judgedStr = new StringBuilder(type2Percent);
                        }else {
                            judgedStr.append("|");
                            judgedStr.append(type2Percent);
                        }
                        continue;
                    }
                    adTypeList.add(adType);
                    if(adType2ConfigMap.get(adType).getEcpm()>maxEcpm){
                        maxEcpmAdType = adType;
                        maxEcpm = adType2ConfigMap.get(adType).getEcpm();
                    }
                }
                // 判断本配置是否进行更改入库
                changed = changed||adTypeList.size()>1;

                // 自动出量逻辑
                for(int adType:adTypeList){
                    String currentStr = null;
                    ExcelFileVo excelFileVo = adType2ConfigMap.get(adType);
                    if(adType==maxEcpmAdType){
                        continue;
                    }
                    if(excelFileVo.getExposurePercent()<=5){
                        currentCount+=excelFileVo.getExposurePercent();
                        currentStr = adType+"_"+excelFileVo.getExposurePercent();
                    }else {
                        Integer currentEcpm = excelFileVo.getEcpm();
                        int percent = (maxEcpm * 100 - currentEcpm * 100) / currentEcpm;
                        if(percent<=5){
                            currentCount+=excelFileVo.getExposurePercent();
                            currentStr = adType+"_"+excelFileVo.getExposurePercent();
                        }else if(percent>5&&percent<=10){
                            currentCount+=excelFileVo.getExposurePercent()*7/10;
                            currentStr = adType+"_"+(excelFileVo.getExposurePercent()*7/10);
                        }else if(percent>10&&percent<=20){
                            currentCount+=excelFileVo.getExposurePercent()*5/10;
                            currentStr = adType+"_"+(excelFileVo.getExposurePercent()*5/10);
                        }else if(percent>20&&percent<=30){
                            currentCount+=excelFileVo.getExposurePercent()*3/10;
                            currentStr = adType+"_"+(excelFileVo.getExposurePercent()*3/10);
                        }else if(percent>30&&percent<=40){
                            currentCount+=excelFileVo.getExposurePercent()*1/10;
                            currentStr = adType+"_"+(excelFileVo.getExposurePercent()*1/10);
                        }else{
                            currentCount+= 5;
                            currentStr = adType+"_"+5;
                        }
                    }
                    if(judgedStr==null){
                        judgedStr = new StringBuilder(currentStr);
                    }else {
                        judgedStr.append("|");
                        judgedStr.append(currentStr);
                    }
                }
                if(judgedStr==null&&maxEcpmAdType!=0){
                    judgedStr = new StringBuilder(maxEcpmAdType+"_"+(currentCount>=100?0:(100-currentCount)));
                }else if(maxEcpmAdType!=0) {
                    judgedStr.append("|");
                    judgedStr.append(maxEcpmAdType+"_"+(currentCount>=100?0:(100-currentCount)));
                }
                baseConfigVO.setAdType(judgedStr.toString());
                adjustedBaseConfigVOS.add(baseConfigVO);

            }
            strategyBaseConfigModel.setConfig(JSONObject.toJSONString(adjustedBaseConfigVOS));
            ConfigBaseEntity configBaseEntity = configBaseModel2entity(strategyBaseConfigModel);
            baseConfigHistoryEntity.setConfigNew(strategyBaseConfigModel.getConfig());

            if(changed){
                int i = configBaseMapper.updateConfig(configBaseEntity);
                if(i == 1){

                    baseConfigHistoryMapper.addBaseConfigHistory(baseConfigHistoryEntity);
                }
            }

        }
    }

    private ConfigBaseEntity configBaseModel2entity(StrategyBaseConfigModel strategyBaseConfigModel){
        ConfigBaseEntity configBaseEntity = new ConfigBaseEntity();
        configBaseEntity.setId(strategyBaseConfigModel.getId());
        configBaseEntity.setState(strategyBaseConfigModel.getState());
        configBaseEntity.setName(strategyBaseConfigModel.getName());
        configBaseEntity.setProduct(strategyBaseConfigModel.getProduct());
        configBaseEntity.setType(strategyBaseConfigModel.getType());
        configBaseEntity.setComment(strategyBaseConfigModel.getComment());
        configBaseEntity.setConfig(strategyBaseConfigModel.getConfig());
        return configBaseEntity;
    }

    private void fillBaseConfigMap(Map<Integer,List<StrategyBaseConfigModel>> baseConfigMap,List<StrategyBaseConfigModel> allBaseConfig){
        for(StrategyBaseConfigModel strategyBaseConfigModel:allBaseConfig){
            if(strategyBaseConfigModel.getType()!= ConfigType.AD_CONFIG.code()){
                continue;
            }
            List<StrategyBaseConfigModel> strategyBaseConfigModels = baseConfigMap.get(strategyBaseConfigModel.getProduct());
            if(strategyBaseConfigModels == null){
                strategyBaseConfigModels = new ArrayList<>();
                baseConfigMap.put(strategyBaseConfigModel.getProduct(),strategyBaseConfigModels);
            }
            strategyBaseConfigModels.add(strategyBaseConfigModel);
        }
    }

    private void  convertExcel(InputStream inputStream, String fileType) throws IOException {
        CommonField.setExcelContainer(new ConcurrentHashMap<>());
        Workbook workbook = null;
        if(fileType.equalsIgnoreCase(CommonField.XLS)){
            workbook = new HSSFWorkbook(inputStream);
        }else if(fileType.equalsIgnoreCase(CommonField.XLSX)){
            workbook = new XSSFWorkbook(inputStream);
        }

        Sheet sheetAt = workbook.getSheetAt(0);
        if(sheetAt == null){
            return;
        }
        int firstRowNum = sheetAt.getFirstRowNum();
        Row firstRow = sheetAt.getRow(firstRowNum);
        if(firstRow == null){
            return;
        }
        int rowStart = firstRowNum+1;
        int rowEnd = sheetAt.getPhysicalNumberOfRows();
        for(int rowNum = rowStart;rowNum<rowEnd;rowNum++){
            Row row = sheetAt.getRow(rowNum);
            if(row == null){
                return;
            }
            int[] currArray = new int[5];
            for(int i = 0; i<5;i++){
                Cell cell = row.getCell(i);
                int cellValue = (int) cell.getNumericCellValue();
                currArray[i] = cellValue;
            }
            // 数据转成对象
            ExcelFileVo excelFileVo = new ExcelFileVo(currArray);
            // 容器填充
            Map<Integer, ExcelFileVo> adTypeMap = CommonField.getExcelContainer().get(excelFileVo.getAppId());
            if(adTypeMap == null){
                adTypeMap = new ConcurrentHashMap<>();
                CommonField.getExcelContainer().put(excelFileVo.getAppId(),adTypeMap);
            }
            adTypeMap.put(excelFileVo.getAdType(),excelFileVo);

        }

    }
}
