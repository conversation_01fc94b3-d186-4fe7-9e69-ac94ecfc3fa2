package com.coohua.ap.admin.controller;

/**
 * <pre>
 *
 * </pre>
 *
 * <AUTHOR>
 * @since 2020/03/13
 */
//@RestController
//@RequestMapping("/test")
public class TestController {
//
//    @JedisClusterClientRefer(namespace = "ap-cluster")
//    private JedisClusterClient apClusterRedisService;
//
//    @RequestMapping("/addKey")
//    public Object addKey(@RequestParam("key") String key) {
//        add(key);
//        return "OK";
//    }
//
//    @RequestMapping("/get")
//    public Object get(@RequestParam("keys") String keys) {
//        List<String> ret = new ArrayList<>();
//        List<Response<String>> temp = new ArrayList<>();
//        List<String> list = Arrays.asList(keys.split(","));
//        try(Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION);) {
//            Pipeline pipeline = jedis.pipelined();
//            for (String key : list) {
//                key = RedisConstants.NONE_USER_KEY_SLOT_LOCATION + key;
//                Response<String> response = pipeline.get(key);
//                temp.add(response);
//            }
//            pipeline.sync();
//
//            for (Response<String> resu : temp) {
//                if (resu != null && resu.get() != null) {
//                    ret.add(resu.get());
//                }
//            }
//        }
//
//        return JSONObject.toJSONString(ret);
//    }
//
//    private void add(String key) {
//        key = RedisConstants.NONE_USER_KEY_SLOT_LOCATION + key;
//        Jedis jedis = apClusterRedisService.getResource(RedisConstants.NONE_USER_KEY_SLOT_LOCATION);
//        jedis.incrBy(key, 1);
//        jedis.expire(key, 120);
//    }
}
