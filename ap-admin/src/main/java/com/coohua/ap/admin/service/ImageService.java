package com.coohua.ap.admin.service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PutObjectRequest;
import com.coohua.ap.admin.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.UUID;

/**
 * Created by liu liandong on 2017/4/14.
 */
@Slf4j
public class ImageService {

    private String uploadFileDir;
    private String bucket;
    private String imageServerDomain;
    private boolean ossEnable;
    private OSSClient ossClient;

    public String getUploadFileDir() {
        return uploadFileDir;
    }

    public void setUploadFileDir(String uploadFileDir) {
        this.uploadFileDir = uploadFileDir;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public boolean isOssEnable() {
        return ossEnable;
    }

    public void setOssEnable(boolean ossEnable) {
        this.ossEnable = ossEnable;
    }

    public OSSClient getOssClient() {
        return ossClient;
    }

    public void setOssClient(OSSClient ossClient) {
        this.ossClient = ossClient;
    }

    public String upload(HttpServletRequest request) {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(
                request.getSession().getServletContext());

        MultipartFile file = null;
        if (multipartResolver.isMultipart(request)) {
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            file = multiRequest.getFile("file");
        }

        String fileName = file.getOriginalFilename();
        String fileType = ImageService.getExtensionName(fileName);
        String destFileName = genFileName(fileType);

        boolean success = false;
        File targetFile = null;
        try {
            String targetFilePath = uploadFileDir + "/" + destFileName;
            targetFile = new File(targetFilePath);
            FileCopyUtils.copy(file.getBytes(), targetFile);
            String key = "upload/nap/" + targetFile.getName();
            saveToOSS(key, targetFile);
            success = true;
        } catch (Exception e) {
            log.error("error upload video {}", e.getMessage(), e);
        } finally {
            // 删除临时文件
            if (targetFile != null && targetFile.exists()) {
                if (!targetFile.delete()) {
                    log.warn("Failed to delete temporary file: {}", targetFile.getAbsolutePath());
                }
            }
        }
        if (success) {
            log.info("upload success:" + fileName + " to " + destFileName);
            destFileName = imageServerDomain + destFileName;
            return destFileName;
        } else {
            log.info("upload fail:" + fileName);
            return null;
        }
    }

    public static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }

    private String genFileName(String fileType) {
        String name = MD5Utils.getMd5Sum(UUID.randomUUID().toString()) + "." + fileType;
        return name;
    }

    private void saveToOSS(String key, File file) {
        this.ossClient.putObject(new PutObjectRequest(bucket, key, file));
    }

    public String getImageServerDomain() {
        return imageServerDomain;
    }

    public void setImageServerDomain(String imageServerDomain) {
        this.imageServerDomain = imageServerDomain;
    }
}
