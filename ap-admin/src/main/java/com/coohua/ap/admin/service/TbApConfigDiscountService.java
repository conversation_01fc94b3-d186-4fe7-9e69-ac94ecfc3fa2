package com.coohua.ap.admin.service;

import com.coohua.ap.admin.controller.vo.Page;
import com.coohua.ap.admin.controller.vo.ThirdPosAdView;
import com.coohua.ap.admin.model.TbApConfigDiscount;
import com.coohua.ap.base.vo.ApConfigDiscountVo;
import com.coohua.ap.base.vo.ApThirdPlatformTypeVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_ap_config_discount(打折系数配置)】的数据库操作Service
* @createDate 2024-12-04 11:01:36
*/
public interface TbApConfigDiscountService {

    /**
     * 分页查询打折系数配置
     *
     * @param
     * @param page
     */
    Page<TbApConfigDiscount> listConfig(int pageNo, int pageSize, ApConfigDiscountVo discountVo, Page<TbApConfigDiscount> page);

    /**
     * 保存打折系数配置
     * @param discountVo
     */
    ApConfigDiscountVo saveOrUpdateConfig(ApConfigDiscountVo discountVo);

    /**
     * 更新打折系数配置
     * @param discountVo
     */
    ApConfigDiscountVo deleteConfig(ApConfigDiscountVo discountVo);

    /**
     * 更新打折系数配置状态
     * @param discountVo
     */
    ApConfigDiscountVo updateConfigState(ApConfigDiscountVo discountVo);

    /**
     * 根据id查询打折系数配置
     * @param id
     * @return
     */
    ApConfigDiscountVo queryById(Long id);

    /**
     *
     * @return
     */
    List<ApThirdPlatformTypeVo> getPlatformAdPos();

}
