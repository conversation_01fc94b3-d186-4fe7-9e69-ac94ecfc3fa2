package com.coohua.ap.admin.controller.vo;

/**
 * Created by Administrator on 2017/4/12.
 */
public class AdPlan {
    private int id;
    private String name;
    private boolean open;
    private String clientName;
    private String productName;
    private String salesman;
    private int product;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSalesman() {
        return salesman;
    }

    public void setSalesman(String salesman) {
        this.salesman = salesman;
    }

    public int getProduct() {
        return product;
    }

    public void setProduct(int product) {
        this.product = product;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AdPlan adPlan = (AdPlan) o;

        if (id != adPlan.id) return false;
        if (open != adPlan.open) return false;
        if (name != null ? !name.equals(adPlan.name) : adPlan.name != null) return false;
        if (clientName != null ? !clientName.equals(adPlan.clientName) : adPlan.clientName != null) return false;
        if (productName != null ? !productName.equals(adPlan.productName) : adPlan.productName != null) return false;
        return salesman != null ? salesman.equals(adPlan.salesman) : adPlan.salesman == null;
    }

    @Override
    public String toString() {
        return "AdPlan{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", open=" + open +
                ", clientName='" + clientName + '\'' +
                ", productName='" + productName + '\'' +
                ", salesman='" + salesman + '\'' +
                ", product=" + product +
                '}';
    }
}
