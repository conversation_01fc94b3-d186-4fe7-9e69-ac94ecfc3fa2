webpackJsonp([22],{230:function(e,o,t){var a=t(87)(t(327),t(362),null,null);e.exports=a.exports},327:function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default={data:function(){return{showCsj:!0,showGdt:!1,showKs:!1,showBd:!1,showOppo:!1,platformCodeList:[{key:1,val:"穿山甲"},{key:2,val:"广点通"},{key:3,val:"快手"},{key:4,val:"百度"},{key:5,val:"OPPO"}],queryParam:{name:"",id:"",pageNo:1,pageSize:10},listCountNum:0,companyList:[],editCompanyForm:{id:"",platformCode:1,mainBody:"",userId:0,roleId:0,securityKey:"",maskRuleId:0},addCompanyForm:{platformCode:1,mainBody:"",userId:"",roleId:"",securityKey:"",maskRuleId:""},isShowEditCompanyForm:!1,isShowAddCompanyForm:!1,companyRules:{mainBody:[{required:!0,message:"主体 不能为空",trigger:"blur"}],userId:[{required:!0,message:"账户Id 不能为空",trigger:"blur"}],roleId:[{required:!0,message:"roleId 不能为空",trigger:"blur"}],securityKey:[{required:!0,message:"securityKey 不能为空",trigger:"blur"}]}}},created:function(){this.getCompanyList()},methods:{getCompanyList:function(){var e=this;e.$axios.post("third/company/list",{},{params:e.queryParam}).then(function(o){if(200===o.status){var t=o.data;if(0===t.ret){var a=t.data;e.companyList=a.items,e.listCountNum=a.count}else e.$message.error(t.data)}else e.$message.error("服务器异常！")})},saveCompany:function(){var e=this,o=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.editCompanyForm.validate(function(t){if(!t)return o.close(),console.log("error submit!!"),!1;e.$axios.post("third/company/updateOne",{},{params:e.editCompanyForm}).then(function(t){if(o.close(),200===t.status){var a=t.data;0===a.ret?(e.isShowEditCompanyForm=!1,e.$message({message:"主体信息更新成功",type:"success"}),e.getCompanyList()):e.$message.error(a.data)}else e.$message.error("服务器异常！")})})},addCompanyPre:function(){this.clearAddForm(),this.isShowAddCompanyForm=!0,this.changeSelect(this.addCompanyForm.platformCode)},addCompany:function(){var e=this,o=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.addCompanyForm.validate(function(t){if(!t)return o.close(),console.log("error submit!!"),!1;e.$axios.post("third/company/addOne",{},{params:e.addCompanyForm}).then(function(t){if(o.close(),200===t.status){var a=t.data;0===a.ret?(e.isShowAddCompanyForm=!1,e.$message({message:"新增主体成功",type:"success"}),e.getCompanyList()):e.$message.error(a.data)}else e.$message.error("服务器异常！")})})},clearAddForm:function(){this.addCompanyForm.platformCode=1,this.addCompanyForm.mainBody="",this.addCompanyForm.userId="",this.addCompanyForm.roleId="",this.addCompanyForm.securityKey="",this.addCompanyForm.maskRuleId=""},updateCompanyPre:function(e){this.changeSelect(e.platformCode),this.editCompanyForm.id=e.id,this.editCompanyForm.platformCode=e.platformCode,this.editCompanyForm.mainBody=e.mainBody,this.editCompanyForm.userId=e.userId,this.editCompanyForm.roleId=e.roleId,this.editCompanyForm.securityKey=e.securityKey,this.editCompanyForm.maskRuleId=e.maskRuleId,this.isShowEditCompanyForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getCompanyList()},queryLog:function(e){},deleteCompany:function(e){var o=this;this.$confirm("此操作将永久删除该主体, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t={id:e.id},a=o;a.$axios.post("third/company/delete",{},{params:t}).then(function(e){if(200===e.status){var o=e.data;0===o.ret?(a.$message.success("成功删除！"),a.getCompanyList()):a.$message.error(o.data)}else a.$message.error("服务器异常！")})}).catch(function(){o.$message({type:"info",message:"已取消删除"})})},changeSelect:function(e){1===e?(this.showCsj=!0,this.showGdt=!1,this.showKs=!1,this.showOppo=!1):2===e?(this.showGdt=!0,this.showCsj=!1,this.showKs=!1,this.showOppo=!1):3===e?(this.showKs=!0,this.showGdt=!1,this.showCsj=!1,this.showOppo=!1):4===e?(this.showBd=!0,this.showGdt=!1,this.showCsj=!1,this.showKs=!1,this.showOppo=!1):5===e&&(this.showBd=!1,this.showGdt=!1,this.showCsj=!1,this.showKs=!1,this.showOppo=!0)}}}},362:function(e,o){e.exports={render:function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div",{staticClass:"table"},[t("div",{staticClass:"crumbs"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",[t("i",{staticClass:"el-icon-menu"}),e._v(" 公司主体")])],1)],1),e._v(" "),t("el-dialog",{attrs:{title:"编辑主体",visible:e.isShowEditCompanyForm},on:{"update:visible":function(o){e.isShowEditCompanyForm=o}}},[t("el-form",{ref:"editCompanyForm",attrs:{model:e.editCompanyForm,"label-width":"200px",rules:e.companyRules}},[t("el-form-item",{attrs:{label:"平台"}},[t("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},on:{change:function(o){return e.changeSelect(e.editCompanyForm.platformCode)}},model:{value:e.editCompanyForm.platformCode,callback:function(o){e.$set(e.editCompanyForm,"platformCode",o)},expression:"editCompanyForm.platformCode"}},e._l(e.platformCodeList,function(e){return t("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),e.showCsj?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.editCompanyForm.mainBody,callback:function(o){e.$set(e.editCompanyForm,"mainBody",o)},expression:"editCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"user_id:",prop:"userId"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.userId,callback:function(o){e.$set(e.editCompanyForm,"userId",o)},expression:"editCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"role_id:",prop:"roleId"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.roleId,callback:function(o){e.$set(e.editCompanyForm,"roleId",o)},expression:"editCompanyForm.roleId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"security_key:",prop:"securityKey"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.securityKey,callback:function(o){e.$set(e.editCompanyForm,"securityKey",o)},expression:"editCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showGdt?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.editCompanyForm.mainBody,callback:function(o){e.$set(e.editCompanyForm,"mainBody",o)},expression:"editCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"账户ID:",prop:"userId"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.userId,callback:function(o){e.$set(e.editCompanyForm,"userId",o)},expression:"editCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"Secret:",prop:"securityKey"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.securityKey,callback:function(o){e.$set(e.editCompanyForm,"securityKey",o)},expression:"editCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showKs?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.editCompanyForm.mainBody,callback:function(o){e.$set(e.editCompanyForm,"mainBody",o)},expression:"editCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"ak:",prop:"userId"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.userId,callback:function(o){e.$set(e.editCompanyForm,"userId",o)},expression:"editCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"sk:",prop:"securityKey"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.securityKey,callback:function(o){e.$set(e.editCompanyForm,"securityKey",o)},expression:"editCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showBd?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.editCompanyForm.mainBody,callback:function(o){e.$set(e.editCompanyForm,"mainBody",o)},expression:"editCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"accessKey:",prop:"userId"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.userId,callback:function(o){e.$set(e.editCompanyForm,"userId",o)},expression:"editCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"privateKey:",prop:"securityKey"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.securityKey,callback:function(o){e.$set(e.editCompanyForm,"securityKey",o)},expression:"editCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showOppo?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.editCompanyForm.mainBody,callback:function(o){e.$set(e.editCompanyForm,"mainBody",o)},expression:"editCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"clientID:",prop:"userId"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.userId,callback:function(o){e.$set(e.editCompanyForm,"userId",o)},expression:"editCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"privateKey:",prop:"securityKey"}},[t("el-input",{attrs:{disabled:""},model:{value:e.editCompanyForm.securityKey,callback:function(o){e.$set(e.editCompanyForm,"securityKey",o)},expression:"editCompanyForm.securityKey"}})],1)],1):e._e()],1),e._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(o){e.isShowEditCompanyForm=!1}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(o){return e.saveCompany()}}},[e._v("确 定")])],1)],1),e._v(" "),t("el-dialog",{attrs:{title:"新建主体",visible:e.isShowAddCompanyForm},on:{"update:visible":function(o){e.isShowAddCompanyForm=o}}},[t("el-form",{ref:"addCompanyForm",attrs:{model:e.addCompanyForm,"label-width":"200px",rules:e.companyRules}},[t("el-form-item",{attrs:{label:"平台"}},[t("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},on:{change:function(o){return e.changeSelect(e.addCompanyForm.platformCode)}},model:{value:e.addCompanyForm.platformCode,callback:function(o){e.$set(e.addCompanyForm,"platformCode",o)},expression:"addCompanyForm.platformCode"}},e._l(e.platformCodeList,function(e){return t("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),e.showCsj?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.addCompanyForm.mainBody,callback:function(o){e.$set(e.addCompanyForm,"mainBody",o)},expression:"addCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"user_id:",prop:"userId"}},[t("el-input",{model:{value:e.addCompanyForm.userId,callback:function(o){e.$set(e.addCompanyForm,"userId",o)},expression:"addCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"role_id:",prop:"roleId"}},[t("el-input",{model:{value:e.addCompanyForm.roleId,callback:function(o){e.$set(e.addCompanyForm,"roleId",o)},expression:"addCompanyForm.roleId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"security_key:",prop:"securityKey"}},[t("el-input",{model:{value:e.addCompanyForm.securityKey,callback:function(o){e.$set(e.addCompanyForm,"securityKey",o)},expression:"addCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showGdt?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.addCompanyForm.mainBody,callback:function(o){e.$set(e.addCompanyForm,"mainBody",o)},expression:"addCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"账户ID:",prop:"userId"}},[t("el-input",{model:{value:e.addCompanyForm.userId,callback:function(o){e.$set(e.addCompanyForm,"userId",o)},expression:"addCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"Secret:",prop:"securityKey"}},[t("el-input",{model:{value:e.addCompanyForm.securityKey,callback:function(o){e.$set(e.addCompanyForm,"securityKey",o)},expression:"addCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showKs?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.addCompanyForm.mainBody,callback:function(o){e.$set(e.addCompanyForm,"mainBody",o)},expression:"addCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"ak:",prop:"userId"}},[t("el-input",{model:{value:e.addCompanyForm.userId,callback:function(o){e.$set(e.addCompanyForm,"userId",o)},expression:"addCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"sk:",prop:"securityKey"}},[t("el-input",{model:{value:e.addCompanyForm.securityKey,callback:function(o){e.$set(e.addCompanyForm,"securityKey",o)},expression:"addCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showBd?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.addCompanyForm.mainBody,callback:function(o){e.$set(e.addCompanyForm,"mainBody",o)},expression:"addCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"accessKey:",prop:"userId"}},[t("el-input",{model:{value:e.addCompanyForm.userId,callback:function(o){e.$set(e.addCompanyForm,"userId",o)},expression:"addCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"privateKey:",prop:"securityKey"}},[t("el-input",{model:{value:e.addCompanyForm.securityKey,callback:function(o){e.$set(e.addCompanyForm,"securityKey",o)},expression:"addCompanyForm.securityKey"}})],1)],1):e._e(),e._v(" "),e.showOppo?t("div",[t("el-form-item",{attrs:{label:"主体名称:",prop:"mainBody"}},[t("el-input",{model:{value:e.addCompanyForm.mainBody,callback:function(o){e.$set(e.addCompanyForm,"mainBody",o)},expression:"addCompanyForm.mainBody"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"clientID:",prop:"userId"}},[t("el-input",{model:{value:e.addCompanyForm.userId,callback:function(o){e.$set(e.addCompanyForm,"userId",o)},expression:"addCompanyForm.userId"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"clientSecret:",prop:"securityKey"}},[t("el-input",{model:{value:e.addCompanyForm.securityKey,callback:function(o){e.$set(e.addCompanyForm,"securityKey",o)},expression:"addCompanyForm.securityKey"}})],1)],1):e._e()],1),e._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(o){e.isShowAddCompanyForm=!1}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(o){return e.addCompany()}}},[e._v("确 定")])],1)],1),e._v(" "),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[t("el-form-item",{attrs:{label:"主体名称"}},[t("el-input",{staticClass:"width-150",model:{value:e.queryParam.name,callback:function(o){e.$set(e.queryParam,"name",o)},expression:"queryParam.name"}})],1),e._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getCompanyList}})],1),e._v(" "),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"primary",round:""},on:{click:function(o){return e.addCompanyPre()}}},[e._v("新建主体")])],1)],1)],1)],1),e._v(" "),t("el-divider",{attrs:{"content-position":"left"}},[e._v("主体列表")]),e._v(" "),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.companyList,stripe:"","highlight-current-row":""}},[t("el-table-column",{attrs:{prop:"platformDesc",label:"平台",width:"160"}}),e._v(" "),t("el-table-column",{attrs:{prop:"id",label:"ID"}}),e._v(" "),t("el-table-column",{attrs:{prop:"mainBody",label:"主体名称"}}),e._v(" "),t("el-table-column",{attrs:{prop:"userId",label:"主体账号"}}),e._v(" "),t("el-table-column",{attrs:{prop:"mainStatus",label:"主体状态"},scopedSlots:e._u([{key:"default",fn:function(o){return[1===o.row.mainStatus?t("span",[e._v("正常")]):t("span",[e._v("异常")])]}}])}),e._v(" "),t("el-table-column",{attrs:{prop:"appCount",label:"应用数量"}}),e._v(" "),t("el-table-column",{attrs:{label:"操作",width:"275",align:"center"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(t){return e.updateCompanyPre(o.row)}}},[e._v("修改")]),e._v(" "),t("el-button",{attrs:{plain:"",type:"info",size:"mini"},on:{click:function(t){return e.queryLog(o.row)}}},[e._v("日志")]),e._v(" "),t("el-button",{attrs:{plain:"",type:"danger",size:"mini"},on:{click:function(t){return e.deleteCompany(o.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),t("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}}});