webpackJsonp([24],{226:function(e,t,r){var a=r(87)(r(323),r(379),null,null);e.exports=a.exports},323:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{username:""},userList:[{id:"",username:0,password:0,name:"",appId:0,appName:"",updateTime:""}],addUserForm:{username:"",password:"",name:"",appId:3},updateUserForm:{id:0,username:"",password:"",name:"",appId:3},isAddUserFormShow:!1,isUpdateUserFormShow:!1,appList:[]}},created:function(){this.getAppList(),this.getUserList()},methods:{getAppList:function(){var e=this;e.$axios.post("app/shortNameList",{},{}).then(function(t){if(200===t.status){var r=t.data;0===r.ret?e.appList=r.data:e.$message.error(r.data)}else e.$message.error("服务器异常！")})},getUserList:function(){var e=this;e.$axios.post("user/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var r=t.data;0===r.ret?e.userList=r.data:e.$message.error(r.data)}else e.$message.error("服务器异常！")})},updateUser:function(){var e=this;e.$axios.post("user/updateUser",e.updateUserForm,{}).then(function(t){if(200===t.status){var r=t.data;0===r.ret?(e.isUpdateUserFormShow=!1,e.$message({message:"用户信息更新成功",type:"success"}),e.getUserList()):e.$message.error(r.data)}else e.$message.error("服务器异常！")})},addUser:function(){var e=this;e.$axios.post("user/addUser",e.addUserForm,{}).then(function(t){if(200===t.status){var r=t.data;0===r.ret?(e.isAddUserFormShow=!1,e.$message({message:"新增用户成功",type:"success"}),e.getUserList()):e.$message.error(r.data)}else e.$message.error("服务器异常！")})},addUserPre:function(){this.clearAddForm(),this.isAddUserFormShow=!0},clearAddForm:function(){this.addUserForm.appId=3,this.addUserForm.name="",this.addUserForm.username="",this.addUserForm.password=""},updateUserPre:function(e){this.updateUserForm.id=e.id,this.updateUserForm.appId=e.appId,this.updateUserForm.name=e.name,this.updateUserForm.password=e.password,this.updateUserForm.username=e.username,this.isUpdateUserFormShow=!0}}}},379:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"table"},[r("div",{staticClass:"crumbs"},[r("el-breadcrumb",{attrs:{separator:"/"}},[r("el-breadcrumb-item",[r("i",{staticClass:"el-icon-menu"}),e._v(" 管理员")]),e._v(" "),r("el-breadcrumb-item",[e._v("用户管理")])],1)],1),e._v(" "),r("el-row",[r("el-col",{attrs:{span:24}},[r("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[r("el-form-item",{attrs:{label:"用户名"}},[r("el-input",{staticClass:"width-150",model:{value:e.queryParam.username,callback:function(t){e.$set(e.queryParam,"username",t)},expression:"queryParam.username"}})],1),e._v(" "),r("el-form-item",[r("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getUserList}})],1),e._v(" "),r("el-form-item",{staticStyle:{float:"right"}},[r("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addUserPre()}}},[e._v("新建用户")])],1)],1)],1)],1),e._v(" "),r("el-divider",{attrs:{"content-position":"left"}},[e._v("用户列表")]),e._v(" "),r("el-dialog",{attrs:{title:"编辑用户",visible:e.isUpdateUserFormShow},on:{"update:visible":function(t){e.isUpdateUserFormShow=t}}},[r("el-form",{attrs:{model:e.updateUserForm,"label-width":"200px"}},[r("el-form-item",{attrs:{label:"用户名"}},[r("el-input",{model:{value:e.updateUserForm.username,callback:function(t){e.$set(e.updateUserForm,"username",t)},expression:"updateUserForm.username"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"密码"}},[r("el-input",{model:{value:e.updateUserForm.password,callback:function(t){e.$set(e.updateUserForm,"password",t)},expression:"updateUserForm.password"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"名称"}},[r("el-input",{model:{value:e.updateUserForm.name,callback:function(t){e.$set(e.updateUserForm,"name",t)},expression:"updateUserForm.name"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"所属应用"}},[r("el-select",{attrs:{placeholder:"请选择",disabled:""},model:{value:e.updateUserForm.appId,callback:function(t){e.$set(e.updateUserForm,"appId",t)},expression:"updateUserForm.appId"}},e._l(e.appList,function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isUpdateUserFormShow=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUser()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-dialog",{attrs:{title:"新增用户",visible:e.isAddUserFormShow},on:{"update:visible":function(t){e.isAddUserFormShow=t}}},[r("el-form",{attrs:{model:e.addUserForm,"label-width":"200px"}},[r("el-form-item",{attrs:{label:"用户名"}},[r("el-input",{model:{value:e.addUserForm.username,callback:function(t){e.$set(e.addUserForm,"username",t)},expression:"addUserForm.username"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"密码"}},[r("el-input",{model:{value:e.addUserForm.password,callback:function(t){e.$set(e.addUserForm,"password",t)},expression:"addUserForm.password"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"名称"}},[r("el-input",{model:{value:e.addUserForm.name,callback:function(t){e.$set(e.addUserForm,"name",t)},expression:"addUserForm.name"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"所属应用"}},[r("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:e.addUserForm.appId,callback:function(t){e.$set(e.addUserForm,"appId",t)},expression:"addUserForm.appId"}},e._l(e.appList,function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.isAddUserFormShow=!1}}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addUser()}}},[e._v("确 定")])],1)],1),e._v(" "),r("el-row",[r("el-col",{attrs:{span:24}},[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userList,stripe:"","highlight-current-row":""}},[r("el-table-column",{attrs:{prop:"id",label:"用户ID",width:"160"}}),e._v(" "),r("el-table-column",{attrs:{prop:"username",label:"用户名"}}),e._v(" "),r("el-table-column",{attrs:{prop:"name",label:"名称",width:"180"}}),e._v(" "),r("el-table-column",{attrs:{prop:"appName",label:"所属应用",width:"180"}}),e._v(" "),r("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{size:"small"},on:{click:function(r){return e.updateUserPre(t.row)}}},[e._v("修改")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});