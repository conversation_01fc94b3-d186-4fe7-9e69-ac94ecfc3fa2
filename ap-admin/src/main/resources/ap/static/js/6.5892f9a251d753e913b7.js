webpackJsonp([6],{213:function(e,t,i){i(401);var a=i(87)(i(310),i(372),null,null);e.exports=a.exports},234:function(e,t,i){e.exports={default:i(235),__esModule:!0}},235:function(e,t,i){var a=i(16),n=a.JSON||(a.JSON={stringify:JSON.stringify});e.exports=function(e){return n.stringify.apply(n,arguments)}},256:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={dateFormat:function(e,t){void 0===t&&(t=e,e=new Date);var i={M:e.getMonth()+1,d:e.getDate(),h:e.getHours(),m:e.getMinutes(),s:e.getSeconds(),q:Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};return t=t.replace(/([yMdhmsqS])+/g,function(t,a){var n=i[a];return void 0!==n?(t.length>1&&(n="0"+n,n=n.substr(n.length-2)),n):"y"===a?(e.getFullYear()+"").substr(4-t.length):t})},getUrlParam:function(e,t){var i=new RegExp("\\?(?:.+&)?"+t+"=(.*?)(?:[?&].*)?$"),a=e.match(i);return a?a[1]:""}}},310:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=i(234),n=i.n(a),o=i(256),s=i.n(o);t.default={data:function(){return{areaList:[{id:11,name:"北京"},{id:12,name:"天津"},{id:13,name:"河北"},{id:14,name:"山西"},{id:15,name:"内蒙古"},{id:21,name:"辽宁"},{id:22,name:"吉林"},{id:23,name:"黑龙江"},{id:31,name:"上海"},{id:32,name:"江苏"},{id:33,name:"浙江"},{id:34,name:"安徽"},{id:35,name:"福建"},{id:36,name:"江西"},{id:37,name:"山东"},{id:41,name:"河南"},{id:42,name:"湖北"},{id:43,name:"湖南"},{id:44,name:"广东"},{id:45,name:"广西"},{id:46,name:"海南"},{id:50,name:"重庆"},{id:51,name:"四川"},{id:52,name:"贵州"},{id:53,name:"云南"},{id:54,name:"西藏"},{id:61,name:"陕西"},{id:62,name:"甘肃"},{id:63,name:"青海"},{id:64,name:"宁夏"},{id:65,name:"新疆"},{id:71,name:"台湾"},{id:81,name:"香港"},{id:91,name:"澳门"}],regionList:[{key:1,val:"受限区"},{key:2,val:"非受限区"},{key:0,val:"不限制"}],lockAreaList:[{key:1,val:"锁区"},{key:2,val:"非锁区"},{key:0,val:"不限制"}],lockActionPointList:[{key:0,val:"所有锁区生效"},{key:1,val:"一道锁生效"},{key:2,val:"二道锁生效"}],state:0,queryAdParams:{adName:"",adPlan:"",adId:"",adType:"",pageNo:1,pageSize:10,startDate:"",endDate:"",status:"",adPos:[],adPosStr:"",userPkg:"",appIdThird:"",posIdThird:""},isIndeterminate:!0,checkAll:!0,adList:[],listCountNum:0,brands:[],adTypeList:[],registDate:"",planList:[],planList2:[],innerPlanList:[],industryList:[],multipleSelection:[],queryAdPlanParams:{name:"",pageNo:1,pageSize:10},isShowBatchUpdateAds:!1,orientation:{noAndroid:!1,noIos:!1,noRegion:!1,maxAndroidVersion:null,maxIosVersion:null,minAndroidVersion:null,minIosVersion:null,tailNumber:null,position:null,regions:[],filterRegion:0,lockArea:0,noIncomeLimit:!1,minIncome:null,maxIncome:null,noRegistTimeLimit:!1,minRegistTime:null,maxRegistTime:null,noRegistLongerLimit:!1,minRegistLonger:null,maxRegistLonger:null,containsPkg:null,notContainsPkg:null,userPkg:null,channelId:null,dsp:null,abPoint:null,lockActionPoint:0,brandLimit:!1,brandLimitList:[]},areaListId:[11,12,13,14,15,21,22,23,31,31,32,33,34,35,36,37,41,42,43,44,45,46,50,51,52,53,54,61,62,63,64,65,71,81,91]}},created:function(){this.queryTime=[s.a.dateFormat(new Date,"yyyy-MM-dd"),s.a.dateFormat(new Date,"yyyy-MM-dd")],this.queryAdParams.startDate=s.a.dateFormat(new Date,"yyyy-MM-dd"),this.queryAdParams.endDate=s.a.dateFormat(new Date,"yyyy-MM-dd"),this.getAdTypeList(),this.getPlanList(),this.getAdList(),this.getJsAdChannelSourceList(),this.getAdPosList(),this.getBrands()},methods:{handleIncomeLimitChange:function(e){this.orientation.noIncomeLimit&&(this.orientation.minIncome="",this.orientation.maxIncome="")},handleRegistLongerLimitChange:function(e){this.orientation.noRegistLongerLimit&&(this.orientation.minRegistLonger="",this.orientation.maxRegistLonger="")},registTimeFormat:function(){this.orientation.noRegistTimeLimit?(this.orientation.minRegistTime="",this.orientation.maxRegistTime=""):(this.orientation.minRegistTime=this.dataFormat(this.registDate[0]),this.orientation.maxRegistTime=this.dataFormat(this.registDate[1]))},handleRegistTimeLimitChange:function(e){this.orientation.noRegistTimeLimit?this.registDate="":this.registDate=[this.orientation.startDate,this.orientation.endDate]},handleNoIosChange:function(e){this.orientation.noIos&&(this.orientation.minIosVersion="",this.orientation.maxIosVersion="")},handleNoAndroidChange:function(e){this.orientation.noAndroid&&(this.orientation.minAndroidVersion="",this.orientation.maxAndroidVersion="")},handleCheckAllChange:function(e){this.orientation.regions=e?this.areaListId:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.areaList.length,this.isIndeterminate=t>0&&t<this.areaList.length},getAdList:function(){var e=this;this.queryAdParams.adPosStr=n()(this.queryAdParams.adPos),this.$axios.post("ad/listAd",{},{params:this.queryAdParams}).then(function(t){if(200===t.status){for(var i=t.data,a=0;a<i.data.items.length;a++)null==i.data.items[a].timeBucketVOList&&(i.data.items[a].timeBucketVOList=timeData);0===i.ret?(e.adList=i.data.items,e.listCountNum=i.data.count):e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},getBrands:function(){var e=this,t=this;t.$axios.post("ad/brandList",{},{}).then(function(i){if(200===i.status){var a=i.data;0===a.ret?e.brands=a.data:t.$message.error("初始化机型失败")}else t.$message.error("服务器异常！")})},getAdTypeList:function(){var e=this;e.$axios.post("adType/list",{},{}).then(function(t){if(200===t.status){var i=t.data;if(0===i.ret)for(var a=i.data,n=0;n<a.length;n++)e.adTypeList.push({type:a[n].code,name:a[n].name});else e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},getAdPosList:function(){var e=this;e.$axios.post("adpos/listByApp",{},{}).then(function(t){if(200===t.status){var i=t.data;if(0===i.ret){console.info(n()(i.data));for(var a=i.data,o=[],s=[],l=0;l<a.length;l++)o.push({key:a[l].id,label:a[l].name}),s.push({code:a[l].id,name:a[l].name});e.adPosList=s,e.adPosOptions=o}else e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},getJsAdChannelSourceList:function(){var e=this;this.$http.post("config/getJsAdChannelSourceList").then(function(t){if(t.ok){var i=t.data;0===i.ret?e.jsAdChannelSource=i.data:e.$message.error("初始化JS广告渠道来源失败")}})},getPlanList:function(){var e=this;this.$axios.post("ad/listPlan",{},{params:this.queryAdPlanParams}).then(function(t){if(200===t.status){var i=t.data;0===i.ret?(e.planList=i.data.page.items,e.planList2=i.data.planList,e.adPlanListNum=i.data.page.count,e.innerPlanList=i.data.planList,e.industryList=i.data.industryVOList):e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},handleCurrentChangePage:function(e){this.queryAdParams.pageNo=e,this.getAdList()},handleSizeChange:function(e){this.queryAdParams.pageSize=e,this.getAdList()},handleSelectionChange:function(e){this.multipleSelection=e},batchUpdateAds:function(){for(var e=this,t=[],i=0;i<e.multipleSelection.length;i++)t.push(e.multipleSelection[i].id);var a={orientation:e.orientation,updateIds:t,state:e.state};e.$http.post("ad/batchUpdate",a,{}).then(function(t){if(t.ok){var i=t.data;0===i.ret?(e.$message({message:"批量修改广告成功",type:"success"}),e.isShowBatchUpdateAds=!1,e.getAdList()):e.$message.error("批量修改广告失败, "+i.data)}else e.$message.error("批量修改广告失败")}),e.isShowBatchUpdateAds=!1}}}},336:function(e,t,i){t=e.exports=i(29)(),t.push([e.i,".el-row{margin-bottom:20px}.el-input{width:350px}.adForm,.el-steps{position:relative;left:30%;margin-bottom:20px}.timePicker{display:inline-block}.areaCheckbox{width:500px;height:200px;overflow-y:scroll;border:1px solid #d5d5d5;margin-top:10px;padding:15px}.areaCheckbox .el-checkbox{width:23%;margin:0;margin-right:10px;height:20px;line-height:20px}.areaCheckbox .el-checkbox .el-checkbox__input{float:right}.areaCheckbox .el-checkbox .el-checkbox__label{float:left}.width-100{width:100px}.width-150{width:150px}.width-300{width:300px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}.budgetSelect,.budgetSelect .el-input{width:100px}.text-center{text-align:center!important}.avatar-uploader .el-upload{border:1px solid #d9d9d9;border-radius:6px;cursor:pointer;position:relative;overflow:hidden}.big-pic .el-upload{width:640px;height:246px}.small-other-pic .el-upload{width:230px;height:152px}.avatar-uploader .el-upload:hover{border-color:#20a0ff}.font-color{color:#ff0}.avatar-uploader-icon{font-size:28px;color:#8c939d;width:100%;height:100%;line-height:178px;text-align:center}.avatar{width:100%;height:100%;display:block}.list-image .el-upload{width:170px;height:170px;margin-right:15px;float:left}.small-desc{height:152px;width:300px}.small-desc .el-textarea,textarea{height:100%}textarea{width:100%}.big-desc{width:640px}.big-desc .el-textarea{width:100%;height:100%}.combination-desc{width:550px}.combination-desc .el-textarea{margin-top:10px}.video-input .el-input{float:left}.upload-video{width:80px;float:left;margin-left:10px}.upload-video .el-upload--text{width:auto;height:auto}.share-img .el-upload--text{width:180px;height:200px}.share-img-qrcode{text-align:center}.el-input.is-disabled /deep/ .el-input__inner{color:#8a2be2}.el-table__body{user-select:auto}",""])},372:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"table"},[i("div",{staticClass:"crumbs"},[i("el-breadcrumb",{attrs:{separator:"/"}},[i("el-breadcrumb-item",[i("i",{staticClass:"el-icon-menu"}),e._v("广告位管理")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"即将修改以下广告",visible:e.isShowBatchUpdateAds},on:{"update:visible":function(t){e.isShowBatchUpdateAds=t}}},[i("el-col",{attrs:{span:24}},[i("el-divider",{attrs:{"content-position":"left"}},[e._v("将修改以下广告的以下选项")]),e._v(" "),i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.multipleSelection,stripe:"","highlight-current-row":""}},[i("el-table-column",{attrs:{prop:"id",label:"广告ID"}}),e._v(" "),i("el-table-column",{attrs:{prop:"name",label:"广告名称","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{label:"广告类型","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(e.adTypeList,function(a){return a.type==t.row.type?i("span",[e._v(e._s(a.name))]):e._e()})}}])})],1)],1),e._v(" "),i("el-form",{attrs:{model:e.orientation,"label-width":"100px","label-position":e.left}},[i("el-form-item",{attrs:{label:"开关"}},[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.state,callback:function(t){e.state=t},expression:"state"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"地域"}},[i("el-radio-group",{attrs:{size:"small"},model:{value:e.orientation.noRegion,callback:function(t){e.$set(e.orientation,"noRegion",t)},expression:"orientation.noRegion"}},[i("el-radio-button",{attrs:{label:!1}},[e._v("不限")]),e._v(" "),i("el-radio-button",{attrs:{label:!0}},[e._v("省市")]),e._v(" "),i("el-radio-button",[e._v("区县")])],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.orientation.noRegion,expression:"orientation.noRegion"}]},[i("div",{staticClass:"areaCheckbox"},[[i("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选\n                            ")]),e._v(" "),i("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.orientation.regions,callback:function(t){e.$set(e.orientation,"regions",t)},expression:"orientation.regions"}},e._l(e.areaList,function(t){return i("el-checkbox",{attrs:{label:t.id}},[e._v(e._s(t.name))])}),1)]],2)])],1),e._v(" "),i("el-form-item",{attrs:{label:"受限区"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:e.orientation.filterRegion,callback:function(t){e.$set(e.orientation,"filterRegion",t)},expression:"orientation.filterRegion"}},e._l(e.regionList,function(e){return i("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"锁区"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:e.orientation.lockArea,callback:function(t){e.$set(e.orientation,"lockArea",t)},expression:"orientation.lockArea"}},e._l(e.lockAreaList,function(e){return i("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),1===e.orientation.lockArea?i("el-form-item",{attrs:{label:"锁区作用点"}},[i("el-select",{staticClass:"width-100",attrs:{placeholder:"请选择锁区作用点"},model:{value:e.orientation.lockActionPoint,callback:function(t){e.$set(e.orientation,"lockActionPoint",t)},expression:"orientation.lockActionPoint"}},e._l(e.lockActionPointList,function(e){return i("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1):e._e(),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"平台版本"}},[i("span",[e._v("IOS:大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noIos},model:{value:e.orientation.minIosVersion,callback:function(t){e.$set(e.orientation,"minIosVersion",t)},expression:"orientation.minIosVersion"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noIos},model:{value:e.orientation.maxIosVersion,callback:function(t){e.$set(e.orientation,"maxIosVersion",t)},expression:"orientation.maxIosVersion"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleNoIosChange},model:{value:e.orientation.noIos,callback:function(t){e.$set(e.orientation,"noIos",t)},expression:"orientation.noIos"}},[e._v("不投放IOS平台\n                ")])],e._v(" "),i("br"),e._v(" "),i("el-form-item",{staticClass:" inline-block"},[i("span",[e._v("Android:大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noAndroid},model:{value:e.orientation.minAndroidVersion,callback:function(t){e.$set(e.orientation,"minAndroidVersion",t)},expression:"orientation.minAndroidVersion"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noAndroid},model:{value:e.orientation.maxAndroidVersion,callback:function(t){e.$set(e.orientation,"maxAndroidVersion",t)},expression:"orientation.maxAndroidVersion"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleNoAndroidChange},model:{value:e.orientation.noAndroid,callback:function(t){e.$set(e.orientation,"noAndroid",t)},expression:"orientation.noAndroid"}},[e._v("\n                    不投放Android平台\n                ")])],e._v(" "),i("el-form-item",{attrs:{label:"注册时间定向"}},[i("div",{staticClass:"timePicker"},[i("el-date-picker",{attrs:{type:"daterange",placeholder:"选择日期",disabled:e.orientation.noRegistTimeLimit},on:{change:e.registTimeFormat},model:{value:e.registDate,callback:function(t){e.registDate=t},expression:"registDate"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleRegistTimeLimitChange},model:{value:e.orientation.noRegistTimeLimit,callback:function(t){e.$set(e.orientation,"noRegistTimeLimit",t)},expression:"orientation.noRegistTimeLimit"}},[e._v("\n                        不开启注册时间定向\n                    ")])]],2),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"注册时长定向(单位：天)"}},[i("span",[e._v("大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noRegistLongerLimit},model:{value:e.orientation.minRegistLonger,callback:function(t){e.$set(e.orientation,"minRegistLonger",t)},expression:"orientation.minRegistLonger"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noRegistLongerLimit},model:{value:e.orientation.maxRegistLonger,callback:function(t){e.$set(e.orientation,"maxRegistLonger",t)},expression:"orientation.maxRegistLonger"}}),e._v(" "),[i("el-checkbox",{on:{change:e.handleRegistLongerLimitChange},model:{value:e.orientation.noRegistLongerLimit,callback:function(t){e.$set(e.orientation,"noRegistLongerLimit",t)},expression:"orientation.noRegistLongerLimit"}},[e._v("\n                        不开启注册时长定向\n                    ")])]],2),e._v(" "),i("br"),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"总收入定向"}},[i("span",[e._v("大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noIncomeLimit},model:{value:e.orientation.minIncome,callback:function(t){e.$set(e.orientation,"minIncome",t)},expression:"orientation.minIncome"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.orientation.noIncomeLimit},model:{value:e.orientation.maxIncome,callback:function(t){e.$set(e.orientation,"maxIncome",t)},expression:"orientation.maxIncome"}}),e._v(" "),[i("el-checkbox",{on:{change:e.handleIncomeLimitChange},model:{value:e.orientation.noIncomeLimit,callback:function(t){e.$set(e.orientation,"noIncomeLimit",t)},expression:"orientation.noIncomeLimit"}},[e._v("\n                        不开启总收入定向\n                    ")])]],2),e._v(" "),i("el-form-item",{attrs:{label:"机型定向"}},[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},model:{value:e.orientation.brandLimit,callback:function(t){e.$set(e.orientation,"brandLimit",t)},expression:"orientation.brandLimit"}})],1),e._v(" "),e.orientation.brandLimit?i("el-form-item",{attrs:{label:"选择机型"}},[i("el-transfer",{attrs:{data:e.brands,titles:["可选机型","已选机型"],"button-texts":["移除","添加"]},model:{value:e.orientation.brandLimitList,callback:function(t){e.$set(e.orientation,"brandLimitList",t)},expression:"orientation.brandLimitList"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"尾号定向"}},[i("el-input",{model:{value:e.orientation.tailNumber,callback:function(t){e.$set(e.orientation,"tailNumber",t)},expression:"orientation.tailNumber"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"包含包名定向"}},[i("el-input",{model:{value:e.orientation.containsPkg,callback:function(t){e.$set(e.orientation,"containsPkg",t)},expression:"orientation.containsPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"不包含包名定向"}},[i("el-input",{model:{value:e.orientation.notContainsPkg,callback:function(t){e.$set(e.orientation,"notContainsPkg",t)},expression:"orientation.notContainsPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户APP包名"}},[i("el-input",{model:{value:e.orientation.userPkg,callback:function(t){e.$set(e.orientation,"userPkg",t)},expression:"orientation.userPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"渠道名"}},[i("el-input",{model:{value:e.orientation.channelId,callback:function(t){e.$set(e.orientation,"channelId",t)},expression:"orientation.channelId"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"投放渠道定向"}},[i("el-input",{staticClass:"width-100",model:{value:e.orientation.dsp,callback:function(t){e.$set(e.orientation,"dsp",t)},expression:"orientation.dsp"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"策略定向"}},[i("el-input",{model:{value:e.orientation.abPoint,callback:function(t){e.$set(e.orientation,"abPoint",t)},expression:"orientation.abPoint"}})],1)],2),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowBatchUpdateAds=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.batchUpdateAds()}}},[e._v("更 新")])],1)],1),e._v(" "),i("el-row",{attrs:{span:24}},[i("el-form",{attrs:{inline:"",model:e.queryAdParams,size:"small"}},[i("el-form-item",{attrs:{label:"广告名称"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"请输入广告名称"},model:{value:e.queryAdParams.adName,callback:function(t){e.$set(e.queryAdParams,"adName",t)},expression:"queryAdParams.adName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"广告计划"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"广告计划",filterable:""},model:{value:e.queryAdParams.adPlan,callback:function(t){e.$set(e.queryAdParams,"adPlan",t)},expression:"queryAdParams.adPlan"}},e._l(e.planList2,function(e){return i("el-option",{attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告ID"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"请输入广告ID"},model:{value:e.queryAdParams.adId,callback:function(t){e.$set(e.queryAdParams,"adId",t)},expression:"queryAdParams.adId"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",filterable:""},model:{value:e.queryAdParams.adType,callback:function(t){e.$set(e.queryAdParams,"adType",t)},expression:"queryAdParams.adType"}},e._l(e.adTypeList,function(e){return i("el-option",{attrs:{label:e.name,value:e.type}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告状态"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"请选择广告状态"},model:{value:e.queryAdParams.status,callback:function(t){e.$set(e.queryAdParams,"status",t)},expression:"queryAdParams.status"}},[i("el-option",{attrs:{label:"不限制",value:"0"}}),e._v(" "),i("el-option",{attrs:{label:"有效广告",value:"1"}}),e._v(" "),i("el-option",{attrs:{label:"无效广告",value:"2"}})],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告位"}},[i("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"请选择"},model:{value:e.queryAdParams.adPos,callback:function(t){e.$set(e.queryAdParams,"adPos",t)},expression:"queryAdParams.adPos"}},e._l(e.adPosList,function(e){return i("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"包名"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"模糊匹配"},model:{value:e.queryAdParams.userPkg,callback:function(t){e.$set(e.queryAdParams,"userPkg",t)},expression:"queryAdParams.userPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"三方appId"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"模糊匹配"},model:{value:e.queryAdParams.appIdThird,callback:function(t){e.$set(e.queryAdParams,"appIdThird",t)},expression:"queryAdParams.appIdThird"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"三方posId"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"模糊匹配"},model:{value:e.queryAdParams.posIdThird,callback:function(t){e.$set(e.queryAdParams,"posIdThird",t)},expression:"queryAdParams.posIdThird"}})],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:function(t){return e.getAdList()}}})],1),e._v(" "),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowBatchUpdateAds=!0}}},[e._v("批量修改所选广告")])],1)],1)],1),e._v(" "),i("el-divider",{attrs:{"content-position":"left"}},[e._v("广告位列表")]),e._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.adList,stripe:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"55",label:""}}),e._v(" "),i("el-table-column",{attrs:{label:"状态","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.state?i("el-tag",{attrs:{type:"success"}},[e._v("投放中")]):e._e(),e._v(" "),2==t.row.state?i("el-tag",{attrs:{type:"warning"}},[e._v("已暂停")]):e._e(),e._v(" "),3==t.row.state?i("el-tag",{attrs:{type:"danger"}},[e._v("已结束")]):e._e()]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"id",label:"广告ID"}}),e._v(" "),i("el-table-column",{attrs:{prop:"name",label:"广告名称","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{prop:"plan.name",label:"广告计划","min-width":"140"}}),e._v(" "),i("el-table-column",{attrs:{label:"广告类型","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(e.adTypeList,function(a){return a.type==t.row.type?i("span",[e._v(e._s(a.name))]):e._e()})}}])}),e._v(" "),i("el-table-column",{attrs:{label:"投放日期","min-width":"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.noEndDate?i("span",[e._v(e._s(t.row.startDate)+"-不限")]):i("span",[e._v(e._s(t.row.startDate)+"-"+e._s(t.row.endDate))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"APPID","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[""===t.row.ext.androidAppId?i("span",[e._v(" "+e._s(t.row.ext.iosAppId))]):i("span",[e._v(e._s(t.row.ext.androidAppId))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"POSID","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[""===t.row.ext.androidAppId?i("span",[e._v(" "+e._s(t.row.ext.iosPosId))]):i("span",[e._v(e._s(t.row.ext.androidPosId))])]}}])})],1),e._v(" "),i("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryAdParams.pageNo,"page-sizes":[10,20,50,100],"page-size":10,layout:"total,sizes, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}},401:function(e,t,i){var a=i(336);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);i(88)("6bd66cb2",a,!0)}});