webpackJsonp([28],{210:function(t,e,a){var p=a(87)(a(307),a(381),null,null);t.exports=p.exports},307:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={data:function(){return{queryParam:{name:""},appList:[{appId:"",appName:"",description:"",updateTime:""}],addAppForm:{appId:"",appName:"",appPkgName:"",description:""},updateAppForm:{appId:"",appName:"",description:""},isAddAppFormShow:!1,isUpdateAppFormShow:!1,applicationList:[]}},created:function(){this.getAppList(),this.getAppDist()},methods:{getAppDist:function(){var t=this;t.$axios.post("app/dist",{},{}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?t.applicationList=a.data:t.$message.error(a.data)}else t.$message.error("服务器异常！")})},getAppList:function(){var t=this;t.$axios.post("app/list",{},{params:t.queryParam}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?t.appList=a.data:t.$message.error(a.data)}else t.$message.error("服务器异常！")})},updateApp:function(){var t=this;t.$axios.post("app/updateApp",t.updateAppForm,{}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?(t.isUpdateAppFormShow=!1,t.$message({message:"应用信息更新成功",type:"success"}),t.getAppList()):t.$message.error(a.data)}else t.$message.error("服务器异常！")})},addApp:function(){var t=this;t.$axios.post("app/addApp",t.addAppForm,{}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?(t.isAddAppFormShow=!1,t.$message({message:"新增应用成功",type:"success"}),t.getAppList()):t.$message.error(a.data)}else t.$message.error("服务器异常！")})},addAppPre:function(){this.clearAddForm(),this.isAddAppFormShow=!0},clearAddForm:function(){this.addAppForm.appId="",this.addAppForm.appName="",this.addAppForm.description=""},updateAppPre:function(t){this.updateAppForm.appId=t.appId,this.updateAppForm.appName=t.appName,this.updateAppForm.description=t.description,this.isUpdateAppFormShow=!0}}}},381:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),t._v(" 管理员")]),t._v(" "),a("el-breadcrumb-item",[t._v("应用管理")])],1)],1),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:t.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"应用名称"}},[a("el-input",{staticClass:"width-150",model:{value:t.queryParam.name,callback:function(e){t.$set(t.queryParam,"name",e)},expression:"queryParam.name"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:t.getAppList}})],1),t._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){return t.addAppPre()}}},[t._v("新建应用")])],1)],1)],1)],1),t._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[t._v("应用列表")]),t._v(" "),a("el-dialog",{attrs:{title:"编辑应用",visible:t.isUpdateAppFormShow},on:{"update:visible":function(e){t.isUpdateAppFormShow=e}}},[a("el-form",{attrs:{model:t.updateAppForm,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"应用"}},[a("el-select",{staticClass:"select-350",attrs:{placeholder:"应用",disabled:""},model:{value:t.updateAppForm.appId,callback:function(e){t.$set(t.updateAppForm,"appId",e)},expression:"updateAppForm.appId"}},t._l(t.applicationList,function(e){return a("el-option",{attrs:{label:e.productName,value:e.appId}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.productName))]),t._v(" "),a("el-tag",{staticStyle:{float:"right"},attrs:{type:"success"}},[t._v(t._s(e.product))])],1)}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"应用描述"}},[a("el-input",{attrs:{type:"textarea",rows:5,placeholder:"备份"},model:{value:t.updateAppForm.description,callback:function(e){t.$set(t.updateAppForm,"description",e)},expression:"updateAppForm.description"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.isUpdateAppFormShow=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.updateApp()}}},[t._v("确 定")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"新增应用",visible:t.isAddAppFormShow},on:{"update:visible":function(e){t.isAddAppFormShow=e}}},[a("el-form",{attrs:{model:t.addAppForm,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"应用"}},[a("el-select",{staticClass:"select-350",attrs:{placeholder:"应用",filterable:""},model:{value:t.addAppForm.appId,callback:function(e){t.$set(t.addAppForm,"appId",e)},expression:"addAppForm.appId"}},t._l(t.applicationList,function(e){return a("el-option",{attrs:{label:e.productName,value:e.appId}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.productName))]),t._v(" "),a("el-tag",{staticStyle:{float:"right"},attrs:{type:"success"}},[t._v(t._s(e.product))])],1)}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"应用包名"}},[a("el-input",{model:{value:t.addAppForm.appPkgName,callback:function(e){t.$set(t.addAppForm,"appPkgName",e)},expression:"addAppForm.appPkgName"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"任务描述"}},[a("el-input",{attrs:{type:"textarea",rows:5,placeholder:"备份"},model:{value:t.addAppForm.description,callback:function(e){t.$set(t.addAppForm,"description",e)},expression:"addAppForm.description"}})],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.isAddAppFormShow=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addApp()}}},[t._v("确 定")])],1)],1),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.appList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"appId",label:"应用ID",width:"160"}}),t._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"应用名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"updateTime",label:"最近修改时间",width:"180",sortable:""}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{size:"small"},on:{click:function(a){return t.updateAppPre(e.row)}}},[t._v("修改")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});