webpackJsonp([17],{208:function(e,o,t){t(403);var a=t(87)(t(305),t(378),null,null);e.exports=a.exports},305:function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.default={data:function(){return{queryParam:{pageNo:1,pageSize:10,id:""},listCountNum:0,replacePosConfigList:[],adPosTypes:[],adIdList:[],adIdListGdt:[],editReplacePosConfigForm:{strategyName:"",os:"",product:"",dsp:"",platform:"",pvGap:0,pvIncomeLimit:0,incomeGap:0,incomeLimit:0,totalIncome:0},addReplacePosConfigForm:{strategyName:"",adTypeName:"",os:"",product:"",dsp:"",platform:"",pvGap:0,pvIncomeLimit:0,incomeGap:0,incomeLimit:0,totalIncome:0},isShowEditReplaceForm:!1,isShowAddReplaceForm:!1}},created:function(){this.getReplacePosConfigList()},methods:{getReplacePosConfigList:function(){var e=this;e.$axios.post("config/replace/list",{},{params:e.queryParam}).then(function(o){if(200===o.status){var t=o.data;e.replacePosConfigList=t.items,e.listCountNum=t.count}else e.$message.error("服务器异常！")})},saveReplacePosConfig:function(){var e=this;e.$refs.editReplacePosConfigForm.validate(function(o){if(!o)return console.log("error submit!!"),!1;e.$axios.post("config/replace/updateOne",{},{params:e.editReplacePosConfigForm}).then(function(o){if(200===o.status){var t=o.data;0===t.ret?(e.isShowEditReplaceForm=!1,e.$message({message:"替换广告配置信息更新成功",type:"success"}),e.getReplacePosConfigList()):e.$message.error(t.data)}else e.$message.error("服务器异常！")})})},addReplacePosConfigPre:function(){this.clearAddForm(),this.isShowAddReplaceForm=!0},addReplacePosConfig:function(){var e=this;e.$refs.addReplacePosConfigForm.validate(function(o){if(!o)return console.log("error submit!!"),!1;e.$axios.post("config/replace/addOne",{},{params:e.addReplacePosConfigForm}).then(function(o){if(200===o.status){var t=o.data;0===t.ret?(e.isShowAddReplaceForm=!1,e.$message({message:"新增Bidding配置成功",type:"success"}),e.getReplacePosConfigList()):e.$message.error(t.data)}else e.$message.error("服务器异常！")})})},clearAddForm:function(){this.addReplacePosConfigForm.adTypeName="",this.addReplacePosConfigForm.adId="",this.addReplacePosConfigForm.endEcpm=0,this.addReplacePosConfigForm.priority=0},updateReplacePosConfig:function(e){this.editReplacePosConfigForm.strategyName=e.strategyName,this.editReplacePosConfigForm.os=e.os,this.editReplacePosConfigForm.product=e.product,this.editReplacePosConfigForm.adTypeName=e.adTypeName,this.editReplacePosConfigForm.dsp=e.dsp,this.editReplacePosConfigForm.platform=e.platform,this.editReplacePosConfigForm.pvGap=e.pvGap,this.editReplacePosConfigForm.pvIncomeLimit=e.pvIncomeLimit,this.editReplacePosConfigForm.incomeGap=e.incomeGap,this.editReplacePosConfigForm.incomeLimit=e.incomeLimit,this.editReplacePosConfigForm.totalIncome=e.totalIncome,this.isShowEditReplaceForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getReplacePosConfigList()},changeConfigSwitchFlag:function(e,o){var t={};t.id=o.id,t.isEnabled=o.isEnabled;var a=this;a.$axios.post("config/replace/updateEnabled",{},{params:t}).then(function(e){if(200===e.status){0===e.data.ret?a.$message({message:"更新状态成功",type:"success"}):a.$message.error("更新状态失败")}else a.$message.error("服务器异常！")})},deleteReplacePosConfig:function(e){var o=this;this.$confirm("此操作将删除该广告位替换配置, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t={id:e.id,delFlag:0},a=o;a.$axios.post("config/replace/deleteOne",{},{params:t}).then(function(e){if(200===e.status){var o=e.data;0===o.ret?(a.$message.success("成功删除！"),a.getReplacePosConfigList()):a.$message.error(o.data)}else a.$message.error("服务器异常！")})}).catch(function(){o.$message({type:"info",message:"已取消删除"})})}}}},338:function(e,o,t){o=e.exports=t(29)(),o.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},378:function(e,o){e.exports={render:function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div",{staticClass:"table"},[t("div",{staticClass:"crumbs"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",[t("i",{staticClass:"el-icon-menu"}),e._v(" 广告位替换配置")]),e._v(" "),t("el-breadcrumb-item",[e._v("广告位替换配置")])],1)],1),e._v(" "),t("el-dialog",{attrs:{title:"编辑广告位替换配置",visible:e.isShowEditReplaceForm},on:{"update:visible":function(o){e.isShowEditReplaceForm=o}}},[t("el-form",{ref:"editReplacePosConfigForm",attrs:{model:e.editReplacePosConfigForm,"label-width":"200px"}},[t("el-form-item",{attrs:{label:"广告位类型"}},[t("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型"},model:{value:e.editReplacePosConfigForm.adTypeName,callback:function(o){e.$set(e.editReplacePosConfigForm,"adTypeName",o)},expression:"editReplacePosConfigForm.adTypeName"}},[t("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adPosTypes,function(e){return t("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),t("el-form-item",{attrs:{label:"系统"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.os,callback:function(o){e.$set(e.editReplacePosConfigForm,"os",o)},expression:"editReplacePosConfigForm.os"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"策略名称"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.strategyName,callback:function(o){e.$set(e.editReplacePosConfigForm,"strategyName",o)},expression:"editReplacePosConfigForm.strategyName"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"产品"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.product,callback:function(o){e.$set(e.editReplacePosConfigForm,"product",o)},expression:"editReplacePosConfigForm.product"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"投放渠道"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.dsp,callback:function(o){e.$set(e.editReplacePosConfigForm,"dsp",o)},expression:"editReplacePosConfigForm.dsp"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"平台"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.platform,callback:function(o){e.$set(e.editReplacePosConfigForm,"platform",o)},expression:"editReplacePosConfigForm.platform"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"pvGap"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.pvGap,callback:function(o){e.$set(e.editReplacePosConfigForm,"pvGap",o)},expression:"editReplacePosConfigForm.pvGap"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"pvIncomeLimit"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.pvIncomeLimit,callback:function(o){e.$set(e.editReplacePosConfigForm,"pvIncomeLimit",o)},expression:"editReplacePosConfigForm.pvIncomeLimit"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"incomeGap"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.incomeGap,callback:function(o){e.$set(e.editReplacePosConfigForm,"incomeGap",o)},expression:"editReplacePosConfigForm.incomeGap"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"incomeLimit"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.incomeLimit,callback:function(o){e.$set(e.editReplacePosConfigForm,"incomeLimit",o)},expression:"editReplacePosConfigForm.incomeLimit"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"totalIncome"}},[t("el-input",{staticClass:"width-100",model:{value:e.editReplacePosConfigForm.totalIncome,callback:function(o){e.$set(e.editReplacePosConfigForm,"totalIncome",o)},expression:"editReplacePosConfigForm.totalIncome"}})],1),e._v(" "),e.adIdListGdt.indexOf(e.editReplacePosConfigForm.adId)>-1?t("div",[t("el-form-item",{attrs:{label:"请求方式"}},[t("el-radio-group",{model:{value:e.editReplacePosConfigForm.biddingType,callback:function(o){e.$set(e.editReplacePosConfigForm,"biddingType",o)},expression:"editReplacePosConfigForm.biddingType"}},[t("el-radio",{attrs:{label:1}},[e._v("C2S")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("S2S")])],1)],1)],1):e._e()],1),e._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(o){e.isShowEditReplaceForm=!1}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(o){return e.saveReplacePosConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),t("el-dialog",{attrs:{title:"新建广告位替换配置",visible:e.isShowAddReplaceForm},on:{"update:visible":function(o){e.isShowAddReplaceForm=o}}},[t("el-form",{ref:"addBiddingForm",attrs:{model:e.addReplacePosConfigForm,"label-width":"200px"}},[t("el-form-item",{attrs:{label:"Start ECPM"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.startEcpm,callback:function(o){e.$set(e.addReplacePosConfigForm,"startEcpm",o)},expression:"addReplacePosConfigForm.startEcpm"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"系统"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.os,callback:function(o){e.$set(e.addReplacePosConfigForm,"os",o)},expression:"addReplacePosConfigForm.os"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"策略名称"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.strategyName,callback:function(o){e.$set(e.addReplacePosConfigForm,"strategyName",o)},expression:"addReplacePosConfigForm.strategyName"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"产品"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.product,callback:function(o){e.$set(e.addReplacePosConfigForm,"product",o)},expression:"addReplacePosConfigForm.product"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"投放渠道"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.dsp,callback:function(o){e.$set(e.addReplacePosConfigForm,"dsp",o)},expression:"addReplacePosConfigForm.dsp"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"平台"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.platform,callback:function(o){e.$set(e.addReplacePosConfigForm,"platform",o)},expression:"addReplacePosConfigForm.platform"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"pvGap"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.pvGap,callback:function(o){e.$set(e.addReplacePosConfigForm,"pvGap",o)},expression:"addReplacePosConfigForm.pvGap"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"pvIncomeLimit"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.pvIncomeLimit,callback:function(o){e.$set(e.addReplacePosConfigForm,"pvIncomeLimit",o)},expression:"addReplacePosConfigForm.pvIncomeLimit"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"incomeGap"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.incomeGap,callback:function(o){e.$set(e.addReplacePosConfigForm,"incomeGap",o)},expression:"addReplacePosConfigForm.incomeGap"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"incomeLimit"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.incomeLimit,callback:function(o){e.$set(e.addReplacePosConfigForm,"incomeLimit",o)},expression:"addReplacePosConfigForm.incomeLimit"}})],1),e._v(" "),t("el-form-item",{attrs:{label:"totalIncome"}},[t("el-input",{staticClass:"width-100",model:{value:e.addReplacePosConfigForm.totalIncome,callback:function(o){e.$set(e.addReplacePosConfigForm,"totalIncome",o)},expression:"addReplacePosConfigForm.totalIncome"}})],1),e._v(" "),e.adIdListGdt.indexOf(e.addReplacePosConfigForm.adId)>-1?t("div",[t("el-form-item",{attrs:{label:"请求方式"}},[t("el-radio-group",{model:{value:e.addReplacePosConfigForm.biddingType,callback:function(o){e.$set(e.addReplacePosConfigForm,"biddingType",o)},expression:"addReplacePosConfigForm.biddingType"}},[t("el-radio",{attrs:{label:1}},[e._v("C2S")]),e._v(" "),t("el-radio",{attrs:{label:2}},[e._v("S2S")])],1)],1)],1):e._e()],1),e._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(o){e.isShowAddReplaceForm=!1}}},[e._v("取 消")]),e._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(o){return e.addReplacePosConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[t("el-form-item",{attrs:{label:"配置id"}},[t("el-input",{staticClass:"width-150",model:{value:e.queryParam.id,callback:function(o){e.$set(e.queryParam,"id",o)},expression:"queryParam.id"}})],1),e._v(" "),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getReplacePosConfigList}}),e._v(" "),t("el-button",{attrs:{type:"primary",round:""},on:{click:function(o){return e.addReplacePosConfigPre()}}},[e._v("新建广告位替换配置")])],1)],1)],1)],1),e._v(" "),t("el-divider",{attrs:{"content-position":"left"}},[e._v("广告位替换配置列表")]),e._v(" "),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-table",{staticStyle:{width:"100%"},attrs:{data:e.replacePosConfigList,stripe:"","highlight-current-row":""}},[t("el-table-column",{attrs:{label:"开关"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},on:{change:function(t){return e.changeConfigSwitchFlag(o.$index,o.row)}},model:{value:o.row.isEnabled,callback:function(t){e.$set(o.row,"isEnabled",t)},expression:"scope.row.isEnabled"}})]}}])}),e._v(" "),t("el-table-column",{attrs:{prop:"id",label:"Id"}}),e._v(" "),t("el-table-column",{attrs:{prop:"adPosType",label:"配置类型"}}),e._v(" "),t("el-table-column",{attrs:{prop:"os",label:"系统"}}),e._v(" "),t("el-table-column",{attrs:{prop:"strategyName",label:"策略名称"}}),e._v(" "),t("el-table-column",{attrs:{prop:"product",label:"产品"}}),e._v(" "),t("el-table-column",{attrs:{prop:"dsp",label:"投放渠道"}}),e._v(" "),t("el-table-column",{attrs:{label:"操作",width:"260"},scopedSlots:e._u([{key:"default",fn:function(o){return[t("el-button",{attrs:{size:"small"},on:{click:function(t){return e.updateReplacePosConfig(o.row)}}},[e._v("修改")]),e._v(" "),t("el-button",{attrs:{size:"small",type:"danger"},on:{click:function(t){return e.deleteReplacePosConfig(o.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),t("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}},403:function(e,o,t){var a=t(338);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);t(88)("0014f44e",a,!0)}});