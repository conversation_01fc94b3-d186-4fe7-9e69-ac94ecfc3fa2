webpackJsonp([32],{203:function(t,e,o){var i=o(87)(o(300),o(360),null,null);t.exports=i.exports},300:function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={data:function(){return{groupList:[],isShowConfigAddForm:!1,isShowConfigEditForm:!1,addForm:{exposure:0,clickA:0,clickB:0},editForm:{id:0,exposure:0,clickA:0,clickB:0}}},created:function(){this.loadAllConfig()},methods:{loadAllConfig:function(){var t=this,e={pageNo:1,pageSize:1e4};t.$axios.post("click/list",{},{params:e}).then(function(e){if(200===e.status){var o=e.data;if(0===o.ret){var i=o.data;t.groupList=i.items}else t.$message.error(o.data)}else t.$message.error("服务器异常！")})},showEditClickConfig:function(t){this.editForm.exposure=t.exposure,this.editForm.id=t.id,this.editForm.clickA=t.clickA,this.editForm.clickB=t.clickB,this.isShowConfigEditForm=!0},addClickConfig:function(){var t=this;t.$axios.post("click/addOne",t.addForm,{}).then(function(e){if(200===e.status){var o=e.data;0===o.ret?(t.$message.success("添加规则成功.."),t.isShowConfigAddForm=!1,t.loadAllConfig()):t.$message.error(o.data)}else t.$message.error("服务器异常！")})},editClickConfig:function(){var t=this;t.$axios.post("click/updateOne",t.editForm,{}).then(function(e){if(200===e.status){var o=e.data;0===o.ret?(t.$message.success("修改规则成功.."),t.isShowConfigEditForm=!1,t.loadAllConfig()):t.$message.error(o.data)}else t.$message.error("服务器异常！")})},deleteClickConfig:function(t){var e=this;this.$confirm("此操作将删除该配置, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var o=e,i={id:t.id};o.$axios.post("click/delete",{},{params:i}).then(function(t){if(200===t.status){var e=t.data;0===e.ret?(o.$message.success("删除规则成功.."),o.loadAllConfig()):o.$message.error(e.data)}else o.$message.error("服务器异常！")})}).catch(function(){e.$message({type:"info",message:"已取消删除"})})}}}},360:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"table"},[o("div",{staticClass:"crumbs"},[o("el-breadcrumb",{attrs:{separator:"/"}},[o("el-breadcrumb-item",[o("i",{staticClass:"el-icon-menu"}),t._v(" 配置管理")]),t._v(" "),o("el-breadcrumb-item",[t._v("广告点击率配置")])],1)],1),t._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form",{attrs:{size:"small",inline:""}},[o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){t.isShowConfigAddForm=!0}}},[t._v("新增配置")])],1)],1)],1)],1),t._v(" "),o("el-divider",{attrs:{"content-position":"left"}},[t._v("配置列表")]),t._v(" "),o("el-dialog",{attrs:{title:"新增配置",visible:t.isShowConfigAddForm},on:{"update:visible":function(e){t.isShowConfigAddForm=e}}},[o("el-form",{attrs:{model:t.addForm,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"曝光次数"}},[o("el-input",{model:{value:t.addForm.exposure,callback:function(e){t.$set(t.addForm,"exposure",e)},expression:"addForm.exposure"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"点击上限-A"}},[o("el-input",{model:{value:t.addForm.clickA,callback:function(e){t.$set(t.addForm,"clickA",e)},expression:"addForm.clickA"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"点击上限-B"}},[o("el-input",{model:{value:t.addForm.clickB,callback:function(e){t.$set(t.addForm,"clickB",e)},expression:"addForm.clickB"}})],1)],1),t._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.isShowConfigAddForm=!1}}},[t._v("取 消")]),t._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addClickConfig()}}},[t._v("确 定")])],1)],1),t._v(" "),o("el-dialog",{attrs:{title:"编辑配置",visible:t.isShowConfigEditForm},on:{"update:visible":function(e){t.isShowConfigEditForm=e}}},[o("el-form",{attrs:{model:t.editForm,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"曝光次数"}},[o("el-input",{model:{value:t.editForm.exposure,callback:function(e){t.$set(t.editForm,"exposure",e)},expression:"editForm.exposure"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"点击上限-A"}},[o("el-input",{model:{value:t.editForm.clickA,callback:function(e){t.$set(t.editForm,"clickA",e)},expression:"editForm.clickA"}})],1),t._v(" "),o("el-form-item",{attrs:{label:"点击上限-B"}},[o("el-input",{model:{value:t.editForm.clickB,callback:function(e){t.$set(t.editForm,"clickB",e)},expression:"editForm.clickB"}})],1)],1),t._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.isShowConfigEditForm=!1}}},[t._v("取 消")]),t._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.editClickConfig()}}},[t._v("确 定")])],1)],1),t._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:t.groupList,stripe:"","highlight-current-row":""}},[o("el-table-column",{attrs:{prop:"exposure",label:"曝光次数"}}),t._v(" "),o("el-table-column",{attrs:{prop:"clickA",label:"点击上限-A"}}),t._v(" "),o("el-table-column",{attrs:{prop:"rateA",label:"点击率-A"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                "+t._s(e.row.rateA)+"%\n                            ")]}}])}),t._v(" "),o("el-table-column",{attrs:{prop:"clickB",label:"点击上限-B"}}),t._v(" "),o("el-table-column",{attrs:{prop:"rateB",label:"点击率-B"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n                                "+t._s(e.row.rateB)+"%\n                            ")]}}])}),t._v(" "),o("el-table-column",{attrs:{label:"操作",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{attrs:{size:"small"},on:{click:function(o){return t.showEditClickConfig(e.row)}}},[t._v("修改")]),t._v(" "),o("el-button",{attrs:{size:"small",type:"danger"},on:{click:function(o){return t.deleteClickConfig(e.row)}}},[t._v("删除")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});