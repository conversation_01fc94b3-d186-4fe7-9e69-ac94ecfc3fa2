webpackJsonp([15],{216:function(e,t,o){o(411);var r=o(87)(o(313),o(391),null,null);e.exports=r.exports},313:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{pageNo:1,pageSize:10},listCountNum:0,exUserConfigList:[],adPosTypes:[],adIdList:[],editExUserConfigForm:{id:0,channels:"",ips:"",models:"",phones:"",videoLimit:0,withDrawRate:0,scoreRate:0,ocpcType:1,ocpcChannel:1,ruleLevel:0},addExUserConfigForm:{channels:"",ips:"",models:"",phones:"",videoLimit:0,withDrawRate:0,scoreRate:0,ocpcType:1,ocpcChannel:1,ruleLevel:0},isShowEditExUserConfigForm:!1,isShowAddExUserConfigForm:!1}},created:function(){this.getExUserConfigList()},methods:{getExUserConfigList:function(){var e=this;e.$axios.post("exUser/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var o=t.data;e.exUserConfigList=o.items,e.listCountNum=o.count}else e.$message.error("服务器异常！")})},saveExUserConfig:function(){var e=this;e.$refs.editExUserConfigForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.editExUserConfigForm.ocpcChannel=Number(e.editExUserConfigForm.ocpcChannel),e.editExUserConfigForm.ruleLevel=Number(e.editExUserConfigForm.ruleLevel),e.$axios.post("exUser/updateOne",e.editExUserConfigForm,{}).then(function(t){if(200===t.status){var o=t.data;0===o.ret?(e.isShowEditExUserConfigForm=!1,e.$message({message:"更新成功",type:"success"}),e.getExUserConfigList()):e.$message.error(o.data)}else e.$message.error("服务器异常！")})})},addExUserConfigPre:function(){this.clearExUserConfigAddForm(),this.isShowAddExUserConfigForm=!0},addExUserConfig:function(){var e=this;e.$refs.addExUserConfigForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("exUser/addOne",e.addExUserConfigForm,{}).then(function(t){if(200===t.status){var o=t.data;0===o.ret?(e.isShowAddExUserConfigForm=!1,e.$message({message:"新增配置成功",type:"success"}),e.getExUserConfigList()):e.$message.error(o.data)}else e.$message.error("服务器异常！")})})},clearExUserConfigAddForm:function(){this.addExUserConfigForm.channels="",this.addExUserConfigForm.ips="",this.addExUserConfigForm.models="",this.addExUserConfigForm.phones="",this.addExUserConfigForm.videoLimit=0,this.addExUserConfigForm.withDrawRate=0,this.addExUserConfigForm.scoreRate=0,this.addExUserConfigForm.ocpcType=1,this.addExUserConfigForm.ocpcChannel=1,this.addExUserConfigForm.ruleLevel=0},updateExUserConfigPre:function(e){this.editExUserConfigForm.id=e.id,this.editExUserConfigForm.channels=e.channel,this.editExUserConfigForm.ips=e.ip,this.editExUserConfigForm.models=e.model,this.editExUserConfigForm.phones=e.phone,this.editExUserConfigForm.videoLimit=e.videoLimit,this.editExUserConfigForm.withDrawRate=e.withdrawRate,this.editExUserConfigForm.ocpcChannel=e.ocpcChannel,this.editExUserConfigForm.scoreRate=e.scoreRate,this.editExUserConfigForm.ocpcType=e.ocpcType,this.editExUserConfigForm.ruleLevel=e.ruleLevel,this.isShowEditExUserConfigForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getExUserConfigList()},changeExUserConfigSwitchFlag:function(e,t){var o={};o.id=t.id,o.switchFlag=t.state;var r=this;r.$axios.post("exUser/switchFlag",{},{params:o}).then(function(e){if(200===e.status){0===e.data.ret?r.$message({message:"更新状态成功",type:"success"}):r.$message.error("更新状态失败")}else r.$message.error("服务器异常！")})}}}},346:function(e,t,o){t=e.exports=o(29)(),t.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},391:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"table"},[o("div",{staticClass:"crumbs"},[o("el-alert",{attrs:{title:"配置在【新建、修改】以后十分钟以内会应用到今日全量活跃用户,并不会立即生效",type:"info","show-icon":""}})],1),e._v(" "),o("div",{staticClass:"crumbs"},[o("el-alert",{attrs:{title:"配置项之间的关联关系是【且】,标签允许不填写，但是不能命中大部分用户！",type:"warning","show-icon":""}})],1),e._v(" "),o("div",{staticClass:"crumbs"},[o("el-breadcrumb",{attrs:{separator:"/"}},[o("el-breadcrumb-item",[o("i",{staticClass:"el-icon-menu"}),e._v("反作弊配置")]),e._v(" "),o("el-breadcrumb-item",[e._v("异常用户标签设置")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"编辑异常标签配置",visible:e.isShowEditExUserConfigForm},on:{"update:visible":function(t){e.isShowEditExUserConfigForm=t}}},[o("el-form",{ref:"editExUserConfigForm",attrs:{model:e.editExUserConfigForm,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"异常渠道",prop:"channels"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.channels,callback:function(t){e.$set(e.editExUserConfigForm,"channels",t)},expression:"editExUserConfigForm.channels"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常ip",prop:"ips"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.ips,callback:function(t){e.$set(e.editExUserConfigForm,"ips",t)},expression:"editExUserConfigForm.ips"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常厂商",prop:"phones"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.phones,callback:function(t){e.$set(e.editExUserConfigForm,"phones",t)},expression:"editExUserConfigForm.phones"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常机型",prop:"models"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.editExUserConfigForm.models,callback:function(t){e.$set(e.editExUserConfigForm,"models",t)},expression:"editExUserConfigForm.models"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"投放渠道",prop:"ocpcChannel"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.ocpcChannel,callback:function(t){e.$set(e.editExUserConfigForm,"ocpcChannel",t)},expression:"editExUserConfigForm.ocpcChannel"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("投放渠道")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("非投放渠道")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("全渠道")])],1)],1),e._v(" "),o("el-form-item",{attrs:{label:"OCPC人群",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.editExUserConfigForm.ocpcType,callback:function(t){e.$set(e.editExUserConfigForm,"ocpcType",t)},expression:"editExUserConfigForm.ocpcType"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("只对非投放用户生效")])],1)],1),e._v(" "),o("el-divider",[o("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),o("el-form-item",{attrs:{label:"曝光次数限制",prop:"videoLimit"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.editExUserConfigForm.videoLimit,callback:function(t){e.$set(e.editExUserConfigForm,"videoLimit",t)},expression:"editExUserConfigForm.videoLimit"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"提现比例",prop:"withDrawRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.editExUserConfigForm.withDrawRate,callback:function(t){e.$set(e.editExUserConfigForm,"withDrawRate",t)},expression:"editExUserConfigForm.withDrawRate"}}),e._v("%\n            ")],1),e._v(" "),o("el-form-item",{attrs:{label:"奖励发放比例",prop:"scoreRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.editExUserConfigForm.scoreRate,callback:function(t){e.$set(e.editExUserConfigForm,"scoreRate",t)},expression:"editExUserConfigForm.scoreRate"}}),e._v("%\n            ")],1),e._v(" "),o("el-form-item",{attrs:{label:"优先级",prop:"ruleLevel"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{type:"number",placeholder:"整数"},model:{value:e.editExUserConfigForm.ruleLevel,callback:function(t){e.$set(e.editExUserConfigForm,"ruleLevel",t)},expression:"editExUserConfigForm.ruleLevel"}})],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowEditExUserConfigForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveExUserConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"新建异常标签配置",visible:e.isShowAddExUserConfigForm},on:{"update:visible":function(t){e.isShowAddExUserConfigForm=t}}},[o("el-form",{ref:"addExUserConfigForm",attrs:{model:e.addExUserConfigForm,"label-width":"200px"}},[o("el-form-item",{attrs:{label:"异常渠道",prop:"channels"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.channels,callback:function(t){e.$set(e.addExUserConfigForm,"channels",t)},expression:"addExUserConfigForm.channels"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常ip",prop:"ips"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.ips,callback:function(t){e.$set(e.addExUserConfigForm,"ips",t)},expression:"addExUserConfigForm.ips"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常厂商",prop:"phones"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.phones,callback:function(t){e.$set(e.addExUserConfigForm,"phones",t)},expression:"addExUserConfigForm.phones"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"异常机型",prop:"models"}},[o("el-input",{attrs:{type:"textarea",rows:2,placeholder:"(英文,分割)"},model:{value:e.addExUserConfigForm.models,callback:function(t){e.$set(e.addExUserConfigForm,"models",t)},expression:"addExUserConfigForm.models"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"投放渠道",prop:"ocpcChannel"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.ocpcChannel,callback:function(t){e.$set(e.addExUserConfigForm,"ocpcChannel",t)},expression:"addExUserConfigForm.ocpcChannel"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("投放渠道")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("非投放渠道")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("全渠道")])],1)],1),e._v(" "),o("el-form-item",{attrs:{label:"OCPC人群",prop:"ocpcType"}},[o("el-radio-group",{model:{value:e.addExUserConfigForm.ocpcType,callback:function(t){e.$set(e.addExUserConfigForm,"ocpcType",t)},expression:"addExUserConfigForm.ocpcType"}},[o("el-radio-button",{attrs:{label:"1"}},[e._v("全量用户")]),e._v(" "),o("el-radio-button",{attrs:{label:"2"}},[e._v("只对投放用户生效")]),e._v(" "),o("el-radio-button",{attrs:{label:"3"}},[e._v("只对非投放用户生效")])],1)],1),e._v(" "),o("el-divider",[o("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),o("el-form-item",{attrs:{label:"曝光次数限制",prop:"videoLimit"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.addExUserConfigForm.videoLimit,callback:function(t){e.$set(e.addExUserConfigForm,"videoLimit",t)},expression:"addExUserConfigForm.videoLimit"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"提现比例",prop:"withDrawRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.addExUserConfigForm.withDrawRate,callback:function(t){e.$set(e.addExUserConfigForm,"withDrawRate",t)},expression:"addExUserConfigForm.withDrawRate"}}),e._v("%\n            ")],1),e._v(" "),o("el-form-item",{attrs:{label:"奖励发放比例",prop:"scoreRate"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{placeholder:"整数"},model:{value:e.addExUserConfigForm.scoreRate,callback:function(t){e.$set(e.addExUserConfigForm,"scoreRate",t)},expression:"addExUserConfigForm.scoreRate"}}),e._v("%\n            ")],1),e._v(" "),o("el-form-item",{attrs:{label:"优先级",prop:"ruleLevel"}},[o("el-input",{staticClass:"inline-input width-100",attrs:{type:"number",placeholder:"整数"},model:{value:e.addExUserConfigForm.ruleLevel,callback:function(t){e.$set(e.addExUserConfigForm,"ruleLevel",t)},expression:"addExUserConfigForm.ruleLevel"}})],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowAddExUserConfigForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addExUserConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addExUserConfigPre()}}},[e._v("新建用户异常标签配置")])],1)],1)],1)],1),e._v(" "),o("el-divider",{attrs:{"content-position":"left"}},[e._v("异常标签配置列表")]),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:e.exUserConfigList,stripe:"","highlight-current-row":""}},[o("el-table-column",{attrs:{label:"开关"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},on:{change:function(o){return e.changeExUserConfigSwitchFlag(t.$index,t.row)}},model:{value:t.row.state,callback:function(o){e.$set(t.row,"state",o)},expression:"scope.row.state"}})]}}])}),e._v(" "),o("el-table-column",{attrs:{prop:"id",label:"Id"}}),e._v(" "),o("el-table-column",{attrs:{prop:"channel",label:"限制渠道"}}),e._v(" "),o("el-table-column",{attrs:{prop:"ip",label:"限制ip"}}),e._v(" "),o("el-table-column",{attrs:{prop:"model",label:"限制机型"}}),e._v(" "),o("el-table-column",{attrs:{prop:"videoLimit",label:"限制视频曝光次数"}}),e._v(" "),o("el-table-column",{attrs:{prop:"createTime",label:"创建时间"}}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"small"},on:{click:function(o){return e.updateExUserConfigPre(t.row)}}},[e._v("修改")])]}}])})],1),e._v(" "),o("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}},411:function(e,t,o){var r=o(346);"string"==typeof r&&(r=[[e.i,r,""]]),r.locals&&(e.exports=r.locals);o(88)("49361d9d",r,!0)}});