webpackJsonp([29],{209:function(t,e,a){var r=a(87)(a(306),a(363),null,null);t.exports=r.exports},306:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={data:function(){return{adTypeList:[{code:"",name:""}]}},created:function(){this.getAdTypeList()},methods:{getAdTypeList:function(){var t=this;t.$axios.post("adType/list",{},{}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?t.adTypeList=a.data:t.$message.error("初始化数据失败")}else t.$message.error("服务器异常！")})}}}},363:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),t._v(" 广告管理")]),t._v(" "),a("el-breadcrumb-item",[t._v("广告类型管理")])],1)],1),t._v(" "),a("br"),t._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[t._v("广告类型列表")]),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.adTypeList,stripe:"",height:"700","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"code",label:"广告类型编码"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",label:"广告类型名称"}})],1)],1)],1)],1)},staticRenderFns:[]}}});