webpackJsonp([19],{205:function(t,e,i){var s=i(87)(i(302),i(365),null,null);t.exports=s.exports},302:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i(398);i.n(s);e.default={inject:["reload"],directives:{handle:s.HandleDirective},data:function(){return{lastPageObj:JSON.parse(this.$route.query.target),items:[],adTypeList:[],defaultAdConfigTemplateList:[],adTypeBasicList:[],queryParam:{configId:JSON.parse(this.$route.query.target).id},thisPagePosType:JSON.parse(this.$route.query.target).posType,isShowFullScreenPart:!1,isShowDefaultAdConfigForm:!1,isShowAddPosForm:!1,isShowEditPosForm:!1,listCountNum:0,addPosForm:{name:"",state:"",price:0,index:"",configId:"",subFilterVoList:[]},editPosForm:{id:"",name:"",state:"",price:0,index:"",configId:"",subFilterVoList:[]},tempPosFormPrice:0,tempPosFormIndex:0,sortNodeFlag:!0}},components:{SlickItem:s.SlickItem,SlickList:s.SlickList},created:function(){this.queryAdPosTypeList(),this.queryTypeBasicList(),this.queryDetailConfig()},methods:{convertThirdName:function(t){switch(t){case 1:return"开屏";case 2:return"静态图";case 3:return"插屏";case 4:case 6:return"视频";case 5:return"Banner";default:return""}},queryDetailConfig:function(){var t=this;t.$axios.post("config/detail/queryById",{},{params:t.queryParam}).then(function(e){if(200===e.status){var i=e.data;if(0===i.ret){var s=i.data;t.items=s.configNewFilterVoList,t.defaultAdConfigTemplateList=s.defaultAdTypeList}else t.$message.error(i.data)}else t.$message.error("服务器异常！")})},queryAdPosTypeList:function(){var t=this;t.$axios.get("config/getAdPosType").then(function(e){if(200===e.status){var i=e.data;0===i.code?t.adTypeList=i.result:t.$message.error("加载数据失败...")}else t.$message.error("服务器异常！")})},queryTypeBasicList:function(){var t=this;t.$axios.get("config/getAdTypeDist").then(function(e){if(200===e.status){var i=e.data;0===i.code?t.adTypeBasicList=i.result:t.$message.error("加载数据失败...")}else t.$message.error("服务器异常！")})},moveEnd:function(t){console.log("MoveEnd:",t);var e=this.items[t.newIndex];this.items[t.oldIndex].price!==e.price?(this.reload(),this.sortNodeFlag=!1):this.sortNodeFlag=!0},getChangeList:function(t){if(!this.sortNodeFlag)return this.$message.error("价格不同不能调整瀑布流顺序..."),!1;console.log("NewList:",t);for(var e={configId:this.queryParam.configId,adConfigCardList:[]},i=[],s=t,o=0;o<s.length;o++){var r={index:o,id:s[o].id};i.push(r)}e.adConfigCardList=i;var n=this,a=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});n.$axios.post("config/detail/sortNode",e,{}).then(function(t){if(200===t.status){a.close();var e=t.data;0===e.ret?(n.$message({message:"瀑布流位置调整成功..",type:"success"}),n.queryDetailConfig()):n.$message.error(e.data)}else a.close(),n.$message.error("服务器异常！")})},deleteNodeCard:function(t){console.log(t);var e={id:t.id},i=this;i.$axios.post("config/detail/delNode",{},{params:e}).then(function(t){if(200===t.status){var e=t.data;0===e.ret?(i.$message({message:"删除瀑布流节点成功..",type:"success"}),i.isShowAddPosForm=!1,i.queryDetailConfig()):i.$message.error(e.data)}else i.$message.error("服务器异常！")})},editNodeCard:function(t,e){this.isShowEditPosForm=!0,this.editPosForm={id:t.id,state:t.switchFlag,price:t.price,configId:this.queryParam.configId,subFilterVoList:t.adTypeList},this.tempPosFormPrice=t.price,this.tempPosFormIndex=e},cancelNodeCard:function(){this.isShowEditPosForm=!1,this.editPosForm={id:"",name:"",state:"",price:0,index:"",configId:"",subFilterVoList:[]},this.reload()},delSubFilterNode:function(t,e){1===t?this.addPosForm.subFilterVoList.splice(e,1):2===t&&this.editPosForm.subFilterVoList.splice(e,1)},addSubFilterNode:function(t){var e={adType:"",ret:""};1===t?this.addPosForm.subFilterVoList.push(e):2===t&&this.editPosForm.subFilterVoList.push(e)},saveAdPos:function(){var t=this,e=/^[1-9][0-9]*([.][0-9]+)?$/,i=t.addPosForm.price;if(void 0!==i&&null!==i&&""!==i&&!e.test(i))return this.$message.error("价格填写异常！请重新填写"),!1;var s=t.items,o=0;if(s.length>0)for(var r=0;r<s.length&&s[r].price>t.addPosForm.price;r++)o++;if(0===t.addPosForm.subFilterVoList.length)return this.$message.error("所选广告位不能为空"),!1;for(var n=0,a=0;a<t.addPosForm.subFilterVoList.length;a++){var l=Number(t.addPosForm.subFilterVoList[a].ret);if(l<0||l>100)return this.$message.error("比例的范围在[0-100],请重新填写！"),!1;if(n+=l,null===t.addPosForm.subFilterVoList[a].adType||""===t.addPosForm.subFilterVoList[a].adType||0===Number(t.addPosForm.subFilterVoList[a].adType))return this.$message.error("广告为不能为空！"),!1}if(100!==n)return this.$message.error("广告位分配比率总和不为100！请重新分配"),!1;console.log("NEW CARD INSERT:",o);var d={configId:t.lastPageObj.id,index:o,state:t.addPosForm.state,price:i,subFilterVoList:t.addPosForm.subFilterVoList};t.$axios.post("config/detail/addNew",d,{}).then(function(e){if(200===e.status){var i=e.data;0===i.ret?(t.$message({message:"新增广告位成功..",type:"success"}),t.isShowAddPosForm=!1,t.queryDetailConfig()):t.$message.error(i.data)}else t.$message.error("服务器异常！")})},editAdPos:function(){var t=this,e=/^[1-9][0-9]*([.][0-9]+)?$/,i=t.editPosForm.price;if(void 0!==i&&null!==i&&""!==i&&!e.test(i))return this.$message.error("价格填写异常！请重新填写"),!1;var s=t.items,o=0,r=!0;if(this.tempPosFormPrice===i&&(console.log("价格未改动，不触发分层排序"),o=this.tempPosFormIndex,r=!1),s.length>0&&r)for(var n=0;n<s.length&&s[n].price>t.addPosForm.price;n++)o++;if(0===t.editPosForm.subFilterVoList.length)return this.$message.error("所选广告位不能为空"),!1;for(var a=0,l=0;l<t.editPosForm.subFilterVoList.length;l++){var d=Number(t.editPosForm.subFilterVoList[l].ret);if(d<0||d>100)return this.$message.error("比例的范围在[0-100],请重新填写！"),!1;if(a+=d,null===t.editPosForm.subFilterVoList[l].adType||""===t.editPosForm.subFilterVoList[l].adType||0===Number(t.editPosForm.subFilterVoList[l].adType))return this.$message.error("广告为不能为空！"),!1}if(100!==a)return this.$message.error("广告位分配比率总和不为100！请重新分配"),!1;var c={id:t.editPosForm.id,configId:t.lastPageObj.id,index:o,state:t.editPosForm.state,price:i,subFilterVoList:t.editPosForm.subFilterVoList},h=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});t.$axios.post("config/detail/editConfig",c,{}).then(function(e){if(200===e.status){h.close();var i=e.data;0===i.ret?(t.$message({message:"编辑广告位成功..",type:"success"}),t.isShowEditPosForm=!1,t.queryDetailConfig()):t.$message.error(i.data)}else h.close(),t.$message.error("服务器异常！")})},saveDefaultConfig:function(){for(var t=this,e={configId:t.lastPageObj.id,defaultAdTypeList:[]},i=[],s=0;s<t.defaultAdConfigTemplateList.length;s++){if(null===t.defaultAdConfigTemplateList[s].adType||""===t.defaultAdConfigTemplateList[s].adType||0===Number(t.defaultAdConfigTemplateList[s].adType))return this.$message.error("广告为不能为空！"),!1;if(null===t.defaultAdConfigTemplateList[s].defaultAdType||""===t.defaultAdConfigTemplateList[s].defaultAdType||0===Number(t.defaultAdConfigTemplateList[s].defaultAdType))return this.$message.error("广告为不能为空！"),!1;var o={adType:t.defaultAdConfigTemplateList[s].adType,defaultAdType:t.defaultAdConfigTemplateList[s].defaultAdType};i.push(o)}e.defaultAdTypeList=i,t.$axios.post("config/detail/editDefaultConfig",e,{}).then(function(e){if(200===e.status){var i=e.data;0===i.ret?(t.$message({message:"配置打底成功",type:"success"}),t.isShowDefaultAdConfigForm=!1,t.queryDetailConfig()):t.$message.error(i.data)}else t.$message.error("服务器异常！")})},addDefaultConfigNode:function(){var t={adType:"",defaultAdType:""};this.defaultAdConfigTemplateList.push(t)},delDefaultConfigNode:function(t){this.defaultAdConfigTemplateList.splice(t,1)},changeSwitchFlagWall:function(t){var e={};e.id=t.id,e.state=t.switchFlag;var i=this;i.$axios.post("config/detail/switchFlag",{},{params:e}).then(function(t){if(200===t.status){0===t.data.ret?i.$message({message:"更新状态成功",type:"success"}):i.$message.error("更新状态失败")}else i.$message.error("服务器异常！")})},backToALL:function(){this.$router.push({path:"/adConfig"})}}}},365:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"table"},[i("SlickList",{attrs:{lockToContainerEdges:!0,lockAxis:"y",hideSortableGhost:!0,useDragHandle:!0},on:{"sort-end":t.moveEnd,input:t.getChangeList},model:{value:t.items,callback:function(e){t.items=e},expression:"items"}},[i("div",{staticClass:"crumbs"},[i("el-breadcrumb",{attrs:{separator:"/"}},[i("el-breadcrumb-item",[i("i",{staticClass:"el-icon-menu"}),t._v(" 策略配置")]),t._v(" "),i("el-breadcrumb-item",[t._v("策略拖拽式配置")])],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"添加广告位",visible:t.isShowAddPosForm},on:{"update:visible":function(e){t.isShowAddPosForm=e}}},[i("el-form",{ref:"form",attrs:{model:t.addPosForm,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"开关"}},[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.addPosForm.state,callback:function(e){t.$set(t.addPosForm,"state",e)},expression:"addPosForm.state"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"价格"}},[i("el-input",{staticClass:"width-150",model:{value:t.addPosForm.price,callback:function(e){t.$set(t.addPosForm,"price",e)},expression:"addPosForm.price"}})],1),t._v(" "),t._l(t.addPosForm.subFilterVoList,function(e,s){return i("div",[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e}},[i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{attrs:{placeholder:"广告类型",filterable:""},model:{value:e.adType,callback:function(i){t.$set(e,"adType",i)},expression:"v.adType"}},[i("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),t._l(t.adTypeBasicList.filter(function(e){return t.convertThirdName(t.thisPagePosType)===e.subTypeName}),function(t){return i("el-option",{attrs:{label:t.typeName,value:t.adType}})})],2)],1),t._v(" "),i("el-form-item",{attrs:{label:"比例"}},[i("el-input",{model:{value:e.ret,callback:function(i){t.$set(e,"ret",i)},expression:"v.ret"}})],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delSubFilterNode(1,s)}}},[t._v("删除")])],1)],1)],1)}),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addSubFilterNode(1)}}},[t._v("添加广告类型")])],2),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.isShowAddPosForm=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveAdPos()}}},[t._v("确 定")])],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"编辑广告位",visible:t.isShowEditPosForm},on:{"update:visible":function(e){t.isShowEditPosForm=e}}},[i("el-form",{ref:"form",attrs:{model:t.editPosForm,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"开关"}},[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.editPosForm.state,callback:function(e){t.$set(t.editPosForm,"state",e)},expression:"editPosForm.state"}})],1),t._v(" "),i("el-form-item",{attrs:{label:"价格"}},[i("el-input",{staticClass:"width-150",model:{value:t.editPosForm.price,callback:function(e){t.$set(t.editPosForm,"price",e)},expression:"editPosForm.price"}})],1),t._v(" "),t._l(t.editPosForm.subFilterVoList,function(e,s){return i("div",[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e}},[i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{attrs:{placeholder:"广告类型",filterable:""},model:{value:e.adType,callback:function(i){t.$set(e,"adType",i)},expression:"v.adType"}},[i("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),t._l(t.adTypeBasicList.filter(function(e){return t.convertThirdName(t.thisPagePosType)===e.subTypeName}),function(t){return i("el-option",{attrs:{label:t.typeName,value:t.adType}})})],2)],1),t._v(" "),i("el-form-item",{attrs:{label:"比例"}},[i("el-input",{model:{value:e.ret,callback:function(i){t.$set(e,"ret",i)},expression:"v.ret"}})],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delSubFilterNode(2,s)}}},[t._v("删除")])],1)],1)],1)}),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addSubFilterNode(2)}}},[t._v("添加广告类型")])],2),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){return t.cancelNodeCard()}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.editAdPos()}}},[t._v("确 定")])],1)],1),t._v(" "),i("el-dialog",{attrs:{title:"打底设置",visible:t.isShowDefaultAdConfigForm},on:{"update:visible":function(e){t.isShowDefaultAdConfigForm=e}}},[t._l(t.defaultAdConfigTemplateList,function(e,s){return i("div",[i("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:e}},[i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{attrs:{placeholder:"广告类型",filterable:""},model:{value:e.adType,callback:function(i){t.$set(e,"adType",i)},expression:"v.adType"}},[i("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),t._l(t.adTypeBasicList.filter(function(e){return t.convertThirdName(t.thisPagePosType)===e.subTypeName}),function(t){return i("el-option",{attrs:{label:t.typeName,value:t.adType}})})],2)],1),t._v(" "),i("el-form-item",{attrs:{label:"对应打底"}},[i("el-select",{attrs:{placeholder:"对应打底",filterable:""},model:{value:e.defaultAdType,callback:function(i){t.$set(e,"defaultAdType",i)},expression:"v.defaultAdType"}},[i("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),t._l(t.adTypeBasicList.filter(function(e){return t.convertThirdName(t.thisPagePosType)===e.subTypeName}),function(t){return i("el-option",{attrs:{label:t.typeName,value:t.adType}})})],2)],1),t._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.delDefaultConfigNode(s)}}},[t._v("删除打底")])],1)],1)],1)}),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addDefaultConfigNode()}}},[t._v("添加打底")]),t._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(e){t.isShowDefaultAdConfigForm=!1}}},[t._v("取 消")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.saveDefaultConfig()}}},[t._v("确 定")])],1)],2),t._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form",{attrs:{model:t.queryParam,size:"small",inline:""}},[i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:t.queryParam.adType,callback:function(e){t.$set(t.queryParam,"adType",e)},expression:"queryParam.adType"}},[i("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),t._l(t.adTypeList,function(t){return i("el-option",{attrs:{label:t.label,value:t.value}})})],2)],1),t._v(" "),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){t.isShowDefaultAdConfigForm=!0}}},[t._v("打底设置")]),t._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(e){t.isShowAddPosForm=!0}}},[t._v("添加广告位")]),t._v(" "),i("el-button",{on:{click:function(e){return t.backToALL()}}},[t._v("返回上一页")])],1)],1)],1)],1),t._v(" "),i("el-divider",[i("i",{staticClass:"el-icon-setting"})]),t._v(" "),t._l(t.items,function(e,s){return i("SlickItem",{key:e,attrs:{index:s}},[i("el-card",{staticClass:"box-card",staticStyle:{"margin-top":"10px"},style:e.disableSort?"background-color: #cecece":"background-color: #f5f5f5",attrs:{shadow:"hover"}},[i("div",{staticClass:"text item"},[i("el-row",{attrs:{span:24}},[i("el-col",{attrs:{span:2}},[i("div",{directives:[{name:"handle",rawName:"v-handle"}],staticClass:"grid-content bg-purple"},[i("i",{staticClass:"el-icon-s-fold"}),t._v(t._s(s))])]),t._v(" "),i("el-col",{attrs:{span:2}},[i("div",{staticClass:"grid-content bg-purple"},[t._v("ID:"+t._s(e.id))])]),t._v(" "),i("el-col",{attrs:{span:2}},[i("div",{staticClass:"grid-content bg-purple"},[t._v("\n                                开关:"),i("el-switch",{attrs:{"active-value":1,"inactive-value":0,"active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(i){return t.changeSwitchFlagWall(e)}},model:{value:e.switchFlag,callback:function(i){t.$set(e,"switchFlag",i)},expression:"item.switchFlag"}})],1)]),t._v(" "),i("el-col",{attrs:{span:8}},t._l(e.adTypeList,function(s,o){return i("span",[t._v(t._s(s.adTypeName)+"\n                                "),o<e.adTypeList.length-1?i("span",[t._v("/")]):t._e()])}),0),t._v(" "),i("el-col",{attrs:{span:4}},[i("div",{staticClass:"grid-content bg-purple"},[t._v("\n                                占比:"),t._l(e.adTypeList,function(s,o){return i("span",[t._v(t._s(s.ret)+"\n                                "),o<e.adTypeList.length-1?i("span",[t._v("/")]):t._e()])})],2)]),t._v(" "),i("el-col",{attrs:{span:4}},[i("div",{staticClass:"grid-content bg-purple"},[t._v("价格:"+t._s(e.price))])]),t._v(" "),i("el-col",{attrs:{span:2}},[i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{stype:"primary",size:"mini",icon:"el-icon-edit",circle:""},on:{click:function(i){return t.editNodeCard(e,s)}}}),t._v(" "),i("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete",circle:""},on:{click:function(i){return t.deleteNodeCard(e)}}})],1)])],1)],1)])],1)})],2)],1)},staticRenderFns:[]}},398:function(t,e,i){!function(t,i){i(e)}(0,function(t){"use strict";function e(t,e,i){var s=t.slice(0);if(i>=s.length)for(var o=i-s.length;1+o--;)s.push(void 0);return s.splice(i,0,s.splice(e,1)[0]),s}function i(t,e){for(;t;){if(e(t))return t;t=t.parentNode}}function s(t,e,i){return i<t?t:i>e?e:i}function o(t){return"px"===t.substr(-2)?parseFloat(t):0}function r(t){var e=window.getComputedStyle(t);return{top:o(e.marginTop),right:o(e.marginRight),bottom:o(e.marginBottom),left:o(e.marginLeft)}}function n(t,e){return{name:t,mixins:[e],props:{tag:{type:String,default:"div"}},render:function(t){return t(this.tag,this.$slots.default)}}}var a={inject:["manager"],props:{index:{type:Number,required:!0},collection:{type:[String,Number],default:"default"},disabled:{type:Boolean,default:!1}},mounted:function(){var t=this.$props,e=t.collection,i=t.disabled,s=t.index;i||this.setDraggable(e,s)},watch:{index:function(t){this.$el&&this.$el.sortableInfo&&(this.$el.sortableInfo.index=t)},disabled:function(t){t?this.removeDraggable(this.collection):this.setDraggable(this.collection,this.index)},collection:function(t,e){this.removeDraggable(e),this.setDraggable(t,this.index)}},beforeDestroy:function(){var t=this.collection;this.disabled||this.removeDraggable(t)},methods:{setDraggable:function(t,e){var i=this.$el;i.sortableInfo={index:e,collection:t,manager:this.manager},this.ref={node:i},this.manager.add(t,this.ref)},removeDraggable:function(t){this.manager.remove(t,this.ref)}}},l=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},d=function(){function t(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(e,i,s){return i&&t(e.prototype,i),s&&t(e,s),e}}(),c=function(){function t(t,e){var i=[],s=!0,o=!1,r=void 0;try{for(var n,a=t[Symbol.iterator]();!(s=(n=a.next()).done)&&(i.push(n.value),!e||i.length!==e);s=!0);}catch(t){o=!0,r=t}finally{try{!s&&a.return&&a.return()}finally{if(o)throw r}}return i}return function(e,i){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return t(e,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),h=function(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)},f=function(){function t(){l(this,t),this.refs={}}return d(t,[{key:"add",value:function(t,e){this.refs[t]||(this.refs[t]=[]),this.refs[t].push(e)}},{key:"remove",value:function(t,e){var i=this.getIndex(t,e);-1!==i&&this.refs[t].splice(i,1)}},{key:"isActive",value:function(){return this.active}},{key:"getActive",value:function(){var t=this;return this.refs[this.active.collection].find(function(e){return e.node.sortableInfo.index==t.active.index})}},{key:"getIndex",value:function(t,e){return this.refs[t].indexOf(e)}},{key:"getOrderedRefs",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.active.collection;return this.refs[t].sort(function(t,e){return t.node.sortableInfo.index-e.node.sortableInfo.index})}}]),t}(),u={start:["touchstart","mousedown"],move:["touchmove","mousemove"],end:["touchend","touchcancel","mouseup"]},p=function(){if("undefined"==typeof window||"undefined"==typeof document)return"";var t=window.getComputedStyle(document.documentElement,"")||["-moz-hidden-iframe"],e=(Array.prototype.slice.call(t).join("").match(/-(moz|webkit|ms)-/)||""===t.OLink&&["","o"])[1];switch(e){case"ms":return"ms";default:return e&&e.length?e[0].toUpperCase()+e.substr(1):""}}(),m={data:function(){return{sorting:!1,sortingIndex:null,manager:new f,events:{start:this.handleStart,move:this.handleMove,end:this.handleEnd}}},props:{value:{type:Array,required:!0},axis:{type:String,default:"y"},distance:{type:Number,default:0},pressDelay:{type:Number,default:0},pressThreshold:{type:Number,default:5},useDragHandle:{type:Boolean,default:!1},useWindowAsScrollContainer:{type:Boolean,default:!1},hideSortableGhost:{type:Boolean,default:!0},lockToContainerEdges:{type:Boolean,default:!1},lockOffset:{type:[String,Number,Array],default:"50%"},transitionDuration:{type:Number,default:300},appendTo:{type:String,default:"body"},draggedSettlingDuration:{type:Number,default:null},lockAxis:String,helperClass:String,contentWindow:Object,shouldCancelStart:{type:Function,default:function(t){return-1!==["input","textarea","select","option","button"].indexOf(t.target.tagName.toLowerCase())}},getHelperDimensions:{type:Function,default:function(t){var e=t.node;return{width:e.offsetWidth,height:e.offsetHeight}}}},provide:function(){return{manager:this.manager}},mounted:function(){var t=this;this.container=this.$el,this.document=this.container.ownerDocument||document,this._window=this.contentWindow||window,this.scrollContainer=this.useWindowAsScrollContainer?this.document.body:this.container;for(var e in this.events)!function(e){t.events.hasOwnProperty(e)&&u[e].forEach(function(i){return t.container.addEventListener(i,t.events[e],{passive:!0})})}(e)},beforeDestroy:function(){var t=this;for(var e in this.events)!function(e){t.events.hasOwnProperty(e)&&u[e].forEach(function(i){return t.container.removeEventListener(i,t.events[e])})}(e)},methods:{handleStart:function(t){var e=this,s=this.$props,o=s.distance,r=s.shouldCancelStart;if(2===t.button||r(t))return!1;this._touched=!0,this._pos=this.getOffset(t);var n=i(t.target,function(t){return null!=t.sortableInfo});if(n&&n.sortableInfo&&this.nodeIsChild(n)&&!this.sorting){var a=this.$props.useDragHandle,l=n.sortableInfo,d=l.index,c=l.collection;if(a&&!i(t.target,function(t){return null!=t.sortableHandle}))return;this.manager.active={index:d,collection:c},"a"===t.target.tagName.toLowerCase()&&t.preventDefault(),o||(0===this.$props.pressDelay?this.handlePress(t):this.pressTimer=setTimeout(function(){return e.handlePress(t)},this.$props.pressDelay))}},nodeIsChild:function(t){return t.sortableInfo.manager===this.manager},handleMove:function(t){var e=this.$props,i=e.distance,s=e.pressThreshold;if(!this.sorting&&this._touched){var o=this.getOffset(t);this._delta={x:this._pos.x-o.x,y:this._pos.y-o.y};var r=Math.abs(this._delta.x)+Math.abs(this._delta.y);i||s&&!(s&&r>=s)?i&&r>=i&&this.manager.isActive()&&this.handlePress(t):(clearTimeout(this.cancelTimer),this.cancelTimer=setTimeout(this.cancel,0))}},handleEnd:function(){var t=this.$props.distance;this._touched=!1,t||this.cancel()},cancel:function(){this.sorting||(clearTimeout(this.pressTimer),this.manager.active=null)},handlePress:function(t){var e=this;t.stopPropagation();var i=this.manager.getActive();if(i){var s=this.$props,o=s.axis,n=s.getHelperDimensions,a=s.helperClass,l=s.hideSortableGhost,d=s.useWindowAsScrollContainer,c=s.appendTo,f=i.node,p=i.collection,m=f.sortableInfo.index,g=r(f),v=this.container.getBoundingClientRect(),y=n({index:m,node:f,collection:p});this.node=f,this.margin=g,this.width=y.width,this.height=y.height,this.marginOffset={x:this.margin.left+this.margin.right,y:Math.max(this.margin.top,this.margin.bottom)},this.boundingClientRect=f.getBoundingClientRect(),this.containerBoundingRect=v,this.index=m,this.newIndex=m,this._axis={x:o.indexOf("x")>=0,y:o.indexOf("y")>=0},this.offsetEdge=this.getEdgeOffset(f),this.initialOffset=this.getOffset(t),this.initialScroll={top:this.scrollContainer.scrollTop,left:this.scrollContainer.scrollLeft},this.initialWindowScroll={top:window.pageYOffset,left:window.pageXOffset};var b=f.querySelectorAll("input, textarea, select"),x=f.cloneNode(!0);if([].concat(h(x.querySelectorAll("input, textarea, select"))).forEach(function(t,e){"file"!==t.type&&b[e]&&(t.value=b[e].value)}),this.helper=this.document.querySelector(c).appendChild(x),this.helper.style.position="fixed",this.helper.style.top=this.boundingClientRect.top-g.top+"px",this.helper.style.left=this.boundingClientRect.left-g.left+"px",this.helper.style.width=this.width+"px",this.helper.style.height=this.height+"px",this.helper.style.boxSizing="border-box",this.helper.style.pointerEvents="none",l&&(this.sortableGhost=f,f.style.visibility="hidden",f.style.opacity=0),this.translate={},this.minTranslate={},this.maxTranslate={},this._axis.x&&(this.minTranslate.x=(d?0:v.left)-this.boundingClientRect.left-this.width/2,this.maxTranslate.x=(d?this._window.innerWidth:v.left+v.width)-this.boundingClientRect.left-this.width/2),this._axis.y&&(this.minTranslate.y=(d?0:v.top)-this.boundingClientRect.top-this.height/2,this.maxTranslate.y=(d?this._window.innerHeight:v.top+v.height)-this.boundingClientRect.top-this.height/2),a){var w;(w=this.helper.classList).add.apply(w,h(a.split(" ")))}this.listenerNode=t.touches?f:this._window,u.move.forEach(function(t){return e.listenerNode.addEventListener(t,e.handleSortMove,!1)}),u.end.forEach(function(t){return e.listenerNode.addEventListener(t,e.handleSortEnd,!1)}),this.sorting=!0,this.sortingIndex=m,this.$emit("sort-start",{event:t,node:f,index:m,collection:p})}},handleSortMove:function(t){t.preventDefault(),this.updatePosition(t),this.animateNodes(),this.autoscroll(),this.$emit("sort-move",{event:t})},handleSortEnd:function(t){var i=this,s=this.manager.active.collection;this.listenerNode&&(u.move.forEach(function(t){return i.listenerNode.removeEventListener(t,i.handleSortMove)}),u.end.forEach(function(t){return i.listenerNode.removeEventListener(t,i.handleSortEnd)}));var o=this.manager.refs[s],r=function(){i.helper.parentNode.removeChild(i.helper),i.hideSortableGhost&&i.sortableGhost&&(i.sortableGhost.style.visibility="",i.sortableGhost.style.opacity="");for(var r=0,n=o.length;r<n;r++){var a=o[r],l=a.node;a.edgeOffset=null,l.style[p+"Transform"]="",l.style[p+"TransitionDuration"]=""}clearInterval(i.autoscrollInterval),i.autoscrollInterval=null,i.manager.active=null,i.sorting=!1,i.sortingIndex=null,i.$emit("sort-end",{event:t,oldIndex:i.index,newIndex:i.newIndex,collection:s}),i.$emit("input",e(i.value,i.index,i.newIndex)),i._touched=!1};this.$props.transitionDuration||this.$props.draggedSettlingDuration?this.transitionHelperIntoPlace(o).then(function(){return r()}):r()},transitionHelperIntoPlace:function(t){var e=this;if(0===this.$props.draggedSettlingDuration||0===t.length)return Promise.resolve();var i={left:this.scrollContainer.scrollLeft-this.initialScroll.left,top:this.scrollContainer.scrollTop-this.initialScroll.top},s=t[this.index].node,o=t[this.newIndex].node,r=-i.left;this.translate&&this.translate.x>0?r+=o.offsetLeft+o.offsetWidth-(s.offsetLeft+s.offsetWidth):r+=o.offsetLeft-s.offsetLeft;var n=-i.top;this.translate&&this.translate.y>0?n+=o.offsetTop+o.offsetHeight-(s.offsetTop+s.offsetHeight):n+=o.offsetTop-s.offsetTop;var a=null!==this.$props.draggedSettlingDuration?this.$props.draggedSettlingDuration:this.$props.transitionDuration;return this.helper.style[p+"Transform"]="translate3d("+r+"px,"+n+"px, 0)",this.helper.style[p+"TransitionDuration"]=a+"ms",new Promise(function(t){var i=function(i){i&&"transform"!==i.propertyName||(clearTimeout(s),e.helper.style[p+"Transform"]="",e.helper.style[p+"TransitionDuration"]="",t())},s=setTimeout(i,a+10);e.helper.addEventListener("transitionend",i,!1)})},getEdgeOffset:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{top:0,left:0};if(t){var i={top:e.top+t.offsetTop,left:e.left+t.offsetLeft};return t.parentNode!==this.container?this.getEdgeOffset(t.parentNode,i):i}},getOffset:function(t){var e=t.touches?t.touches[0]:t;return{x:e.pageX,y:e.pageY}},getLockPixelOffsets:function(){var t=this.$props.lockOffset;if(Array.isArray(this.lockOffset)||(t=[t,t]),2!==t.length)throw new Error("lockOffset prop of SortableContainer should be a single value or an array of exactly two values. Given "+t);var e=t,i=c(e,2),s=i[0],o=i[1];return[this.getLockPixelOffset(s),this.getLockPixelOffset(o)]},getLockPixelOffset:function(t){var e=t,i=t,s="px";if("string"==typeof t){var o=/^[+-]?\d*(?:\.\d*)?(px|%)$/.exec(t);if(null===o)throw new Error('lockOffset value should be a number or a string of a number followed by "px" or "%". Given '+t);e=i=parseFloat(t),s=o[1]}if(!isFinite(e)||!isFinite(i))throw new Error("lockOffset value should be a finite. Given "+t);return"%"===s&&(e=e*this.width/100,i=i*this.height/100),{x:e,y:i}},updatePosition:function(t){var e=this.$props,i=e.lockAxis,o=e.lockToContainerEdges,r=this.getOffset(t),n={x:r.x-this.initialOffset.x,y:r.y-this.initialOffset.y};if(n.y-=window.pageYOffset-this.initialWindowScroll.top,n.x-=window.pageXOffset-this.initialWindowScroll.left,this.translate=n,o){var a=this.getLockPixelOffsets(),l=c(a,2),d=l[0],h=l[1],f={x:this.width/2-d.x,y:this.height/2-d.y},u={x:this.width/2-h.x,y:this.height/2-h.y};n.x=s(this.minTranslate.x+f.x,this.maxTranslate.x-u.x,n.x),n.y=s(this.minTranslate.y+f.y,this.maxTranslate.y-u.y,n.y)}"x"===i?n.y=0:"y"===i&&(n.x=0),this.helper.style[p+"Transform"]="translate3d("+n.x+"px,"+n.y+"px, 0)"},animateNodes:function(){var t=this.$props,e=t.transitionDuration,i=t.hideSortableGhost,s=this.manager.getOrderedRefs(),o={left:this.scrollContainer.scrollLeft-this.initialScroll.left,top:this.scrollContainer.scrollTop-this.initialScroll.top},r={left:this.offsetEdge.left+this.translate.x+o.left,top:this.offsetEdge.top+this.translate.y+o.top},n={top:window.pageYOffset-this.initialWindowScroll.top,left:window.pageXOffset-this.initialWindowScroll.left};this.newIndex=null;for(var a=0,l=s.length;a<l;a++){var d=s[a].node,c=d.sortableInfo.index,h=d.offsetWidth,f=d.offsetHeight,u={width:this.width>h?h/2:this.width/2,height:this.height>f?f/2:this.height/2},m={x:0,y:0},g=s[a].edgeOffset;g||(s[a].edgeOffset=g=this.getEdgeOffset(d));var v=a<s.length-1&&s[a+1],y=a>0&&s[a-1];v&&!v.edgeOffset&&(v.edgeOffset=this.getEdgeOffset(v.node)),c!==this.index?(e&&(d.style[p+"TransitionDuration"]=e+"ms"),this._axis.x?this._axis.y?c<this.index&&(r.left+n.left-u.width<=g.left&&r.top+n.top<=g.top+u.height||r.top+n.top+u.height<=g.top)?(m.x=this.width+this.marginOffset.x,g.left+m.x>this.containerBoundingRect.width-u.width&&(m.x=v.edgeOffset.left-g.left,m.y=v.edgeOffset.top-g.top),null===this.newIndex&&(this.newIndex=c)):c>this.index&&(r.left+n.left+u.width>=g.left&&r.top+n.top+u.height>=g.top||r.top+n.top+u.height>=g.top+f)&&(m.x=-(this.width+this.marginOffset.x),g.left+m.x<this.containerBoundingRect.left+u.width&&(m.x=y.edgeOffset.left-g.left,m.y=y.edgeOffset.top-g.top),this.newIndex=c):c>this.index&&r.left+n.left+u.width>=g.left?(m.x=-(this.width+this.marginOffset.x),this.newIndex=c):c<this.index&&r.left+n.left<=g.left+u.width&&(m.x=this.width+this.marginOffset.x,null==this.newIndex&&(this.newIndex=c)):this._axis.y&&(c>this.index&&r.top+n.top+u.height>=g.top?(m.y=-(this.height+this.marginOffset.y),this.newIndex=c):c<this.index&&r.top+n.top<=g.top+u.height&&(m.y=this.height+this.marginOffset.y,null==this.newIndex&&(this.newIndex=c))),d.style[p+"Transform"]="translate3d("+m.x+"px,"+m.y+"px,0)"):i&&(this.sortableGhost=d,d.style.visibility="hidden",d.style.opacity=0)}null==this.newIndex&&(this.newIndex=this.index)},autoscroll:function(){var t=this,e=this.translate,i={x:0,y:0},s={x:1,y:1},o={x:10,y:10};e.y>=this.maxTranslate.y-this.height/2?(i.y=1,s.y=o.y*Math.abs((this.maxTranslate.y-this.height/2-e.y)/this.height)):e.x>=this.maxTranslate.x-this.width/2?(i.x=1,s.x=o.x*Math.abs((this.maxTranslate.x-this.width/2-e.x)/this.width)):e.y<=this.minTranslate.y+this.height/2?(i.y=-1,s.y=o.y*Math.abs((e.y-this.height/2-this.minTranslate.y)/this.height)):e.x<=this.minTranslate.x+this.width/2&&(i.x=-1,s.x=o.x*Math.abs((e.x-this.width/2-this.minTranslate.x)/this.width)),this.autoscrollInterval&&(clearInterval(this.autoscrollInterval),this.autoscrollInterval=null,this.isAutoScrolling=!1),0===i.x&&0===i.y||(this.autoscrollInterval=setInterval(function(){t.isAutoScrolling=!0;var e={left:1*s.x*i.x,top:1*s.y*i.y};t.scrollContainer.scrollTop+=e.top,t.scrollContainer.scrollLeft+=e.left,t.translate.x+=e.left,t.translate.y+=e.top,t.animateNodes()},5))}}},g={bind:function(t){t.sortableHandle=!0}},v=n("slick-list",m),y=n("slick-item",a);t.ElementMixin=a,t.ContainerMixin=m,t.HandleDirective=g,t.SlickList=v,t.SlickItem=y,t.arrayMove=e,Object.defineProperty(t,"__esModule",{value:!0})})}});