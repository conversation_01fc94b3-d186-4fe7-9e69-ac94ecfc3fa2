webpackJsonp([27],{221:function(e,t,s){var r=s(87)(s(318),s(377),null,null);e.exports=r.exports},318:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{username:""},userList:[{id:"",username:0,password:0,name:"",appId:0,appName:"",updateTime:""}],addUserForm:{username:"",password:"",name:"",appId:3},updateUserForm:{id:0,username:"",password:"",name:"",appId:3},isAddUserFormShow:!1,isUpdateUserFormShow:!1,appList:[]}},created:function(){this.getAppList(),this.getUserList()},methods:{getAppList:function(){var e=this;e.$axios.post("app/shortNameList",{},{}).then(function(t){if(200===t.status){var s=t.data;0===s.ret?e.appList=s.data:e.$message.error(s.data)}else e.$message.error("服务器异常！")})},getUserList:function(){var e=this;e.$axios.post("user/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var s=t.data;0===s.ret?e.userList=s.data:e.$message.error(s.data)}else e.$message.error("服务器异常！")})},updateUser:function(){var e=this;e.$axios.post("user/updateUser",e.updateUserForm,{}).then(function(t){if(200===t.status){var s=t.data;0===s.ret?(e.isUpdateUserFormShow=!1,e.$message({message:"用户信息更新成功",type:"success"}),e.getUserList()):e.$message.error(s.data)}else e.$message.error("服务器异常！")})},addUser:function(){var e=this;e.$axios.post("user/addUser",e.addUserForm,{}).then(function(t){if(200===t.status){var s=t.data;0===s.ret?(e.isAddUserFormShow=!1,e.$message({message:"新增用户成功",type:"success"}),e.getUserList()):e.$message.error(s.data)}else e.$message.error("服务器异常！")})},addUserPre:function(){this.clearAddForm(),this.isAddUserFormShow=!0},clearAddForm:function(){this.addUserForm.appId=3,this.addUserForm.name="",this.addUserForm.username="",this.addUserForm.password=""},updateUserPre:function(e){this.updateUserForm.id=e.id,this.updateUserForm.appId=e.appId,this.updateUserForm.name=e.name,this.updateUserForm.password=e.password,this.updateUserForm.username=e.username,this.isUpdateUserFormShow=!0}}}},377:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"table"},[s("div",{staticClass:"crumbs"},[s("el-breadcrumb",{attrs:{separator:"/"}},[s("el-breadcrumb-item",[s("i",{staticClass:"el-icon-menu"}),e._v("策略模板管理")])],1)],1),e._v(" "),s("el-divider",{attrs:{"content-position":"left"}},[e._v("默认策略列表")]),e._v(" "),s("el-dialog",{attrs:{title:"编辑策略",visible:e.isUpdateUserFormShow},on:{"update:visible":function(t){e.isUpdateUserFormShow=t}}},[s("el-form",{attrs:{model:e.updateUserForm,"label-width":"200px"}},[s("el-form-item",{attrs:{label:"用户名"}},[s("el-input",{model:{value:e.updateUserForm.username,callback:function(t){e.$set(e.updateUserForm,"username",t)},expression:"updateUserForm.username"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"密码"}},[s("el-input",{model:{value:e.updateUserForm.password,callback:function(t){e.$set(e.updateUserForm,"password",t)},expression:"updateUserForm.password"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"名称"}},[s("el-input",{model:{value:e.updateUserForm.name,callback:function(t){e.$set(e.updateUserForm,"name",t)},expression:"updateUserForm.name"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"所属应用"}},[s("el-select",{attrs:{placeholder:"请选择",disabled:""},model:{value:e.updateUserForm.appId,callback:function(t){e.$set(e.updateUserForm,"appId",t)},expression:"updateUserForm.appId"}},e._l(e.appList,function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(t){e.isUpdateUserFormShow=!1}}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUser()}}},[e._v("确 定")])],1)],1),e._v(" "),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:e.userList,stripe:"","highlight-current-row":""}},[s("el-table-column",{attrs:{prop:"id",label:"用户ID",width:"160"}}),e._v(" "),s("el-table-column",{attrs:{prop:"username",label:"用户名"}}),e._v(" "),s("el-table-column",{attrs:{prop:"name",label:"名称",width:"180"}}),e._v(" "),s("el-table-column",{attrs:{prop:"appName",label:"所属应用",width:"180"}}),e._v(" "),s("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{size:"small"},on:{click:function(s){return e.updateUserPre(t.row)}}},[e._v("修改")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});