webpackJsonp([31],{206:function(t,e,a){var r=a(87)(a(303),a(369),null,null);t.exports=r.exports},303:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a(1);a.n(r);e.default={data:function(){return{loading:!1,productList:[{appId:0,product:"全部",count:0}],emptyAdInfoList:[{id:0,product:"",appId:0,userId:0,strategyId:0,os:"",region:"",anonymous:"",registTime:"",income:0,version:"",pkgName:"",channelId:"",createTime:""}],queryParam:{appId:0,pageNo:1,pageSize:50},totalCount:0}},created:function(){this.getProductCount(0),this.getEmptyAdInfoList()},methods:{getEmptyAdInfoList:function(){var t=this;t.loading=!0,t.$axios.post("emptyAd/getEmptyAdInfo",{},{params:t.queryParam}).then(function(e){if(200===e.status){var a=e.data;if(0===a.ret){t.emptyAdInfoList=a.data.items;for(var r in t.productList)if(t.productList[r].appId===t.queryParam.appId){t.totalCount=t.productList[r].count;break}t.loading=!1}else t.loading=!1,t.$message.error(a.data)}else t.loading=!1,t.$message.error("加载失败！")})},getProductCount:function(t){var e=this;e.loading=!0,e.$axios.post("emptyAd/getEmptyCount",{},{params:{appId:t}}).then(function(t){if(200===t.status){var a=t.data;0===a.ret?(e.productList=a.data,e.loading=!1):(e.loading=!1,e.$message.error(a.data))}else e.loading=!1,e.$message.error("加载失败！")})},handleCurrentChange:function(t){this.queryParam.pageNo=t,this.getEmptyAdInfoList()},changeAppId:function(t){this.queryParam.appId=t,this.queryParam.pageNo=1,this.getProductCount(0),this.getEmptyAdInfoList()}}}},369:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),t._v(" 首页")]),t._v(" "),a("el-breadcrumb-item",[t._v("无广告记录")])],1),t._v(" "),a("br"),t._v(" "),a("el-row",t._l(t.productList,function(e,r){return a("el-button",{key:r,attrs:{autofocus:"true"},on:{click:function(a){return t.changeAppId(e.appId)}}},[t._v(t._s(e.product)+"("+t._s(e.count)+")")])}),1)],1),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.emptyAdInfoList,stripe:"",border:"","element-loading-text":"玩命加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[a("el-table-column",{attrs:{prop:"id",label:"日志ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"product",label:"产品"}}),t._v(" "),a("el-table-column",{attrs:{prop:"appId",label:"产品id"}}),t._v(" "),a("el-table-column",{attrs:{prop:"userId",label:"用户id"}}),t._v(" "),a("el-table-column",{attrs:{prop:"strategyId",label:"策略id"}}),t._v(" "),a("el-table-column",{attrs:{prop:"os",label:"操作系统"}}),t._v(" "),a("el-table-column",{attrs:{prop:"region",label:"是否受限"}}),t._v(" "),a("el-table-column",{attrs:{prop:"anonymous",label:"是否匿名"}}),t._v(" "),a("el-table-column",{attrs:{prop:"registTime",label:"注册时间"}}),t._v(" "),a("el-table-column",{attrs:{prop:"income",label:"收入"}}),t._v(" "),a("el-table-column",{attrs:{prop:"version",label:"版本"}}),t._v(" "),a("el-table-column",{attrs:{prop:"pkgName",label:"包名"}}),t._v(" "),a("el-table-column",{attrs:{prop:"channelId",label:"渠道id"}}),t._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"记录时间"}})],1)],1)],1),t._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":t.queryParam.pageNo,"page-size":50,layout:"total, prev, pager, next, jumper",total:t.totalCount},on:{"current-change":t.handleCurrentChange}})],1)},staticRenderFns:[]}}});