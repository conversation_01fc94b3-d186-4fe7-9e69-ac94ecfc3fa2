webpackJsonp([18],{204:function(e,t,s){var o=s(87)(s(301),s(368),null,null);e.exports=o.exports},234:function(e,t,s){e.exports={default:s(235),__esModule:!0}},235:function(e,t,s){var o=s(16),a=o.JSON||(o.JSON={stringify:JSON.stringify});e.exports=function(e){return a.stringify.apply(a,arguments)}},301:function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=s(234),a=s.n(o);t.default={data:function(){return{posList:[],adPosTypes:[],queryParam:{id:null,switchT:null,configType:null,configName:"",pageNo:1,pageSize:10},listCountNum:0,isShowAddThirdAdPosForm:!1,isShowEditAdPosForm:!1,addAdPosForm:{name:"",posType:""},editAdPosForm:{id:"",name:"",posType:""},adPosRules:{name:[{required:!0,message:"配置名称 不能为空",trigger:"blur"}]}}},created:function(){this.queryAdPosTypes(),this.queryPosConfigList()},methods:{addAdPosConfig:function(){var e=this;return null===e.addAdPosForm.posType||""===e.addAdPosForm.posType||0===Number(e.addAdPosForm.posType)?(this.$message.error("配置类型不能为空！"),!1):null===e.addAdPosForm.name||""===e.addAdPosForm.name?(this.$message.error("配置名称不能为空！"),!1):void e.$axios.post("config/new/addNew",e.addAdPosForm,{}).then(function(t){if(200===t.status){var s=t.data;0===s.ret?(e.$message({message:"新建配置成功..",type:"success"}),e.queryPosConfigList(),e.isShowAddThirdAdPosForm=!1):e.$message.error(s.data)}else e.$message.error("服务器异常！")})},addNewPosConfig:function(){this.isShowAddThirdAdPosForm=!0,this.addAdPosForm.name="",this.addAdPosForm.posType=""},queryPosConfigList:function(){var e=this;e.$axios.post("config/new/queryList",{},{params:e.queryParam}).then(function(t){if(200===t.status){var s=t.data;if(0===s.ret){var o=s.data;e.posList=o.items,e.listCountNum=o.count}else e.$message.error(s.data)}else e.$message.error("服务器异常！")})},queryAdPosTypes:function(){var e=this;e.$axios.get("config/getAdPosType").then(function(t){if(200===t.status){var s=t.data;0===s.code?e.adPosTypes=s.result:e.$message.error("加载数据失败...")}else e.$message.error("服务器异常！")})},changeSwitchFlagPos:function(e,t){var s={};s.id=t.id,s.switchT=t.state?1:0;var o=this;o.$axios.post("config/new/switchPos",{},{params:s}).then(function(e){if(200===e.status){0===e.data.ret?o.$message({message:"更新状态成功",type:"success"}):o.$message.error("更新状态失败")}else o.$message.error("服务器异常！")})},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.queryPosConfigList()},copyConfig:function(e){var t={};t.id=e.id;var s=this;s.$axios.post("config/new/copyNew",{},{params:t}).then(function(e){if(200===e.status){0===e.data.ret?(s.$message({message:"复制成功",type:"success"}),s.queryPosConfigList()):s.$message.error("复制失败")}else s.$message.error("服务器异常！")})},delConfig:function(e){var t={};t.id=e.id;var s=this;s.$axios.post("config/new/delPos",{},{params:t}).then(function(e){if(200===e.status){0===e.data.ret?(s.$message({message:"删除成功",type:"success"}),s.queryPosConfigList()):s.$message.error("删除失败")}else s.$message.error("服务器异常！")})},editConfig:function(e){var t={id:e.id,posType:e.posType};this.$router.push({path:"/adConfigPull",query:{target:a()(t)}})},editConfigName:function(e){this.isShowEditAdPosForm=!0,this.editAdPosForm={id:e.id,name:e.name,posType:e.posType}},sendEditPost:function(){var e=this,t=this,s={id:this.editAdPosForm.id,name:this.editAdPosForm.name};t.$axios.post("config/new/editName",{},{params:s}).then(function(s){if(200===s.status){0===s.data.ret?(t.$message({message:"修改名称成功..",type:"success"}),e.isShowEditAdPosForm=!1,t.queryPosConfigList()):t.$message.error("修改名称失败")}else t.$message.error("服务器异常！")})}}}},368:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"table"},[s("div",{staticClass:"crumbs"},[s("el-breadcrumb",{attrs:{separator:"/"}},[s("el-breadcrumb-item",[s("i",{staticClass:"el-icon-menu"}),e._v(" 策略配置")])],1)],1),e._v(" "),s("el-dialog",{attrs:{title:"添加配置",visible:e.isShowAddThirdAdPosForm},on:{"update:visible":function(t){e.isShowAddThirdAdPosForm=t}}},[s("el-form",{ref:"addAdPosForm",attrs:{model:e.addAdPosForm,"label-width":"200px",rules:e.adPosRules}},[s("el-form-item",{attrs:{label:"配置名称",prop:"posName"}},[s("el-input",{model:{value:e.addAdPosForm.name,callback:function(t){e.$set(e.addAdPosForm,"name",t)},expression:"addAdPosForm.name"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"配置类型"}},[s("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.addAdPosForm.posType,callback:function(t){e.$set(e.addAdPosForm,"posType",t)},expression:"addAdPosForm.posType"}},e._l(e.adPosTypes,function(e){return s("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(t){e.isShowAddThirdAdPosForm=!1}}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdPosConfig()}}},[e._v("确 定")])],1)],1),e._v(" "),s("el-dialog",{attrs:{title:"修改配置",visible:e.isShowEditAdPosForm},on:{"update:visible":function(t){e.isShowEditAdPosForm=t}}},[s("el-form",{ref:"editAdPosForm",attrs:{model:e.editAdPosForm,"label-width":"200px",rules:e.adPosRules}},[s("el-form-item",{attrs:{label:"配置名称",prop:"posName"}},[s("el-input",{model:{value:e.editAdPosForm.name,callback:function(t){e.$set(e.editAdPosForm,"name",t)},expression:"editAdPosForm.name"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"配置类型"}},[s("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:"disabled"},model:{value:e.editAdPosForm.posType,callback:function(t){e.$set(e.editAdPosForm,"posType",t)},expression:"editAdPosForm.posType"}},e._l(e.adPosTypes,function(e){return s("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{on:{click:function(t){e.isShowEditAdPosForm=!1}}},[e._v("取 消")]),e._v(" "),s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.sendEditPost()}}},[e._v("确 定")])],1)],1),e._v(" "),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[s("el-form-item",{attrs:{label:"开关"}},[s("el-select",{staticClass:"select-150",attrs:{placeholder:"开关"},model:{value:e.queryParam.switchT,callback:function(t){e.$set(e.queryParam,"switchT",t)},expression:"queryParam.switchT"}},[s("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),s("el-option",{attrs:{value:"1"}},[e._v("开")]),e._v(" "),s("el-option",{attrs:{value:"0"}},[e._v("关")])],1)],1),e._v(" "),s("el-form-item",{attrs:{label:"配置类型"}},[s("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型"},model:{value:e.queryParam.configType,callback:function(t){e.$set(e.queryParam,"configType",t)},expression:"queryParam.configType"}},[s("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adPosTypes,function(e){return s("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),s("el-form-item",{attrs:{label:"配置名称"}},[s("el-input",{staticClass:"width-150",model:{value:e.queryParam.configName,callback:function(t){e.$set(e.queryParam,"configName",t)},expression:"queryParam.configName"}})],1),e._v(" "),s("el-form-item",{attrs:{label:"ID"}},[s("el-input",{staticClass:"width-150",model:{value:e.queryParam.id,callback:function(t){e.$set(e.queryParam,"id",t)},expression:"queryParam.id"}})],1),e._v(" "),s("el-form-item",[s("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:function(t){return e.queryPosConfigList()}}})],1),e._v(" "),s("el-form-item",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addNewPosConfig()}}},[e._v("添加配置")])],1)],1)],1)],1),e._v(" "),s("el-divider",{attrs:{"content-position":"left"}},[e._v("配置项列表")]),e._v(" "),s("el-row",[s("el-col",{attrs:{span:24}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:e.posList,stripe:"","highlight-current-row":""}},[s("el-table-column",{attrs:{label:"开关",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":!0,"inactive-value":!1},on:{change:function(s){return e.changeSwitchFlagPos(t.$index,t.row)}},model:{value:t.row.state,callback:function(s){e.$set(t.row,"state",s)},expression:"scope.row.state"}})]}}])}),e._v(" "),s("el-table-column",{attrs:{prop:"id",label:"ID"}}),e._v(" "),s("el-table-column",{attrs:{prop:"name",label:"配置名称"}}),e._v(" "),s("el-table-column",{attrs:{prop:"posType",label:"配置类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.posType?s("span",[e._v("开屏")]):2===t.row.posType?s("span",[e._v("信息流")]):3===t.row.posType?s("span",[e._v("插屏")]):4===t.row.posType?s("span",[e._v("激励视频")]):5===t.row.posType?s("span",[e._v("Banner")]):e._e()]}}])}),e._v(" "),s("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{attrs:{plain:"",type:"warning",size:"mini"},on:{click:function(s){return e.copyConfig(t.row)}}},[e._v("复制")]),e._v(" "),s("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(s){return e.editConfig(t.row)}}},[e._v("编辑")]),e._v(" "),s("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(s){return e.editConfigName(t.row)}}},[e._v("修改名称")]),e._v(" "),s("el-button",{attrs:{plain:"",type:"danger",size:"mini"},on:{click:function(s){return e.delConfig(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),s("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}}});