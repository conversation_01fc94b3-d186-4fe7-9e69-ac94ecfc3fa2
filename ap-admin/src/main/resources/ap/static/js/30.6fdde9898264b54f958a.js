webpackJsonp([30],{207:function(e,t,o){var s=o(87)(o(304),o(367),null,null);e.exports=s.exports},304:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{name:"",id:"",pageNo:1,pageSize:10},listCountNum:0,adPosList:[{id:"",name:"",extendInfo:"",createTime:"",updateTime:""}],editAdPosForm:{id:"",name:"",extendInfo:""},addAdPosForm:{name:"",extendInfo:""},isShowEditAdPosForm:!1,isShowAddAdPosForm:!1,adPosRules:{name:[{required:!0,message:"请输入广告位名称",trigger:"blur"}]}}},created:function(){this.getAdPosList()},methods:{getAdPosList:function(){var e=this;e.$axios.post("adpos/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var o=t.data;if(0===o.ret){var s=o.data;e.adPosList=s.items,e.listCountNum=s.count}else e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},saveAdPos:function(){var e=this;e.$refs.editAdPosForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("adpos/updateOne",{},{params:e.editAdPosForm}).then(function(t){if(200===t.status){0===t.data.ret?(e.isShowEditAdPosForm=!1,e.$message({message:"广告位信息更新成功",type:"success"}),e.getAdPosList()):e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})})},addAdPosPre:function(){this.clearAddForm(),this.isShowAddAdPosForm=!0},addAdPos:function(){var e=this;e.$refs.addAdPosForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("adpos/addOne",{},{params:e.addAdPosForm}).then(function(t){if(200===t.status){0===t.data.ret?(e.isShowAddAdPosForm=!1,e.$message({message:"新增广告位成功",type:"success"}),e.getAdPosList()):e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})})},clearAddForm:function(){this.addAdPosForm.name="",this.addAdPosForm.extendInfo=""},updateAdPosPre:function(e){this.editAdPosForm.id=e.id,this.editAdPosForm.name=e.name,this.editAdPosForm.extendInfo=e.extendInfo,this.isShowEditAdPosForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getAdPosList()},copyAdPosInfo:function(){var e=this,t="产品名称："+localStorage.getItem("product_name")+"\n";this.adPosList.forEach(function(e,o){t+=e.id+"  "+e.name+"\n"}),this.$copyText(t).then(function(t){console.log(t),e.$message("已成功复制")},function(t){e.$message("复制失败")})}}}},367:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"table"},[o("div",{staticClass:"crumbs"},[o("el-breadcrumb",{attrs:{separator:"/"}},[o("el-breadcrumb-item",[o("i",{staticClass:"el-icon-menu"}),e._v(" 广告管理")]),e._v(" "),o("el-breadcrumb-item",[e._v("广告位管理")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"编辑广告位",visible:e.isShowEditAdPosForm},on:{"update:visible":function(t){e.isShowEditAdPosForm=t}}},[o("el-form",{ref:"editAdPosForm",attrs:{model:e.editAdPosForm,"label-width":"200px",rules:e.adPosRules}},[o("el-form-item",{attrs:{label:"广告位名称",prop:"name"}},[o("el-input",{model:{value:e.editAdPosForm.name,callback:function(t){e.$set(e.editAdPosForm,"name",t)},expression:"editAdPosForm.name"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"额外说明信息"}},[o("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20}},model:{value:e.editAdPosForm.extendInfo,callback:function(t){e.$set(e.editAdPosForm,"extendInfo",t)},expression:"editAdPosForm.extendInfo"}})],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowEditAdPosForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"新增广告位",visible:e.isShowAddAdPosForm},on:{"update:visible":function(t){e.isShowAddAdPosForm=t}}},[o("el-form",{ref:"addAdPosForm",attrs:{model:e.addAdPosForm,"label-width":"200px",rules:e.adPosRules}},[o("el-form-item",{attrs:{label:"广告位名称",prop:"name"}},[o("el-input",{model:{value:e.addAdPosForm.name,callback:function(t){e.$set(e.addAdPosForm,"name",t)},expression:"addAdPosForm.name"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"额外说明信息"}},[o("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20}},model:{value:e.addAdPosForm.extendInfo,callback:function(t){e.$set(e.addAdPosForm,"extendInfo",t)},expression:"addAdPosForm.extendInfo"}})],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowAddAdPosForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[o("el-form-item",{attrs:{label:"广告位名称"}},[o("el-input",{staticClass:"width-150",model:{value:e.queryParam.name,callback:function(t){e.$set(e.queryParam,"name",t)},expression:"queryParam.name"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"广告位ID"}},[o("el-input",{staticClass:"width-150",model:{value:e.queryParam.id,callback:function(t){e.$set(e.queryParam,"id",t)},expression:"queryParam.id"}})],1),e._v(" "),o("el-form-item",[o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getAdPosList}})],1),e._v(" "),o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",round:""},on:{click:e.copyAdPosInfo}},[e._v("一键复制广告位信息")])],1),e._v(" "),o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addAdPosPre()}}},[e._v("新建广告位")])],1)],1)],1)],1),e._v(" "),o("el-divider",{attrs:{"content-position":"left"}},[e._v("广告位列表")]),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-table",{staticStyle:{width:"100%"},attrs:{data:e.adPosList,stripe:"","highlight-current-row":""}},[o("el-table-column",{attrs:{prop:"id",label:"广告位ID",width:"160"}}),e._v(" "),o("el-table-column",{attrs:{prop:"name",label:"广告位名称"}}),e._v(" "),o("el-table-column",{attrs:{prop:"extendInfo",label:"额外说明信息"}}),e._v(" "),o("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"180"}}),e._v(" "),o("el-table-column",{attrs:{prop:"updateTime",label:"最近修改时间",width:"180"}}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"small"},on:{click:function(o){return e.updateAdPosPre(t.row)}}},[e._v("修改\n                        ")])]}}])})],1),e._v(" "),o("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}}});