webpackJsonp([12],{225:function(e,t,a){a(406);var o=a(87)(a(322),a(384),null,null);e.exports=o.exports},322:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{platformCodeList:[{key:1,val:"穿山甲"},{key:2,val:"广点通"},{key:3,val:"快手"},{key:4,val:"百度"},{key:5,val:"OPPO"}],adTypeList:[{key:1,val:"开屏"},{key:2,val:"信息流"},{key:3,val:"插屏"},{key:4,val:"激励视频"},{key:5,val:"新插屏"},{key:6,val:"Banner"}],importStatusList:[{key:0,val:"未导入"},{key:1,val:"已导入"}],osList:[{key:1,val:"Android"},{key:2,val:"IOS"}],innerTypeList:[{key:0,val:"半屏图片(默认)"},{key:1,val:"半屏，图片+视频"},{key:2,val:"全屏+半屏，图片+视频"}],adSiteList:[{key:1,val:"弹窗"},{key:2,val:"固定位"},{key:3,val:"自渲染大图"}],csjAdSubTypeList:["大图","组图","单图","横板视频","竖图","竖版视频"],gdtAdSubTypeList:["上文下图","左图右文","横版视频模板"],gdtAdSubOwnTypeList:["16:9图片","16:9视频","9:16图片","9:16视频","3:2图片(3张)"],gdtVideoList:[],csjVideoList:[],ksVideoList:[],bdVideoList:[],oppoVideoList:[],gdtChapList:[],csjChapList:[],ksChapList:[],bdChapList:[],oppoChapList:[],gdtNewChapList:[],csjNewChapList:[],ksNewChapList:[],bdNewChapList:[],oppoNewChapList:[],gdtStaticList:[],csjStaticList:[],ksStaticList:[],bdStaticList:[],oppoStaticList:[],gdtKpList:[],csjKpList:[],ksKpList:[],bdKpList:[],oppoKpList:[],importType:2,biddingImportType:1,applicationList:[],applicationListNormal:[],templateList:[],companyList:[],csjCompanyList:[],gdtCompanyList:[],ksCompanyList:[],bdCompanyList:[],oppoCompanyList:[],csjApplicationList:[],gdtApplicationList:[],ksApplicationList:[],bdApplicationList:[],oppoApplicationList:[],pickerOptions:{disabledDate:function(e){return e.getTime()>Date.now()},shortcuts:[{text:"今天",onClick:function(e){e.$emit("pick",new Date)}},{text:"昨天",onClick:function(e){var t=new Date;t.setTime(t.getTime()-864e5),e.$emit("pick",t)}},{text:"一周前",onClick:function(e){var t=new Date;t.setTime(t.getTime()-6048e5),e.$emit("pick",t)}}]},queryParam:{thirdAppId:"",posName:"",adType:"",id:"",queryDay:"",posId:"",importStatus:"",pageNo:1,pageSize:10},queryParamTask:{batchNo:"",pageNo:1,pageSize:10},queryParamLowPrice:{code:"",os:1,pageNo:1,pageSize:10},batchNo:"",listCountNum:0,listTaskQueenCountNum:0,listLowPriceNum:0,currentQueryCode:1,thirdAdPosList:[],thirdTaskQueenList:[],lowPriceList:[],adTypeSubChecked:[],adTypeRadioChecked:"",editThirdAdPosForm:{id:"",platformCode:1,companyId:"",appId:"",posName:"",adType:"",adSubType:[],adPrice:"",adSite:[],adTypeOwn:""},addThirdAdPosForm:{platformCode:1,companyId:"",renderType:"1",appId:"",os:1,posName:"",adType:"",adSubType:[],adPrice:"",adSite:[],adTypeOwn:"",isBidding:0,biddingType:1,isSetPrice:1,innerType:0},addThirdAdPosBatchForm:{os:"",csjCompanyId:"",gdtCompanyId:"",ksCompanyId:"",bdCompanyId:"",oppoCompanyId:"",csjAppId:"",gdtAppId:"",ksAppId:"",bdAppId:"",oppoAppId:"",templateId:""},sycnPositionForm:{appId:""},isShowEditThirdAdPosForm:!1,isShowAddThirdAdPosForm:!1,isShowAddThirdAdPosBatchForm:!1,isShowSycnPositionForm:!1,isShowAddThirdLoadForm:!1,isShowLowPriceForm:!1,isShowTaskQueenForm:!1,multipleSelection:[],loadingWaitLoadAdList:!0,waitLoadList:[],thirdAdPosRules:{posName:[{required:!0,message:"广告位名称 不能为空",trigger:"blur"}]}}},created:function(){this.getThirdAdPostList(),this.getCompanyList(),this.getTemplateList(),this.getApplicationList(),this.getAdTypeDist()},methods:{getAdTypeDist:function(){var e=this;e.$axios.get("config/getAdTypeDist").then(function(t){if(200===t.status){var a=t.data;if(0===a.code)for(var o=a.result,s=0;s<o.length;s++){var i=o[s];"广点通"===i.adSource?"视频"===i.subTypeName?e.gdtVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("2.0")>-1?e.gdtNewChapList.push({key:i.adType,val:i.typeName}):e.gdtChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.gdtStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.gdtKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.gdtNewChapList.push({key:i.adType,val:i.typeName}):"穿山甲"===i.adSource?"视频"===i.subTypeName?e.csjVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.csjNewChapList.push({key:i.adType,val:i.typeName}):e.csjChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.csjStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.csjKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.csjNewChapList.push({key:i.adType,val:i.typeName}):"快手"===i.adSource?"视频"===i.subTypeName?e.ksVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.ksNewChapList.push({key:i.adType,val:i.typeName}):e.ksChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.ksStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.ksKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.ksNewChapList.push({key:i.adType,val:i.typeName}):"百度"===i.adSource?"视频"===i.subTypeName?e.bdVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.bdNewChapList.push({key:i.adType,val:i.typeName}):e.bdChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.bdStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.bdKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.bdNewChapList.push({key:i.adType,val:i.typeName}):"OPPO"===i.adSource&&("视频"===i.subTypeName?e.oppoVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.oppoNewChapList.push({key:i.adType,val:i.typeName}):e.oppoChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.oppoStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.oppoKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.oppoNewChapList.push({key:i.adType,val:i.typeName}))}else e.$message.error("加载数据失败...")}else e.$message.error("服务器异常！")})},showAndCheckAd:function(){var e=this;e.isShowAddThirdLoadForm=!0,e.loadingWaitLoadAdList=!0;for(var t=[],a=0;a<e.multipleSelection.length;a++)t.push(e.multipleSelection[a].id);e.$axios.post("third/adPos/queryAndCheck",t,{}).then(function(t){if(200===t.status){var a=t.data;0===a.ret?(e.waitLoadList=a.data,e.loadingWaitLoadAdList=!1,e.$message({message:"三方巡检成功",type:"success"})):e.$message.error(t.data.data)}else e.$message.error("查询三方失败！请重新尝试")})},getThirdAdPostList:function(){var e=this;e.$axios.post("third/adPos/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){var o=a.data;e.thirdAdPosList=o.items,e.listCountNum=o.count}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},getAdTypeDymicList:function(e,t){if(1===e){if(1===t)return this.csjKpList;if(2===t)return this.csjStaticList;if(3===t)return this.csjChapList;if(4===t)return this.csjVideoList;if(5===t)return this.csjNewChapList}else if(2===e){if(1===t)return this.gdtKpList;if(2===t)return this.gdtStaticList;if(3===t)return this.gdtChapList;if(4===t)return this.gdtVideoList;if(5===t)return this.gdtNewChapList}else if(3===e){if(1===t)return this.ksKpList;if(2===t)return this.ksStaticList;if(3===t)return this.ksChapList;if(4===t)return this.ksVideoList;if(5===t)return this.ksNewChapList}else if(4===e){if(1===t)return this.bdKpList;if(2===t)return this.bdStaticList;if(3===t)return this.bdChapList;if(4===t)return this.bdVideoList;if(5===t)return this.bdNewChapList}else if(5===e){if(1===t)return this.oppoKpList;if(2===t)return this.oppoStaticList;if(3===t)return this.oppoChapList;if(4===t)return this.oppoVideoList;if(5===t)return this.oppoNewChapList}},getCompanyList:function(){var e=this,t={name:"",id:"",pageNo:1,pageSize:1e6};e.$axios.post("third/company/list",{},{params:t}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var o=a.data,s=[],i=[],r=[],d=[],l=[],n=[],p=0;p<o.items.length;p++){var c={key:o.items[p].id,val:o.items[p].mainBody};s.push(c),1===o.items[p].platformCode?i.push(c):2===o.items[p].platformCode?r.push(c):3===o.items[p].platformCode?d.push(c):4===o.items[p].platformCode?l.push(c):5===o.items[p].platformCode&&n.push(c)}e.companyList=s,e.csjCompanyList=i,e.gdtCompanyList=r,e.ksCompanyList=d,e.bdCompanyList=l,e.oppoCompanyList=n}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},getTemplateList:function(){var e=this,t={name:"",id:"",pageNo:1,pageSize:1e6};e.$axios.post("third/template/list",{},{params:t}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var o=a.data,s=[],i=0;i<o.items.length;i++){var r={key:o.items[i].id,val:o.items[i].modelName};s.push(r)}e.templateList=s}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},getApplicationList:function(){var e=this,t={name:"",id:"",os:null,pageNo:1,pageSize:1e6};e.$axios.post("third/app/list",{},{params:t}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var o=a.data,s=[],i=[],r=[],d=[],l=[],n=[],p=[],c=0;c<o.items.length;c++){1===o.items[c].switchFlag&&p.push(o.items[c]);var m=1===o.items[c].os?"Android":"IOS",u={key:o.items[c].id,val:o.items[c].appName,desc:o.items[c].mainBody,os:m};s.push(u),1===o.items[c].platformCode?i.push(u):2===o.items[c].platformCode?r.push(u):3===o.items[c].platformCode?d.push(u):4===o.items[c].platformCode?l.push(u):5===o.items[c].platformCode&&n.push(u)}e.applicationList=s,e.csjApplicationList=i,e.gdtApplicationList=r,e.ksApplicationList=d,e.bdApplicationList=l,e.oppoApplicationList=n,e.applicationListNormal=p}else e.$message.error("初始化应用数据异常！")}else e.$message.error("服务器异常！")})},saveApp:function(){var e=this,t=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});""!==e.adTypeRadioChecked&&e.adTypeSubChecked.push(e.adTypeRadioChecked),e.addThirdAdPosForm.adSubType=e.adTypeSubChecked,e.$refs.editThirdAdPosForm.validate(function(a){if(!a)return t.close(),console.log("error submit!!"),!1;e.$axios.post("third/adPos/updateOne",e.editThirdAdPosForm,{}).then(function(a){if(t.close(),200===a.status){0===a.data.ret?(e.isShowEditThirdAdPosForm=!1,e.$message({message:"广告位信息更新成功",type:"success"}),e.getThirdAdPostList(),e.adTypeSubChecked=[],e.adTypeRadioChecked=""):e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})})},addAdPostPre:function(){this.clearThirdAdPosForm(),this.isShowAddThirdAdPosForm=!0},addAdPostBatchPre:function(){this.clearThirdAdPosBatchForm(),this.isShowAddThirdAdPosBatchForm=!0},syncPositionPre:function(){this.clearSyncPositionForm(),this.isShowSycnPositionForm=!0},findFitApp:function(e){1===e?this.addThirdAdPosBatchForm.csjAppId=this.csjApplicationList[0].key:2===e?this.addThirdAdPosBatchForm.gdtAppId=this.gdtApplicationList[0].key:3===e?this.addThirdAdPosBatchForm.ksAppId=this.ksApplicationList[0].key:4===e?this.addThirdAdPosBatchForm.bdAppId=this.bdApplicationList[0].key:5===e&&(this.addThirdAdPosBatchForm.oppoAppId=this.oppoApplicationList[0].key)},addAdPos:function(){var e=this,t=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});""!==e.adTypeRadioChecked&&e.adTypeSubChecked.push(e.adTypeRadioChecked),e.addThirdAdPosForm.adSubType=e.adTypeSubChecked,e.$refs.addThirdAdPosForm.validate(function(a){if(!a)return t.close(),console.log("error submit!!"),!1;e.$axios.post("third/adPos/addOne",e.addThirdAdPosForm,{}).then(function(a){if(t.close(),200===a.status){0===a.data.ret?(e.isShowAddThirdAdPosForm=!1,e.$message({message:"新增广告位成功",type:"success"}),e.getThirdAdPostList(),e.adTypeSubChecked=[],e.adTypeRadioChecked=""):e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})})},sychAdPosList:function(){var e=this,t=this.$loading({lock:!0,text:"正在提交处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$axios.post("third/adPos/pullAll",{},{params:e.sycnPositionForm}).then(function(a){if(t.close(),200===a.status){var o=a.data;if(0===o.ret){e.isShowSycnPositionForm=!1;var s="";s=o.data?"已提交同步任务，请稍后查看列表":"任务正在执行中..稍安勿躁 勿重复点击!",e.$message({message:s,type:"success"}),e.getThirdAdPostList(),e.adTypeSubChecked=[],e.adTypeRadioChecked=""}else e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})},addAdPosBatch:function(){var e=this,t=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.addThirdAdPosBatchForm.validate(function(a){if(!a)return console.log("error submit!!"),!1;e.$axios.post("third/adPos/addBatch",e.addThirdAdPosBatchForm,{}).then(function(a){if(t.close(),200===a.status){var o=a.data;0===o.ret?(e.isShowAddThirdAdPosBatchForm=!1,e.$message({message:"批量任务提交成功",type:"success"}),e.isShowTaskQueenForm=!0,e.batchNo=o.data,e.loadTaskQueenList(o.data)):e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})})},retryTask:function(e){var t=this,a=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),o={taskId:e};t.$axios.post("third/adPos/retryTask",{},{params:o}).then(function(e){if(a.close(),200===e.status){var o=e.data;0===o.ret?(t.$message({message:"已完成重试",type:"success"}),t.thirdTaskQueenList()):t.$message.error(o.data)}else t.$message.error("服务器异常！")})},loadTaskQueenList:function(){var e=this;e.queryParamTask.batchNo=e.batchNo,e.$axios.post("third/adPos/queryTaskList",{},{params:e.queryParamTask}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){var o=a.data;e.thirdTaskQueenList=o.items,e.listTaskQueenCountNum=o.count}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},loadThirdHaveAdPrice:function(e){this.currentQueryCode=e;var t=this;t.queryParamLowPrice.code=t.currentQueryCode;var a=this.$loading({lock:!0,text:"正在查询中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});t.$axios.post("third/adPos/queryThirdAdCurrent",{},{params:t.queryParamLowPrice}).then(function(e){if(a.close(),200===e.status){var o=e.data;if(0===o.ret){var s=o.data;t.lowPriceList=s.items,t.listLowPriceNum=s.count}else t.$message.error(o.data)}else t.$message.error("服务器异常！")})},loadAdPosBatch:function(){for(var e=this,t=[],a=0;a<e.multipleSelection.length;a++)t.push(e.multipleSelection[a].id);var o=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),s={importType:e.importType,biddingImportType:e.biddingImportType};e.$axios.post("third/adPos/loadToSystem",t,{params:s}).then(function(t){if(o.close(),200===t.status){0===t.data.ret?(e.isShowAddThirdLoadForm=!1,e.$message({message:"批量导入提交成功",type:"success"}),e.getThirdAdPostList()):e.$message.error(t.data.data)}else e.$message.error("服务器异常！")})},clearThirdAdPosForm:function(){this.addThirdAdPosForm.platformCode=1,this.addThirdAdPosForm.companyId="",this.addThirdAdPosForm.appId="",this.addThirdAdPosForm.posName="",this.addThirdAdPosForm.adType="",this.addThirdAdPosForm.adSubType="",this.addThirdAdPosForm.adPrice="",this.addThirdAdPosForm.adTypeOwn="",this.addThirdAdPosForm.adSite="",this.adTypeSubChecked=[],this.adTypeRadioChecked=""},clearThirdAdPosBatchForm:function(){this.addThirdAdPosBatchForm.templateId=""},clearSyncPositionForm:function(){this.sycnPositionForm.appId=""},updateAdPostPre:function(e){this.editThirdAdPosForm.id=e.id,this.editThirdAdPosForm.platformCode=e.platformCode,this.editThirdAdPosForm.companyId=e.companyId,this.editThirdAdPosForm.appId=e.applicationId,this.editThirdAdPosForm.posName=e.posName,this.editThirdAdPosForm.adType=e.adType,this.editThirdAdPosForm.adSubType=e.adSubType,this.editThirdAdPosForm.adPrice=e.adPrice,this.editThirdAdPosForm.adSite=e.adSite,this.editThirdAdPosForm.adTypeOwn=e.adTypeOwn,this.adTypeSubChecked=e.adSubType,2===e.platformCode&&2===e.adType&&(this.adTypeRadioChecked=e.adSubType[0]),this.isShowEditThirdAdPosForm=!0},handleSizeChange:function(e){this.queryParam.pageSize=e,this.getThirdAdPostList()},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getThirdAdPostList()},handleTaskQueenCurrentChangePage:function(e){this.queryParamTask.pageNo=e,this.loadTaskQueenList()},handleLowPriceChangePage:function(e){this.queryParamLowPrice.pageNo=e,this.loadThirdHaveAdPrice(this.currentQueryCode)},queryLog:function(e){},changeSwitchFlag:function(e,t){var a={};a.id=t.id,a.state=t.switchFlag;var o=this;o.$axios.post("third/adPos/switchFlag",a,{}).then(function(e){if(200===e.status){0===e.data.ret?o.$message({message:"更新状态成功",type:"success"}):o.$message.error("更新状态失败")}else o.$message.error("服务器异常！")})},handleSelectionChange:function(e){this.multipleSelection=e}}}},341:function(e,t,a){t=e.exports=a(29)(),t.push([e.i,".select-350 .el-input{width:350px}.width-100,.width-150{width:100px}",""])},384:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),e._v("广告位管理")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"编辑广告位",visible:e.isShowEditThirdAdPosForm},on:{"update:visible":function(t){e.isShowEditThirdAdPosForm=t}}},[a("el-form",{ref:"editThirdAdPosForm",attrs:{model:e.editThirdAdPosForm,"label-width":"200px",rules:e.thirdAdPosRules}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.editThirdAdPosForm.platformCode,callback:function(t){e.$set(e.editThirdAdPosForm,"platformCode",t)},expression:"editThirdAdPosForm.platformCode"}},e._l(e.platformCodeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体",disabled:""},model:{value:e.editThirdAdPosForm.companyId,callback:function(t){e.$set(e.editThirdAdPosForm,"companyId",t)},expression:"editThirdAdPosForm.companyId"}},e._l(e.companyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-select",{staticClass:"select-350",attrs:{placeholder:"应用",disabled:""},model:{value:e.editThirdAdPosForm.appId,callback:function(t){e.$set(e.editThirdAdPosForm,"appId",t)},expression:"editThirdAdPosForm.appId"}},e._l(e.applicationList,function(t){return a("el-option",{attrs:{label:t.val,value:t.key}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.val))]),e._v(" "),a("el-tag",{staticStyle:{float:"right"},attrs:{type:"success"}},[e._v(e._s(t.os))]),e._v(" "),a("el-tag",{staticStyle:{float:"right"}},[e._v(e._s(t.desc))])],1)}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"广告位名称",prop:"posName"}},[a("el-input",{model:{value:e.editThirdAdPosForm.posName,callback:function(t){e.$set(e.editThirdAdPosForm,"posName",t)},expression:"editThirdAdPosForm.posName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"广告类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:""},model:{value:e.editThirdAdPosForm.adType,callback:function(t){e.$set(e.editThirdAdPosForm,"adType",t)},expression:"editThirdAdPosForm.adType"}},e._l(e.adTypeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",[2===e.editThirdAdPosForm.adType?a("div",[a("el-col",{attrs:{gutter:30}},[a("el-col",{attrs:{span:5}},[a("div",{staticClass:"grid-content bg-purple"},[a("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.editThirdAdPosForm.adSite,callback:function(t){e.$set(e.editThirdAdPosForm,"adSite",t)},expression:"editThirdAdPosForm.adSite"}},e._l(e.adSiteList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),a("el-col",{attrs:{span:19}},[a("div",{staticClass:"grid-content bg-purple"},[1===e.editThirdAdPosForm.platformCode?a("el-checkbox-group",{attrs:{size:"small",min:1,max:"4"},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.csjAdSubTypeList,function(t){return a("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                        ")])}),1):e._e(),e._v(" "),2===e.editThirdAdPosForm.platformCode?a("el-radio-group",{attrs:{size:"small"},model:{value:e.adTypeRadioChecked,callback:function(t){e.adTypeRadioChecked=t},expression:"adTypeRadioChecked"}},e._l(e.gdtAdSubTypeList,function(t){return a("el-radio",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                        ")])}),1):e._e()],1)])],1)],1):e._e()]),e._v(" "),a("el-form-item",{attrs:{label:"自家广告类型"}},[a("div",{staticClass:"grid-content bg-purple"},[a("div",[a("el-select",{staticClass:"select-150",attrs:{placeholder:"自家广告类型"},model:{value:e.editThirdAdPosForm.adTypeOwn,callback:function(t){e.$set(e.editThirdAdPosForm,"adTypeOwn",t)},expression:"editThirdAdPosForm.adTypeOwn"}},e._l(e.getAdTypeDymicList(e.editThirdAdPosForm.platformCode,e.editThirdAdPosForm.adType),function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])]),e._v(" "),a("el-form-item",{attrs:{label:"目标价格",prop:"packageName"}},[a("el-tooltip",{attrs:{content:"修改可能会失败，请以三方查价结果为准",effect:"light",placement:"right"}},[a("el-input",{staticClass:"width-100",model:{value:e.editThirdAdPosForm.adPrice,callback:function(t){e.$set(e.editThirdAdPosForm,"adPrice",t)},expression:"editThirdAdPosForm.adPrice"}})],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowEditThirdAdPosForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveApp()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"新建广告位",visible:e.isShowAddThirdAdPosForm},on:{"update:visible":function(t){e.isShowAddThirdAdPosForm=t}}},[a("el-form",{ref:"addThirdAdPosForm",attrs:{model:e.addThirdAdPosForm,"label-width":"200px",rules:e.thirdAdPosRules}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},model:{value:e.addThirdAdPosForm.platformCode,callback:function(t){e.$set(e.addThirdAdPosForm,"platformCode",t)},expression:"addThirdAdPosForm.platformCode"}},e._l(e.platformCodeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"系统"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"系统"},model:{value:e.addThirdAdPosForm.os,callback:function(t){e.$set(e.addThirdAdPosForm,"os",t)},expression:"addThirdAdPosForm.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"广告位名称",prop:"posName"}},[a("el-input",{model:{value:e.addThirdAdPosForm.posName,callback:function(t){e.$set(e.addThirdAdPosForm,"posName",t)},expression:"addThirdAdPosForm.posName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"广告类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",filterable:""},model:{value:e.addThirdAdPosForm.adType,callback:function(t){e.$set(e.addThirdAdPosForm,"adType",t)},expression:"addThirdAdPosForm.adType"}},e._l(e.adTypeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",[2===e.addThirdAdPosForm.adType?a("div",[a("el-col",{attrs:{gutter:30}},[a("el-col",{attrs:{span:5}},[a("div",{staticClass:"grid-content bg-purple"},[a("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.addThirdAdPosForm.adSite,callback:function(t){e.$set(e.addThirdAdPosForm,"adSite",t)},expression:"addThirdAdPosForm.adSite"}},e._l(e.adSiteList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),a("el-radio-group",{attrs:{size:"mini"},model:{value:e.addThirdAdPosForm.renderType,callback:function(t){e.$set(e.addThirdAdPosForm,"renderType",t)},expression:"addThirdAdPosForm.renderType"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),a("el-col",{attrs:{span:19}},[a("div",{staticClass:"grid-content bg-purple"},[1===e.addThirdAdPosForm.platformCode?a("el-checkbox-group",{attrs:{size:"small",min:1,max:"4"},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.csjAdSubTypeList,function(t){return a("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                        ")])}),1):e._e(),e._v(" "),2===e.addThirdAdPosForm.platformCode&&"2"===e.addThirdAdPosForm.renderType?a("el-checkbox-group",{attrs:{size:"small",min:1,max:"4"},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.gdtAdSubOwnTypeList,function(t){return a("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                        ")])}),1):e._e(),e._v(" "),2===e.addThirdAdPosForm.platformCode&&"1"===e.addThirdAdPosForm.renderType?a("el-radio-group",{attrs:{size:"small"},model:{value:e.adTypeRadioChecked,callback:function(t){e.adTypeRadioChecked=t},expression:"adTypeRadioChecked"}},e._l(e.gdtAdSubTypeList,function(t){return a("el-radio",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                        ")])}),1):e._e()],1)])],1)],1):e._e()]),e._v(" "),a("el-form-item",{attrs:{label:"自家广告类型"}},[a("div",{staticClass:"grid-content bg-purple"},[a("div",[a("el-select",{staticClass:"select-150",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.addThirdAdPosForm.adTypeOwn,callback:function(t){e.$set(e.addThirdAdPosForm,"adTypeOwn",t)},expression:"addThirdAdPosForm.adTypeOwn"}},e._l(e.getAdTypeDymicList(e.addThirdAdPosForm.platformCode,e.addThirdAdPosForm.adType),function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])]),e._v(" "),a("el-form-item",{attrs:{label:"目标价格",prop:"packageName"}},[a("el-input",{model:{value:e.addThirdAdPosForm.adPrice,callback:function(t){e.$set(e.addThirdAdPosForm,"adPrice",t)},expression:"addThirdAdPosForm.adPrice"}})],1),e._v(" "),3===e.addThirdAdPosForm.adType||5===e.addThirdAdPosForm.adType?a("div",[a("el-form-item",{attrs:{label:"插屏样式"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.addThirdAdPosForm.innerType,callback:function(t){e.$set(e.addThirdAdPosForm,"innerType",t)},expression:"addThirdAdPosForm.innerType"}},e._l(e.innerTypeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1):e._e(),e._v(" "),e.addThirdAdPosForm.adType>=3?a("div",[a("el-form-item",{attrs:{label:"竞价模式",prop:"packageName"}},[a("el-radio-group",{model:{value:e.addThirdAdPosForm.isBidding,callback:function(t){e.$set(e.addThirdAdPosForm,"isBidding",t)},expression:"addThirdAdPosForm.isBidding"}},[a("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),1===e.addThirdAdPosForm.isBidding?a("div",[a("el-form-item",{attrs:{label:"Bidding接入模式",prop:"packageName"}},[a("el-radio-group",{model:{value:e.addThirdAdPosForm.biddingType,callback:function(t){e.$set(e.addThirdAdPosForm,"biddingType",t)},expression:"addThirdAdPosForm.biddingType"}},[a("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),a("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1),e._v(" "),3===e.addThirdAdPosForm.platformCode?a("div",[a("el-form-item",{attrs:{label:"价格策略",prop:"packageName"}},[a("el-radio-group",{model:{value:e.addThirdAdPosForm.isSetPrice,callback:function(t){e.$set(e.addThirdAdPosForm,"isSetPrice",t)},expression:"addThirdAdPosForm.isSetPrice"}},[a("el-radio",{attrs:{label:1}},[e._v("不设置目标价格")]),e._v(" "),a("el-radio",{attrs:{label:2}},[e._v("设置目标价格")])],1)],1)],1):e._e()],1):e._e()],1):e._e()],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowAddThirdAdPosForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"批量创建广告位",visible:e.isShowAddThirdAdPosBatchForm},on:{"update:visible":function(t){e.isShowAddThirdAdPosBatchForm=t}}},[a("el-form",{ref:"addThirdAdPosBatchForm",attrs:{model:e.addThirdAdPosBatchForm,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"应用类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"应用类型"},model:{value:e.addThirdAdPosBatchForm.os,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"os",t)},expression:"addThirdAdPosBatchForm.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"穿山甲主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},on:{change:function(t){return e.findFitApp(1)}},model:{value:e.addThirdAdPosBatchForm.csjCompanyId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"csjCompanyId",t)},expression:"addThirdAdPosBatchForm.csjCompanyId"}},e._l(e.csjCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"穿山甲应用"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"应用"},model:{value:e.addThirdAdPosBatchForm.csjAppId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"csjAppId",t)},expression:"addThirdAdPosBatchForm.csjAppId"}},e._l(e.applicationListNormal.filter(function(t){return 1===t.platformCode&&t.companyId===e.addThirdAdPosBatchForm.csjCompanyId&&t.os===e.addThirdAdPosBatchForm.os}),function(e){return a("el-option",{attrs:{label:e.appName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"广点通主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},on:{change:function(t){return e.findFitApp(2)}},model:{value:e.addThirdAdPosBatchForm.gdtCompanyId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"gdtCompanyId",t)},expression:"addThirdAdPosBatchForm.gdtCompanyId"}},e._l(e.gdtCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"广点通应用"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"应用"},model:{value:e.addThirdAdPosBatchForm.gdtAppId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"gdtAppId",t)},expression:"addThirdAdPosBatchForm.gdtAppId"}},e._l(e.applicationListNormal.filter(function(t){return 2===t.platformCode&&t.companyId===e.addThirdAdPosBatchForm.gdtCompanyId&&t.os===e.addThirdAdPosBatchForm.os}),function(e){return a("el-option",{attrs:{label:e.appName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"快手主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},on:{change:function(t){return e.findFitApp(3)}},model:{value:e.addThirdAdPosBatchForm.ksCompanyId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"ksCompanyId",t)},expression:"addThirdAdPosBatchForm.ksCompanyId"}},e._l(e.ksCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"快手应用"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"应用"},model:{value:e.addThirdAdPosBatchForm.ksAppId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"ksAppId",t)},expression:"addThirdAdPosBatchForm.ksAppId"}},e._l(e.applicationListNormal.filter(function(t){return 3===t.platformCode&&t.companyId===e.addThirdAdPosBatchForm.ksCompanyId&&t.os===e.addThirdAdPosBatchForm.os}),function(e){return a("el-option",{attrs:{label:e.appName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"百度主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},on:{change:function(t){return e.findFitApp(4)}},model:{value:e.addThirdAdPosBatchForm.bdCompanyId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"bdCompanyId",t)},expression:"addThirdAdPosBatchForm.bdCompanyId"}},e._l(e.bdCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"百度应用"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"应用"},model:{value:e.addThirdAdPosBatchForm.bdAppId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"bdAppId",t)},expression:"addThirdAdPosBatchForm.bdAppId"}},e._l(e.applicationListNormal.filter(function(t){return 4===t.platformCode&&t.companyId===e.addThirdAdPosBatchForm.bdCompanyId&&t.os===e.addThirdAdPosBatchForm.os}),function(e){return a("el-option",{attrs:{label:e.appName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"OPPO主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},on:{change:function(t){return e.findFitApp(4)}},model:{value:e.addThirdAdPosBatchForm.oppoCompanyId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"oppoCompanyId",t)},expression:"addThirdAdPosBatchForm.oppoCompanyId"}},e._l(e.oppoCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"OPPO应用"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"应用"},model:{value:e.addThirdAdPosBatchForm.oppoAppId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"oppoAppId",t)},expression:"addThirdAdPosBatchForm.oppoAppId"}},e._l(e.applicationListNormal.filter(function(t){return 5===t.platformCode&&t.companyId===e.addThirdAdPosBatchForm.oppoCompanyId&&t.os===e.addThirdAdPosBatchForm.os}),function(e){return a("el-option",{attrs:{label:e.appName,value:e.id}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"模板"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"模板",filterable:""},model:{value:e.addThirdAdPosBatchForm.templateId,callback:function(t){e.$set(e.addThirdAdPosBatchForm,"templateId",t)},expression:"addThirdAdPosBatchForm.templateId"}},e._l(e.templateList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowAddThirdAdPosBatchForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdPosBatch()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"同步应用广告位",visible:e.isShowSycnPositionForm},on:{"update:visible":function(t){e.isShowSycnPositionForm=t}}},[a("el-form",{ref:"sycnPositionForm",attrs:{model:e.sycnPositionForm,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"请选择应用"}},[a("el-select",{staticClass:"select-350",attrs:{filterable:"",placeholder:"请选择应用"},model:{value:e.sycnPositionForm.appId,callback:function(t){e.$set(e.sycnPositionForm,"appId",t)},expression:"sycnPositionForm.appId"}},e._l(e.applicationList,function(t){return a("el-option",{attrs:{label:t.val,value:t.key}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.val))]),e._v(" "),a("el-tag",{staticStyle:{float:"right"},attrs:{type:"success"}},[e._v(e._s(t.os))]),e._v(" "),a("el-tag",{staticStyle:{float:"right"}},[e._v(e._s(t.desc))])],1)}),1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowSycnPositionForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.sychAdPosList()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"即将导入以下广告",visible:e.isShowAddThirdLoadForm},on:{"update:visible":function(t){e.isShowAddThirdLoadForm=t}}},[a("el-divider",{attrs:{"content-position":"left"}},[e._v("导入方式")]),e._v(" "),a("el-radio-group",{attrs:{size:"mini"},model:{value:e.importType,callback:function(t){e.importType=t},expression:"importType"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("只导入，不处理相同AdType下的旧广告位")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("导入后关停相同AdType/AppId下的旧广告位")])],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("价格策略")]),e._v(" "),a("el-radio-group",{attrs:{size:"mini"},model:{value:e.biddingImportType,callback:function(t){e.biddingImportType=t},expression:"biddingImportType"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("名称包含bidding自动设置竞价模式为[实时竞价]")]),e._v(" "),a("el-radio-button",{attrs:{label:"2"}},[e._v("按照[目标价]模式全部导入")])],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("导入的广告位和对应产品")]),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingWaitLoadAdList,expression:"loadingWaitLoadAdList"}],staticStyle:{width:"100%"},attrs:{data:e.waitLoadList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"appName",label:"应用名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posId",label:"广告位ID"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",label:"广告位名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"adType",label:"广告类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.adType?a("span",[e._v("开屏")]):2===t.row.adType?a("span",[e._v("信息流")]):3===t.row.adType?a("span",[e._v("插屏")]):4===t.row.adType?a("span",[e._v("激励视频")]):5===t.row.adType?a("span",[e._v("新插屏")]):6===t.row.adType?a("span",[e._v("Banner")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"adPrice",label:"三方底价"}}),e._v(" "),a("el-table-column",{attrs:{prop:"isSamePrice",label:"Check"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.isSamePrice?a("el-tag",{attrs:{type:"success"}},[e._v("价格一致")]):1===t.row.isSamePrice?a("el-tag",{attrs:{type:"warning"}},[e._v("我方过高")]):-1===t.row.isSamePrice?a("el-tag",{attrs:{type:"danger"}},[e._v("三方过高")]):2===t.row.isSamePrice?a("el-tag",{attrs:{type:"info"}},[e._v("没查询到")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"adStatus",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.adStatus?a("el-tag",{attrs:{type:"success"}},[e._v("正常")]):1===t.row.adStatus?a("el-tag",{attrs:{type:"danger"}},[e._v("异常")]):e._e()]}}])})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowAddThirdLoadForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{directives:[{name:"loading",rawName:"v-loading.fullscreen.lock",value:e.fullscreenLoading,expression:"fullscreenLoading",modifiers:{fullscreen:!0,lock:!0}}],attrs:{type:"primary"},on:{click:function(t){return e.loadAdPosBatch()}}},[e._v("导 入")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"底价列表",visible:e.isShowLowPriceForm},on:{"update:visible":function(t){e.isShowLowPriceForm=t}}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParamLowPrice,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"应用类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"应用类型"},model:{value:e.queryParamLowPrice.os,callback:function(t){e.$set(e.queryParamLowPrice,"os",t)},expression:"queryParamLowPrice.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.loadThirdHaveAdPrice(1)}}},[e._v("查看穿山甲")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.loadThirdHaveAdPrice(2)}}},[e._v("查看广点通")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.loadThirdHaveAdPrice(3)}}},[e._v("查看快手")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.loadThirdHaveAdPrice(4)}}},[e._v("查看百度")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("可设置底价列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.lowPriceList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"codeStr",label:"平台"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posId",label:"广告位id"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",label:"广告位名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"osStr",label:"操作系统"}}),e._v(" "),a("el-table-column",{attrs:{prop:"price",label:"返回底价"}})],1),e._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParamLowPrice.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listLowPriceNum,background:""},on:{"current-change":e.handleLowPriceChangePage}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowLowPriceForm=!1}}},[e._v("关闭该弹窗")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"正在批量创建的广告位",visible:e.isShowTaskQueenForm},on:{"update:visible":function(t){e.isShowTaskQueenForm=t}}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.loadTaskQueenList()}}},[e._v("刷新任务列表")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("任务列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.thirdTaskQueenList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"taskId",label:"任务编号"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",label:"广告位名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"retryTimes",label:"重试次数"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"taskStatus",label:"创建状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.taskStatus?a("span",[e._v("成功创建")]):0===t.row.taskStatus?a("span",[e._v("初始化")]):a("span",[e._v("创建失败")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"描述"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[2===t.row.taskStatus?a("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(a){return e.retryTask(t.row.taskId)}}},[e._v("重试")]):e._e()]}}])})],1),e._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParamTask.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listTaskQueenCountNum,background:""},on:{"current-change":e.handleTaskQueenCurrentChangePage}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowTaskQueenForm=!1}}},[e._v("关闭该弹窗")])],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"appId"}},[a("el-input",{staticClass:"width-150",model:{value:e.queryParam.thirdAppId,callback:function(t){e.$set(e.queryParam,"thirdAppId",t)},expression:"queryParam.thirdAppId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"广告位名称"}},[a("el-input",{staticClass:"width-150",model:{value:e.queryParam.posName,callback:function(t){e.$set(e.queryParam,"posName",t)},expression:"queryParam.posName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"posId"}},[a("el-input",{staticClass:"width-150",model:{value:e.queryParam.posId,callback:function(t){e.$set(e.queryParam,"posId",t)},expression:"queryParam.posId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"导入状态"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"导入状态"},model:{value:e.queryParam.importStatus,callback:function(t){e.$set(e.queryParam,"importStatus",t)},expression:"queryParam.importStatus"}},[a("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.importStatusList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})})],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"广告类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.queryParam.adType,callback:function(t){e.$set(e.queryParam,"adType",t)},expression:"queryParam.adType"}},[a("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adTypeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})})],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{align:"right",type:"date",placeholder:"选择日期","picker-options":e.pickerOptions,"value-format":"yyyy-MM-dd"},model:{value:e.queryParam.queryDay,callback:function(t){e.$set(e.queryParam,"queryDay",t)},expression:"queryParam.queryDay"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getThirdAdPostList}})],1)],1)],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{size:"small",inline:""}},[a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.syncPositionPre()}}},[e._v("同步广告位")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addAdPostBatchPre()}}},[e._v("批量添加广告位")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addAdPostPre()}}},[e._v("添加广告位")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.showAndCheckAd()}}},[e._v("一键导入")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowLowPriceForm=!0}}},[e._v("底价查询")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("广告位列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.thirdAdPosList,stripe:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",label:""}}),e._v(" "),a("el-table-column",{attrs:{prop:"appId",label:"appId",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"platformCode",label:"平台",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.platformCode?a("span",[e._v("穿山甲")]):2===t.row.platformCode?a("span",[e._v("广点通")]):3===t.row.platformCode?a("span",[e._v("快手")]):4===t.row.platformCode?a("span",[e._v("百度")]):5===t.row.platformCode?a("span",[e._v("OPPO")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"posId",label:"广告位ID",width:"210"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",label:"广告位名称",width:"330"}}),e._v(" "),a("el-table-column",{attrs:{prop:"adType",label:"广告类型",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.adType?a("span",[e._v("开屏")]):2===t.row.adType?a("span",[e._v("信息流")]):3===t.row.adType?a("span",[e._v("插屏")]):4===t.row.adType?a("span",[e._v("激励视频")]):5===t.row.adType?a("span",[e._v("新插屏")]):6===t.row.adType?a("span",[e._v("Banner")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"posStatus",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.posStatus?a("span",[e._v("正常")]):a("span",[e._v("异常")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"os",label:"系统",width:"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.os?a("span",[e._v("Android")]):2===t.row.os?a("span",[e._v("IOS")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime",width:"190",label:"创建时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"incomeStatus",label:"状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.incomeStatus?a("span",[e._v("已导入")]):a("span",[e._v("未导入")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"adPrice",label:"底价",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(a){return e.updateAdPostPre(t.row)}}},[e._v("修改\n                            ")])]}}])})],1),e._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-sizes":[10,20,50,100],"page-size":10,layout:"total,sizes, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}},406:function(e,t,a){var o=a(341);"string"==typeof o&&(o=[[e.i,o,""]]),o.locals&&(e.exports=o.locals);a(88)("3aab8075",o,!0)}});