webpackJsonp([16],{214:function(e,t,i){i(409);var d=i(87)(i(311),i(387),null,null);e.exports=d.exports},311:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{pageNo:1,pageSize:10,adId:""},listCountNum:0,biddingConfigList:[],adPosTypes:[],adIdList:[],adIdListGdt:[],editBiddingForm:{id:"",adPosType:"",adId:"",startEcpm:0,endEcpm:0,priority:0,biddingType:2,playType:1},addBiddingForm:{adPosType:"",adId:"",startEcpm:0,endEcpm:0,priority:0,biddingType:2,playType:1},isShowEditBiddingForm:!1,isShowAddBiddingForm:!1}},created:function(){this.getBiddingList(),this.queryAdPosTypes(),this.queryAdIdList()},methods:{queryAdPosTypes:function(){var e=this;e.$axios.get("config/getAdPosType").then(function(t){if(200===t.status){var i=t.data;0===i.code?e.adPosTypes=i.result:e.$message.error("加载数据失败...")}else e.$message.error("服务器异常！")})},queryAdIdList:function(){var e=this;e.$axios.get("ad/queryAdByProduct").then(function(t){if(200===t.status){var i=t.data;0===i.ret?e.adIdList=i.data:e.$message.error("加载广告失败...")}else e.$message.error("服务器异常！")}),e.$axios.get("ad/queryAdByProductGdt").then(function(t){if(200===t.status){var i=t.data;0===i.ret?e.adIdListGdt=i.data:e.$message.error("加载广告失败...")}else e.$message.error("服务器异常！")})},getBiddingList:function(){var e=this;e.$axios.post("bidding/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var i=t.data;e.biddingConfigList=i.items,e.listCountNum=i.count}else e.$message.error("服务器异常！")})},saveAdPos:function(){var e=this;e.$refs.editBiddingForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("bidding/updateOne",{},{params:e.editBiddingForm}).then(function(t){if(200===t.status){var i=t.data;0===i.ret?(e.isShowEditBiddingForm=!1,e.$message({message:"Bidding信息更新成功",type:"success"}),e.getBiddingList()):e.$message.error(i.data)}else e.$message.error("服务器异常！")})})},addAdPosPre:function(){this.clearAddForm(),this.isShowAddBiddingForm=!0},addAdPos:function(){var e=this;e.$refs.addBiddingForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("bidding/addOne",{},{params:e.addBiddingForm}).then(function(t){if(200===t.status){var i=t.data;0===i.ret?(e.isShowAddBiddingForm=!1,e.$message({message:"新增Bidding配置成功",type:"success"}),e.getBiddingList()):e.$message.error(i.data)}else e.$message.error("服务器异常！")})})},clearAddForm:function(){this.addBiddingForm.adPosType="",this.addBiddingForm.adId="",this.addBiddingForm.startEcpm=0,this.addBiddingForm.endEcpm=0,this.addBiddingForm.priority=0},updateAdPosPre:function(e){this.editBiddingForm.id=e.id,this.editBiddingForm.adPosType=e.adPosType,this.editBiddingForm.adId=e.adId,this.editBiddingForm.startEcpm=e.startEcpm,this.editBiddingForm.endEcpm=e.endEcpm,this.editBiddingForm.priority=e.priority,this.editBiddingForm.biddingType=e.biddingType,this.editBiddingForm.playType=e.playType,this.isShowEditBiddingForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getBiddingList()},changeBiddingSwitchFlag:function(e,t){var i={};i.id=t.id,i.switchFlag=t.switchFlag;var d=this;d.$axios.post("bidding/switchFlag",{},{params:i}).then(function(e){if(200===e.status){0===e.data.ret?d.$message({message:"更新状态成功",type:"success"}):d.$message.error("更新状态失败")}else d.$message.error("服务器异常！")})},deleteBidding:function(e){var t=this;this.$confirm("此操作将删除该广告位配置, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var i={id:e.id,delFlag:0},d=t;d.$axios.post("bidding/delFlag",{},{params:i}).then(function(e){if(200===e.status){var t=e.data;0===t.ret?(d.$message.success("成功删除！"),d.getBiddingList()):d.$message.error(t.data)}else d.$message.error("服务器异常！")})}).catch(function(){t.$message({type:"info",message:"已取消删除"})})}}}},344:function(e,t,i){t=e.exports=i(29)(),t.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}",""])},387:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"table"},[i("div",{staticClass:"crumbs"},[i("el-breadcrumb",{attrs:{separator:"/"}},[i("el-breadcrumb-item",[i("i",{staticClass:"el-icon-menu"}),e._v(" Bidding广告位配置")]),e._v(" "),i("el-breadcrumb-item",[e._v("Bidding广告位配置")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"编辑Bidding广告位",visible:e.isShowEditBiddingForm},on:{"update:visible":function(t){e.isShowEditBiddingForm=t}}},[i("el-form",{ref:"editBiddingForm",attrs:{model:e.editBiddingForm,"label-width":"200px"}},[i("el-form-item",{attrs:{label:"广告位类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型"},model:{value:e.editBiddingForm.adPosType,callback:function(t){e.$set(e.editBiddingForm,"adPosType",t)},expression:"editBiddingForm.adPosType"}},[i("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adPosTypes,function(e){return i("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告Id"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"AdId",filterable:""},model:{value:e.editBiddingForm.adId,callback:function(t){e.$set(e.editBiddingForm,"adId",t)},expression:"editBiddingForm.adId"}},[i("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adIdList,function(e){return i("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),i("el-form-item",{attrs:{label:"Start ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.editBiddingForm.startEcpm,callback:function(t){e.$set(e.editBiddingForm,"startEcpm",t)},expression:"editBiddingForm.startEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"End ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.editBiddingForm.endEcpm,callback:function(t){e.$set(e.editBiddingForm,"endEcpm",t)},expression:"editBiddingForm.endEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"优先级"}},[i("el-input",{staticClass:"width-100",model:{value:e.editBiddingForm.priority,callback:function(t){e.$set(e.editBiddingForm,"priority",t)},expression:"editBiddingForm.priority"}})],1),e._v(" "),e.adIdListGdt.indexOf(e.editBiddingForm.adId)>-1?i("div",[i("el-form-item",{attrs:{label:"请求方式"}},[i("el-radio-group",{model:{value:e.editBiddingForm.biddingType,callback:function(t){e.$set(e.editBiddingForm,"biddingType",t)},expression:"editBiddingForm.biddingType"}},[i("el-radio",{attrs:{label:1}},[e._v("C2S")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("S2S")])],1)],1)],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"是否参与打底竞价"}},[i("el-radio-group",{model:{value:e.editBiddingForm.playType,callback:function(t){e.$set(e.editBiddingForm,"playType",t)},expression:"editBiddingForm.playType"}},[i("el-radio",{attrs:{label:1}},[e._v("参与")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("不参与")])],1)],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowEditBiddingForm=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"新建Bidding广告位",visible:e.isShowAddBiddingForm},on:{"update:visible":function(t){e.isShowAddBiddingForm=t}}},[i("el-form",{ref:"addBiddingForm",attrs:{model:e.addBiddingForm,"label-width":"200px"}},[i("el-form-item",{attrs:{label:"广告位类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型"},model:{value:e.addBiddingForm.adPosType,callback:function(t){e.$set(e.addBiddingForm,"adPosType",t)},expression:"addBiddingForm.adPosType"}},[i("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adPosTypes,function(e){return i("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告Id"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"AdId",filterable:""},model:{value:e.addBiddingForm.adId,callback:function(t){e.$set(e.addBiddingForm,"adId",t)},expression:"addBiddingForm.adId"}},[i("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adIdList,function(e){return i("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),i("el-form-item",{attrs:{label:"Start ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.addBiddingForm.startEcpm,callback:function(t){e.$set(e.addBiddingForm,"startEcpm",t)},expression:"addBiddingForm.startEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"End ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.addBiddingForm.endEcpm,callback:function(t){e.$set(e.addBiddingForm,"endEcpm",t)},expression:"addBiddingForm.endEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"优先级"}},[i("el-input",{staticClass:"width-100",model:{value:e.addBiddingForm.priority,callback:function(t){e.$set(e.addBiddingForm,"priority",t)},expression:"addBiddingForm.priority"}})],1),e._v(" "),e.adIdListGdt.indexOf(e.addBiddingForm.adId)>-1?i("div",[i("el-form-item",{attrs:{label:"请求方式"}},[i("el-radio-group",{model:{value:e.addBiddingForm.biddingType,callback:function(t){e.$set(e.addBiddingForm,"biddingType",t)},expression:"addBiddingForm.biddingType"}},[i("el-radio",{attrs:{label:1}},[e._v("C2S")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("S2S")])],1)],1)],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"是否参与打底竞价"}},[i("el-radio-group",{model:{value:e.addBiddingForm.playType,callback:function(t){e.$set(e.addBiddingForm,"playType",t)},expression:"addBiddingForm.playType"}},[i("el-radio",{attrs:{label:1}},[e._v("参与")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("不参与")])],1)],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowAddBiddingForm=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[i("el-form-item",{attrs:{label:"广告id"}},[i("el-input",{staticClass:"width-150",model:{value:e.queryParam.adId,callback:function(t){e.$set(e.queryParam,"adId",t)},expression:"queryParam.adId"}})],1),e._v(" "),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getBiddingList}}),e._v(" "),i("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addAdPosPre()}}},[e._v("新建Bidding配置")])],1)],1)],1)],1),e._v(" "),i("el-divider",{attrs:{"content-position":"left"}},[e._v("Bidding广告位列表")]),e._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.biddingConfigList,stripe:"","highlight-current-row":""}},[i("el-table-column",{attrs:{label:"开关"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949","active-value":1,"inactive-value":0},on:{change:function(i){return e.changeBiddingSwitchFlag(t.$index,t.row)}},model:{value:t.row.switchFlag,callback:function(i){e.$set(t.row,"switchFlag",i)},expression:"scope.row.switchFlag"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"id",label:"Id"}}),e._v(" "),i("el-table-column",{attrs:{prop:"adPosType",label:"配置类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.adPosType?i("span",[e._v("开屏")]):2===t.row.adPosType?i("span",[e._v("信息流")]):3===t.row.adPosType?i("span",[e._v("插屏")]):4===t.row.adPosType?i("span",[e._v("激励视频")]):5===t.row.adPosType?i("span",[e._v("Banner")]):6===t.row.adPosType?i("span",[e._v("全屏视频")]):e._e()]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"adId",label:"映射广告Id"}}),e._v(" "),i("el-table-column",{attrs:{prop:"adName",label:"广告名称"}}),e._v(" "),i("el-table-column",{attrs:{prop:"adTypeName",label:"广告类型"}}),e._v(" "),i("el-table-column",{attrs:{prop:"createTime",label:"创建时间"}}),e._v(" "),i("el-table-column",{attrs:{prop:"updateTime",label:"最近修改时间"}}),e._v(" "),i("el-table-column",{attrs:{label:"操作",width:"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"small"},on:{click:function(i){return e.updateAdPosPre(t.row)}}},[e._v("修改")]),e._v(" "),i("el-button",{attrs:{size:"small",type:"danger"},on:{click:function(i){return e.deleteBidding(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),i("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}},409:function(e,t,i){var d=i(344);"string"==typeof d&&(d=[[e.i,d,""]]),d.locals&&(e.exports=d.locals);i(88)("c0a7acf0",d,!0)}});