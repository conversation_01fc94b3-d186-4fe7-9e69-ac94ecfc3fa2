webpackJsonp([21],{231:function(e,t,a){var l=a(87)(a(328),a(366),null,null);e.exports=l.exports},328:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{logday:"",type:""},exAdposList:[],dayList:[],typeList:[{key:"视频",val:"视频"},{key:"插屏",val:"插屏"},{key:"新插屏",val:"新插屏"}]}},created:function(){this.get7Day(),this.queryExList()},methods:{get7Day:function(){for(var e=[],t=-1;t>=-6;t--){var a=new Date,l=a.getTime()+864e5*t;a.setTime(l);var r=a.getFullYear(),s=a.getMonth(),o=a.getDate();s=this.doHandleMonth(s+1),o=this.doHandleMonth(o);var n=r+"-"+s+"-"+o;e.push({key:n,val:n})}this.dayList=e},doHandleMonth:function(e){var t=e;return 1===e.toString().length&&(t="0"+e),t},queryExList:function(){var e=this;e.$axios.post("third/exception/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){var l=a.data;e.exAdposList=l}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})}}}},366:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),e._v("异常广告位")])],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"日期"},model:{value:e.queryParam.logday,callback:function(t){e.$set(e.queryParam,"logday",t)},expression:"queryParam.logday"}},[a("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.dayList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})})],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"广告类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.queryParam.type,callback:function(t){e.$set(e.queryParam,"type",t)},expression:"queryParam.type"}},[a("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.typeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})})],2)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.queryExList()}}},[e._v("搜索")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("广告位列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.exAdposList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"logday",label:"日期"}}),e._v(" "),a("el-table-column",{attrs:{prop:"os",sortable:"",label:"OS"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productName",sortable:"",label:"产品名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sourceName",sortable:"",label:"平台"}}),e._v(" "),a("el-table-column",{attrs:{prop:"typeName",label:"广告类型"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",label:"广告位",width:"350"}}),e._v(" "),a("el-table-column",{attrs:{prop:"rate",sortable:"",label:"pv差异"}}),e._v(" "),a("el-table-column",{attrs:{prop:"income",sortable:"",label:"收入"}})],1)],1)],1)],1)},staticRenderFns:[]}}});