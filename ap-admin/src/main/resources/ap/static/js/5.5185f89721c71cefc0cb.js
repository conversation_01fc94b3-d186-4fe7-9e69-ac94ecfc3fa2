webpackJsonp([5],{

/***/ 227:
/***/ (function(module, exports, __webpack_require__) {


/* styles */
__webpack_require__(408)

var Component = __webpack_require__(87)(
  /* script */
  __webpack_require__(324),
  /* template */
  __webpack_require__(386),
  /* scopeId */
  "data-v-b49f39fc",
  /* cssModules */
  null
)

module.exports = Component.exports


/***/ }),

/***/ 292:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* unused harmony export warn */
/* unused harmony export error */
/* harmony export (immutable) */ __webpack_exports__["c"] = nextTick;
/* unused harmony export trim */
/* unused harmony export trimEnd */
/* unused harmony export toLower */
/* unused harmony export toUpper */
/* unused harmony export isString */
/* unused harmony export isBoolean */
/* unused harmony export isFunction */
/* harmony export (immutable) */ __webpack_exports__["a"] = isObject;
/* unused harmony export isPlainObject */
/* unused harmony export isBlob */
/* unused harmony export isFormData */
/* harmony export (immutable) */ __webpack_exports__["b"] = when;
/* unused harmony export options */
/* unused harmony export each */
/* unused harmony export merge */
/* unused harmony export defaults */
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__promise__ = __webpack_require__(397);
/**
 * Utility functions.
 */



var {hasOwnProperty} = {}, {slice} = [], debug = false, ntick;

const inBrowser = typeof window !== 'undefined';
/* unused harmony export inBrowser */


/* unused harmony default export */ var _unused_webpack_default_export = (function ({config, nextTick}) {
    ntick = nextTick;
    debug = config.debug || !config.silent;
});

function warn(msg) {
    if (typeof console !== 'undefined' && debug) {
        console.warn('[VueResource warn]: ' + msg);
    }
}

function error(msg) {
    if (typeof console !== 'undefined') {
        console.error(msg);
    }
}

function nextTick(cb, ctx) {
    return ntick(cb, ctx);
}

function trim(str) {
    return str ? str.replace(/^\s*|\s*$/g, '') : '';
}

function trimEnd(str, chars) {

    if (str && chars === undefined) {
        return str.replace(/\s+$/, '');
    }

    if (!str || !chars) {
        return str;
    }

    return str.replace(new RegExp(`[${chars}]+$`), '');
}

function toLower(str) {
    return str ? str.toLowerCase() : '';
}

function toUpper(str) {
    return str ? str.toUpperCase() : '';
}

const isArray = Array.isArray;
/* unused harmony export isArray */


function isString(val) {
    return typeof val === 'string';
}

function isBoolean(val) {
    return val === true || val === false;
}

function isFunction(val) {
    return typeof val === 'function';
}

function isObject(obj) {
    return obj !== null && typeof obj === 'object';
}

function isPlainObject(obj) {
    return isObject(obj) && Object.getPrototypeOf(obj) == Object.prototype;
}

function isBlob(obj) {
    return typeof Blob !== 'undefined' && obj instanceof Blob;
}

function isFormData(obj) {
    return typeof FormData !== 'undefined' && obj instanceof FormData;
}

function when(value, fulfilled, rejected) {

    var promise = __WEBPACK_IMPORTED_MODULE_0__promise__["a" /* default */].resolve(value);

    if (arguments.length < 2) {
        return promise;
    }

    return promise.then(fulfilled, rejected);
}

function options(fn, obj, opts) {

    opts = opts || {};

    if (isFunction(opts)) {
        opts = opts.call(obj);
    }

    return merge(fn.bind({$vm: obj, $options: opts}), fn, {$options: opts});
}

function each(obj, iterator) {

    var i, key;

    if (isArray(obj)) {
        for (i = 0; i < obj.length; i++) {
            iterator.call(obj[i], obj[i], i);
        }
    } else if (isObject(obj)) {
        for (key in obj) {
            if (hasOwnProperty.call(obj, key)) {
                iterator.call(obj[key], obj[key], key);
            }
        }
    }

    return obj;
}

const assign = Object.assign || _assign;
/* unused harmony export assign */


function merge(target) {

    var args = slice.call(arguments, 1);

    args.forEach(source => {
        _merge(target, source, true);
    });

    return target;
}

function defaults(target) {

    var args = slice.call(arguments, 1);

    args.forEach(source => {

        for (var key in source) {
            if (target[key] === undefined) {
                target[key] = source[key];
            }
        }

    });

    return target;
}

function _assign(target) {

    var args = slice.call(arguments, 1);

    args.forEach(source => {
        _merge(target, source);
    });

    return target;
}

function _merge(target, source, deep) {
    for (var key in source) {
        if (deep && (isPlainObject(source[key]) || isArray(source[key]))) {
            if (isPlainObject(source[key]) && !isPlainObject(target[key])) {
                target[key] = {};
            }
            if (isArray(source[key]) && !isArray(target[key])) {
                target[key] = [];
            }
            _merge(target[key], source[key], deep);
        } else if (source[key] !== undefined) {
            target[key] = source[key];
        }
    }
}


/***/ }),

/***/ 324:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_vue_resource_src_http_interceptor_json__ = __webpack_require__(395);
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1_element_ui_src_locale_lang_da__ = __webpack_require__(350);





/* harmony default export */ __webpack_exports__["default"] = ({
    data: function data() {
        return {
            osOptions: [{
                value: 2,
                label: 'ios'
            }, {
                value: 1,
                label: 'android'
            }],
            platformOptions: [{
                adTypeName: '',
                adTypeCode: ''
            }],
            products: [{
                label: "",
                value: ""
            }],
            boolProduct: false,
            boolPlatform: false,
            boolSdkVersion: true,
            boolVersion: true,
            deleteDialogSwitcher: false,
            dialogSwitcher: false,
            dialogType: null,
            configDiscountList: [{
                isEnable: "",
                id: "",
                discountName: "",
                os: "",
                platformCode: "",
                adPos: "",
                waterFallDiscountValue: "",
                bidDiscountValue: "",
                product: "",
                priority: ""
            }],
            configDiscountAddOrEditParam: {
                id: null,
                discountName: '',
                bidDiscountValue: '',
                os: '',
                waterFallDiscountValue: '',
                platformCode: '',
                product: [],
                adPos: [],
                biddingType: '',
                isEnabled: true,
                appVersionOrientation: {
                    start: '',
                    end: '',
                    limit: ''
                },
                sdkVersionOrientation: {
                    start: '',
                    end: '',
                    limit: ''
                },
                channelIdOrientation: '',
                skipChannelOrientation: '',
                abTestOrientation: '',
                userSourceOrientation: '',
                priority: ''
            },
            configDiscountAdd: {
                discountName: null,
                os: null,
                bidDiscountValue: null,
                waterFallDiscountValue: null,
                isEnabled: false,
                appGreaterVersionOrientation: '',
                appLessVersionOrientation: '',
                sdkGreaterVersionOrientation: '',
                sdkLessVersionOrientation: '',
                channelIdOrientation: null,
                skipChannelOrientation: null,
                abTestOrientation: null,
                priority: 1,
                checkedPlatformOptions: [],
                checkedProduct: [],
                checkAll: false,
                checkAllPlatform: false
            },
            configDiscountEdit: {
                id: '',
                discountName: null,
                os: null,
                bidDiscountValue: null,
                waterFallDiscountValue: null,
                isEnabled: false,
                appGreaterVersionOrientation: '',
                appLessVersionOrientation: '',
                sdkGreaterVersionOrientation: '',
                sdkLessVersionOrientation: '',
                channelIdOrientation: null,
                skipChannelOrientation: null,
                abTestOrientation: null,
                priority: 1,
                checkedPlatformOptions: [],
                checkedProduct: [],
                checkAll: false,
                checkAllPlatform: false
            },
            configDiscountView: {
                id: '',
                discountName: '',
                os: '',
                waterFallDiscountValue: '',
                bidDiscountValue: '',
                isEnabled: true,
                appVersionOrientation: {
                    start: '',
                    end: '',
                    limit: ''
                },
                sdkVersionOrientation: {
                    start: '',
                    end: '',
                    limit: ''
                },
                channelIdOrientation: '',
                skipChannelOrientation: '',
                abTestOrientation: '',
                priority: '',
                platformOptions: [],
                products: [],
                checkedProduct: [],
                checkedPlatformOptions: [],
                checkAll: false
            },
            configDiscountDelete: {
                id: null
            },
            selectedCities: [],
            queryParam: {
                pageNo: 1,
                pageSize: 10,
                discountName: ''
            },
            listCountNum: null
        };
    },

    methods: {
        openDialog: function openDialog(type, row) {
            this.clearConfigDiscountAdd();
            this.dialogSwitcher = true;
            this.dialogType = type;
            if (type === 'view' || type === 'edit') {
                this.configDiscountDelete.id = row.id;
                this.queryMessageById(type);
            }
        },
        clearConfigDiscountAdd: function clearConfigDiscountAdd() {
            this.configDiscountAdd.discountName = '', this.configDiscountAdd.os = '', this.configDiscountAdd.discountNamewaterFallDiscountValue = '', this.configDiscountAdd.discountNameisEnabled = true, this.configDiscountAdd.discountNameappGreaterVersionOrientation = '', this.configDiscountAdd.discountNameappLessVersionOrientation = '', this.configDiscountAdd.discountNamesdkGreaterVersionOrientation = '', this.configDiscountAdd.discountNamesdkLessVersionOrientation = '', this.configDiscountAdd.discountNamechannelIdOrientation = '', this.configDiscountAdd.discountNameskipChannelOrientation = '', this.configDiscountAdd.discountNameabTestOrientation = '', this.configDiscountAdd.discountNamepriority = '', this.configDiscountAdd.discountNameplatformOptions = [], this.configDiscountAdd.discountNameproducts = [], this.configDiscountAdd.discountNamecheckedProduct = [], this.configDiscountAdd.checkAll = false;
        },
        closeDialog: function closeDialog() {
            this.dialogSwitcher = false;
        },
        createConfigDiscount: function createConfigDiscount(type) {
            var _this2 = this;

            this.initParam(type);

            var _this = this;
            _this.$axios.post('/config/discount/saveOrUpdate', _this.configDiscountAddOrEditParam, {}).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        _this2.closeDialog();
                        _this.$message({ message: '新增竞价系数配置成功', type: 'success' });
                        _this.getConfigDiscountList();
                    } else {
                        _this.$message.error(data.data);
                    }
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        initParam: function initParam(type) {
            if (type === 'save') {
                this.configDiscountAddOrEditParam.discountName = this.configDiscountAdd.discountName;
                this.configDiscountAddOrEditParam.bidDiscountValue = this.configDiscountAdd.bidDiscountValue;
                this.configDiscountAddOrEditParam.os = this.configDiscountAdd.os;
                this.configDiscountAddOrEditParam.waterFallDiscountValue = this.configDiscountAdd.waterFallDiscountValue;
                if (this.configDiscountAdd.checkAll) {
                    this.configDiscountAddOrEditParam.product = 'all';
                } else {
                    this.configDiscountAddOrEditParam.product = this.configDiscountAdd.checkedProduct.join(',');
                }
                if (this.configDiscountAdd.checkAllPlatform) {
                    this.configDiscountAddOrEditParam.adPos = 'all';
                } else {
                    this.configDiscountAddOrEditParam.adPos = this.configDiscountAdd.checkedPlatformOptions.join(',');
                }
                this.configDiscountAddOrEditParam.biddingType = 0;
                this.configDiscountAddOrEditParam.isEnabled = this.configDiscountAdd.isEnabled === 'true' ? 1 : 0;
                this.configDiscountAddOrEditParam.appVersionOrientation.start = this.configDiscountAdd.appGreaterVersionOrientation;
                this.configDiscountAddOrEditParam.appVersionOrientation.end = this.configDiscountAdd.appLessVersionOrientation;
                this.configDiscountAddOrEditParam.appVersionOrientation.limit = this.boolVersion ? 0 : 1;
                this.configDiscountAddOrEditParam.sdkVersionOrientation.start = this.configDiscountAdd.sdkGreaterVersionOrientation;
                this.configDiscountAddOrEditParam.sdkVersionOrientation.end = this.configDiscountAdd.sdkLessVersionOrientation;
                this.configDiscountAddOrEditParam.sdkVersionOrientation.limit = this.boolSdkVersion ? 0 : 1;
                this.configDiscountAddOrEditParam.channelIdOrientation = this.configDiscountAdd.channelIdOrientation;
                this.configDiscountAddOrEditParam.skipChannelOrientation = this.configDiscountAdd.skipChannelOrientation;
                this.configDiscountAddOrEditParam.abTestOrientation = this.configDiscountAdd.abTestOrientation;
                this.configDiscountAddOrEditParam.priority = this.configDiscountAdd.priority;
            } else if (type === 'edit') {
                this.configDiscountAddOrEditParam.id = this.configDiscountEdit.id;
                this.configDiscountAddOrEditParam.discountName = this.configDiscountEdit.discountName;
                this.configDiscountAddOrEditParam.bidDiscountValue = this.configDiscountEdit.bidDiscountValue;
                this.configDiscountAddOrEditParam.os = this.configDiscountEdit.os;
                this.configDiscountAddOrEditParam.waterFallDiscountValue = this.configDiscountEdit.waterFallDiscountValue;
                if (this.configDiscountEdit.checkAll) {
                    this.configDiscountAddOrEditParam.product = 'all';
                } else {
                    this.configDiscountAddOrEditParam.product = this.configDiscountEdit.checkedProduct.join(',');
                }
                if (this.configDiscountEdit.checkAllPlatform) {
                    this.configDiscountAddOrEditParam.adPos = 'all';
                } else {
                    this.configDiscountAddOrEditParam.adPos = this.configDiscountEdit.checkedPlatformOptions.join(',');
                }
                this.configDiscountAddOrEditParam.biddingType = 0;
                this.configDiscountAddOrEditParam.isEnabled = this.configDiscountEdit.isEnabled ? 1 : 0;
                this.configDiscountAddOrEditParam.appVersionOrientation.start = this.configDiscountEdit.appGreaterVersionOrientation;
                this.configDiscountAddOrEditParam.appVersionOrientation.end = this.configDiscountEdit.appLessVersionOrientation;
                this.configDiscountAddOrEditParam.appVersionOrientation.limit = this.boolVersion ? 0 : 1;
                this.configDiscountAddOrEditParam.sdkVersionOrientation.start = this.configDiscountEdit.sdkGreaterVersionOrientation;
                this.configDiscountAddOrEditParam.sdkVersionOrientation.end = this.configDiscountEdit.sdkLessVersionOrientation;
                this.configDiscountAddOrEditParam.sdkVersionOrientation.limit = this.boolSdkVersion ? 0 : 1;
                this.configDiscountAddOrEditParam.channelIdOrientation = this.configDiscountEdit.channelIdOrientation;
                this.configDiscountAddOrEditParam.skipChannelOrientation = this.configDiscountEdit.skipChannelOrientation;
                this.configDiscountAddOrEditParam.abTestOrientation = this.configDiscountEdit.abTestOrientation;
                this.configDiscountAddOrEditParam.priority = this.configDiscountEdit.priority;
            }
        },
        editConfigDiscount: function editConfigDiscount(type) {
            this.initParam(type);
            var _this = this;
            _this.$axios.post('/config/discount/saveOrUpdate', _this.configDiscountAddOrEditParam, {}).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        _this.closeDialog();
                        _this.$message({ message: '竞价系数信息更新成功', type: 'success' });
                        _this.getConfigDiscountList();
                    } else {
                        _this.$message.error(data.data);
                    }
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        handleCurrentChangePage: function handleCurrentChangePage(val) {
            this.queryParam.pageNo = val;
            this.getConfigDiscountList();
        },
        getConfigDiscountList: function getConfigDiscountList() {
            var _this = this;
            _this.$axios.post('config/discount/list', {}, { params: _this.queryParam }).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        data.data.items.forEach(function (item) {
                            if (item.os === 1) {
                                item.os = "android";
                            } else if (item.os === 0) {
                                item.os = "ios";
                            }
                            if (item.isEnabled === 1) {
                                item.isEnabled = true;
                            } else if (item.isEnabled === 0) {
                                item.isEnabled = false;
                            }
                        });
                        _this.configDiscountList = data.data.items;
                        _this.listCountNum = data.data.count;
                    } else {
                        _this.$message.error(data.data);
                    }
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        deleteDialogPre: function deleteDialogPre(row) {
            this.configDiscountDelete.id = row.id;
            this.deleteDialogSwitcher = true;
        },
        handleDelete4Sure: function handleDelete4Sure() {
            var _this = this;
            _this.$axios.put('/config/discount/delete', _this.configDiscountDelete, {}).then(function (res) {
                if (res.status === 200) {
                    _this.deleteDialogSwitcher = false;
                    _this.$message({ message: '竞价系数配置删除成功', type: 'success' });
                    _this.getConfigDiscountList();
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        queryMessageById: function queryMessageById(type) {
            var _this = this;
            _this.$axios.get('/config/discount/queryById', { params: _this.configDiscountDelete }).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        if (type === 'edit') {
                            _this.fillData(type, data);
                        } else if (type === 'view') {
                            _this.fillData(type, data);
                        }
                    } else {
                        _this.$message.error(data.data);
                    }
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        fillData: function fillData(type, data) {
            var _this3 = this;

            if (type === 'edit') {
                this.configDiscountEdit.id = data.data.id;
                this.configDiscountEdit.discountName = data.data.discountName;
                this.configDiscountEdit.os = data.data.os;
                this.configDiscountEdit.bidDiscountValue = data.data.bidDiscountValue;
                this.configDiscountEdit.isEnabled = data.data.isEnabled === 1;
                if (data.data.sdkVersionOrientation.limit === 0) {
                    this.boolSdkVersion = true;
                    this.configDiscountEdit.sdkGreaterVersionOrientation = '';
                    this.configDiscountEdit.sdkLessVersionOrientation = '';
                } else {
                    this.boolSdkVersion = false;
                    this.configDiscountEdit.sdkGreaterVersionOrientation = data.data.sdkVersionOrientation.start;
                    this.configDiscountEdit.sdkLessVersionOrientation = data.data.sdkVersionOrientation.end;
                }
                if (data.data.appVersionOrientation.limit === 0) {
                    this.boolVersion = true;
                    this.configDiscountEdit.appGreaterVersionOrientation = '';
                    this.configDiscountEdit.appLessVersionOrientation = '';
                } else {
                    this.boolVersion = false;
                    this.configDiscountEdit.appGreaterVersionOrientation = data.data.appVersionOrientation.start;
                    this.configDiscountEdit.appLessVersionOrientation = data.data.appVersionOrientation.end;
                }
                this.configDiscountEdit.channelIdOrientation = data.data.channelIdOrientation;
                this.configDiscountEdit.skipChannelOrientation = data.data.skipChannelOrientation;
                this.configDiscountEdit.abTestOrientation = data.data.abTestOrientation;
                this.configDiscountEdit.priority = data.data.priority;
                if (data.data.adPos === 'all') {
                    this.boolPlatform = true;
                } else if (data.data.adPos.length > 0) {
                    this.configDiscountEdit.checkedPlatformOptions = [];
                    data.data.adPos.split(',').forEach(function (ad) {
                        _this3.configDiscountEdit.checkedPlatformOptions.push(parseInt(ad));
                    });
                }
                if (data.data.product === 'all') {
                    this.boolProduct = true;
                } else if (data.data.product.length > 0) {
                    this.configDiscountEdit.checkedProduct = [];
                    data.data.product.split(',').forEach(function (prod) {
                        _this3.configDiscountEdit.checkedProduct.push(parseInt(prod));
                    });
                }
                this.configDiscountEdit.waterFallDiscountValue = data.data.waterFallDiscountValue;
                this.configDiscountEdit.checkAll = data.data.product === 'all';
                this.configDiscountEdit.checkAllPlatform = data.data.adPos === 'all';
                this.boolProduct = data.data.product === 'all';
                this.boolPlatform = data.data.adPos === 'all';
            } else if (type === 'view') {
                this.configDiscountView = data.data;
                if (data.data.adPos === 'all') {
                    this.boolPlatform = true;
                    this.configDiscountView.checkedPlatformOptions = 'all';
                } else if (data.data.adPos.length > 0) {
                    this.configDiscountView.checkedPlatformOptions = [];
                    data.data.adPos.split(',').forEach(function (ad) {
                        _this3.configDiscountView.checkedPlatformOptions.push(parseInt(ad));
                    });
                }
                if (data.data.product === 'all') {
                    this.boolProduct = true;
                    this.configDiscountView.checkedProduct = 'all';
                } else if (data.data.product.length > 0) {
                    this.configDiscountView.checkedProduct = [];
                    data.data.product.split(',').forEach(function (prod) {
                        _this3.configDiscountView.checkedProduct.push(parseInt(prod));
                    });
                }
                this.configDiscountView.isEnabled = data.data.isEnabled === 1;
                this.configDiscountView.os = data.data.os === 1 ? 'android' : 'ios';
            }
        },
        getAppList: function getAppList() {
            var _this4 = this;

            var _this = this;
            _this.$axios.post('app/shortNameList', {}, {}).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        _this4.products = data.data;
                    } else {
                        _this.$message.error(data.data);
                    }
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        getPlatformAdPos: function getPlatformAdPos() {
            var _this5 = this;

            var _this = this;
            _this.$axios.get('config/discount/getPlatformAdPos', {}).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        _this5.platformOptions = data.data;
                    }
                } else {
                    _this.$message.error('服务器异常！');
                }
            });
        },
        handleFlagVersion: function handleFlagVersion(type) {
            if (this.boolVersion) {
                if (type === 'add') {
                    this.configDiscountAdd.appGreaterVersionOrientation = "";
                    this.configDiscountAdd.appLessVersionOrientation = "";
                } else if (type === 'edit') {
                    this.configDiscountEdit.appGreaterVersionOrientation = "";
                    this.configDiscountEdit.appLessVersionOrientation = "";
                }
            }
        },
        handleFlagSdkVersion: function handleFlagSdkVersion(type) {
            if (this.boolSdkVersion) {
                if (type === 'add') {
                    this.configDiscountAdd.sdkGreaterVersionOrientation = "";
                    this.configDiscountAdd.sdkLessVersionOrientation = "";
                } else if (type === 'edit') {
                    this.configDiscountEdit.sdkGreaterVersionOrientation = "";
                    this.configDiscountEdit.sdkLessVersionOrientation = "";
                }
            }
        },
        handlePlatformCheckAll: function handlePlatformCheckAll(type) {
            if (this.boolPlatform) {
                if (type === 'add') {
                    this.configDiscountAdd.checkAllPlatform = true;
                    this.configDiscountAdd.checkedPlatformOptions = [];
                } else if (type === 'edit') {
                    this.configDiscountEdit.checkAllPlatform = true;
                    this.configDiscountEdit.checkedPlatformOptions = [];
                }
            } else {
                if (type === 'add') {
                    this.configDiscountAdd.checkAllPlatform = false;
                } else if (type === 'edit') {
                    this.configDiscountEdit.checkAllPlatform = false;
                }
            }
        },
        handleProductCheckAll: function handleProductCheckAll(type) {
            if (this.boolProduct) {
                if (type === 'add') {
                    this.configDiscountAdd.checkAll = true;
                    this.configDiscountAdd.checkedProduct = [];
                } else if (type === 'edit') {
                    this.configDiscountEdit.checkAll = true;
                    this.configDiscountEdit.checkedProduct = [];
                }
            } else {
                if (type === 'add') {
                    this.configDiscountAdd.checkAll = false;
                } else if (type === 'edit') {
                    this.configDiscountEdit.checkAll = false;
                }
            }
        },
        handleSwitchChange: function handleSwitchChange(row) {
            var _this6 = this;

            var dataToSend = {
                id: row.id,
                isEnabled: row.isEnabled
            };

            this.$axios.put('config/discount/updateState', dataToSend, {}).then(function (res) {
                if (res.status === 200) {
                    var data = res.data;
                    if (data.ret === 0) {
                        _this6.$message({ message: '新增竞价系数开关更改成功', type: 'success' });
                    } else {
                        _this6.$message.error(data.data);
                    }
                } else {
                    _this6.$message.error('服务器异常！');
                }
            });
        }
    },
    created: function created() {
        this.getConfigDiscountList();
        this.getPlatformAdPos();
        this.getAppList();
    }
});

/***/ }),

/***/ 343:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(29)();
// imports


// module
exports.push([module.i, ".add-button[data-v-b49f39fc]{display:flex;justify-content:flex-end;margin-bottom:30px}.config-discount-table[data-v-b49f39fc]{width:100%}.input-inline1[data-v-b49f39fc]{order:2;width:25%}.input-inline2[data-v-b49f39fc]{order:4;width:25%}", ""]);

// exports


/***/ }),

/***/ 350:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* unused harmony default export */ var _unused_webpack_default_export = ({
  el: {
    colorpicker: {
      confirm: 'OK',
      clear: 'Ryd'
    },
    datepicker: {
      now: 'Nu',
      today: 'I dag',
      cancel: 'Annuller',
      clear: 'Ryd',
      confirm: 'OK',
      selectDate: 'Vælg dato',
      selectTime: 'Vælg tidspunkt',
      startDate: 'Startdato',
      startTime: 'Starttidspunkt',
      endDate: 'Slutdato',
      endTime: 'Sluttidspunkt',
      prevYear: 'Forrige år',
      nextYear: 'Næste år',
      prevMonth: 'Forrige måned',
      nextMonth: 'Næste måned',
      year: '',
      month1: 'Januar',
      month2: 'Februar',
      month3: 'Marts',
      month4: 'April',
      month5: 'Maj',
      month6: 'Juni',
      month7: 'Juli',
      month8: 'August',
      month9: 'September',
      month10: 'Oktober',
      month11: 'November',
      month12: 'December',
      week: 'uge',
      weeks: {
        sun: 'Søn',
        mon: 'Man',
        tue: 'Tir',
        wed: 'Ons',
        thu: 'Tor',
        fri: 'Fre',
        sat: 'Lør'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Maj',
        jun: 'Jun',
        jul: 'Jul',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dec'
      }
    },
    select: {
      loading: 'Henter',
      noMatch: 'Ingen matchende data',
      noData: 'Ingen data',
      placeholder: 'Vælg'
    },
    cascader: {
      noMatch: 'Ingen matchende data',
      loading: 'Henter',
      placeholder: 'Vælg',
      noData: 'Ingen data'
    },
    pagination: {
      goto: 'Gå til',
      pagesize: '/side',
      total: 'Total {total}',
      pageClassifier: ''
    },
    messagebox: {
      confirm: 'OK',
      cancel: 'Annuller',
      error: 'Ugyldig input'
    },
    upload: {
      deleteTip: 'tryk slet for at fjerne',
      delete: 'Slet',
      preview: 'Forhåndsvisning',
      continue: 'Fortsæt'
    },
    table: {
      emptyText: 'Ingen data',
      confirmFilter: 'Bekræft',
      resetFilter: 'Nulstil',
      clearFilter: 'Alle',
      sumText: 'Sum'
    },
    tree: {
      emptyText: 'Ingen data'
    },
    transfer: {
      noMatch: 'Ingen matchende data',
      noData: 'Ingen data',
      titles: ['Liste 1', 'Liste 2'],
      filterPlaceholder: 'Indtast søgeord',
      noCheckedFormat: '{total} emner',
      hasCheckedFormat: '{checked}/{total} valgt'
    },
    image: {
      error: 'FAILED' // to be translated
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    }
  }
});


/***/ }),

/***/ 386:
/***/ (function(module, exports) {

module.exports={render:function (){var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;
  return _c('div', [_c('el-row', [_c('el-col', {
    attrs: {
      "span": 24
    }
  }, [_c('el-form', {
    attrs: {
      "model": _vm.queryParam,
      "size": "small",
      "inline": ""
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "策略名称"
    }
  }, [_c('el-input', {
    staticClass: "width-150",
    model: {
      value: (_vm.queryParam.discountName),
      callback: function($$v) {
        _vm.$set(_vm.queryParam, "discountName", $$v)
      },
      expression: "queryParam.discountName"
    }
  })], 1), _vm._v(" "), _c('el-form-item', [_c('el-button', {
    attrs: {
      "type": "primary",
      "plain": "",
      "icon": "el-icon-search",
      "circle": ""
    },
    on: {
      "click": function($event) {
        return _vm.getConfigDiscountList()
      }
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    staticStyle: {
      "float": "right"
    }
  }, [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": function($event) {
        return _vm.openDialog('add')
      }
    }
  }, [_vm._v("添加配置")])], 1)], 1)], 1)], 1), _vm._v(" "), _c('el-divider', {
    attrs: {
      "content-position": "left"
    }
  }, [_vm._v("系数配置列表")]), _vm._v(" "), _c('div', {
    staticClass: "config-discount-table"
  }, [_c('el-row', [_c('el-col', {
    attrs: {
      "span": 24
    }
  }, [_c('el-table', {
    staticStyle: {
      "width": "100%"
    },
    attrs: {
      "data": _vm.configDiscountList,
      "stripe": "",
      "border": ""
    }
  }, [_c('el-table-column', {
    attrs: {
      "fixed": "",
      "prop": "isEnabled",
      "label": "开关",
      "width": "100"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function(scope) {
        return [_c('el-switch', {
          attrs: {
            "active-color": "#13ce66",
            "inactive-color": "#ff4949"
          },
          on: {
            "change": function($event) {
              return _vm.handleSwitchChange(scope.row)
            }
          },
          model: {
            value: (scope.row.isEnabled),
            callback: function($$v) {
              _vm.$set(scope.row, "isEnabled", $$v)
            },
            expression: "scope.row.isEnabled"
          }
        })]
      }
    }])
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "id",
      "label": "id"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "discountName",
      "label": "策略名称"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "product",
      "label": "生效应用"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "os",
      "label": "系统"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "adPos",
      "label": "广告位"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "waterFallDiscountValue",
      "label": "瀑布流竞价系数"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "prop": "bidDiscountValue",
      "label": "bidding竞价系数"
    }
  }), _vm._v(" "), _c('el-table-column', {
    attrs: {
      "label": "操作",
      "align": "center",
      "width": "240"
    },
    scopedSlots: _vm._u([{
      key: "default",
      fn: function(scope) {
        return [_c('el-button-group', [_c('el-button', {
          attrs: {
            "plain": "",
            "type": "primary",
            "size": "mini"
          },
          on: {
            "click": function($event) {
              return _vm.openDialog('view', scope.row)
            }
          }
        }, [_vm._v("查看")]), _vm._v(" "), _c('el-button', {
          attrs: {
            "plain": "",
            "type": "primary",
            "size": "mini"
          },
          on: {
            "click": function($event) {
              return _vm.openDialog('edit', scope.row)
            }
          }
        }, [_vm._v("编辑")]), _vm._v(" "), _c('el-button', {
          attrs: {
            "plain": "",
            "type": "danger",
            "size": "mini"
          },
          on: {
            "click": function($event) {
              return _vm.deleteDialogPre(scope.row)
            }
          }
        }, [_vm._v("删除")])], 1)]
      }
    }])
  })], 1), _vm._v(" "), _c('el-pagination', {
    staticStyle: {
      "float": "right"
    },
    attrs: {
      "current-page": _vm.queryParam.pageNo,
      "page-size": 10,
      "layout": "total, prev, pager, next, jumper",
      "total": _vm.listCountNum,
      "background": ""
    },
    on: {
      "current-change": _vm.handleCurrentChangePage
    }
  })], 1)], 1)], 1), _vm._v(" "), _c('el-dialog', {
    attrs: {
      "title": "操作",
      "visible": _vm.dialogSwitcher,
      "width": "50%"
    },
    on: {
      "update:visible": function($event) {
        _vm.dialogSwitcher = $event
      }
    }
  }, [_c('el-row', [_c('el-col', {
    attrs: {
      "span": 24
    }
  }, [(_vm.dialogType === 'add') ? _c('el-form', {
    ref: "configDiscountAdd",
    attrs: {
      "model": _vm.configDiscountAdd,
      "label-width": "120px"
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "策略名称"
    }
  }, [_c('el-input', {
    attrs: {
      "prop": "discountName"
    },
    model: {
      value: (_vm.configDiscountAdd.discountName),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "discountName", $$v)
      },
      expression: "configDiscountAdd.discountName"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "os",
      "prop": "os"
    }
  }, [_c('el-select', {
    model: {
      value: (_vm.configDiscountAdd.os),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "os", $$v)
      },
      expression: "configDiscountAdd.os"
    }
  }, _vm._l((_vm.osOptions), function(item) {
    return _c('el-option', {
      key: item.value,
      attrs: {
        "label": item.label,
        "value": item.value
      }
    })
  }), 1)], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "生效应用",
      "prop": "product"
    }
  }, [_c('el-select', {
    attrs: {
      "multiple": "",
      "placeholder": "请选择有效应用",
      "filterable": "",
      "disabled": _vm.boolProduct
    },
    model: {
      value: (_vm.configDiscountAdd.checkedProduct),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "checkedProduct", $$v)
      },
      expression: "configDiscountAdd.checkedProduct"
    }
  }, _vm._l((_vm.products), function(product) {
    return _c('el-option', {
      key: product.value,
      attrs: {
        "label": product.label,
        "value": product.value
      }
    })
  }), 1), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handleProductCheckAll('add')
      }
    },
    model: {
      value: (_vm.boolProduct),
      callback: function($$v) {
        _vm.boolProduct = $$v
      },
      expression: "boolProduct"
    }
  }, [_vm._v("\n                                    全选应用\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "广告类型",
      "prop": "adPos"
    }
  }, [_c('el-select', {
    attrs: {
      "multiple": "",
      "placeholder": "请选择广告类型",
      "filterable": "",
      "disabled": _vm.boolPlatform
    },
    model: {
      value: (_vm.configDiscountAdd.checkedPlatformOptions),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "checkedPlatformOptions", $$v)
      },
      expression: "configDiscountAdd.checkedPlatformOptions"
    }
  }, _vm._l((_vm.platformOptions), function(platform) {
    return _c('el-option', {
      key: platform.adTypeCode,
      attrs: {
        "label": platform.adTypeName,
        "value": platform.adTypeCode
      }
    })
  }), 1), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handlePlatformCheckAll('add')
      }
    },
    model: {
      value: (_vm.boolPlatform),
      callback: function($$v) {
        _vm.boolPlatform = $$v
      },
      expression: "boolPlatform"
    }
  }, [_vm._v("\n                                    全选广告类型\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "bidding折扣值",
      "prop": "bidDiscountValue"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountAdd.bidDiscountValue),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "bidDiscountValue", $$v)
      },
      expression: "configDiscountAdd.bidDiscountValue"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "瀑布流折扣值",
      "prop": "waterFallDiscountValue"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountAdd.waterFallDiscountValue),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "waterFallDiscountValue", $$v)
      },
      expression: "configDiscountAdd.waterFallDiscountValue"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "APP版本定向"
    }
  }, [_c('span', [_vm._v("大于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline1",
    attrs: {
      "disabled": _vm.boolVersion
    },
    model: {
      value: (_vm.configDiscountAdd.appGreaterVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "appGreaterVersionOrientation", $$v)
      },
      expression: "configDiscountAdd.appGreaterVersionOrientation"
    }
  }), _vm._v(" "), _c('span', [_vm._v("小于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline2",
    attrs: {
      "disabled": _vm.boolVersion
    },
    model: {
      value: (_vm.configDiscountAdd.appLessVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "appLessVersionOrientation", $$v)
      },
      expression: "configDiscountAdd.appLessVersionOrientation"
    }
  }), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handleFlagVersion('add')
      }
    },
    model: {
      value: (_vm.boolVersion),
      callback: function($$v) {
        _vm.boolVersion = $$v
      },
      expression: "boolVersion"
    }
  }, [_vm._v("\n                                    不开启版本定向\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "sdk版本定向"
    }
  }, [_c('span', [_vm._v("大于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline1",
    attrs: {
      "disabled": _vm.boolSdkVersion
    },
    model: {
      value: (_vm.configDiscountAdd.sdkGreaterVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "sdkGreaterVersionOrientation", $$v)
      },
      expression: "configDiscountAdd.sdkGreaterVersionOrientation"
    }
  }), _vm._v(" "), _c('span', [_vm._v("小于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline2",
    attrs: {
      "disabled": _vm.boolSdkVersion
    },
    model: {
      value: (_vm.configDiscountAdd.sdkLessVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "sdkLessVersionOrientation", $$v)
      },
      expression: "configDiscountAdd.sdkLessVersionOrientation"
    }
  }), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handleFlagSdkVersion('add')
      }
    },
    model: {
      value: (_vm.boolSdkVersion),
      callback: function($$v) {
        _vm.boolSdkVersion = $$v
      },
      expression: "boolSdkVersion"
    }
  }, [_vm._v("\n                                    不开启SDK版本定向\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "渠道ID定向"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountAdd.channelIdOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "channelIdOrientation", $$v)
      },
      expression: "configDiscountAdd.channelIdOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "跳过渠道"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountAdd.skipChannelOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "skipChannelOrientation", $$v)
      },
      expression: "configDiscountAdd.skipChannelOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "ABtest分组"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountAdd.abTestOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountAdd, "abTestOrientation", $$v)
      },
      expression: "configDiscountAdd.abTestOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": function($event) {
        return _vm.createConfigDiscount('save')
      }
    }
  }, [_vm._v("立即创建")]), _vm._v(" "), _c('el-button', {
    on: {
      "click": _vm.closeDialog
    }
  }, [_vm._v("取消")])], 1)], 1) : _vm._e(), _vm._v(" "), (_vm.dialogType === 'edit') ? _c('el-form', {
    ref: "form",
    attrs: {
      "model": _vm.configDiscountEdit,
      "label-width": "120px"
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "策略名称"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountEdit.discountName),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "discountName", $$v)
      },
      expression: "configDiscountEdit.discountName"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "os"
    }
  }, [_c('el-select', {
    attrs: {
      "placeholder": "请选择"
    },
    model: {
      value: (_vm.configDiscountEdit.os),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "os", $$v)
      },
      expression: "configDiscountEdit.os"
    }
  }, _vm._l((_vm.osOptions), function(os) {
    return _c('el-option', {
      key: os.value,
      attrs: {
        "label": os.label,
        "value": os.value
      }
    })
  }), 1)], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "生效应用"
    }
  }, [_c('el-select', {
    attrs: {
      "multiple": "",
      "placeholder": "请选择有效应用",
      "filterable": "",
      "disabled": true
    },
    model: {
      value: (_vm.configDiscountEdit.checkedProduct),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "checkedProduct", $$v)
      },
      expression: "configDiscountEdit.checkedProduct"
    }
  }, _vm._l((_vm.products), function(product) {
    return _c('el-option', {
      key: product.value,
      attrs: {
        "label": product.label,
        "value": product.value
      }
    })
  }), 1), _vm._v(" "), [_c('el-checkbox', {
    attrs: {
      "disabled": true
    },
    on: {
      "change": function($event) {
        return _vm.handleProductCheckAll('edit')
      }
    },
    model: {
      value: (_vm.boolProduct),
      callback: function($$v) {
        _vm.boolProduct = $$v
      },
      expression: "boolProduct"
    }
  }, [_vm._v("\n                                    全选应用\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "广告类型"
    }
  }, [_c('el-select', {
    attrs: {
      "multiple": "",
      "placeholder": "请选择广告类型",
      "filterable": "",
      "disabled": _vm.boolPlatform
    },
    model: {
      value: (_vm.configDiscountEdit.checkedPlatformOptions),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "checkedPlatformOptions", $$v)
      },
      expression: "configDiscountEdit.checkedPlatformOptions"
    }
  }, _vm._l((_vm.platformOptions), function(platform) {
    return _c('el-option', {
      key: platform.adTypeCode,
      attrs: {
        "label": platform.adTypeName,
        "value": platform.adTypeCode
      }
    })
  }), 1), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handlePlatformCheckAll('edit')
      }
    },
    model: {
      value: (_vm.boolPlatform),
      callback: function($$v) {
        _vm.boolPlatform = $$v
      },
      expression: "boolPlatform"
    }
  }, [_vm._v("\n                                    全选广告类型\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "bidding折扣值"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountEdit.bidDiscountValue),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "bidDiscountValue", $$v)
      },
      expression: "configDiscountEdit.bidDiscountValue"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "瀑布流折扣值"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountEdit.waterFallDiscountValue),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "waterFallDiscountValue", $$v)
      },
      expression: "configDiscountEdit.waterFallDiscountValue"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "APP版本定向"
    }
  }, [_c('span', [_vm._v("大于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline1",
    attrs: {
      "disabled": _vm.boolVersion
    },
    model: {
      value: (_vm.configDiscountEdit.appGreaterVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "appGreaterVersionOrientation", $$v)
      },
      expression: "configDiscountEdit.appGreaterVersionOrientation"
    }
  }), _vm._v(" "), _c('span', [_vm._v("小于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline2",
    attrs: {
      "disabled": _vm.boolVersion
    },
    model: {
      value: (_vm.configDiscountEdit.appLessVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "appLessVersionOrientation", $$v)
      },
      expression: "configDiscountEdit.appLessVersionOrientation"
    }
  }), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handleFlagVersion('edit')
      }
    },
    model: {
      value: (_vm.boolVersion),
      callback: function($$v) {
        _vm.boolVersion = $$v
      },
      expression: "boolVersion"
    }
  }, [_vm._v("\n                                    不开启版本定向\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "sdk版本定向"
    }
  }, [_c('span', [_vm._v("大于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline1",
    attrs: {
      "disabled": _vm.boolSdkVersion
    },
    model: {
      value: (_vm.configDiscountEdit.sdkGreaterVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "sdkGreaterVersionOrientation", $$v)
      },
      expression: "configDiscountEdit.sdkGreaterVersionOrientation"
    }
  }), _vm._v(" "), _c('span', [_vm._v("小于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline2",
    attrs: {
      "disabled": _vm.boolSdkVersion
    },
    model: {
      value: (_vm.configDiscountEdit.sdkLessVersionOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "sdkLessVersionOrientation", $$v)
      },
      expression: "configDiscountEdit.sdkLessVersionOrientation"
    }
  }), _vm._v(" "), [_c('el-checkbox', {
    on: {
      "change": function($event) {
        return _vm.handleFlagSdkVersion('edit')
      }
    },
    model: {
      value: (_vm.boolSdkVersion),
      callback: function($$v) {
        _vm.boolSdkVersion = $$v
      },
      expression: "boolSdkVersion"
    }
  }, [_vm._v("\n                                    不开启SDK版本定向\n                                ")])]], 2), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "渠道ID定向"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountEdit.channelIdOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "channelIdOrientation", $$v)
      },
      expression: "configDiscountEdit.channelIdOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "跳过渠道"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountEdit.skipChannelOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "skipChannelOrientation", $$v)
      },
      expression: "configDiscountEdit.skipChannelOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "ABtest分组"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountEdit.abTestOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountEdit, "abTestOrientation", $$v)
      },
      expression: "configDiscountEdit.abTestOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', [_c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": function($event) {
        return _vm.editConfigDiscount('edit')
      }
    }
  }, [_vm._v("保存修改")]), _vm._v(" "), _c('el-button', {
    on: {
      "click": _vm.closeDialog
    }
  }, [_vm._v("取消")])], 1)], 1) : _vm._e(), _vm._v(" "), (_vm.dialogType === 'view') ? _c('el-form', {
    ref: "form",
    attrs: {
      "model": _vm.configDiscountView,
      "label-width": "120px"
    }
  }, [_c('el-form-item', {
    attrs: {
      "label": "策略名称"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.discountName),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "discountName", $$v)
      },
      expression: "configDiscountView.discountName"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "os"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.os),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "os", $$v)
      },
      expression: "configDiscountView.os"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "生效应用"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.checkedProduct),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "checkedProduct", $$v)
      },
      expression: "configDiscountView.checkedProduct"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "广告平台(广告位)"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.checkedPlatformOptions),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "checkedPlatformOptions", $$v)
      },
      expression: "configDiscountView.checkedPlatformOptions"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "折扣值"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.bidDiscountValue),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "bidDiscountValue", $$v)
      },
      expression: "configDiscountView.bidDiscountValue"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "瀑布流"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.waterFallDiscountValue),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "waterFallDiscountValue", $$v)
      },
      expression: "configDiscountView.waterFallDiscountValue"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "是否启用"
    }
  }, [_c('el-radio-group', {
    model: {
      value: (_vm.configDiscountView.isEnabled),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "isEnabled", $$v)
      },
      expression: "configDiscountView.isEnabled"
    }
  }, [_c('el-radio-button', {
    attrs: {
      "label": "true"
    }
  }, [_vm._v("是")]), _vm._v(" "), _c('el-radio-button', {
    attrs: {
      "label": "false"
    }
  }, [_vm._v("否")])], 1)], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "APP版本定向"
    }
  }, [_c('span', [_vm._v("大于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline1",
    model: {
      value: (_vm.configDiscountView.appVersionOrientation.start),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView.appVersionOrientation, "start", $$v)
      },
      expression: "configDiscountView.appVersionOrientation.start"
    }
  }), _vm._v(" "), _c('span', [_vm._v("小于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline2",
    model: {
      value: (_vm.configDiscountView.appVersionOrientation.end),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView.appVersionOrientation, "end", $$v)
      },
      expression: "configDiscountView.appVersionOrientation.end"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "sdk版本定向"
    }
  }, [_c('span', [_vm._v("大于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline1",
    model: {
      value: (_vm.configDiscountView.sdkVersionOrientation.start),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView.sdkVersionOrientation, "start", $$v)
      },
      expression: "configDiscountView.sdkVersionOrientation.start"
    }
  }), _vm._v(" "), _c('span', [_vm._v("小于等于")]), _vm._v(" "), _c('el-input', {
    staticClass: "input-inline2",
    model: {
      value: (_vm.configDiscountView.sdkVersionOrientation.end),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView.sdkVersionOrientation, "end", $$v)
      },
      expression: "configDiscountView.sdkVersionOrientation.end"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "渠道ID定向"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.channelIdOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "channelIdOrientation", $$v)
      },
      expression: "configDiscountView.channelIdOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "跳过渠道"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.skipChannelOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "skipChannelOrientation", $$v)
      },
      expression: "configDiscountView.skipChannelOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', {
    attrs: {
      "label": "ABtest分组"
    }
  }, [_c('el-input', {
    model: {
      value: (_vm.configDiscountView.abTestOrientation),
      callback: function($$v) {
        _vm.$set(_vm.configDiscountView, "abTestOrientation", $$v)
      },
      expression: "configDiscountView.abTestOrientation"
    }
  })], 1), _vm._v(" "), _c('el-form-item', [_c('el-button', {
    on: {
      "click": _vm.closeDialog
    }
  }, [_vm._v("取消")])], 1)], 1) : _vm._e()], 1)], 1)], 1), _vm._v(" "), _c('el-dialog', {
    attrs: {
      "title": "提示",
      "visible": _vm.deleteDialogSwitcher,
      "width": "30%"
    },
    on: {
      "update:visible": function($event) {
        _vm.deleteDialogSwitcher = $event
      }
    }
  }, [_c('span', [_vm._v("您确定要永久删除本策略吗？一旦删除，他将无法恢复")]), _vm._v(" "), _c('span', {
    staticClass: "dialog-footer",
    attrs: {
      "slot": "footer"
    },
    slot: "footer"
  }, [_c('el-button', {
    on: {
      "click": function($event) {
        _vm.deleteDialogSwitcher = false
      }
    }
  }, [_vm._v("取 消")]), _vm._v(" "), _c('el-button', {
    attrs: {
      "type": "primary"
    },
    on: {
      "click": _vm.handleDelete4Sure
    }
  }, [_vm._v("确 定")])], 1)])], 1)
},staticRenderFns: []}

/***/ }),

/***/ 395:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__util__ = __webpack_require__(292);
/**
 * JSON Interceptor.
 */



/* unused harmony default export */ var _unused_webpack_default_export = (function (request) {

    const type = request.headers.get('Content-Type') || '';

    if (__webpack_require__.i(__WEBPACK_IMPORTED_MODULE_0__util__["a" /* isObject */])(request.body) && type.indexOf('application/json') === 0) {
        request.body = JSON.stringify(request.body);
    }

    return response => {

        return response.bodyText ? __webpack_require__.i(__WEBPACK_IMPORTED_MODULE_0__util__["b" /* when */])(response.text(), text => {

            const type = response.headers.get('Content-Type') || '';

            if (type.indexOf('application/json') === 0 || isJson(text)) {

                try {
                    response.body = JSON.parse(text);
                } catch (e) {
                    response.body = null;
                }

            } else {
                response.body = text;
            }

            return response;

        }) : response;

    };
});

function isJson(str) {

    const start = str.match(/^\s*(\[|\{)/);
    const end = {'[': /]\s*$/, '{': /}\s*$/};

    return start && end[start[1]].test(str);
}


/***/ }),

/***/ 396:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (immutable) */ __webpack_exports__["a"] = Promise;
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__util__ = __webpack_require__(292);
/**
 * Promises/A+ polyfill v1.1.4 (https://github.com/bramstein/promis)
 */

const RESOLVED = 0;
const REJECTED = 1;
const PENDING = 2;



function Promise(executor) {

    this.state = PENDING;
    this.value = undefined;
    this.deferred = [];

    var promise = this;

    try {
        executor(function (x) {
            promise.resolve(x);
        }, function (r) {
            promise.reject(r);
        });
    } catch (e) {
        promise.reject(e);
    }
}

Promise.reject = function (r) {
    return new Promise(function (resolve, reject) {
        reject(r);
    });
};

Promise.resolve = function (x) {
    return new Promise(function (resolve, reject) {
        resolve(x);
    });
};

Promise.all = function all(iterable) {
    return new Promise(function (resolve, reject) {
        var count = 0, result = [];

        if (iterable.length === 0) {
            resolve(result);
        }

        function resolver(i) {
            return function (x) {
                result[i] = x;
                count += 1;

                if (count === iterable.length) {
                    resolve(result);
                }
            };
        }

        for (var i = 0; i < iterable.length; i += 1) {
            Promise.resolve(iterable[i]).then(resolver(i), reject);
        }
    });
};

Promise.race = function race(iterable) {
    return new Promise(function (resolve, reject) {
        for (var i = 0; i < iterable.length; i += 1) {
            Promise.resolve(iterable[i]).then(resolve, reject);
        }
    });
};

var p = Promise.prototype;

p.resolve = function resolve(x) {
    var promise = this;

    if (promise.state === PENDING) {
        if (x === promise) {
            throw new TypeError('Promise settled with itself.');
        }

        var called = false;

        try {
            var then = x && x['then'];

            if (x !== null && typeof x === 'object' && typeof then === 'function') {
                then.call(x, function (x) {
                    if (!called) {
                        promise.resolve(x);
                    }
                    called = true;

                }, function (r) {
                    if (!called) {
                        promise.reject(r);
                    }
                    called = true;
                });
                return;
            }
        } catch (e) {
            if (!called) {
                promise.reject(e);
            }
            return;
        }

        promise.state = RESOLVED;
        promise.value = x;
        promise.notify();
    }
};

p.reject = function reject(reason) {
    var promise = this;

    if (promise.state === PENDING) {
        if (reason === promise) {
            throw new TypeError('Promise settled with itself.');
        }

        promise.state = REJECTED;
        promise.value = reason;
        promise.notify();
    }
};

p.notify = function notify() {
    var promise = this;

    __webpack_require__.i(__WEBPACK_IMPORTED_MODULE_0__util__["c" /* nextTick */])(function () {
        if (promise.state !== PENDING) {
            while (promise.deferred.length) {
                var deferred = promise.deferred.shift(),
                    onResolved = deferred[0],
                    onRejected = deferred[1],
                    resolve = deferred[2],
                    reject = deferred[3];

                try {
                    if (promise.state === RESOLVED) {
                        if (typeof onResolved === 'function') {
                            resolve(onResolved.call(undefined, promise.value));
                        } else {
                            resolve(promise.value);
                        }
                    } else if (promise.state === REJECTED) {
                        if (typeof onRejected === 'function') {
                            resolve(onRejected.call(undefined, promise.value));
                        } else {
                            reject(promise.value);
                        }
                    }
                } catch (e) {
                    reject(e);
                }
            }
        }
    });
};

p.then = function then(onResolved, onRejected) {
    var promise = this;

    return new Promise(function (resolve, reject) {
        promise.deferred.push([onResolved, onRejected, resolve, reject]);
        promise.notify();
    });
};

p.catch = function (onRejected) {
    return this.then(undefined, onRejected);
};


/***/ }),

/***/ 397:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export (immutable) */ __webpack_exports__["a"] = PromiseObj;
/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__lib_promise__ = __webpack_require__(396);
/**
 * Promise adapter.
 */



if (typeof Promise === 'undefined') {
    window.Promise = __WEBPACK_IMPORTED_MODULE_0__lib_promise__["a" /* default */];
}

function PromiseObj(executor, context) {

    if (executor instanceof Promise) {
        this.promise = executor;
    } else {
        this.promise = new Promise(executor.bind(context));
    }

    this.context = context;
}

PromiseObj.all = function (iterable, context) {
    return new PromiseObj(Promise.all(iterable), context);
};

PromiseObj.resolve = function (value, context) {
    return new PromiseObj(Promise.resolve(value), context);
};

PromiseObj.reject = function (reason, context) {
    return new PromiseObj(Promise.reject(reason), context);
};

PromiseObj.race = function (iterable, context) {
    return new PromiseObj(Promise.race(iterable), context);
};

var p = PromiseObj.prototype;

p.bind = function (context) {
    this.context = context;
    return this;
};

p.then = function (fulfilled, rejected) {

    if (fulfilled && fulfilled.bind && this.context) {
        fulfilled = fulfilled.bind(this.context);
    }

    if (rejected && rejected.bind && this.context) {
        rejected = rejected.bind(this.context);
    }

    return new PromiseObj(this.promise.then(fulfilled, rejected), this.context);
};

p.catch = function (rejected) {

    if (rejected && rejected.bind && this.context) {
        rejected = rejected.bind(this.context);
    }

    return new PromiseObj(this.promise.catch(rejected), this.context);
};

p.finally = function (callback) {

    return this.then(function (value) {
        callback.call(this);
        return value;
    }, function (reason) {
        callback.call(this);
        return Promise.reject(reason);
    }
    );
};


/***/ }),

/***/ 408:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(343);
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var update = __webpack_require__(88)("4a1c7351", content, true);

/***/ })

});