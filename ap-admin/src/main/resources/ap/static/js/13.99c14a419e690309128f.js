webpackJsonp([13],{222:function(a,e,t){var s=t(87)(t(319),t(374),null,null);a.exports=s.exports},234:function(a,e,t){a.exports={default:t(235),__esModule:!0}},235:function(a,e,t){var s=t(16),o=s.JSON||(s.JSON={stringify:JSON.stringify});a.exports=function(a){return o.stringify.apply(o,arguments)}},319:function(a,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=t(234),o=t.n(s);e.default={data:function(){return{queryParam:{name:""},taskList:[{tagName:"",id:"",appId:0,taskType:0,taskName:"",posId:0,taskTypeName:"",taskConfig:"",description:"",updateTime:"",priority:0,userTagId:-1,state:!0}],addTaskForm:{taskName:"",taskType:1,posId:0,description:"",priority:0,userTagId:-1,state:!0,taskConfig:{rewardName:"",exchangeRate:0,proportion:0,callback:"",readTaskType:[],futureTaskAdType:"",futureTaskWakeupConfig:"",checkpoint:0,remainTime:0,rewardNum:0,popPercent:0,redEnvelopeConfig:"",adRewardConfig:""}},userTagList:[{id:"",name:"",tagInfo:""}],userTagInfo:{name:"",anonymous:"",filterRegion:"",os:"",tailNumber:"",userChannel:"",userPackage:"",version:""},searchTaskType:"",searchPosId:"",updateTaskForm:{id:0,taskName:"",posId:0,taskType:1,description:"",priority:0,userTagId:-1,state:!0,taskConfig:{rewardName:"",exchangeRate:0,proportion:0,callback:"",readTaskType:[],futureTaskAdType:"",futureTaskWakeupConfig:"",checkpoint:0,remainTime:0,rewardNum:0,popPercent:0,redEnvelopeConfig:"",adRewardConfig:""}},taskListBak:[],isAddTaskFormShow:!1,isUpdateTaskFormShow:!1,taskTypeList:[{value:1,label:"阅读60秒"},{value:2,label:"后续行为提升"},{value:3,label:"draw激励视频"},{value:4,label:"打工页"}],adPosList:[{id:"0",name:"不限制"}]}},created:function(){this.getTaskList(),this.getAdPosList()},methods:{initUserTagInfo:function(){var a=this;a.$axios.post("userTag/list").then(function(e){if(200===e.status){var t=e.data;if(0===t.ret){a.userTagList=t.data;var s={id:-1,name:"未分配标签"};a.userTagList.unshift(s)}else a.$message.error(t.data)}else a.$message.error("加载失败！")})},getTaskList:function(){var a=this;this.initUserTagInfo();var e=this;e.$axios.post("task/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var s=t.data;0===s.ret?(e.taskList=s.data,a.taskListBak=a.taskList):e.$message.error(s.data)}else e.$message.error("服务器异常！")})},updateTask:function(){var a=this,e={taskName:a.updateTaskForm.taskName,id:a.updateTaskForm.id,description:a.updateTaskForm.description,priority:a.updateTaskForm.priority,userTagId:a.updateTaskForm.userTagId,state:a.updateTaskForm.state,taskConfig:o()(a.updateTaskForm.taskConfig)};a.$axios.post("task/updateTask",e,{}).then(function(e){if(200===e.status){var t=e.data;0===t.ret?(a.isUpdateTaskFormShow=!1,a.$message({message:"任务信息更新成功",type:"success"}),a.getTaskList()):a.$message.error(t.data)}else a.$message.error("服务器异常！")})},addTask:function(){var a=this,e={taskName:a.addTaskForm.taskName,taskType:a.addTaskForm.taskType,posId:a.addTaskForm.posId,description:a.addTaskForm.description,priority:a.addTaskForm.priority,userTagId:a.addTaskForm.userTagId,state:a.addTaskForm.state,taskConfig:o()(a.addTaskForm.taskConfig)};a.$axios.post("task/addTask",e,{}).then(function(e){if(200===e.status){var t=e.data;0===t.ret?(a.isAddTaskFormShow=!1,a.$message({message:"新增任务成功",type:"success"}),a.getTaskList()):a.$message.error(t.data)}else a.$message.error("服务器异常！")})},addTaskPre:function(){this.clearAddForm(),this.isAddTaskFormShow=!0},resetSearch:function(){this.taskList=this.taskListBak,this.searchPosId="",this.searchTaskType=""},clearAddForm:function(){this.addTaskForm.taskType=1,this.addTaskForm.description="",this.addTaskForm.taskConfig.rewardName="",this.addTaskForm.taskConfig.exchangeRate=0,this.addTaskForm.taskConfig.proportion=0,this.addTaskForm.taskConfig.readTaskType=[],this.addTaskForm.taskConfig.callback="",this.addTaskForm.taskConfig.futureTaskAdType="",this.addTaskForm.taskConfig.futureTaskWakeupConfig="",this.addTaskForm.taskConfig.redEnvelopeConfig="",this.addTaskForm.taskConfig.adRewardConfig="",this.addTaskForm.taskConfig.checkpoint=0,this.addTaskForm.taskConfig.remainTime=0,this.addTaskForm.taskConfig.rewardNum=0,this.addTaskForm.taskConfig.popPercent=0},updateTaskPre:function(a){this.updateTaskForm.taskName=a.taskName,this.updateTaskForm.id=a.id,this.updateTaskForm.posId=a.posId,this.updateTaskForm.taskType=a.taskType,this.updateTaskForm.description=a.description,this.updateTaskForm.state=a.state,this.updateTaskForm.priority=a.priority,this.updateTaskForm.userTagId=a.userTagId;var e=JSON.parse(a.taskConfig);this.updateTaskForm.taskConfig.rewardName=e.rewardName,this.updateTaskForm.taskConfig.exchangeRate=e.exchangeRate,this.updateTaskForm.taskConfig.proportion=e.proportion,this.updateTaskForm.taskConfig.readTaskType=e.readTaskType,this.updateTaskForm.taskConfig.callback=e.callback,this.updateTaskForm.taskConfig.futureTaskAdType=e.futureTaskAdType,this.updateTaskForm.taskConfig.futureTaskWakeupConfig=e.futureTaskWakeupConfig,this.updateTaskForm.taskConfig.redEnvelopeConfig=e.redEnvelopeConfig,this.updateTaskForm.taskConfig.adRewardConfig=e.adRewardConfig,this.updateTaskForm.taskConfig.checkpoint=e.checkpoint,this.updateTaskForm.taskConfig.remainTime=e.remainTime,this.updateTaskForm.taskConfig.rewardNum=e.rewardNum,this.updateTaskForm.taskConfig.popPercent=e.popPercent,this.isUpdateTaskFormShow=!0},changeSwitch:function(a){this.updateTaskPre(a),this.isUpdateTaskFormShow=!1,this.updateTaskForm.state=a.state,this.updateTask()},hoverShowPre:function(a){var e="";for(var t in this.userTagList)this.userTagList[t].id===a&&(e=this.userTagList[t]);if(""==e)return void(this.userTagInfo.version="未分配");this.userTagInfo.name=e.name,this.userTagInfo.version=e.tagInfo.version?e.tagInfo.version:"不限制",this.userTagInfo.os=0==e.tagInfo.os?"不限制":1==e.tagInfo.os?"Android":"iOS"},searchByTaskType:function(){this.searchPosId="";var a=[],e=this.taskListBak;for(var t in e)e[t].taskType==this.searchTaskType&&(a[a.length+1]=e[t]);this.taskList=a},searchByPosId:function(){var a=[],e=this.taskList;for(var t in e)e[t].posId==this.searchPosId&&(a[a.length+1]=e[t]);this.taskList=a},getAdPosList:function(){var a=this;a.$axios.post("adpos/listByApp",{},{}).then(function(e){if(200===e.status){var t=e.data;if(0===t.ret){var s=t.data;a.adPosList=s;var o={id:0,name:"不限制"};a.adPosList.unshift(o)}else a.$message.error("初始化数据失败")}else a.$message.error("服务器异常！")})}}}},374:function(a,e){a.exports={render:function(){var a=this,e=a.$createElement,t=a._self._c||e;return t("div",{staticClass:"table"},[t("div",{staticClass:"crumbs"},[t("el-breadcrumb",{attrs:{separator:"/"}},[t("el-breadcrumb-item",[t("i",{staticClass:"el-icon-menu"}),a._v(" 任务中心")]),a._v(" "),t("el-breadcrumb-item",[a._v("任务管理")])],1)],1),a._v(" "),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-form",{attrs:{model:a.queryParam,size:"small",inline:""}},[t("el-form-item",{attrs:{label:"任务名称"}},[t("el-input",{staticClass:"width-150",model:{value:a.queryParam.name,callback:function(e){a.$set(a.queryParam,"name",e)},expression:"queryParam.name"}})],1),a._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:a.getTaskList}})],1),a._v(" "),t("el-form-item",{attrs:{label:"任务类型"}},[t("el-select",{attrs:{placeholder:"请选择"},on:{change:a.searchByTaskType},model:{value:a.searchTaskType,callback:function(e){a.searchTaskType=e},expression:"searchTaskType"}},a._l(a.taskTypeList,function(a){return t("el-option",{key:a.value,attrs:{label:a.label,value:a.value}})}),1)],1),a._v(" "),t("el-form-item",{attrs:{label:"广告位"}},[t("el-select",{attrs:{placeholder:"请选择"},on:{change:a.searchByPosId},model:{value:a.searchPosId,callback:function(e){a.searchPosId=e},expression:"searchPosId"}},a._l(a.adPosList,function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})}),1)],1),a._v(" "),t("el-form-item",[t("el-button",{attrs:{type:"info",round:""},on:{click:function(e){return a.resetSearch()}}},[a._v("复位")])],1),a._v(" "),t("el-form-item",{staticStyle:{float:"right"}},[t("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){return a.addTaskPre()}}},[a._v("新建任务")])],1)],1)],1)],1),a._v(" "),t("el-divider",{attrs:{"content-position":"left"}},[a._v("任务列表")]),a._v(" "),t("el-dialog",{attrs:{title:"编辑任务",visible:a.isUpdateTaskFormShow},on:{"update:visible":function(e){a.isUpdateTaskFormShow=e}}},[t("el-form",{attrs:{model:a.updateTaskForm,"label-width":"200px"}},[t("el-form-item",{attrs:{label:"任务名称"}},[t("el-input",{model:{value:a.updateTaskForm.taskName,callback:function(e){a.$set(a.updateTaskForm,"taskName",e)},expression:"updateTaskForm.taskName"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"广告位"}},[t("el-select",{attrs:{disabled:"",placeholder:"请选择"},model:{value:a.updateTaskForm.posId,callback:function(e){a.$set(a.updateTaskForm,"posId",e)},expression:"updateTaskForm.posId"}},a._l(a.adPosList,function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})}),1)],1),a._v(" "),t("el-form-item",{attrs:{label:"奖励名称"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.rewardName,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"rewardName",e)},expression:"updateTaskForm.taskConfig.rewardName"}})],1),a._v(" "),4!=a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"汇率"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.exchangeRate,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"exchangeRate",e)},expression:"updateTaskForm.taskConfig.exchangeRate"}})],1):a._e(),a._v(" "),4!=a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"发放比例"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.proportion,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"proportion",e)},expression:"updateTaskForm.taskConfig.proportion"}})],1):a._e(),a._v(" "),4!=a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"奖励回调地址"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.callback,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"callback",e)},expression:"updateTaskForm.taskConfig.callback"}})],1):a._e(),a._v(" "),t("el-form-item",{attrs:{label:"优先级"}},[[t("el-input-number",{attrs:{min:0,max:1e4,step:1},model:{value:a.updateTaskForm.priority,callback:function(e){a.$set(a.updateTaskForm,"priority",e)},expression:"updateTaskForm.priority"}})]],2),a._v(" "),t("el-form-item",{attrs:{label:"用户标签"}},[t("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:a.updateTaskForm.userTagId,callback:function(e){a.$set(a.updateTaskForm,"userTagId",e)},expression:"updateTaskForm.userTagId"}},a._l(a.userTagList,function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})}),1)],1),a._v(" "),1===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"支持的任务类型"}},[t("el-checkbox-group",{model:{value:a.updateTaskForm.taskConfig.readTaskType,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"readTaskType",e)},expression:"updateTaskForm.taskConfig.readTaskType"}},[t("el-checkbox",{attrs:{label:0}},[a._v("内开")]),a._v(" "),t("el-checkbox",{attrs:{label:1}},[a._v("外开")]),a._v(" "),t("el-checkbox",{attrs:{label:2}},[a._v("时长+多跳")]),a._v(" "),t("el-checkbox",{attrs:{label:3}},[a._v("视频")])],1)],1):a._e(),a._v(" "),2===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"可生成任务的广告类型"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.updateTaskForm.taskConfig.futureTaskAdType,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"futureTaskAdType",e)},expression:"updateTaskForm.taskConfig.futureTaskAdType"}})],1):a._e(),a._v(" "),2===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"留存任务配置"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.updateTaskForm.taskConfig.futureTaskWakeupConfig,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"futureTaskWakeupConfig",e)},expression:"updateTaskForm.taskConfig.futureTaskWakeupConfig"}})],1):a._e(),a._v(" "),2===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"检索时间间隔"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.checkpoint,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"checkpoint",e)},expression:"updateTaskForm.taskConfig.checkpoint"}})],1):a._e(),a._v(" "),3===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"停留时间"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.remainTime,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"remainTime",e)},expression:"updateTaskForm.taskConfig.remainTime"}})],1):a._e(),a._v(" "),3===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"奖励金额"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.rewardNum,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"rewardNum",e)},expression:"updateTaskForm.taskConfig.rewardNum"}})],1):a._e(),a._v(" "),3===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"弹窗展示概率"}},[t("el-input",{model:{value:a.updateTaskForm.taskConfig.popPercent,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"popPercent",e)},expression:"updateTaskForm.taskConfig.popPercent"}})],1):a._e(),a._v(" "),4===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"红包配置"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.updateTaskForm.taskConfig.redEnvelopeConfig,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"redEnvelopeConfig",e)},expression:"updateTaskForm.taskConfig.redEnvelopeConfig"}})],1):a._e(),a._v(" "),4===a.updateTaskForm.taskType?t("el-form-item",{attrs:{label:"广告奖励配置"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.updateTaskForm.taskConfig.adRewardConfig,callback:function(e){a.$set(a.updateTaskForm.taskConfig,"adRewardConfig",e)},expression:"updateTaskForm.taskConfig.adRewardConfig"}})],1):a._e(),a._v(" "),t("el-form-item",{attrs:{label:"任务描述"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"备份"},model:{value:a.updateTaskForm.description,callback:function(e){a.$set(a.updateTaskForm,"description",e)},expression:"updateTaskForm.description"}})],1)],1),a._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(e){a.isUpdateTaskFormShow=!1}}},[a._v("取 消")]),a._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(e){return a.updateTask()}}},[a._v("确 定")])],1)],1),a._v(" "),t("el-dialog",{attrs:{title:"新增任务",visible:a.isAddTaskFormShow},on:{"update:visible":function(e){a.isAddTaskFormShow=e}}},[t("el-form",{attrs:{model:a.addTaskForm,"label-width":"200px"}},[t("el-form-item",{attrs:{label:"任务名称"}},[t("el-input",{model:{value:a.addTaskForm.taskName,callback:function(e){a.$set(a.addTaskForm,"taskName",e)},expression:"addTaskForm.taskName"}})],1),a._v(" "),t("el-form-item",{attrs:{label:"任务类型"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:a.addTaskForm.taskType,callback:function(e){a.$set(a.addTaskForm,"taskType",e)},expression:"addTaskForm.taskType"}},a._l(a.taskTypeList,function(a){return t("el-option",{key:a.value,attrs:{label:a.label,value:a.value}})}),1)],1),a._v(" "),t("el-form-item",{attrs:{label:"广告位"}},[t("el-select",{attrs:{placeholder:"请选择"},model:{value:a.addTaskForm.posId,callback:function(e){a.$set(a.addTaskForm,"posId",e)},expression:"addTaskForm.posId"}},a._l(a.adPosList,function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})}),1)],1),a._v(" "),t("el-form-item",{attrs:{label:"奖励名称"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.rewardName,callback:function(e){a.$set(a.addTaskForm.taskConfig,"rewardName",e)},expression:"addTaskForm.taskConfig.rewardName"}})],1),a._v(" "),4!=a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"汇率"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.exchangeRate,callback:function(e){a.$set(a.addTaskForm.taskConfig,"exchangeRate",e)},expression:"addTaskForm.taskConfig.exchangeRate"}})],1):a._e(),a._v(" "),4!=a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"发放比例"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.proportion,callback:function(e){a.$set(a.addTaskForm.taskConfig,"proportion",e)},expression:"addTaskForm.taskConfig.proportion"}})],1):a._e(),a._v(" "),4!=a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"奖励回调地址"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.callback,callback:function(e){a.$set(a.addTaskForm.taskConfig,"callback",e)},expression:"addTaskForm.taskConfig.callback"}})],1):a._e(),a._v(" "),t("el-form-item",{attrs:{label:"优先级"}},[[t("el-input-number",{attrs:{min:0,max:1e4,step:1},model:{value:a.addTaskForm.priority,callback:function(e){a.$set(a.addTaskForm,"priority",e)},expression:"addTaskForm.priority"}})]],2),a._v(" "),t("el-form-item",{attrs:{label:"用户标签"}},[t("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:a.addTaskForm.userTagId,callback:function(e){a.$set(a.addTaskForm,"userTagId",e)},expression:"addTaskForm.userTagId"}},a._l(a.userTagList,function(a){return t("el-option",{key:a.id,attrs:{label:a.name,value:a.id}})}),1)],1),a._v(" "),1===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"支持的任务类型"}},[t("el-checkbox-group",{model:{value:a.addTaskForm.taskConfig.readTaskType,callback:function(e){a.$set(a.addTaskForm.taskConfig,"readTaskType",e)},expression:"addTaskForm.taskConfig.readTaskType"}},[t("el-checkbox",{attrs:{label:0}},[a._v("内开")]),a._v(" "),t("el-checkbox",{attrs:{label:1}},[a._v("外开")]),a._v(" "),t("el-checkbox",{attrs:{label:2}},[a._v("时长+多跳")]),a._v(" "),t("el-checkbox",{attrs:{label:3}},[a._v("视频")])],1)],1):a._e(),a._v(" "),2===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"可生成任务的广告类型"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.addTaskForm.taskConfig.futureTaskAdType,callback:function(e){a.$set(a.addTaskForm.taskConfig,"futureTaskAdType",e)},expression:"addTaskForm.taskConfig.futureTaskAdType"}})],1):a._e(),a._v(" "),2===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"留存任务配置"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.addTaskForm.taskConfig.futureTaskWakeupConfig,callback:function(e){a.$set(a.addTaskForm.taskConfig,"futureTaskWakeupConfig",e)},expression:"addTaskForm.taskConfig.futureTaskWakeupConfig"}})],1):a._e(),a._v(" "),2===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"检索时间间隔"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.checkpoint,callback:function(e){a.$set(a.addTaskForm.taskConfig,"checkpoint",e)},expression:"addTaskForm.taskConfig.checkpoint"}})],1):a._e(),a._v(" "),3===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"停留时间"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.remainTime,callback:function(e){a.$set(a.addTaskForm.taskConfig,"remainTime",e)},expression:"addTaskForm.taskConfig.remainTime"}})],1):a._e(),a._v(" "),3===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"奖励金额"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.rewardNum,callback:function(e){a.$set(a.addTaskForm.taskConfig,"rewardNum",e)},expression:"addTaskForm.taskConfig.rewardNum"}})],1):a._e(),a._v(" "),3===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"弹窗展示概率"}},[t("el-input",{model:{value:a.addTaskForm.taskConfig.popPercent,callback:function(e){a.$set(a.addTaskForm.taskConfig,"popPercent",e)},expression:"addTaskForm.taskConfig.popPercent"}})],1):a._e(),a._v(" "),4===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"红包配置"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.addTaskForm.taskConfig.redEnvelopeConfig,callback:function(e){a.$set(a.addTaskForm.taskConfig,"redEnvelopeConfig",e)},expression:"addTaskForm.taskConfig.redEnvelopeConfig"}})],1):a._e(),a._v(" "),4===a.addTaskForm.taskType?t("el-form-item",{attrs:{label:"广告奖励配置"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入配置"},model:{value:a.addTaskForm.taskConfig.adRewardConfig,callback:function(e){a.$set(a.addTaskForm.taskConfig,"adRewardConfig",e)},expression:"addTaskForm.taskConfig.adRewardConfig"}})],1):a._e(),a._v(" "),t("el-form-item",{attrs:{label:"任务描述"}},[t("el-input",{attrs:{type:"textarea",rows:5,placeholder:"备份"},model:{value:a.addTaskForm.description,callback:function(e){a.$set(a.addTaskForm,"description",e)},expression:"addTaskForm.description"}})],1)],1),a._v(" "),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:function(e){a.isAddTaskFormShow=!1}}},[a._v("取 消")]),a._v(" "),t("el-button",{attrs:{type:"primary"},on:{click:function(e){return a.addTask()}}},[a._v("确 定")])],1)],1),a._v(" "),t("el-row",[t("el-col",{attrs:{span:24}},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:a.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:a.taskList,stripe:"","highlight-current-row":"","element-loading-text":"玩命加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[t("el-table-column",{attrs:{prop:"id",label:"任务ID",width:"160"}}),a._v(" "),t("el-table-column",{attrs:{label:"状态",width:"160"},scopedSlots:a._u([{key:"default",fn:function(e){return[t("el-switch",{on:{change:function(t){return a.changeSwitch(e.row)}},model:{value:e.row.state,callback:function(t){a.$set(e.row,"state",t)},expression:"scope.row.state"}})]}}])}),a._v(" "),t("el-table-column",{attrs:{prop:"taskName",label:"任务名称"}}),a._v(" "),t("el-table-column",{attrs:{prop:"taskTypeName",label:"任务类型"}}),a._v(" "),t("el-table-column",{attrs:{label:"广告位"},scopedSlots:a._u([{key:"default",fn:function(e){return a._l(a.adPosList,function(s){return s.id===e.row.posId?t("span",[a._v(a._s(s.name))]):a._e()})}}])}),a._v(" "),t("el-table-column",{attrs:{prop:"userTagId",label:"用户标签ID"}}),a._v(" "),t("el-table-column",{attrs:{label:"用户标签名称"},scopedSlots:a._u([{key:"default",fn:function(e){return[t("el-popover",{attrs:{title:"用户标签",trigger:"hover",placement:"top"},on:{show:function(t){return a.hoverShowPre(e.row.userTagId)}}},[t("p",[a._v("\n                                版本定向："+a._s(a.userTagInfo.version)),t("br"),a._v("\n                                操作系统定向："+a._s(a.userTagInfo.os)),t("br")]),a._v(" "),t("div",{staticClass:"name-wrapper",attrs:{slot:"reference"},slot:"reference"},a._l(a.userTagList,function(s){return s.id===e.row.userTagId?t("span",[t("el-tag",{attrs:{size:"medium"}},[a._v(a._s(s.name))])],1):a._e()}),0)])]}}])}),a._v(" "),t("el-table-column",{attrs:{prop:"priority",label:"优先级"}}),a._v(" "),t("el-table-column",{attrs:{prop:"updateTime",label:"最近修改时间",width:"180"}}),a._v(" "),t("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:a._u([{key:"default",fn:function(e){return[t("el-button",{attrs:{size:"small"},on:{click:function(t){return a.updateTaskPre(e.row)}}},[a._v("修改")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});