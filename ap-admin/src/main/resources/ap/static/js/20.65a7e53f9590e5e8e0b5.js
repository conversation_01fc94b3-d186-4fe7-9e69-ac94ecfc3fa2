webpackJsonp([20],{232:function(e,t,a){var l=a(87)(a(329),a(364),null,null);e.exports=l.exports},329:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{queryParam:{product:456,os:1,platform:1,appId:5181715,type:""},needCloseAdList:[],appList:[],platformList:[{key:1,val:"穿山甲"},{key:2,val:"广点通"}],osList:[{key:1,val:"Android"},{key:2,val:"IOS"}],applicationList:[],typeList:[{key:"开屏",val:"开屏"},{key:"静态图",val:"静态图"},{key:"插屏",val:"插屏"},{key:"新插屏",val:"新插屏"},{key:"Banner",val:"Banner"},{key:"视频",val:"视频"}],multipleSelection:[],loading:!0,isShowBatchClose:!1}},created:function(){this.getApplicationList(),this.getAppList(),this.queryNeedCloseList()},methods:{handleSelectionChange:function(e){this.multipleSelection=e},stopAdSingle:function(e){var t=this,a=this,l={posId:e.posId};a.$axios.post("third/waitClose/close",{},{params:l}).then(function(e){if(200===e.status){var l=e.data;0===l.ret?t.queryNeedCloseList():a.$message.error(l.data)}else a.$message.error("服务器异常！")})},queryNeedCloseList:function(){var e=this;e.loading=!0,e.$axios.post("third/waitClose/list",{},{params:this.queryParam}).then(function(t){if(200===t.status){var a=t.data;0===a.ret?(e.needCloseAdList=a.data,e.loading=!1):(e.$message.error(a.data),e.loading=!1)}else e.$message.error("服务器异常！"),e.loading=!1})},batchStopAd:function(){for(var e=this,t=this,a=[],l=0;l<t.multipleSelection.length;l++)1===t.multipleSelection[l].status&&a.push(t.multipleSelection[l].posId);t.$axios.post("third/waitClose/closeBatch",a,{}).then(function(a){if(200===a.status){var l=a.data;0===l.ret?(t.isShowBatchClose=!1,e.queryNeedCloseList()):t.$message.error(l.data)}else t.$message.error("服务器异常！")})},getAppList:function(){var e=this;e.$axios.post("app/shortNameList",{},{}).then(function(t){if(200===t.status){var a=t.data;0===a.ret?e.appList=a.data:e.$message.error(a.data)}else e.$message.error("服务器异常！")})},clearAppId:function(){this.queryParam.appId=0},getApplicationList:function(){var e=this,t={name:"",id:"",os:null,pageNo:1,pageSize:1e6};e.$axios.post("third/app/list",{},{params:t}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var l=a.data,s=[],r=0;r<l.items.length;r++){var o={key:l.items[r].appId,val:l.items[r].appName,desc:l.items[r].mainBody,os:l.items[r].os,product:l.items[r].product,platform:l.items[r].platformCode};s.push(o)}e.applicationList=s}else e.$message.error("初始化应用数据异常！")}else e.$message.error("服务器异常！")})}}}},364:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),e._v("需要关停的广告位")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"即将关停以下广告",visible:e.isShowBatchClose},on:{"update:visible":function(t){e.isShowBatchClose=t}}},[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.multipleSelection,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"posId",sortable:"",label:"posId"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",width:"350",label:"广告位名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"platform",sortable:"",label:"平台"}}),e._v(" "),a("el-table-column",{attrs:{prop:"adType",label:"广告类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.adType?a("span",[e._v("开屏")]):2===t.row.adType?a("span",[e._v("信息流")]):3===t.row.adType?a("span",[e._v("插屏")]):4===t.row.adType?a("span",[e._v("激励视频")]):5===t.row.adType?a("span",[e._v("新插屏")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"status",sortable:"",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("开启")]):e._e(),e._v(" "),0===t.row.status?a("el-tag",{attrs:{type:"danger"}},[e._v("暂停")]):e._e()]}}])})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowBatchClose=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.batchStopAd()}}},[e._v("确认关闭")])],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"内部应用"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"应用"},on:{change:e.clearAppId},model:{value:e.queryParam.product,callback:function(t){e.$set(e.queryParam,"product",t)},expression:"queryParam.product"}},e._l(e.appList,function(e){return a("el-option",{attrs:{label:e.label,value:e.value}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"平台"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},on:{change:e.clearAppId},model:{value:e.queryParam.platform,callback:function(t){e.$set(e.queryParam,"platform",t)},expression:"queryParam.platform"}},e._l(e.platformList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"OS"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"OS"},on:{change:e.clearAppId},model:{value:e.queryParam.os,callback:function(t){e.$set(e.queryParam,"os",t)},expression:"queryParam.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"应用"},model:{value:e.queryParam.appId,callback:function(t){e.$set(e.queryParam,"appId",t)},expression:"queryParam.appId"}},[a("el-option",{attrs:{value:0}},[e._v("请选择")]),e._v(" "),e._l(e.applicationList.filter(function(t){return t.platform===e.queryParam.platform&&t.product===e.queryParam.product&&t.os===e.queryParam.os}),function(t){return a("el-option",{attrs:{label:t.val,value:t.key}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.val))]),e._v(" "),a("el-tag",{staticStyle:{float:"right"}},[e._v(e._s(t.desc))])],1)})],2)],1),e._v(" "),a("el-form-item",{attrs:{label:"广告类型"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.queryParam.type,callback:function(t){e.$set(e.queryParam,"type",t)},expression:"queryParam.type"}},[a("el-option",[e._v("请选择")]),e._v(" "),e._l(e.typeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})})],2)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.queryNeedCloseList()}}},[e._v("搜索")])],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.isShowBatchClose=!0}}},[e._v("批量关停")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("待关闭广告位列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.needCloseAdList,stripe:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",label:""}}),e._v(" "),a("el-table-column",{attrs:{prop:"posId",sortable:"",label:"posId"}}),e._v(" "),a("el-table-column",{attrs:{prop:"posName",width:"350",label:"广告位名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"platform",sortable:"",label:"平台"}}),e._v(" "),a("el-table-column",{attrs:{prop:"adType",label:"广告类型"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.adType?a("span",[e._v("开屏")]):2===t.row.adType?a("span",[e._v("信息流")]):3===t.row.adType?a("span",[e._v("插屏")]):4===t.row.adType?a("span",[e._v("激励视频")]):5===t.row.adType?a("span",[e._v("新插屏")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"status",sortable:"",label:"状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("开启")]):e._e(),e._v(" "),0===t.row.status?a("el-tag",{attrs:{type:"danger"}},[e._v("暂停")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"275",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(a){return e.stopAdSingle(t.row)}}},[e._v("关停")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});