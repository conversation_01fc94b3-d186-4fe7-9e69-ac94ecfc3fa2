webpackJsonp([23],{229:function(e,t,a){var o=a(87)(a(326),a(389),null,null);e.exports=o.exports},326:function(e,t,a){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{showCsj:!0,showGdt:!1,showKs:!1,platformCodeList:[{key:1,val:"穿山甲"},{key:2,val:"广点通"},{key:3,val:"快手"},{key:4,val:"百度"},{key:5,val:"OPPO"}],gdtIndustryList:[{key:50706,val:"语言学习"},{key:50707,val:"教育工具"},{key:50801,val:"汽车资讯"},{key:50802,val:"驾照考试"},{key:50803,val:"汽车交易"},{key:50804,val:"违章查询"},{key:50902,val:"理财服务"},{key:50904,val:"股票证券"},{key:50905,val:"彩票双色球"},{key:50906,val:"支付"},{key:51002,val:"旅游服务"},{key:51004,val:"用车服务"},{key:51006,val:"地图导航"},{key:51008,val:"公交服务"},{key:51009,val:"共享单车"},{key:51010,val:"航班服务"},{key:51011,val:"火车服务"},{key:51301,val:"网络K歌"},{key:51302,val:"音乐播放器"},{key:51304,val:"在线音乐"},{key:51305,val:"广播电台"},{key:51306,val:"音乐乐器"},{key:51401,val:"在线视频"},{key:51402,val:"在线直播"},{key:51403,val:"视频播放器"},{key:51406,val:"短视频"},{key:51407,val:"电视视频"},{key:51408,val:"视频工具"},{key:51602,val:"婚恋交友"},{key:51603,val:"社交交友"},{key:51604,val:"生活社区"},{key:51605,val:"微博社交"},{key:51606,val:"问答社区"},{key:51607,val:"论坛贴吧"},{key:51704,val:"棋牌游戏"},{key:50102,val:"日程备忘"},{key:50103,val:"办公室软件"},{key:50104,val:"文件管理"},{key:50105,val:"网络云盘"},{key:50201,val:"输入法"},{key:50202,val:"浏览器"},{key:50203,val:"电池插件"},{key:50204,val:"安全防护"},{key:50205,val:"内存清理"},{key:50206,val:"WIFI"},{key:50207,val:"账号辅助"},{key:50208,val:"应用商店"},{key:50209,val:"主题美化"},{key:50210,val:"电话通讯"},{key:50211,val:"性能优化"},{key:50402,val:"万年历"},{key:50403,val:"天气服务"},{key:50405,val:"运势信仰"},{key:50406,val:"闹钟"},{key:50407,val:"红包助手"},{key:50408,val:"计算器"},{key:50409,val:"日记手账"},{key:50410,val:"辅助工具"},{key:50501,val:"导购分享"},{key:50506,val:"二手交易"},{key:50507,val:"移动电商"},{key:50508,val:"优惠比价"},{key:50603,val:"运动健身"},{key:50604,val:"医疗问询"},{key:50606,val:"健康养生"},{key:50701,val:"学前教育"},{key:50702,val:"词典翻译"},{key:50703,val:"K12"},{key:50704,val:"高等教育"},{key:50705,val:"职业培训"},{key:51713,val:"消除游戏"},{key:51714,val:"模拟经营"},{key:51715,val:"益智休闲"},{key:51716,val:"游戏助手"},{key:51717,val:"游戏平台"},{key:51718,val:"其他手游"},{key:51805,val:"报刊杂志"},{key:51806,val:"有声听书"},{key:51807,val:"小说阅读"},{key:51808,val:"手机漫画"},{key:51809,val:"幽默段子"},{key:51901,val:"育儿工具"},{key:51902,val:"孕育社区"},{key:51903,val:"经期健康"},{key:52001,val:"垂类资讯"},{key:52002,val:"综合资讯"},{key:52003,val:"网赚资讯"},{key:52101,val:"相机"},{key:52102,val:"图片美化"},{key:52103,val:"设计制作"},{key:52201,val:"体育资讯"},{key:52202,val:"体育直播"},{key:52301,val:"平台"},{key:52401,val:"美食菜谱"},{key:52402,val:"求职招聘"},{key:52403,val:"快递物流"},{key:52404,val:"在线团购"},{key:52405,val:"房屋租赁"},{key:52406,val:"装修服务"},{key:52407,val:"票务服务"},{key:52408,val:"网赚平台"},{key:52409,val:"运营商服务"},{key:52410,val:"美妆美发"}],csjIndustryList:[{key:120102,val:"办公-笔记文档"},{key:120104,val:"办公-效率办公"},{key:120106,val:"办公-其他"},{key:120201,val:"出行-地图导航"},{key:120202,val:"出行-出行服务"},{key:120203,val:"出行-出行工具"},{key:120211,val:"出行-旅行服务"},{key:120212,val:"出行-车主服务"},{key:120218,val:"出行-其他"},{key:120301,val:"购物&电商-导购比价"},{key:120302,val:"购物&电商-微店服务"},{key:120303,val:"购物&电商-闲置交易"},{key:120305,val:"购物&电商-电商"},{key:120306,val:"购物&电商-其他"},{key:120402,val:"健康-健康管理"},{key:120403,val:"健康-丽人美妆"},{key:120404,val:"健康-医疗美容"},{key:120405,val:"健康-运动健身"},{key:120407,val:"健康-医药服务"},{key:120408,val:"健康-挂号问诊"},{key:120409,val:"健康-女性健康"},{key:120413,val:"健康-其他"},{key:120501,val:"教育-K12"},{key:120502,val:"教育-词典翻译"},{key:120503,val:"教育-高等教育"},{key:120504,val:"教育-教育工具"},{key:120505,val:"教育-学前教育"},{key:120506,val:"教育-语言学习"},{key:120507,val:"教育-职业教育"},{key:120508,val:"教育-其他"},{key:120601,val:"金融-P2P理财"},{key:120602,val:"金融-保险服务"},{key:120603,val:"金融-股票交易"},{key:120604,val:"金融-记账理财"},{key:120605,val:"金融-网络彩票"},{key:120606,val:"金融-网上银行"},{key:120607,val:"金融-现金借贷"},{key:120608,val:"金融-消费金融"},{key:120609,val:"金融-虚拟货币"},{key:120610,val:"金融-支付结算"},{key:120611,val:"金融-综合理财"},{key:120612,val:"金融-网赚平台"},{key:120613,val:"金融-其他"},{key:120701,val:"社交-互动交友"},{key:120702,val:"社交-即时通讯"},{key:120703,val:"社交-社区论坛"},{key:120708,val:"社交-其他"},{key:120803,val:"生活-本地生活"},{key:120809,val:"生活-美食菜谱"},{key:120810,val:"生活-求职招聘"},{key:120812,val:"生活-运营商服务"},{key:120813,val:"生活-其他"},{key:120903,val:"视频-横版短视频"},{key:120905,val:"视频-视频播放器"},{key:120906,val:"视频-视频工具"},{key:120908,val:"视频-直播"},{key:120909,val:"视频-长视频"},{key:120911,val:"视频-竖版短视频"},{key:120910,val:"视频-其他"},{key:121001,val:"图像-拍照摄影"},{key:121002,val:"图像-图片分享"},{key:121003,val:"图像-图片美化"},{key:121004,val:"图像-相册图库"},{key:121005,val:"图像-其他"},{key:121101,val:"工具-电子邮件"},{key:121102,val:"工具-通讯辅助"},{key:121104,val:"工具-电话短信"},{key:121106,val:"工具-辅助工具"},{key:121111,val:"工具-天气服务"},{key:121113,val:"工具-万年历"},{key:121114,val:"工具-星座运势"},{key:121116,val:"工具-WiFi"},{key:121118,val:"工具-浏览器"},{key:121119,val:"工具-输入法"},{key:121122,val:"工具-文件管理"},{key:121123,val:"工具-清理安全"},{key:121124,val:"工具-应用商店"},{key:121127,val:"工具-壁纸桌面"},{key:121128,val:"工具-手机铃声"},{key:121133,val:"工具-其他"},{key:121201,val:"音乐-网络K歌"},{key:121203,val:"音乐-音乐播放器"},{key:121204,val:"音乐-音乐乐器"},{key:121205,val:"音乐-音乐识别"},{key:121206,val:"音乐-在线音乐"},{key:121208,val:"音乐-有声听书"},{key:121207,val:"音乐-其他"},{key:121336,val:"休闲-棋牌捕鱼"},{key:121318,val:"硬核-赛车"},{key:121319,val:"硬核-角色扮演"},{key:121320,val:"硬核-策略"},{key:121321,val:"硬核-模拟经营"},{key:121322,val:"游戏-休闲-社交"},{key:121323,val:"游戏-休闲-射击"},{key:121324,val:"游戏-休闲-赛车"},{key:121325,val:"游戏-休闲-体育"},{key:121326,val:"游戏-休闲-模拟经营"},{key:121327,val:"游戏-休闲-动作"},{key:121328,val:"游戏-休闲-策略塔防"},{key:121329,val:"游戏-休闲-合成"},{key:121330,val:"游戏-休闲-消除"},{key:121331,val:"游戏-休闲-放置"},{key:121332,val:"游戏-休闲-答题"},{key:121333,val:"游戏-休闲-益智解迷"},{key:121334,val:"游戏-休闲-音乐节奏"},{key:121335,val:"游戏-休闲-跑酷"},{key:121317,val:"游戏-硬核-射击"},{key:121315,val:"游戏-游戏大厅"},{key:121402,val:"阅读-手机动漫"},{key:121405,val:"阅读-在线阅读"},{key:121406,val:"阅读-其他"},{key:121501,val:"政府-电子政务"},{key:121502,val:"政府-其他"},{key:121601,val:"智能设备-智能穿戴"},{key:121602,val:"智能设备-智能家居"},{key:121606,val:"智能设备-其他"},{key:121704,val:"资讯-体育资讯"},{key:121707,val:"资讯-综合资讯"},{key:121710,val:"资讯-门户资讯"},{key:121709,val:"资讯-政府资讯"},{key:121708,val:"资讯-其他"}],osList:[{key:1,val:"Android"},{key:2,val:"IOS"}],companyList:[],csjCompanyList:[],gdtCompanyList:[],ksCompanyList:[],bdCompanyList:[],oppoCompanyList:[],multipleSelection:[],multipleSelectionOwn:[],queryParam:{name:"",id:"",thirdAppId:0,os:"",pageNo:1,pageSize:10},queryParamTempList:{batchNo:"",pageNo:1,pageSize:10},listCountNum:0,listTempCountNum:0,appList:[],appTempList:[],editAppForm:{id:"",platformCode:4,companyId:"",mainBody:"",appId:"",appName:"",product:"",os:"",workLite:"",appExt:{downloadUrl:"",packageName:"",apkSign:"",keyWords:"",desc:""}},addSyncAppForm:{platformCode:4,companyId:""},addAppForm:{platformCode:4,companyId:"",mainBody:"",appId:"",appName:"",product:"",os:"",workLite:"",appExt:{downloadUrl:"",packageName:"",apkSign:"",keyWords:"",desc:""}},copyAppForm:{product:""},isShowEditAppForm:!1,isShowAddAppForm:!1,isShowSyncAppList:!1,isShowCopyAppList:!1,isShowSyncAppTempList:!1,isShowLoadAppTempList:!1,batchNo:"",appRules:{appName:[{required:!0,message:"应用名称 不能为空",trigger:"blur"}],product:[{required:!0,message:"内部AppId 不能为空",trigger:"blur"}]}}},created:function(){this.getAppList(),this.getCompanyList()},methods:{getAppList:function(){var e=this;e.$axios.post("third/app/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var o=a.data,l=0;l<o.items.length;l++)o.items[l].appExt=JSON.parse(o.items[l].appExt);e.appList=o.items,e.listCountNum=o.count}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},getCompanyList:function(){var e=this,t={name:"",id:"",pageNo:1,pageSize:1e6};e.$axios.post("third/company/list",{},{params:t}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var o=a.data,l=[],p=[],r=[],s=[],i=[],n=[],d=0;d<o.items.length;d++){var m={key:o.items[d].id,val:o.items[d].mainBody};l.push(m),1===o.items[d].platformCode?p.push(m):2===o.items[d].platformCode?r.push(m):3===o.items[d].platformCode?s.push(m):4===o.items[d].platformCode?i.push(m):5===o.items[d].platformCode&&n.push(m)}e.csjCompanyList=p,e.gdtCompanyList=r,e.ksCompanyList=s,e.bdCompanyList=i,e.oppoCompanyList=n,e.companyList=l}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},saveApp:function(){var e=this,t=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.editAppForm.validate(function(a){if(!a)return t.close(),console.log("error submit!!"),!1;e.$axios.post("third/app/updateOne",e.editAppForm,{}).then(function(a){if(t.close(),200===a.status){0===a.data.ret?(e.isShowEditAppForm=!1,e.$message({message:"主体信息更新成功",type:"success"}),e.getAppList()):e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})})},addAppPre:function(){this.clearAddForm(),this.isShowAddAppForm=!0,this.changeSelect(this.addAppForm.platformCode)},addApp:function(){var e=this,t=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.addAppForm.validate(function(a){if(!a)return t.close(),console.log("error submit!!"),!1;e.$axios.post("third/app/addOne",e.addAppForm,{}).then(function(a){if(t.close(),200===a.status){0===a.data.ret?(e.isShowAddAppForm=!1,e.$message({message:"新增主体成功",type:"success"}),e.getAppList()):e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})})},sycnAppList:function(){var e=this,t=this.$loading({lock:!0,text:"正在提交中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$axios.post("third/app/loadTempToSystem",{},{params:e.addSyncAppForm}).then(function(a){if(t.close(),e.isShowSyncAppList=!1,200===a.status){var o=a.data;0===o.ret?(e.$message({message:"任务已提交,在当前页查看",type:"success"}),e.isShowSyncAppTempList=!0,e.batchNo=o.data,e.refreshTempList()):e.$message.error(a.data.data)}else e.$message.error("服务器异常！")})},handleSelectionChange:function(e){this.multipleSelection=e},handleSelectionChangeOwn:function(e){this.multipleSelectionOwn=e},removeThisTemp:function(){var e=this;this.$confirm("此操作将丢失当前已同步的应用, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var t={batchNo:e.batchNo},a=e;a.$axios.post("third/app/removeTempFormSystem",{},{params:t}).then(function(e){if(a.isShowSyncAppTempList=!1,200===e.status){var t=e.data;0===t.ret?(a.$message.success("已取消同步"),a.getAppList()):a.$message.error(t.data)}else a.$message.error("服务器异常！")})}).catch(function(){e.$message({type:"info",message:"已取消退出"})})},clearAddForm:function(){this.addAppForm.platformCode=1,this.addAppForm.companyId="",this.addAppForm.mainBody="",this.addAppForm.appId="",this.addAppForm.appName="",this.addAppForm.product="",this.addAppForm.os="",this.addAppForm.appExt.downloadUrl="",this.addAppForm.appExt.packageName="",this.addAppForm.workLite="",this.addAppForm.appExt.keyWords="",this.addAppForm.appExt.apkSign="",this.addAppForm.appExt.desc=""},updateCompanyPre:function(e){this.changeSelect(e.platformCode),this.editAppForm.id=e.id,this.editAppForm.platformCode=e.platformCode,this.editAppForm.companyId=e.companyId,this.editAppForm.mainBody=e.mainBody,this.editAppForm.appId=e.appId,this.editAppForm.appName=e.appName,this.editAppForm.product=e.product,this.editAppForm.os=e.os,this.editAppForm.appExt.downloadUrl=e.appExt.downloadUrl,this.editAppForm.appExt.packageName=e.appExt.packageName,this.editAppForm.workLite=parseInt(e.workLite),this.editAppForm.appExt.keyWords=e.appExt.keyWords,this.editAppForm.appExt.desc=e.appExt.desc,this.editAppForm.appExt.apkSign=e.appExt.apkSign,this.isShowEditAppForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getAppList()},handleTempCurrentChangePage:function(e){this.queryParamTempList.pageNo=e,this.refreshTempList()},handleSizeChange:function(e){this.queryParamTempList.pageSize=e,this.refreshTempList()},handleSizeChangeRt:function(e){this.queryParam.pageSize=e,this.getAppList()},queryLog:function(e){},deleteApp:function(e){var t=this;this.$confirm("此操作将从我方系统删除该应用, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var a={id:e.id},o=t;o.$axios.post("third/app/delete",{},{params:a}).then(function(e){if(200===e.status){var t=e.data;0===t.ret?(o.$message.success("成功删除！"),o.getTemplateList()):o.$message.error(t.data)}else o.$message.error("服务器异常！")})}).catch(function(){t.$message({type:"info",message:"已取消删除"})})},loadAppChecked:function(){for(var e=this,t=[],a=0;a<e.multipleSelection.length;a++)t.push(e.multipleSelection[a].id);var o=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$axios.post("third/app/loadToSystemReal",t,{}).then(function(t){if(o.close(),200===t.status){0===t.data.ret?(e.isShowLoadAppTempList=!1,e.$message({message:"批量导入提交成功",type:"success"})):e.$message.error(t.data.data)}else e.$message.error("服务器异常！")})},copyAppChecked:function(){for(var e=this,t=[],a=0;a<e.multipleSelectionOwn.length;a++)t.push(e.multipleSelectionOwn[a].id);var o=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$axios.post("third/app/copyThoseToNewApp",t,{params:e.copyAppForm}).then(function(t){if(o.close(),200===t.status){0===t.data.ret?(e.isShowCopyAppList=!1,e.$message({message:"复制成功",type:"success"})):e.$message.error(t.data.data)}else e.$message.error("服务器异常！")})},refreshTempList:function(){var e=this;e.queryParamTempList.batchNo=e.batchNo,e.$axios.post("third/app/queryTempList",{},{params:e.queryParamTempList}).then(function(t){if(200===t.status){var a=t.data;if(0===a.ret){for(var o=a.data,l=0;l<o.items.length;l++)try{o.items[l].appExt=JSON.parse(o.items[l].appExt)}catch(e){console.log(e)}e.appTempList=o.items,e.listTempCountNum=o.count}else e.$message.error(a.data)}else e.$message.error("服务器异常！")})},changeSelect:function(e){1===e?(this.showCsj=!0,this.showGdt=!1):2===e&&(this.showGdt=!0,this.showCsj=!1)},changeSwitchFlag:function(e,t){var a={};a.id=t.id,a.state=t.switchFlag;var o=this;o.$axios.post("third/app/switchFlag",a,{}).then(function(e){if(200===e.status){0===e.data.ret?o.$message({message:"更新状态成功",type:"success"}):o.$message.error("更新状态失败")}else o.$message.error("服务器异常！")})}}}},389:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),e._v("应用列表")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"编辑应用",visible:e.isShowEditAppForm},on:{"update:visible":function(t){e.isShowEditAppForm=t}}},[a("el-form",{ref:"editAppForm",attrs:{model:e.editAppForm,"label-width":"200px",rules:e.appRules}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},on:{change:function(t){return e.changeSelect(e.editAppForm.platformCode)}},model:{value:e.editAppForm.platformCode,callback:function(t){e.$set(e.editAppForm,"platformCode",t)},expression:"editAppForm.platformCode"}},e._l(e.platformCodeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"主体"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体",disabled:""},model:{value:e.editAppForm.companyId,callback:function(t){e.$set(e.editAppForm,"companyId",t)},expression:"editAppForm.companyId"}},e._l(e.companyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"操作系统"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"操作系统",disabled:""},model:{value:e.editAppForm.os,callback:function(t){e.$set(e.editAppForm,"os",t)},expression:"editAppForm.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用名称",prop:"appName"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editAppForm.appName,callback:function(t){e.$set(e.editAppForm,"appName",t)},expression:"editAppForm.appName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"内部AppId",prop:"product"}},[a("el-input",{model:{value:e.editAppForm.product,callback:function(t){e.$set(e.editAppForm,"product",t)},expression:"editAppForm.product"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"包名",prop:"packageName"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editAppForm.appExt.packageName,callback:function(t){e.$set(e.editAppForm.appExt,"packageName",t)},expression:"editAppForm.appExt.packageName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"下载链接",prop:"downloadUrl"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editAppForm.appExt.downloadUrl,callback:function(t){e.$set(e.editAppForm.appExt,"downloadUrl",t)},expression:"editAppForm.appExt.downloadUrl"}})],1),e._v(" "),e.showCsj?a("div",[a("el-form-item",{attrs:{label:"行业"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"行业",disabled:""},model:{value:e.editAppForm.workLite,callback:function(t){e.$set(e.editAppForm,"workLite",t)},expression:"editAppForm.workLite"}},e._l(e.csjIndustryList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用包SHA1值",prop:"apkSign"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editAppForm.appExt.apkSign,callback:function(t){e.$set(e.editAppForm.appExt,"apkSign",t)},expression:"editAppForm.appExt.apkSign"}})],1)],1):e._e(),e._v(" "),e.showGdt?a("div",[a("el-form-item",{attrs:{label:"行业"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"行业",disabled:""},model:{value:e.editAppForm.workLite,callback:function(t){e.$set(e.editAppForm,"workLite",t)},expression:"editAppForm.workLite"}},e._l(e.gdtIndustryList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"关键词",prop:"keywords"}},[a("el-input",{attrs:{disabled:""},model:{value:e.editAppForm.appExt.keyWords,callback:function(t){e.$set(e.editAppForm.appExt,"keyWords",t)},expression:"editAppForm.appExt.keyWords"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20},disabled:""},model:{value:e.editAppForm.appExt.desc,callback:function(t){e.$set(e.editAppForm.appExt,"desc",t)},expression:"editAppForm.appExt.desc"}})],1)],1):e._e()],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowEditAppForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveApp()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"新建应用",visible:e.isShowAddAppForm},on:{"update:visible":function(t){e.isShowAddAppForm=t}}},[a("el-form",{ref:"addAppForm",attrs:{model:e.addAppForm,"label-width":"200px",rules:e.appRules}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},on:{change:function(t){return e.changeSelect(e.addAppForm.platformCode)}},model:{value:e.addAppForm.platformCode,callback:function(t){e.$set(e.addAppForm,"platformCode",t)},expression:"addAppForm.platformCode"}},e._l(e.platformCodeList.filter(function(e){return 4===e.key||5===e.key}),function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"主体"}},[1===e.addAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addAppForm.companyId,callback:function(t){e.$set(e.addAppForm,"companyId",t)},expression:"addAppForm.companyId"}},e._l(e.csjCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e(),e._v(" "),2===e.addAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addAppForm.companyId,callback:function(t){e.$set(e.addAppForm,"companyId",t)},expression:"addAppForm.companyId"}},e._l(e.gdtCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e(),e._v(" "),4===e.addAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addAppForm.companyId,callback:function(t){e.$set(e.addAppForm,"companyId",t)},expression:"addAppForm.companyId"}},e._l(e.bdCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e(),e._v(" "),5===e.addAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addAppForm.companyId,callback:function(t){e.$set(e.addAppForm,"companyId",t)},expression:"addAppForm.companyId"}},e._l(e.oppoCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e()],1),e._v(" "),a("el-form-item",{attrs:{label:"操作系统"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"操作系统"},model:{value:e.addAppForm.os,callback:function(t){e.$set(e.addAppForm,"os",t)},expression:"addAppForm.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用名称",prop:"appName"}},[a("el-input",{model:{value:e.addAppForm.appName,callback:function(t){e.$set(e.addAppForm,"appName",t)},expression:"addAppForm.appName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"内部AppId",prop:"product"}},[a("el-input",{model:{value:e.addAppForm.product,callback:function(t){e.$set(e.addAppForm,"product",t)},expression:"addAppForm.product"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"包名",prop:"packageName"}},[a("el-input",{model:{value:e.addAppForm.appExt.packageName,callback:function(t){e.$set(e.addAppForm.appExt,"packageName",t)},expression:"addAppForm.appExt.packageName"}})],1),e._v(" "),4===e.addAppForm.platformCode||5===e.addAppForm.platformCode?a("div",[a("el-form-item",{attrs:{label:"三方appId",prop:"appId"}},[a("el-input",{model:{value:e.addAppForm.appId,callback:function(t){e.$set(e.addAppForm,"appId",t)},expression:"addAppForm.appId"}})],1)],1):e._e(),e._v(" "),4!==e.addAppForm.platformCode&&5!==e.addAppForm.platformCode?a("el-form-item",{attrs:{label:"下载链接",prop:"downloadUrl"}},[a("el-input",{model:{value:e.addAppForm.appExt.downloadUrl,callback:function(t){e.$set(e.addAppForm.appExt,"downloadUrl",t)},expression:"addAppForm.appExt.downloadUrl"}})],1):e._e(),e._v(" "),e.showCsj&&4!==e.addAppForm.platformCode&&5!==e.addAppForm.platformCode?a("div",[a("el-form-item",{attrs:{label:"行业"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"行业"},model:{value:e.addAppForm.workLite,callback:function(t){e.$set(e.addAppForm,"workLite",t)},expression:"addAppForm.workLite"}},e._l(e.csjIndustryList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"应用包SHA1值",prop:"apkSign"}},[a("el-input",{model:{value:e.addAppForm.appExt.apkSign,callback:function(t){e.$set(e.addAppForm.appExt,"apkSign",t)},expression:"addAppForm.appExt.apkSign"}})],1)],1):e._e(),e._v(" "),e.showGdt&&4!==e.addAppForm.platformCode&&5!==e.addAppForm.platformCode?a("div",[a("el-form-item",{attrs:{label:"行业"}},[a("el-select",{staticClass:"select-150",attrs:{filterable:"",placeholder:"行业"},model:{value:e.addAppForm.workLite,callback:function(t){e.$set(e.addAppForm,"workLite",t)},expression:"addAppForm.workLite"}},e._l(e.gdtIndustryList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"关键词",prop:"keywords"}},[a("el-input",{model:{value:e.addAppForm.appExt.keyWords,callback:function(t){e.$set(e.addAppForm.appExt,"keyWords",t)},expression:"addAppForm.appExt.keyWords"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20}},model:{value:e.addAppForm.appExt.desc,callback:function(t){e.$set(e.addAppForm.appExt,"desc",t)},expression:"addAppForm.appExt.desc"}})],1)],1):e._e()],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowAddAppForm=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addApp()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"选择同步的应用",visible:e.isShowSyncAppList},on:{"update:visible":function(t){e.isShowSyncAppList=t}}},[a("el-form",{ref:"addSyncAppForm",attrs:{model:e.addSyncAppForm,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"平台"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},on:{change:function(t){return e.changeSelect(e.addSyncAppForm.platformCode)}},model:{value:e.addSyncAppForm.platformCode,callback:function(t){e.$set(e.addSyncAppForm,"platformCode",t)},expression:"addSyncAppForm.platformCode"}},e._l(e.platformCodeList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"主体"}},[1===e.addSyncAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addSyncAppForm.companyId,callback:function(t){e.$set(e.addSyncAppForm,"companyId",t)},expression:"addSyncAppForm.companyId"}},e._l(e.csjCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e(),e._v(" "),2===e.addSyncAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addSyncAppForm.companyId,callback:function(t){e.$set(e.addSyncAppForm,"companyId",t)},expression:"addSyncAppForm.companyId"}},e._l(e.gdtCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e(),e._v(" "),3===e.addSyncAppForm.platformCode?a("el-select",{staticClass:"select-150",attrs:{placeholder:"主体"},model:{value:e.addSyncAppForm.companyId,callback:function(t){e.$set(e.addSyncAppForm,"companyId",t)},expression:"addSyncAppForm.companyId"}},e._l(e.ksCompanyList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1):e._e()],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowSyncAppList=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.sycnAppList()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"即将复制以下三方应用",visible:e.isShowCopyAppList},on:{"update:visible":function(t){e.isShowCopyAppList=t}}},[a("el-form",{ref:"copyAppForm",attrs:{model:e.addSyncAppForm,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"内部AppId",prop:"product"}},[a("el-input",{model:{value:e.copyAppForm.product,callback:function(t){e.$set(e.copyAppForm,"product",t)},expression:"copyAppForm.product"}})],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("即将复制以下配置到上述AppId")]),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.multipleSelectionOwn,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"platformCode",label:"平台",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.platformCode?a("span",[e._v("穿山甲")]):2===t.row.platformCode?a("span",[e._v("广点通")]):3===t.row.platformCode?a("span",[e._v("快手")]):4===t.row.platformCode?a("span",[e._v("百度")]):5===t.row.platformCode?a("span",[e._v("OPPO")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"mainBody",label:"主体"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appId",label:"应用ID"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"应用名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"os",label:"操作系统"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.os?a("span",[e._v("Android")]):2===t.row.os?a("span",[e._v("IOS")]):e._e()]}}])})],1)],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowCopyAppList=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.copyAppChecked()}}},[e._v("确 定")])],1)],1),e._v(" "),a("el-dialog",{attrs:{title:"可以同步到当前系统的应用",visible:e.isShowSyncAppTempList,fullscreen:"true","show-close":"false"},on:{"update:visible":function(t){e.isShowSyncAppTempList=t}}},[a("el-dialog",{attrs:{title:"即将导入以下应用",visible:e.isShowLoadAppTempList,"append-to-body":""},on:{"update:visible":function(t){e.isShowLoadAppTempList=t}}},[a("el-divider",{attrs:{"content-position":"left"}},[e._v("导入的应用")]),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.multipleSelection,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"platformCode",label:"平台",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.platformCode?a("span",[e._v("穿山甲")]):2===t.row.platformCode?a("span",[e._v("广点通")]):3===t.row.platformCode?a("span",[e._v("快手")]):4===t.row.platformCode?a("span",[e._v("百度")]):5===t.row.platformCode?a("span",[e._v("OPPO")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"mainBody",label:"主体"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appId",label:"应用ID"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"应用名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"product",label:"内部APPId"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appStatus",label:"应用状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.appStatus?a("span",[e._v("正常")]):a("span",[e._v("异常")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"os",label:"操作系统"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.os?a("span",[e._v("Android")]):2===t.row.os?a("span",[e._v("IOS")]):e._e()]}}])})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.isShowLoadAppTempList=!1}}},[e._v("取 消")]),e._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.loadAppChecked()}}},[e._v("导 入")])],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParamTempList,size:"small",inline:""}},[a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.refreshTempList()}}},[e._v("刷新临时列表")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowLoadAppTempList=!0}}},[e._v("导入已勾选应用")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("临时应用列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.appTempList,stripe:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",label:""}}),e._v(" "),a("el-table-column",{attrs:{prop:"platformCode",label:"平台",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.platformCode?a("span",[e._v("穿山甲")]):2===t.row.platformCode?a("span",[e._v("广点通")]):3===t.row.platformCode?a("span",[e._v("快手")]):4===t.row.platformCode?a("span",[e._v("百度")]):5===t.row.platformCode?a("span",[e._v("OPPO")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"mainBody",label:"主体"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appId",label:"应用ID"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"应用名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"product",label:"内部APPId"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appStatus",label:"应用状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.appStatus?a("span",[e._v("正常")]):a("span",[e._v("异常")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"os",label:"操作系统"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.os?a("span",[e._v("Android")]):2===t.row.os?a("span",[e._v("IOS")]):e._e()]}}])})],1),e._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParamTempList.pageNo,"page-size":10,"page-sizes":[10,20,50,100],layout:"total,sizes, prev, pager, next, jumper",total:e.listTempCountNum,background:""},on:{"size-change":e.handleSizeChange,"current-change":e.handleTempCurrentChangePage}})],1)],1),e._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.removeThisTemp()}}},[e._v("放弃本批次同步")])],1)],1),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[a("el-form-item",{attrs:{label:"应用名称"}},[a("el-input",{staticClass:"width-150",model:{value:e.queryParam.name,callback:function(t){e.$set(e.queryParam,"name",t)},expression:"queryParam.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"三方应用id"}},[a("el-input",{staticClass:"width-150",model:{value:e.queryParam.thirdAppId,callback:function(t){e.$set(e.queryParam,"thirdAppId",t)},expression:"queryParam.thirdAppId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"操作系统"}},[a("el-select",{staticClass:"select-150",attrs:{placeholder:"操作系统"},model:{value:e.queryParam.os,callback:function(t){e.$set(e.queryParam,"os",t)},expression:"queryParam.os"}},e._l(e.osList,function(e){return a("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getAppList}})],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowCopyAppList=!0}}},[e._v("一键复制到新产品")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){e.isShowSyncAppList=!0}}},[e._v("同步应用")])],1),e._v(" "),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addAppPre()}}},[e._v("添加应用")])],1)],1)],1)],1),e._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[e._v("应用列表")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.appList,stripe:"","highlight-current-row":""},on:{"selection-change":e.handleSelectionChangeOwn}},[a("el-table-column",{attrs:{type:"selection",width:"55",label:""}}),e._v(" "),a("el-table-column",{attrs:{prop:"platformCode",label:"平台",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.platformCode?a("span",[e._v("穿山甲")]):2===t.row.platformCode?a("span",[e._v("广点通")]):3===t.row.platformCode?a("span",[e._v("快手")]):4===t.row.platformCode?a("span",[e._v("百度")]):5===t.row.platformCode?a("span",[e._v("OPPO")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"mainBody",label:"主体"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appId",label:"应用ID"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"应用名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"product",label:"产品AppId"}}),e._v(" "),a("el-table-column",{attrs:{prop:"productName",label:"产品名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appStatus",label:"应用状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.appStatus?a("span",[e._v("正常")]):a("span",[e._v("异常")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"workLite",label:"行业"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.platformCode?a("div",e._l(e.csjIndustryList.filter(function(e){return e.key===parseInt(t.row.workLite)}),function(t){return a("span",[e._v(e._s(t.val))])}),0):e._e(),e._v(" "),2===t.row.platformCode?a("div",e._l(e.gdtIndustryList.filter(function(e){return e.key===parseInt(t.row.workLite)}),function(t){return a("span",[e._v(e._s(t.val))])}),0):e._e(),e._v(" "),3===t.row.platformCode?a("div",e._l(e.gdtIndustryList.filter(function(e){return e.key===parseInt(t.row.workLite)}),function(t){return a("span",[e._v(e._s(t.val))])}),0):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"os",label:"操作系统"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.os?a("span",[e._v("Android")]):2===t.row.os?a("span",[e._v("IOS")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(a){return e.updateCompanyPre(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{attrs:{plain:"",type:"danger",size:"mini"},on:{click:function(a){return e.deleteApp(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),a("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-sizes":[10,20,50],"page-size":10,layout:"total,sizes, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"size-change":e.handleSizeChangeRt,"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}}});