webpackJsonp([11],{233:function(e,t,l){l(399);var a=l(87)(l(330),l(359),null,null);e.exports=a.exports},330:function(e,t,l){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{csjTemplateList:[],gdtTemplateList:[],ksTemplateList:[],bdTemplateList:[],oppoTemplateList:[],templateModel:{adName:"广告位0",adPlatform:1,adType:1,renderType:"1",adTypeSub:[],adPrice:"",adTypeOwn:"",adSite:[],isBidding:0,biddingType:1,innerType:0},adTypeSubChecked:[],adTypeRadioChecked:"",isShowAdTypeSub:!1,adSiteList:[{key:1,val:"弹窗"},{key:2,val:"固定位"},{key:3,val:"自渲染大图"}],csjAdSubTypeList:["大图","组图","单图","横板视频","竖图","竖版视频"],gdtAdSubTypeList:["上文下图","左图右文","横版视频模板"],gdtAdSubOwnTypeList:["16:9图片","16:9视频","9:16图片","9:16视频","3:2图片(3张)"],platformCodeList:[{key:1,val:"穿山甲"},{key:2,val:"广点通"},{key:3,val:"快手"},{key:4,val:"百度"},{key:5,val:"OPPO"}],innerTypeList:[{key:0,val:"半屏图片(默认)"},{key:1,val:"半屏，图片+视频"},{key:2,val:"全屏+半屏，图片+视频"}],adTypeList:[{key:1,val:"开屏"},{key:2,val:"信息流"},{key:3,val:"插屏"},{key:4,val:"激励视频"},{key:5,val:"新插屏"}],gdtVideoList:[],csjVideoList:[],ksVideoList:[],bdVideoList:[],oppoVideoList:[],gdtChapList:[],csjChapList:[],ksChapList:[],bdChapList:[],oppoChapList:[],gdtNewChapList:[],csjNewChapList:[],ksNewChapList:[],bdNewChapList:[],oppoNewChapList:[],gdtStaticList:[],csjStaticList:[],ksStaticList:[],bdStaticList:[],oppoStaticList:[],gdtKpList:[],csjKpList:[],ksKpList:[],bdKpList:[],oppoKpList:[],queryParam:{name:"",id:"",pageNo:1,pageSize:10},listCountNum:0,thirdTemplateList:[],editThirdTemplateForm:{id:"",modelName:"",templateList:[]},addThirdTemplateForm:{modelName:"",templateList:[]},thirdTemplateForm:{modelName:"",csjTemplateList:[],gdtTemplateList:[],ksTemplateList:[],bdTemplateList:[],oppoTemplateList:[]},isShowEditThirdTemplateForm:!1,isShowAddThirdTemplateForm:!1,isShowThirdTemplateForm:!1,templateRules:{modelName:[{required:!0,message:"模板名称 不能为空",trigger:"blur"}]}}},created:function(){this.getTemplateList(),this.getAdTypeDist()},methods:{getAdTypeDist:function(){var e=this;e.$axios.get("config/getAdTypeDist").then(function(t){if(200===t.status){var l=t.data;if(0===l.code)for(var a=l.result,s=0;s<a.length;s++){var i=a[s];"广点通"===i.adSource?"视频"===i.subTypeName?e.gdtVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("2.0")>-1?e.gdtNewChapList.push({key:i.adType,val:i.typeName}):e.gdtChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.gdtStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.gdtKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.gdtNewChapList.push({key:i.adType,val:i.typeName}):"穿山甲"===i.adSource?"视频"===i.subTypeName?e.csjVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.csjNewChapList.push({key:i.adType,val:i.typeName}):e.csjChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.csjStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.csjKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.csjNewChapList.push({key:i.adType,val:i.typeName}):"快手"===i.adSource?"视频"===i.subTypeName?e.ksVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.ksNewChapList.push({key:i.adType,val:i.typeName}):e.ksChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.ksStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.ksKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.ksNewChapList.push({key:i.adType,val:i.typeName}):"百度"===i.adSource?"视频"===i.subTypeName?e.bdVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.bdNewChapList.push({key:i.adType,val:i.typeName}):e.bdChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.bdStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.bdKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.bdNewChapList.push({key:i.adType,val:i.typeName}):"OPPO"===i.adSource&&("视频"===i.subTypeName?e.oppoVideoList.push({key:i.adType,val:i.typeName}):"插屏"===i.subTypeName?i.typeName.indexOf("新")>-1?e.oppoNewChapList.push({key:i.adType,val:i.typeName}):e.oppoChapList.push({key:i.adType,val:i.typeName}):"静态图"===i.subTypeName?e.oppoStaticList.push({key:i.adType,val:i.typeName}):"开屏"===i.subTypeName?e.oppoKpList.push({key:i.adType,val:i.typeName}):"新插屏"===i.subTypeName&&e.oppoNewChapList.push({key:i.adType,val:i.typeName}))}else e.$message.error("加载数据失败...")}else e.$message.error("服务器异常！")})},getAdTypeDymicList:function(e,t){if(1===e){if(1===t)return this.csjKpList;if(2===t)return this.csjStaticList;if(3===t)return this.csjChapList;if(4===t)return this.csjVideoList;if(5===t)return this.csjNewChapList}else if(2===e){if(1===t)return this.gdtKpList;if(2===t)return this.gdtStaticList;if(3===t)return this.gdtChapList;if(4===t)return this.gdtVideoList;if(5===t)return this.gdtNewChapList}else if(3===e){if(1===t)return this.ksKpList;if(2===t)return this.ksStaticList;if(3===t)return this.ksChapList;if(4===t)return this.ksVideoList;if(5===t)return this.ksNewChapList}else if(4===e){if(1===t)return this.bdKpList;if(2===t)return this.bdStaticList;if(3===t)return this.bdChapList;if(4===t)return this.bdVideoList;if(5===t)return this.bdNewChapList}else if(5===e){if(1===t)return this.oppoKpList;if(2===t)return this.oppoStaticList;if(3===t)return this.oppoChapList;if(4===t)return this.oppoVideoList;if(5===t)return this.oppoNewChapList}},getTemplateList:function(){var e=this;e.$axios.post("third/template/list",{},{params:e.queryParam}).then(function(t){if(200===t.status){var l=t.data;if(0===l.ret){var a=l.data;e.thirdTemplateList=a.items,e.listCountNum=a.count}else e.$message.error(l.data)}else e.$message.error("服务器异常！")})},saveTemplate:function(){var e=this,t=[];t=t.concat(e.csjTemplateList),t=t.concat(e.gdtTemplateList),t=t.concat(e.ksTemplateList),t=t.concat(e.bdTemplateList),t=t.concat(e.oppoTemplateList),e.editThirdTemplateForm.templateList=t;var l=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.editThirdTemplateForm.validate(function(t){if(!t)return l.close(),console.log("error submit!!"),!1;e.$axios.post("third/template/updateOne",e.editThirdTemplateForm,{}).then(function(t){if(l.close(),200===t.status){0===t.data.ret?(e.isShowEditThirdTemplateForm=!1,e.$message({message:"主体信息更新成功",type:"success"}),e.getTemplateList()):e.$message.error(t.data.data)}else e.$message.error("服务器异常！")})})},addTemplatePre:function(){this.clearAddTemplateForm(),this.isShowAddThirdTemplateForm=!0},addTemplate:function(){var e=this,t=[];t=t.concat(this.csjTemplateList),t=t.concat(this.gdtTemplateList),t=t.concat(this.ksTemplateList),t=t.concat(this.bdTemplateList),t=t.concat(this.oppoTemplateList),e.addThirdTemplateForm.templateList=t;var l=this.$loading({lock:!0,text:"正在处理中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});e.$refs.addThirdTemplateForm.validate(function(t){if(!t)return l.close(),console.log("error submit!!"),!1;e.$axios.post("third/template/addOne",e.addThirdTemplateForm,{}).then(function(t){if(l.close(),200===t.status){0===t.data.ret?(e.isShowAddThirdTemplateForm=!1,e.$message({message:"新增模板成功！",type:"success"}),e.getTemplateList()):e.$message.error(t.data.data)}else e.$message.error("服务器异常！")})})},clearAddTemplateForm:function(){this.addThirdTemplateForm.modelName="",this.addThirdTemplateForm.templateList=[{adName:"",adPlatform:1,adType:"",adTypeSub:"",adPrice:""}],this.csjTemplateList=[],this.gdtTemplateList=[],this.ksTemplateList=[]},updateTemplatePre:function(e){this.csjTemplateList=[],this.gdtTemplateList=[];for(var t=[],l=[],a=[],s=[],i=[],r=e.templateInfoList,p=0;p<r.length;p++)1===r[p].adPlatform?t.push(r[p]):2===r[p].adPlatform?l.push(r[p]):3===r[p].adPlatform?a.push(r[p]):4===r[p].adPlatform?s.push(r[p]):5===r[p].adPlatform&&i.push(r[p]);this.csjTemplateList=t,this.gdtTemplateList=l,this.ksTemplateList=a,this.bdTemplateList=s,this.oppoTemplateList=i;var d=this.csjTemplateList.length+this.gdtTemplateList.length+this.ksTemplateList.length+1+this.bdTemplateList.length+this.oppoTemplateList.length,o="广告位"+d;this.templateModel={adName:o,adPlatform:this.templateModel.adPlatform,adType:this.templateModel.adType,adTypeSub:this.templateModel.adTypeSub,adPrice:this.templateModel.adPrice,isBidding:this.templateModel.isBidding,biddingType:this.templateModel.biddingType},this.editThirdTemplateForm.id=e.id,this.editThirdTemplateForm.modelName=e.modelName,this.isShowEditThirdTemplateForm=!0},showDetail:function(e){for(var t=[],l=[],a=[],s=[],i=[],r=e.templateInfoList,p=0;p<r.length;p++)1===r[p].adPlatform?t.push(r[p]):2===r[p].adPlatform?l.push(r[p]):3===r[p].adPlatform?a.push(r[p]):4===r[p].adPlatform?s.push(r[p]):5===r[p].adPlatform&&i.push(r[p]);this.thirdTemplateForm.modelName=e.modelName,this.thirdTemplateForm.csjTemplateList=t,this.thirdTemplateForm.gdtTemplateList=l,this.thirdTemplateForm.ksTemplateList=a,this.thirdTemplateForm.bdTemplateList=s,this.thirdTemplateForm.oppoTemplateList=i,this.isShowThirdTemplateForm=!0},handleCurrentChangePage:function(e){this.queryParam.pageNo=e,this.getTemplateList()},copyTemplate:function(e){var t=this;this.$confirm("即将复制, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l={id:e.id},a=t;a.$axios.post("third/template/copy",{},{params:l}).then(function(e){if(200===e.status){var t=e.data;0===t.ret?(a.$message.success("成功复制！"),a.getTemplateList()):a.$message.error(t.data)}else a.$message.error("服务器异常！")})}).catch(function(){t.$message({type:"info",message:"已取消复制"})})},deleteTemplate:function(e){var t=this;this.$confirm("此操作将永久删除该模板, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){var l={id:e.id},a=t;a.$axios.post("third/template/delete",{},{params:l}).then(function(e){if(200===e.status){var t=e.data;0===t.ret?(a.$message.success("成功删除！"),a.getTemplateList()):a.$message.error(t.data)}else a.$message.error("服务器异常！")})}).catch(function(){t.$message({type:"info",message:"已取消删除"})})},deleteNode:function(e,t){"csjTemplateList"===t?this.csjTemplateList.splice(e,1):"gdtTemplateList"===t?this.gdtTemplateList.splice(e,1):"ksTemplateList"===t?this.ksTemplateList.splice(e,1):"bdTemplateList"===t?this.bdTemplateList.splice(e,1):"oppoTemplateList"===t&&this.oppoTemplateList.splice(e,1)},addNode:function(){var e=this.csjTemplateList.length+this.gdtTemplateList.length+this.ksTemplateList.length+this.bdTemplateList.length+1,t="广告位"+e;""!=this.adTypeRadioChecked&&this.adTypeSubChecked.push(this.adTypeRadioChecked);var l={adName:this.templateModel.adName,adPlatform:this.templateModel.adPlatform,adType:this.templateModel.adType,adTypeSub:this.adTypeSubChecked,renderType:this.templateModel.renderType,adPrice:this.templateModel.adPrice,adTypeOwn:this.templateModel.adTypeOwn,adSite:this.templateModel.adSite,biddingType:this.templateModel.biddingType,isBidding:this.templateModel.isBidding,innerType:this.templateModel.innerType};if(0===this.templateModel.adName.length||this.templateModel.adName.length>30)return this.$message.error("广告名称填写异常！请重新填写"),!1;var a=/^[0-9][0-9]*([.][0-9]+)?$/,s=this.templateModel.adPrice;if(void 0!==s&&null!==s&&""!==s&&!a.test(s))return this.$message.error("目标价填写异常！请重新填写"),!1;1===this.templateModel.adPlatform?this.csjTemplateList.push(l):2===this.templateModel.adPlatform?this.gdtTemplateList.push(l):3===this.templateModel.adPlatform?this.ksTemplateList.push(l):4===this.templateModel.adPlatform?this.bdTemplateList.push(l):5===this.templateModel.adPlatform&&this.oppoTemplateList.push(l),this.templateModel.adName=t,this.adTypeSubChecked=[],this.adTypeRadioChecked=""},changeAdType:function(e){this.isShowAdTypeSub=2===e},changeAdPlatform:function(e){this.templateModel.adTypeSub="",this.templateModel.adSite=""}}}},334:function(e,t,l){t=e.exports=l(29)(),t.push([e.i,".width-100{width:100px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}.select-200 .el-input{width:290px}",""])},359:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"table"},[l("div",{staticClass:"crumbs"},[l("el-breadcrumb",{attrs:{separator:"/"}},[l("el-breadcrumb-item",[l("i",{staticClass:"el-icon-menu"}),e._v("模板管理")])],1)],1),e._v(" "),l("el-dialog",{attrs:{title:"修改模板",visible:e.isShowEditThirdTemplateForm,fullscreen:"true"},on:{"update:visible":function(t){e.isShowEditThirdTemplateForm=t}}},[l("el-form",{ref:"editThirdTemplateForm",attrs:{model:e.editThirdTemplateForm,rules:e.templateRules}},[l("el-form-item",{attrs:{label:"模板名称",prop:"modelName"}},[l("el-input",{attrs:{placeholder:""},model:{value:e.editThirdTemplateForm.modelName,callback:function(t){e.$set(e.editThirdTemplateForm,"modelName",t)},expression:"editThirdTemplateForm.modelName"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"穿山甲"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.csjTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.csjTemplateList[a].adName,callback:function(t){e.$set(e.csjTemplateList[a],"adName",t)},expression:"csjTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.csjTemplateList[a].adPlatform,callback:function(t){e.$set(e.csjTemplateList[a],"adPlatform",t)},expression:"csjTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",filterable:""},model:{value:e.csjTemplateList[a].adType,callback:function(t){e.$set(e.csjTemplateList[a],"adType",t)},expression:"csjTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.csjTemplateList[a].adPrice,callback:function(t){e.$set(e.csjTemplateList[a],"adPrice",t)},expression:"csjTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.csjTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.csjTemplateList[a],"adTypeOwn",t)},expression:"csjTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(1,e.csjTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"csjTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.csjTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.csjTemplateList[a].adSite,callback:function(t){e.$set(e.csjTemplateList[a],"adSite",t)},expression:"csjTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.csjTemplateList[a].renderType,callback:function(t){e.$set(e.csjTemplateList[a],"renderType",t)},expression:"csjTemplateList[i].renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),1===e.csjTemplateList[a].adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1},model:{value:e.csjTemplateList[a].adTypeSub,callback:function(t){e.$set(e.csjTemplateList[a],"adTypeSub",t)},expression:"csjTemplateList[i].adTypeSub"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.csjTemplateList[a].adPlatform?l("el-radio-group",{attrs:{size:"small",min:1},model:{value:e.csjTemplateList[a].adTypeSub[0],callback:function(t){e.$set(e.csjTemplateList[a].adTypeSub,0,t)},expression:"csjTemplateList[i].adTypeSub[0]"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1):e._e(),e._v(" "),e.csjTemplateList[a].adType>=3&&1===e.csjTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.csjTemplateList[a].isBidding,callback:function(t){e.$set(e.csjTemplateList[a],"isBidding",t)},expression:"csjTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.csjTemplateList[a].adType||5===e.csjTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.csjTemplateList[a].innerType,callback:function(t){e.$set(e.csjTemplateList[a],"innerType",t)},expression:"csjTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.csjTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.csjTemplateList[a].biddingType,callback:function(t){e.$set(e.csjTemplateList[a],"biddingType",t)},expression:"csjTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"广点通"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.gdtTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.gdtTemplateList[a].adName,callback:function(t){e.$set(e.gdtTemplateList[a],"adName",t)},expression:"gdtTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.gdtTemplateList[a].adPlatform,callback:function(t){e.$set(e.gdtTemplateList[a],"adPlatform",t)},expression:"gdtTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",filterable:""},model:{value:e.gdtTemplateList[a].adType,callback:function(t){e.$set(e.gdtTemplateList[a],"adType",t)},expression:"gdtTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.gdtTemplateList[a].adPrice,callback:function(t){e.$set(e.gdtTemplateList[a],"adPrice",t)},expression:"gdtTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.gdtTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.gdtTemplateList[a],"adTypeOwn",t)},expression:"gdtTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(2,e.gdtTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"gdtTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.gdtTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.gdtTemplateList[a].adSite,callback:function(t){e.$set(e.gdtTemplateList[a],"adSite",t)},expression:"gdtTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.gdtTemplateList[a].renderType,callback:function(t){e.$set(e.gdtTemplateList[a],"renderType",t)},expression:"gdtTemplateList[i].renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),1===e.gdtTemplateList[a].adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1},model:{value:e.gdtTemplateList[a].adTypeSub,callback:function(t){e.$set(e.gdtTemplateList[a],"adTypeSub",t)},expression:"gdtTemplateList[i].adTypeSub"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.gdtTemplateList[a].adPlatform&&"2"===e.gdtTemplateList[a].renderType?l("el-checkbox-group",{attrs:{size:"small",min:1},model:{value:e.gdtTemplateList[a].adTypeSub,callback:function(t){e.$set(e.gdtTemplateList[a],"adTypeSub",t)},expression:"gdtTemplateList[i].adTypeSub"}},e._l(e.gdtAdSubOwnTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.gdtTemplateList[a].adPlatform&&"1"===e.gdtTemplateList[a].renderType?l("el-radio-group",{attrs:{size:"small",min:1},model:{value:e.gdtTemplateList[a].adTypeSub[0],callback:function(t){e.$set(e.gdtTemplateList[a].adTypeSub,0,t)},expression:"gdtTemplateList[i].adTypeSub[0]"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{min:1,max:4,label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1):e._e(),e._v(" "),e.gdtTemplateList[a].adType>=3&&2===e.gdtTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.gdtTemplateList[a].isBidding,callback:function(t){e.$set(e.gdtTemplateList[a],"isBidding",t)},expression:"gdtTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.gdtTemplateList[a].adType||5===e.gdtTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.gdtTemplateList[a].innerType,callback:function(t){e.$set(e.gdtTemplateList[a],"innerType",t)},expression:"gdtTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.gdtTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.gdtTemplateList[a].biddingType,callback:function(t){e.$set(e.gdtTemplateList[a],"biddingType",t)},expression:"gdtTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"快手"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.ksTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.ksTemplateList[a].adName,callback:function(t){e.$set(e.ksTemplateList[a],"adName",t)},expression:"ksTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.ksTemplateList[a].adPlatform,callback:function(t){e.$set(e.ksTemplateList[a],"adPlatform",t)},expression:"ksTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.ksTemplateList[a].adType,callback:function(t){e.$set(e.ksTemplateList[a],"adType",t)},expression:"ksTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.ksTemplateList[a].adPrice,callback:function(t){e.$set(e.ksTemplateList[a],"adPrice",t)},expression:"ksTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.ksTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.ksTemplateList[a],"adTypeOwn",t)},expression:"ksTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(3,e.ksTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"ksTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.ksTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.ksTemplateList[a].adSite,callback:function(t){e.$set(e.ksTemplateList[a],"adSite",t)},expression:"ksTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.ksTemplateList[a].adType>=3&&3===e.ksTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.ksTemplateList[a].isBidding,callback:function(t){e.$set(e.ksTemplateList[a],"isBidding",t)},expression:"ksTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.ksTemplateList[a].adType||5===e.ksTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.ksTemplateList[a].innerType,callback:function(t){e.$set(e.ksTemplateList[a],"innerType",t)},expression:"ksTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.ksTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.ksTemplateList[a].biddingType,callback:function(t){e.$set(e.ksTemplateList[a],"biddingType",t)},expression:"ksTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"百度"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.bdTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.bdTemplateList[a].adName,callback:function(t){e.$set(e.bdTemplateList[a],"adName",t)},expression:"bdTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.bdTemplateList[a].adPlatform,callback:function(t){e.$set(e.bdTemplateList[a],"adPlatform",t)},expression:"bdTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.bdTemplateList[a].adType,callback:function(t){e.$set(e.bdTemplateList[a],"adType",t)},expression:"bdTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.bdTemplateList[a].adPrice,callback:function(t){e.$set(e.bdTemplateList[a],"adPrice",t)},expression:"bdTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.bdTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.bdTemplateList[a],"adTypeOwn",t)},expression:"bdTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(4,e.bdTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"bdTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.bdTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.bdTemplateList[a].adSite,callback:function(t){e.$set(e.bdTemplateList[a],"adSite",t)},expression:"bdTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.bdTemplateList[a].adType>=3&&4===e.bdTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.bdTemplateList[a].isBidding,callback:function(t){e.$set(e.bdTemplateList[a],"isBidding",t)},expression:"bdTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.bdTemplateList[a].adType||5===e.bdTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.bdTemplateList[a].innerType,callback:function(t){e.$set(e.bdTemplateList[a],"innerType",t)},expression:"bdTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.bdTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.bdTemplateList[a].biddingType,callback:function(t){e.$set(e.bdTemplateList[a],"biddingType",t)},expression:"bdTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"OPPO"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.oppoTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.oppoTemplateList[a].adName,callback:function(t){e.$set(e.oppoTemplateList[a],"adName",t)},expression:"oppoTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.oppoTemplateList[a].adPlatform,callback:function(t){e.$set(e.oppoTemplateList[a],"adPlatform",t)},expression:"oppoTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.oppoTemplateList[a].adType,callback:function(t){e.$set(e.oppoTemplateList[a],"adType",t)},expression:"oppoTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.oppoTemplateList[a].adPrice,callback:function(t){e.$set(e.oppoTemplateList[a],"adPrice",t)},expression:"oppoTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.oppoTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.oppoTemplateList[a],"adTypeOwn",t)},expression:"oppoTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(5,e.oppoTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"oppoTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.oppoTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.oppoTemplateList[a].adSite,callback:function(t){e.$set(e.oppoTemplateList[a],"adSite",t)},expression:"oppoTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.oppoTemplateList[a].adType>=3&&5===e.oppoTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-radio-group",{model:{value:e.oppoTemplateList[a].isBidding,callback:function(t){e.$set(e.oppoTemplateList[a],"isBidding",t)},expression:"oppoTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1),e._v(" "),1===e.oppoTemplateList[a].isBidding?l("div",[l("el-radio-group",{model:{value:e.oppoTemplateList[a].biddingType,callback:function(t){e.$set(e.oppoTemplateList[a],"biddingType",t)},expression:"oppoTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1):e._e()],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),l("el-form-item",[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.templateModel.adName,callback:function(t){e.$set(e.templateModel,"adName",t)},expression:"templateModel.adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},on:{change:function(t){return e.changeAdPlatform(e.templateModel.adPlatform)}},model:{value:e.templateModel.adPlatform,callback:function(t){e.$set(e.templateModel,"adPlatform",t)},expression:"templateModel.adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},on:{change:function(t){return e.changeAdType(e.templateModel.adType)}},model:{value:e.templateModel.adType,callback:function(t){e.$set(e.templateModel,"adType",t)},expression:"templateModel.adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.templateModel.adPrice,callback:function(t){e.$set(e.templateModel,"adPrice",t)},expression:"templateModel.adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("div",[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.templateModel.adTypeOwn,callback:function(t){e.$set(e.templateModel,"adTypeOwn",t)},expression:"templateModel.adTypeOwn"}},e._l(e.getAdTypeDymicList(e.templateModel.adPlatform,e.templateModel.adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"primary",icon:"el-icon-plus",circle:""},on:{click:function(t){return e.addNode()}}})],1)])],1)],1),e._v(" "),e.isShowAdTypeSub?l("div",[l("el-form-item",[l("el-col",{attrs:{gutter:30}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.templateModel.adSite,callback:function(t){e.$set(e.templateModel,"adSite",t)},expression:"templateModel.adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.templateModel.renderType,callback:function(t){e.$set(e.templateModel,"renderType",t)},expression:"templateModel.renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),l("el-col",{attrs:{span:19}},[l("div",{staticClass:"grid-content bg-purple"},[1===e.templateModel.adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1,max:"4"},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.templateModel.adPlatform&&"2"===e.templateModel.renderType?l("el-checkbox-group",{attrs:{size:"small",min:1,max:"4"},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.gdtAdSubOwnTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.templateModel.adPlatform&&"1"===e.templateModel.renderType?l("el-radio-group",{attrs:{size:"small"},model:{value:e.adTypeRadioChecked,callback:function(t){e.adTypeRadioChecked=t},expression:"adTypeRadioChecked"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{min:1,max:4,label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1)])],1)],1)],1):e._e(),e._v(" "),e.templateModel.adType>=3?l("div",[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"竞价模式",prop:"packageName"}},[l("el-radio-group",{model:{value:e.templateModel.isBidding,callback:function(t){e.$set(e.templateModel,"isBidding",t)},expression:"templateModel.isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1)],1),e._v(" "),3===e.templateModel.adType||5===e.templateModel.adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.templateModel.innerType,callback:function(t){e.$set(e.templateModel,"innerType",t)},expression:"templateModel.innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.templateModel.isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"Bidding接入模式",prop:"packageName"}},[l("el-radio-group",{model:{value:e.templateModel.biddingType,callback:function(t){e.$set(e.templateModel,"biddingType",t)},expression:"templateModel.biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1)],1):e._e()],1)],1):e._e()],1),e._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.isShowEditThirdTemplateForm=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveTemplate()}}},[e._v("确 定")])],1)],1),e._v(" "),l("el-dialog",{attrs:{title:"添加模板",visible:e.isShowAddThirdTemplateForm,fullscreen:"true"},on:{"update:visible":function(t){e.isShowAddThirdTemplateForm=t}}},[l("el-form",{ref:"addThirdTemplateForm",attrs:{model:e.addThirdTemplateForm,rules:e.templateRules}},[l("el-form-item",{attrs:{label:"模板名称",prop:"modelName"}},[l("el-input",{attrs:{placeholder:""},model:{value:e.addThirdTemplateForm.modelName,callback:function(t){e.$set(e.addThirdTemplateForm,"modelName",t)},expression:"addThirdTemplateForm.modelName"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"穿山甲"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.csjTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.csjTemplateList[a].adName,callback:function(t){e.$set(e.csjTemplateList[a],"adName",t)},expression:"csjTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.csjTemplateList[a].adPlatform,callback:function(t){e.$set(e.csjTemplateList[a],"adPlatform",t)},expression:"csjTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.csjTemplateList[a].adType,callback:function(t){e.$set(e.csjTemplateList[a],"adType",t)},expression:"csjTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.csjTemplateList[a].adPrice,callback:function(t){e.$set(e.csjTemplateList[a],"adPrice",t)},expression:"csjTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.csjTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.csjTemplateList[a],"adTypeOwn",t)},expression:"csjTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(1,e.csjTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"csjTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.csjTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.csjTemplateList[a].adSite,callback:function(t){e.$set(e.csjTemplateList[a],"adSite",t)},expression:"csjTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.csjTemplateList[a].renderType,callback:function(t){e.$set(e.csjTemplateList[a],"renderType",t)},expression:"csjTemplateList[i].renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),1===e.csjTemplateList[a].adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1},model:{value:e.csjTemplateList[a].adTypeSub,callback:function(t){e.$set(e.csjTemplateList[a],"adTypeSub",t)},expression:"csjTemplateList[i].adTypeSub"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.csjTemplateList[a].adPlatform?l("el-radio-group",{attrs:{size:"small",min:1},model:{value:e.csjTemplateList[a].adTypeSub[0],callback:function(t){e.$set(e.csjTemplateList[a].adTypeSub,0,t)},expression:"csjTemplateList[i].adTypeSub[0]"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1):e._e(),e._v(" "),e.csjTemplateList[a].adType>=3&&1===e.csjTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.csjTemplateList[a].isBidding,callback:function(t){e.$set(e.csjTemplateList[a],"isBidding",t)},expression:"csjTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.csjTemplateList[a].adType||5===e.csjTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.csjTemplateList[a].innerType,callback:function(t){e.$set(e.csjTemplateList[a],"innerType",t)},expression:"csjTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.csjTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.csjTemplateList[a].biddingType,callback:function(t){e.$set(e.csjTemplateList[a],"biddingType",t)},expression:"csjTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"广点通"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.gdtTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.gdtTemplateList[a].adName,callback:function(t){e.$set(e.gdtTemplateList[a],"adName",t)},expression:"gdtTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.gdtTemplateList[a].adPlatform,callback:function(t){e.$set(e.gdtTemplateList[a],"adPlatform",t)},expression:"gdtTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.gdtTemplateList[a].adType,callback:function(t){e.$set(e.gdtTemplateList[a],"adType",t)},expression:"gdtTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.gdtTemplateList[a].adPrice,callback:function(t){e.$set(e.gdtTemplateList[a],"adPrice",t)},expression:"gdtTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.gdtTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.gdtTemplateList[a],"adTypeOwn",t)},expression:"gdtTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(2,e.gdtTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"gdtTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.gdtTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.gdtTemplateList[a].adSite,callback:function(t){e.$set(e.gdtTemplateList[a],"adSite",t)},expression:"gdtTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.gdtTemplateList[a].renderType,callback:function(t){e.$set(e.gdtTemplateList[a],"renderType",t)},expression:"gdtTemplateList[i].renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),1===e.gdtTemplateList[a].adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1},model:{value:e.gdtTemplateList[a].adTypeSub,callback:function(t){e.$set(e.gdtTemplateList[a],"adTypeSub",t)},expression:"gdtTemplateList[i].adTypeSub"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.gdtTemplateList[a].adPlatform&&"2"===e.gdtTemplateList[a].renderType?l("el-checkbox-group",{attrs:{size:"small",min:1},model:{value:e.gdtTemplateList[a].adTypeSub,callback:function(t){e.$set(e.gdtTemplateList[a],"adTypeSub",t)},expression:"gdtTemplateList[i].adTypeSub"}},e._l(e.gdtAdSubOwnTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.gdtTemplateList[a].adPlatform&&"1"===e.gdtTemplateList[a].renderType?l("el-radio-group",{attrs:{size:"small",min:1},model:{value:e.gdtTemplateList[a].adTypeSub[0],callback:function(t){e.$set(e.gdtTemplateList[a].adTypeSub,0,t)},expression:"gdtTemplateList[i].adTypeSub[0]"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{min:1,max:4,label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1):e._e(),e._v(" "),e.gdtTemplateList[a].adType>=3&&1===e.gdtTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.gdtTemplateList[a].isBidding,callback:function(t){e.$set(e.gdtTemplateList[a],"isBidding",t)},expression:"gdtTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.gdtTemplateList[a].adType||5===e.gdtTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.gdtTemplateList[a].innerType,callback:function(t){e.$set(e.gdtTemplateList[a],"innerType",t)},expression:"gdtTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.gdtTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.gdtTemplateList[a].biddingType,callback:function(t){e.$set(e.gdtTemplateList[a],"biddingType",t)},expression:"gdtTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"快手"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.ksTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.ksTemplateList[a].adName,callback:function(t){e.$set(e.ksTemplateList[a],"adName",t)},expression:"ksTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.ksTemplateList[a].adPlatform,callback:function(t){e.$set(e.ksTemplateList[a],"adPlatform",t)},expression:"ksTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.ksTemplateList[a].adType,callback:function(t){e.$set(e.ksTemplateList[a],"adType",t)},expression:"ksTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.ksTemplateList[a].adPrice,callback:function(t){e.$set(e.ksTemplateList[a],"adPrice",t)},expression:"ksTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.ksTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.ksTemplateList[a],"adTypeOwn",t)},expression:"ksTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(3,e.ksTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"ksTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.ksTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.ksTemplateList[a].adSite,callback:function(t){e.$set(e.ksTemplateList[a],"adSite",t)},expression:"ksTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.ksTemplateList[a].adType>=3&&3===e.ksTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.ksTemplateList[a].isBidding,callback:function(t){e.$set(e.ksTemplateList[a],"isBidding",t)},expression:"ksTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.ksTemplateList[a].adType||5===e.ksTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.ksTemplateList[a].innerType,callback:function(t){e.$set(e.ksTemplateList[a],"innerType",t)},expression:"ksTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.ksTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.ksTemplateList[a].biddingType,callback:function(t){e.$set(e.ksTemplateList[a],"biddingType",t)},expression:"ksTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"百度"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.bdTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.bdTemplateList[a].adName,callback:function(t){e.$set(e.bdTemplateList[a],"adName",t)},expression:"bdTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.bdTemplateList[a].adPlatform,callback:function(t){e.$set(e.bdTemplateList[a],"adPlatform",t)},expression:"bdTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.bdTemplateList[a].adType,callback:function(t){e.$set(e.bdTemplateList[a],"adType",t)},expression:"bdTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.bdTemplateList[a].adPrice,callback:function(t){e.$set(e.bdTemplateList[a],"adPrice",t)},expression:"bdTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.bdTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.bdTemplateList[a],"adTypeOwn",t)},expression:"bdTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(4,e.bdTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"bdTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.bdTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.bdTemplateList[a].adSite,callback:function(t){e.$set(e.bdTemplateList[a],"adSite",t)},expression:"bdTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.bdTemplateList[a].adType>=3&&4===e.bdTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.bdTemplateList[a].isBidding,callback:function(t){e.$set(e.bdTemplateList[a],"isBidding",t)},expression:"bdTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.bdTemplateList[a].adType||5===e.bdTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.bdTemplateList[a].innerType,callback:function(t){e.$set(e.bdTemplateList[a],"innerType",t)},expression:"bdTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.bdTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.bdTemplateList[a].biddingType,callback:function(t){e.$set(e.bdTemplateList[a],"biddingType",t)},expression:"bdTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"OPPO"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.oppoTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.oppoTemplateList[a].adName,callback:function(t){e.$set(e.oppoTemplateList[a],"adName",t)},expression:"oppoTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.oppoTemplateList[a].adPlatform,callback:function(t){e.$set(e.oppoTemplateList[a],"adPlatform",t)},expression:"oppoTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},model:{value:e.oppoTemplateList[a].adType,callback:function(t){e.$set(e.oppoTemplateList[a],"adType",t)},expression:"oppoTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.oppoTemplateList[a].adPrice,callback:function(t){e.$set(e.oppoTemplateList[a],"adPrice",t)},expression:"oppoTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.oppoTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.oppoTemplateList[a],"adTypeOwn",t)},expression:"oppoTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(5,e.oppoTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"danger",icon:"el-icon-delete",circle:""},on:{click:function(t){return e.deleteNode(a,"oppoTemplateList")}}})],1)])],1),e._v(" "),l("el-row",[2===e.oppoTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.oppoTemplateList[a].adSite,callback:function(t){e.$set(e.oppoTemplateList[a],"adSite",t)},expression:"oppoTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.oppoTemplateList[a].adType>=3&&5===e.oppoTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-radio-group",{model:{value:e.oppoTemplateList[a].isBidding,callback:function(t){e.$set(e.oppoTemplateList[a],"isBidding",t)},expression:"oppoTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1),e._v(" "),1===e.oppoTemplateList[a].isBidding?l("div",[l("el-radio-group",{model:{value:e.oppoTemplateList[a].biddingType,callback:function(t){e.$set(e.oppoTemplateList[a],"biddingType",t)},expression:"oppoTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1):e._e()],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-s-tools"})]),e._v(" "),l("el-form-item",[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称"},model:{value:e.templateModel.adName,callback:function(t){e.$set(e.templateModel,"adName",t)},expression:"templateModel.adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台"},on:{change:function(t){return e.changeAdPlatform(e.templateModel.adPlatform)}},model:{value:e.templateModel.adPlatform,callback:function(t){e.$set(e.templateModel,"adPlatform",t)},expression:"templateModel.adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型"},on:{change:function(t){return e.changeAdType(e.templateModel.adType)}},model:{value:e.templateModel.adType,callback:function(t){e.$set(e.templateModel,"adType",t)},expression:"templateModel.adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价"},model:{value:e.templateModel.adPrice,callback:function(t){e.$set(e.templateModel,"adPrice",t)},expression:"templateModel.adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("div",[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",filterable:""},model:{value:e.templateModel.adTypeOwn,callback:function(t){e.$set(e.templateModel,"adTypeOwn",t)},expression:"templateModel.adTypeOwn"}},e._l(e.getAdTypeDymicList(e.templateModel.adPlatform,e.templateModel.adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-button",{attrs:{type:"primary",icon:"el-icon-plus",circle:""},on:{click:function(t){return e.addNode()}}})],1)])],1)],1),e._v(" "),e.isShowAdTypeSub?l("div",[l("el-form-item",[l("el-col",{attrs:{gutter:30}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置"},model:{value:e.templateModel.adSite,callback:function(t){e.$set(e.templateModel,"adSite",t)},expression:"templateModel.adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.templateModel.renderType,callback:function(t){e.$set(e.templateModel,"renderType",t)},expression:"templateModel.renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),l("el-col",{attrs:{span:19}},[l("div",{staticClass:"grid-content bg-purple"},[1===e.templateModel.adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1,max:4},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.templateModel.adPlatform&&"2"===e.templateModel.renderType?l("el-checkbox-group",{attrs:{size:"small",min:1,max:4},model:{value:e.adTypeSubChecked,callback:function(t){e.adTypeSubChecked=t},expression:"adTypeSubChecked"}},e._l(e.gdtAdSubOwnTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.templateModel.adPlatform&&"1"===e.templateModel.renderType?l("el-radio-group",{attrs:{size:"small"},model:{value:e.adTypeRadioChecked,callback:function(t){e.adTypeRadioChecked=t},expression:"adTypeRadioChecked"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{min:1,max:4,label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1)])],1)],1)],1):e._e(),e._v(" "),e.templateModel.adType>=3?l("div",[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"竞价模式",prop:"packageName"}},[l("el-radio-group",{model:{value:e.templateModel.isBidding,callback:function(t){e.$set(e.templateModel,"isBidding",t)},expression:"templateModel.isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1)],1),e._v(" "),3===e.templateModel.adType||5===e.templateModel.adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.templateModel.innerType,callback:function(t){e.$set(e.templateModel,"innerType",t)},expression:"templateModel.innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.templateModel.isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"Bidding接入模式",prop:"packageName"}},[l("el-radio-group",{model:{value:e.templateModel.biddingType,callback:function(t){e.$set(e.templateModel,"biddingType",t)},expression:"templateModel.biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1)],1):e._e()],1)],1):e._e()],1),e._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.isShowAddThirdTemplateForm=!1}}},[e._v("取 消")]),e._v(" "),l("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addTemplate()}}},[e._v("确 定")])],1)],1),e._v(" "),l("el-dialog",{attrs:{title:"模板详情",visible:e.isShowThirdTemplateForm,fullscreen:"true"},on:{"update:visible":function(t){e.isShowThirdTemplateForm=t}}},[l("el-form",{ref:"thirdTemplateForm",attrs:{model:e.thirdTemplateForm,rules:e.templateRules}},[l("el-form-item",{attrs:{label:"模板名称",prop:"modelName"}},[l("el-input",{attrs:{disabled:""},model:{value:e.thirdTemplateForm.modelName,callback:function(t){e.$set(e.thirdTemplateForm,"modelName",t)},expression:"thirdTemplateForm.modelName"}})],1),e._v(" "),l("el-form-item",{attrs:{label:"穿山甲"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.thirdTemplateForm.csjTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称",disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adName,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adName",t)},expression:"thirdTemplateForm.csjTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adPlatform,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adPlatform",t)},expression:"thirdTemplateForm.csjTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adType,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adType",t)},expression:"thirdTemplateForm.csjTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价",disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adPrice,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adPrice",t)},expression:"thirdTemplateForm.csjTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("div",[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adTypeOwn",t)},expression:"thirdTemplateForm.csjTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(e.thirdTemplateForm.csjTemplateList[a].adPlatform,e.thirdTemplateForm.csjTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])])],1),e._v(" "),l("el-row",[2===e.thirdTemplateForm.csjTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置",disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adSite,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adSite",t)},expression:"thirdTemplateForm.csjTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.thirdTemplateForm.csjTemplateList[a].renderType,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"renderType",t)},expression:"thirdTemplateForm.csjTemplateList[i].renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),1===e.thirdTemplateForm.csjTemplateList[a].adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1,disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adTypeSub,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"adTypeSub",t)},expression:"thirdTemplateForm.csjTemplateList[i].adTypeSub"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.thirdTemplateForm.csjTemplateList[a].adPlatform?l("el-radio-group",{attrs:{size:"small",min:1,disabled:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].adTypeSub[0],callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a].adTypeSub,0,t)},expression:"thirdTemplateForm.csjTemplateList[i].adTypeSub[0]"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1):e._e(),e._v(" "),e.thirdTemplateForm.csjTemplateList[a].adType>=3&&1===e.thirdTemplateForm.csjTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.csjTemplateList[a].isBidding,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"isBidding",t)},expression:"thirdTemplateForm.csjTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.thirdTemplateForm.csjTemplateList[a].adType||5===e.thirdTemplateForm.csjTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.thirdTemplateForm.csjTemplateList[a].innerType,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"innerType",t)},expression:"thirdTemplateForm.csjTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.thirdTemplateForm.csjTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.csjTemplateList[a].biddingType,callback:function(t){e.$set(e.thirdTemplateForm.csjTemplateList[a],"biddingType",t)},expression:"thirdTemplateForm.csjTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"广点通"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.thirdTemplateForm.gdtTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adName,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adName",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adPlatform,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adPlatform",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adType,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adType",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adPrice,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adPrice",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("div",[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adTypeOwn",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(e.thirdTemplateForm.gdtTemplateList[a].adPlatform,e.thirdTemplateForm.gdtTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])])],1),e._v(" "),l("el-row",[2===e.thirdTemplateForm.gdtTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adSite,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adSite",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-radio-group",{attrs:{size:"mini"},model:{value:e.thirdTemplateForm.gdtTemplateList[a].renderType,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"renderType",t)},expression:"thirdTemplateForm.gdtTemplateList[i].renderType"}},[l("el-radio-button",{attrs:{label:"1"}},[e._v("模板渲染")]),e._v(" "),l("el-radio-button",{attrs:{label:"2"}},[e._v("自渲染")])],1),e._v(" "),1===e.thirdTemplateForm.gdtTemplateList[a].adPlatform?l("el-checkbox-group",{attrs:{size:"small",min:1,disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adTypeSub,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adTypeSub",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adTypeSub"}},e._l(e.csjAdSubTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.thirdTemplateForm.gdtTemplateList[a].adPlatform&&"2"===e.thirdTemplateForm.gdtTemplateList[a].renderType?l("el-checkbox-group",{attrs:{size:"small",min:1,disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adTypeSub,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"adTypeSub",t)},expression:"thirdTemplateForm.gdtTemplateList[i].adTypeSub"}},e._l(e.gdtAdSubOwnTypeList,function(t){return l("el-checkbox",{key:t,attrs:{label:t,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e(),e._v(" "),2===e.thirdTemplateForm.gdtTemplateList[a].adPlatform&&"1"===e.thirdTemplateForm.gdtTemplateList[a].renderType?l("el-radio-group",{attrs:{size:"small",disabled:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].adTypeSub[0],callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a].adTypeSub,0,t)},expression:"thirdTemplateForm.gdtTemplateList[i].adTypeSub[0]"}},e._l(e.gdtAdSubTypeList,function(t){return l("el-radio",{key:t,attrs:{label:t,min:1,border:""}},[e._v(e._s(t)+"\n                                    ")])}),1):e._e()],1):e._e(),e._v(" "),e.thirdTemplateForm.gdtTemplateList[a].adType>=3&&2===e.thirdTemplateForm.gdtTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.gdtTemplateList[a].isBidding,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"isBidding",t)},expression:"thirdTemplateForm.gdtTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.thirdTemplateForm.gdtTemplateList[a].adType||5===e.thirdTemplateForm.gdtTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.thirdTemplateForm.gdtTemplateList[a].innerType,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"innerType",t)},expression:"thirdTemplateForm.gdtTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.thirdTemplateForm.gdtTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.gdtTemplateList[a].biddingType,callback:function(t){e.$set(e.thirdTemplateForm.gdtTemplateList[a],"biddingType",t)},expression:"thirdTemplateForm.gdtTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"快手"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.thirdTemplateForm.ksTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称",disabled:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].adName,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"adName",t)},expression:"thirdTemplateForm.ksTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].adPlatform,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"adPlatform",t)},expression:"thirdTemplateForm.ksTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].adType,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"adType",t)},expression:"thirdTemplateForm.ksTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价",disabled:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].adPrice,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"adPrice",t)},expression:"thirdTemplateForm.ksTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",disabled:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"adTypeOwn",t)},expression:"thirdTemplateForm.ksTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(3,e.thirdTemplateForm.ksTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1),e._v(" "),l("el-row",[2===e.thirdTemplateForm.ksTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置",disabled:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].adSite,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"adSite",t)},expression:"thirdTemplateForm.ksTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.thirdTemplateForm.ksTemplateList[a].adType>=3&&3===e.thirdTemplateForm.ksTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.ksTemplateList[a].isBidding,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"isBidding",t)},expression:"thirdTemplateForm.ksTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.thirdTemplateForm.ksTemplateList[a].adType||5===e.thirdTemplateForm.ksTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.thirdTemplateForm.ksTemplateList[a].innerType,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"innerType",t)},expression:"thirdTemplateForm.ksTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.thirdTemplateForm.ksTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.ksTemplateList[a].biddingType,callback:function(t){e.$set(e.thirdTemplateForm.ksTemplateList[a],"biddingType",t)},expression:"thirdTemplateForm.ksTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"百度"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.thirdTemplateForm.bdTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称",disabled:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].adName,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"adName",t)},expression:"thirdTemplateForm.bdTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].adPlatform,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"adPlatform",t)},expression:"thirdTemplateForm.bdTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].adType,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"adType",t)},expression:"thirdTemplateForm.bdTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价",disabled:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].adPrice,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"adPrice",t)},expression:"thirdTemplateForm.bdTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",disabled:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"adTypeOwn",t)},expression:"thirdTemplateForm.bdTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(4,e.thirdTemplateForm.bdTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1),e._v(" "),l("el-row",[2===e.thirdTemplateForm.bdTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置",disabled:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].adSite,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"adSite",t)},expression:"thirdTemplateForm.bdTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.thirdTemplateForm.bdTemplateList[a].adType>=3&&4===e.thirdTemplateForm.bdTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.bdTemplateList[a].isBidding,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"isBidding",t)},expression:"thirdTemplateForm.bdTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1)],1),e._v(" "),3===e.thirdTemplateForm.bdTemplateList[a].adType||5===e.thirdTemplateForm.bdTemplateList[a].adType?l("div",[l("el-col",{attrs:{span:6}},[l("el-form-item",{attrs:{label:"插屏样式",size:"small"}},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"插屏样式",filterable:""},model:{value:e.thirdTemplateForm.bdTemplateList[a].innerType,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"innerType",t)},expression:"thirdTemplateForm.bdTemplateList[i].innerType"}},e._l(e.innerTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)],1)],1):e._e(),e._v(" "),1===e.thirdTemplateForm.bdTemplateList[a].isBidding?l("div",[l("el-col",{attrs:{span:6}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.bdTemplateList[a].biddingType,callback:function(t){e.$set(e.thirdTemplateForm.bdTemplateList[a],"biddingType",t)},expression:"thirdTemplateForm.bdTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1)],1):e._e()],1)],1):e._e()],1)],1)],1)}),0),e._v(" "),l("el-form-item",{attrs:{label:"OPPO"}}),e._v(" "),l("el-divider",[l("i",{staticClass:"el-icon-data-line"})]),e._v(" "),l("el-form-item",e._l(e.thirdTemplateForm.oppoTemplateList,function(t,a){return l("div",[l("el-form-item",{attrs:{size:"small"}},[l("el-row",{attrs:{gutter:24}},[l("el-col",{attrs:{span:4}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"广告位名称",disabled:""},model:{value:e.thirdTemplateForm.oppoTemplateList[a].adName,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"adName",t)},expression:"thirdTemplateForm.oppoTemplateList[i].adName"}})],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"平台",disabled:""},model:{value:e.thirdTemplateForm.oppoTemplateList[a].adPlatform,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"adPlatform",t)},expression:"thirdTemplateForm.oppoTemplateList[i].adPlatform"}},e._l(e.platformCodeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:3}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",disabled:""},model:{value:e.thirdTemplateForm.oppoTemplateList[a].adType,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"adType",t)},expression:"thirdTemplateForm.oppoTemplateList[i].adType"}},e._l(e.adTypeList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)]),e._v(" "),l("el-col",{attrs:{span:2}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-input",{attrs:{placeholder:"目标价",disabled:""},model:{value:e.thirdTemplateForm.oppoTemplateList[a].adPrice,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"adPrice",t)},expression:"thirdTemplateForm.oppoTemplateList[i].adPrice"}})],1)]),e._v(" "),l("el-col",{attrs:{span:10}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-200",attrs:{placeholder:"自家广告类型",disabled:""},model:{value:e.thirdTemplateForm.oppoTemplateList[a].adTypeOwn,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"adTypeOwn",t)},expression:"thirdTemplateForm.oppoTemplateList[i].adTypeOwn"}},e._l(e.getAdTypeDymicList(5,e.thirdTemplateForm.oppoTemplateList[a].adType),function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1),e._v(" "),l("el-row",[2===e.thirdTemplateForm.oppoTemplateList[a].adType?l("el-col",{attrs:{span:"24"}},[l("el-col",{attrs:{span:5}},[l("div",{staticClass:"grid-content bg-purple"},[l("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"广告位置",disabled:""},model:{value:e.thirdTemplateForm.oppoTemplateList[a].adSite,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"adSite",t)},expression:"thirdTemplateForm.oppoTemplateList[i].adSite"}},e._l(e.adSiteList,function(e){return l("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1)])],1):e._e(),e._v(" "),e.thirdTemplateForm.oppoTemplateList[a].adType>=3&&5===e.thirdTemplateForm.oppoTemplateList[a].adPlatform?l("el-col",{attrs:{span:"24"}},[l("el-radio-group",{model:{value:e.thirdTemplateForm.oppoTemplateList[a].isBidding,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"isBidding",t)},expression:"thirdTemplateForm.oppoTemplateList[i].isBidding"}},[l("el-radio",{attrs:{label:0}},[e._v("目标底价")]),e._v(" "),l("el-radio",{attrs:{label:1}},[e._v("实时BIDDING")])],1),e._v(" "),1===e.thirdTemplateForm.oppoTemplateList[a].isBidding?l("div",[l("el-radio-group",{model:{value:e.thirdTemplateForm.oppoTemplateList[a].biddingType,callback:function(t){e.$set(e.thirdTemplateForm.oppoTemplateList[a],"biddingType",t)},expression:"thirdTemplateForm.oppoTemplateList[i].biddingType"}},[l("el-radio",{attrs:{label:1}},[e._v("客户端Bidding")]),e._v(" "),l("el-radio",{attrs:{label:2}},[e._v("服务端Bidding")])],1)],1):e._e()],1):e._e()],1)],1)],1)}),0)],1),e._v(" "),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:function(t){e.isShowThirdTemplateForm=!1}}},[e._v("取 消")])],1)],1),e._v(" "),l("el-row",[l("el-col",{attrs:{span:24}},[l("el-form",{attrs:{model:e.queryParam,size:"small",inline:""}},[l("el-form-item",{attrs:{label:"模板名称"}},[l("el-input",{staticClass:"width-150",model:{value:e.queryParam.name,callback:function(t){e.$set(e.queryParam,"name",t)},expression:"queryParam.name"}})],1),e._v(" "),l("el-form-item",[l("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getTemplateList}})],1),e._v(" "),l("el-form-item",{staticStyle:{float:"right"}},[l("el-button",{attrs:{type:"primary",round:""},on:{click:function(t){return e.addTemplatePre()}}},[e._v("添加模板")])],1)],1)],1)],1),e._v(" "),l("el-divider",{attrs:{"content-position":"left"}},[e._v("模板列表")]),e._v(" "),l("el-row",[l("el-col",{attrs:{span:24}},[l("el-table",{staticStyle:{width:"100%"},attrs:{data:e.thirdTemplateList,stripe:"","highlight-current-row":""}},[l("el-table-column",{attrs:{prop:"id",label:"模板ID"}}),e._v(" "),l("el-table-column",{attrs:{prop:"modelName",label:"模板名称"}}),e._v(" "),l("el-table-column",{attrs:{label:"模板详情"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{plain:"",type:"text"},on:{click:function(l){return e.showDetail(t.row)}}},[e._v("详情")])]}}])}),e._v(" "),l("el-table-column",{attrs:{prop:"createTime",label:"创建时间"}}),e._v(" "),l("el-table-column",{attrs:{prop:"alreadyIncomeApp",label:"导入过的应用"}}),e._v(" "),l("el-table-column",{attrs:{label:"操作",width:"275",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{attrs:{plain:"",type:"primary",size:"mini"},on:{click:function(l){return e.updateTemplatePre(t.row)}}},[e._v("修改\n                        ")]),e._v(" "),l("el-button",{attrs:{plain:"",type:"info",size:"mini"},on:{click:function(l){return e.copyTemplate(t.row)}}},[e._v("复制")]),e._v(" "),l("el-button",{attrs:{plain:"",type:"danger",size:"mini"},on:{click:function(l){return e.deleteTemplate(t.row)}}},[e._v("删除")])]}}])})],1),e._v(" "),l("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryParam.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.listCountNum,background:""},on:{"current-change":e.handleCurrentChangePage}})],1)],1)],1)},staticRenderFns:[]}},399:function(e,t,l){var a=l(334);"string"==typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);l(88)("377b84b1",a,!0)}});