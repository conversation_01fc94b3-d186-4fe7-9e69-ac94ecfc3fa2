webpackJsonp([26],{223:function(e,t,o){var a=o(87)(o(320),o(388),null,null);e.exports=a.exports},320:function(e,t,o){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={data:function(){return{loading:!1,userTagList:[{id:0,name:"",comment:"",updateTime:"",tagInfo:{version:"",tailNumber:"",os:0,filterRegion:0,anonymous:0,userPackage:"",userChannel:""}}],editForm:{id:0,name:"",comment:"",tagInfo:{version:"",tailNumber:"",os:0,filterRegion:0,anonymous:0,userPackage:"",userChannel:""}},addForm:{name:"",comment:"",tagInfo:{version:"",tailNumber:"",os:0,filterRegion:0,anonymous:0,userPackage:"",userChannel:""}},queryParam:{name:"",comment:""},isShowEditForm:!1,isShowAddForm:!1,osList:[{value:0,label:"不限"},{value:1,label:"Android"},{value:2,label:"iOS"}],regionList:[{value:0,label:"不限"},{value:1,label:"受限区"},{value:2,label:"非受限区"}],anonymousList:[{value:0,label:"不限"},{value:1,label:"匿名用户"},{value:2,label:"非匿名用户"}],rules:{name:[{required:!0,message:"请输入用户标签名称",trigger:"blur"}],comment:[{required:!0,message:"请输入用户标签说明",trigger:"blur"}]}}},created:function(){this.getAllUserTag()},methods:{getAllUserTag:function(){var e=this;e.loading=!0,e.$axios.post("userTag/list").then(function(t){if(200===t.status){var o=t.data;0===o.ret?(e.userTagList=o.data,e.loading=!1):(e.loading=!1,e.$message.error(o.data))}else e.loading=!1,e.$message.error("加载失败！")})},updateUserTag:function(){var e=this;e.$refs.editForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("userTag/update",e.editForm).then(function(t){if(200===t.status){var o=t.data;0===o.ret?(e.isShowEditForm=!1,e.$message({message:"保存成功，将在2秒后刷新页面",type:"success"}),setTimeout(function(){e.getAllUserTag()},2e3)):e.$message.error(o.data)}else e.$message.error("修改配置失败")})})},addUserTag:function(){var e=this;e.$refs.addForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("userTag/add",e.addForm).then(function(t){if(200===t.status){var o=t.data;0===o.ret?(e.isShowAddForm=!1,e.$message({message:"保存成功，将在2秒后刷新页面",type:"success"}),setTimeout(function(){e.getAllUserTag()},2e3)):e.$message.error(o.message)}else e.$message.error("修改配置失败")})})},updateUserTagPre:function(e){this.editForm.id=e.id,this.editForm.name=e.name,this.editForm.comment=e.comment,this.editForm.tagInfo.version=e.tagInfo.version,this.editForm.tagInfo.tailNumber=e.tagInfo.tailNumber,this.editForm.tagInfo.os=e.tagInfo.os,this.editForm.tagInfo.filterRegion=e.tagInfo.filterRegion,this.editForm.tagInfo.anonymous=e.tagInfo.anonymous,this.editForm.tagInfo.userPackage=e.tagInfo.userPackage,this.editForm.tagInfo.userChannel=e.tagInfo.userChannel,this.isShowEditForm=!0},changeShowAddForm:function(){this.isShowAddForm=!0}}}},388:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"table"},[o("div",{staticClass:"crumbs"},[o("el-breadcrumb",{attrs:{separator:"/"}},[o("el-breadcrumb-item",[o("i",{staticClass:"el-icon-menu"}),e._v(" 任务中心")]),e._v(" "),o("el-breadcrumb-item",[e._v("用户标签管理")])],1),e._v(" "),o("br"),e._v(" "),o("el-form",{attrs:{model:e.queryParam,size:"small",inline:!0}},[o("el-form-item",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",icon:"small"},on:{click:e.changeShowAddForm}},[e._v("新建标签")])],1)],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"编辑用户标签",visible:e.isShowEditForm},on:{"update:visible":function(t){e.isShowEditForm=t}}},[o("el-form",{ref:"editForm",attrs:{model:e.editForm,"label-width":"200px",rules:e.rules}},[o("el-form-item",{attrs:{label:"名称",prop:"name"}},[o("el-input",{model:{value:e.editForm.name,callback:function(t){e.$set(e.editForm,"name",t)},expression:"editForm.name"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"说明",prop:"comment"}},[o("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20}},model:{value:e.editForm.comment,callback:function(t){e.$set(e.editForm,"comment",t)},expression:"editForm.comment"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"版本限制"}},[o("el-input",{model:{value:e.editForm.tagInfo.version,callback:function(t){e.$set(e.editForm.tagInfo,"version",t)},expression:"editForm.tagInfo.version"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"系统限制"}},[o("el-select",{attrs:{placeholder:"请选择"},model:{value:e.editForm.tagInfo.os,callback:function(t){e.$set(e.editForm.tagInfo,"os",t)},expression:"editForm.tagInfo.os"}},e._l(e.osList,function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowEditForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.updateUserTag()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-dialog",{attrs:{title:"新增用户标签",visible:e.isShowAddForm},on:{"update:visible":function(t){e.isShowAddForm=t}}},[o("el-form",{ref:"addForm",attrs:{model:e.addForm,"label-width":"200px",rules:e.rules}},[o("el-form-item",{attrs:{label:"名称",prop:"name"}},[o("el-input",{model:{value:e.addForm.name,callback:function(t){e.$set(e.addForm,"name",t)},expression:"addForm.name"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"说明",prop:"comment"}},[o("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20}},model:{value:e.addForm.comment,callback:function(t){e.$set(e.addForm,"comment",t)},expression:"addForm.comment"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"版本限制"}},[o("el-input",{model:{value:e.addForm.tagInfo.version,callback:function(t){e.$set(e.addForm.tagInfo,"version",t)},expression:"addForm.tagInfo.version"}})],1),e._v(" "),o("el-form-item",{attrs:{label:"系统限制"}},[o("el-select",{attrs:{placeholder:"请选择"},model:{value:e.addForm.tagInfo.os,callback:function(t){e.$set(e.addForm.tagInfo,"os",t)},expression:"addForm.tagInfo.os"}},e._l(e.osList,function(e){return o("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}),1)],1)],1),e._v(" "),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.isShowAddForm=!1}}},[e._v("取 消")]),e._v(" "),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addUserTag()}}},[e._v("确 定")])],1)],1),e._v(" "),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.userTagList,stripe:"",border:"","element-loading-text":"玩命加载中...","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[o("el-table-column",{attrs:{prop:"id",label:"ID",width:"200"}}),e._v(" "),o("el-table-column",{attrs:{prop:"name",label:"用户标签名称"}}),e._v(" "),o("el-table-column",{attrs:{prop:"comment",label:"说明"}}),e._v(" "),o("el-table-column",{attrs:{prop:"updateTime",label:"上一次修改日期",width:"220"}}),e._v(" "),o("el-table-column",{attrs:{label:"操作",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{size:"small"},on:{click:function(o){return e.updateUserTagPre(t.row)}}},[e._v("修改\n                        ")])]}}])})],1)],1)],1)],1)},staticRenderFns:[]}}});