webpackJsonp([3],{202:function(e,t,i){i(407);var n=i(87)(i(299),i(385),null,null);e.exports=n.exports},234:function(e,t,i){e.exports={default:i(235),__esModule:!0}},235:function(e,t,i){var n=i(16),o=n.JSON||(n.JSON={stringify:JSON.stringify});e.exports=function(e){return o.stringify.apply(o,arguments)}},253:function(e,t,i){var n,o;/*!
 * jQuery JavaScript Library v1.12.4
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-05-20T17:17Z
 */
!function(t,i){"object"==typeof e&&"object"==typeof e.exports?e.exports=t.document?i(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return i(e)}:i(t)}("undefined"!=typeof window?window:this,function(i,r){function a(e){var t=!!e&&"length"in e&&e.length,i=ge.type(e);return"function"!==i&&!ge.isWindow(e)&&("array"===i||0===t||"number"==typeof t&&t>0&&t-1 in e)}function s(e,t,i){if(ge.isFunction(t))return ge.grep(e,function(e,n){return!!t.call(e,n,e)!==i});if(t.nodeType)return ge.grep(e,function(e){return e===t!==i});if("string"==typeof t){if(_e.test(t))return ge.filter(t,e,i);t=ge.filter(t,e)}return ge.grep(e,function(e){return ge.inArray(e,t)>-1!==i})}function l(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}function d(e){var t={};return ge.each(e.match(Ee)||[],function(e,i){t[i]=!0}),t}function c(){se.addEventListener?(se.removeEventListener("DOMContentLoaded",u),i.removeEventListener("load",u)):(se.detachEvent("onreadystatechange",u),i.detachEvent("onload",u))}function u(){(se.addEventListener||"load"===i.event.type||"complete"===se.readyState)&&(c(),ge.ready())}function m(e,t,i){if(void 0===i&&1===e.nodeType){var n="data-"+t.replace(je,"-$1").toLowerCase();if("string"==typeof(i=e.getAttribute(n))){try{i="true"===i||"false"!==i&&("null"===i?null:+i+""===i?+i:$e.test(i)?ge.parseJSON(i):i)}catch(e){}ge.data(e,t,i)}else i=void 0}return i}function h(e){var t;for(t in e)if(("data"!==t||!ge.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function p(e,t,i,n){if(De(e)){var o,r,a=ge.expando,s=e.nodeType,l=s?ge.cache:e,d=s?e[a]:e[a]&&a;if(d&&l[d]&&(n||l[d].data)||void 0!==i||"string"!=typeof t)return d||(d=s?e[a]=ae.pop()||ge.guid++:a),l[d]||(l[d]=s?{}:{toJSON:ge.noop}),"object"!=typeof t&&"function"!=typeof t||(n?l[d]=ge.extend(l[d],t):l[d].data=ge.extend(l[d].data,t)),r=l[d],n||(r.data||(r.data={}),r=r.data),void 0!==i&&(r[ge.camelCase(t)]=i),"string"==typeof t?null==(o=r[t])&&(o=r[ge.camelCase(t)]):o=r,o}}function f(e,t,i){if(De(e)){var n,o,r=e.nodeType,a=r?ge.cache:e,s=r?e[ge.expando]:ge.expando;if(a[s]){if(t&&(n=i?a[s]:a[s].data)){ge.isArray(t)?t=t.concat(ge.map(t,ge.camelCase)):t in n?t=[t]:(t=ge.camelCase(t),t=t in n?[t]:t.split(" ")),o=t.length;for(;o--;)delete n[t[o]];if(i?!h(n):!ge.isEmptyObject(n))return}(i||(delete a[s].data,h(a[s])))&&(r?ge.cleanData([e],!0):fe.deleteExpando||a!=a.window?delete a[s]:a[s]=void 0)}}}function g(e,t,i,n){var o,r=1,a=20,s=n?function(){return n.cur()}:function(){return ge.css(e,t,"")},l=s(),d=i&&i[3]||(ge.cssNumber[t]?"":"px"),c=(ge.cssNumber[t]||"px"!==d&&+l)&&qe.exec(ge.css(e,t));if(c&&c[3]!==d){d=d||c[3],i=i||[],c=+l||1;do{r=r||".5",c/=r,ge.style(e,t,c+d)}while(r!==(r=s()/l)&&1!==r&&--a)}return i&&(c=+c||+l||0,o=i[1]?c+(i[1]+1)*i[2]:+i[2],n&&(n.unit=d,n.start=c,n.end=o)),o}function v(e){var t=Ve.split("|"),i=e.createDocumentFragment();if(i.createElement)for(;t.length;)i.createElement(t.pop());return i}function w(e,t){var i,n,o=0,r=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):void 0;if(!r)for(r=[],i=e.childNodes||e;null!=(n=i[o]);o++)!t||ge.nodeName(n,t)?r.push(n):ge.merge(r,w(n,t));return void 0===t||t&&ge.nodeName(e,t)?ge.merge([e],r):r}function b(e,t){for(var i,n=0;null!=(i=e[n]);n++)ge._data(i,"globalEval",!t||ge._data(t[n],"globalEval"))}function y(e){He.test(e.type)&&(e.defaultChecked=e.checked)}function x(e,t,i,n,o){for(var r,a,s,l,d,c,u,m=e.length,h=v(t),p=[],f=0;f<m;f++)if((a=e[f])||0===a)if("object"===ge.type(a))ge.merge(p,a.nodeType?[a]:a);else if(Je.test(a)){for(l=l||h.appendChild(t.createElement("div")),d=(Ue.exec(a)||["",""])[1].toLowerCase(),u=Xe[d]||Xe._default,l.innerHTML=u[1]+ge.htmlPrefilter(a)+u[2],r=u[0];r--;)l=l.lastChild;if(!fe.leadingWhitespace&&We.test(a)&&p.push(t.createTextNode(We.exec(a)[0])),!fe.tbody)for(a="table"!==d||Ge.test(a)?"<table>"!==u[1]||Ge.test(a)?0:l:l.firstChild,r=a&&a.childNodes.length;r--;)ge.nodeName(c=a.childNodes[r],"tbody")&&!c.childNodes.length&&a.removeChild(c);for(ge.merge(p,l.childNodes),l.textContent="";l.firstChild;)l.removeChild(l.firstChild);l=h.lastChild}else p.push(t.createTextNode(a));for(l&&h.removeChild(l),fe.appendChecked||ge.grep(w(p,"input"),y),f=0;a=p[f++];)if(n&&ge.inArray(a,n)>-1)o&&o.push(a);else if(s=ge.contains(a.ownerDocument,a),l=w(h.appendChild(a),"script"),s&&b(l),i)for(r=0;a=l[r++];)ze.test(a.type||"")&&i.push(a);return l=null,h}function A(){return!0}function F(){return!1}function k(){try{return se.activeElement}catch(e){}}function T(e,t,i,n,o,r){var a,s;if("object"==typeof t){"string"!=typeof i&&(n=n||i,i=void 0);for(s in t)T(e,s,i,n,t[s],r);return e}if(null==n&&null==o?(o=i,n=i=void 0):null==o&&("string"==typeof i?(o=n,n=void 0):(o=n,n=i,i=void 0)),!1===o)o=F;else if(!o)return e;return 1===r&&(a=o,o=function(e){return ge().off(e),a.apply(this,arguments)},o.guid=a.guid||(a.guid=ge.guid++)),e.each(function(){ge.event.add(this,t,o,n,i)})}function _(e,t){return ge.nodeName(e,"table")&&ge.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function C(e){return e.type=(null!==ge.find.attr(e,"type"))+"/"+e.type,e}function S(e){var t=at.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function P(e,t){if(1===t.nodeType&&ge.hasData(e)){var i,n,o,r=ge._data(e),a=ge._data(t,r),s=r.events;if(s){delete a.handle,a.events={};for(i in s)for(n=0,o=s[i].length;n<o;n++)ge.event.add(t,i,s[i][n])}a.data&&(a.data=ge.extend({},a.data))}}function L(e,t){var i,n,o;if(1===t.nodeType){if(i=t.nodeName.toLowerCase(),!fe.noCloneEvent&&t[ge.expando]){o=ge._data(t);for(n in o.events)ge.removeEvent(t,n,o.handle);t.removeAttribute(ge.expando)}"script"===i&&t.text!==e.text?(C(t).text=e.text,S(t)):"object"===i?(t.parentNode&&(t.outerHTML=e.outerHTML),fe.html5Clone&&e.innerHTML&&!ge.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===i&&He.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===i?t.defaultSelected=t.selected=e.defaultSelected:"input"!==i&&"textarea"!==i||(t.defaultValue=e.defaultValue)}}function E(e,t,i,n){t=de.apply([],t);var o,r,a,s,l,d,c=0,u=e.length,m=u-1,h=t[0],p=ge.isFunction(h);if(p||u>1&&"string"==typeof h&&!fe.checkClone&&rt.test(h))return e.each(function(o){var r=e.eq(o);p&&(t[0]=h.call(this,o,r.html())),E(r,t,i,n)});if(u&&(d=x(t,e[0].ownerDocument,!1,e,n),o=d.firstChild,1===d.childNodes.length&&(d=o),o||n)){for(s=ge.map(w(d,"script"),C),a=s.length;c<u;c++)r=d,c!==m&&(r=ge.clone(r,!0,!0),a&&ge.merge(s,w(r,"script"))),i.call(e[c],r,c);if(a)for(l=s[s.length-1].ownerDocument,ge.map(s,S),c=0;c<a;c++)r=s[c],ze.test(r.type||"")&&!ge._data(r,"globalEval")&&ge.contains(l,r)&&(r.src?ge._evalUrl&&ge._evalUrl(r.src):ge.globalEval((r.text||r.textContent||r.innerHTML||"").replace(st,"")));d=o=null}return e}function N(e,t,i){for(var n,o=t?ge.filter(t,e):e,r=0;null!=(n=o[r]);r++)i||1!==n.nodeType||ge.cleanData(w(n)),n.parentNode&&(i&&ge.contains(n.ownerDocument,n)&&b(w(n,"script")),n.parentNode.removeChild(n));return e}function I(e,t){var i=ge(t.createElement(e)).appendTo(t.body),n=ge.css(i[0],"display");return i.detach(),n}function D(e){var t=se,i=ut[e];return i||(i=I(e,t),"none"!==i&&i||(ct=(ct||ge("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(ct[0].contentWindow||ct[0].contentDocument).document,t.write(),t.close(),i=I(e,t),ct.detach()),ut[e]=i),i}function $(e,t){return{get:function(){return e()?void delete this.get:(this.get=t).apply(this,arguments)}}}function j(e){if(e in _t)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),i=Tt.length;i--;)if((e=Tt[i]+t)in _t)return e}function B(e,t){for(var i,n,o,r=[],a=0,s=e.length;a<s;a++)n=e[a],n.style&&(r[a]=ge._data(n,"olddisplay"),i=n.style.display,t?(r[a]||"none"!==i||(n.style.display=""),""===n.style.display&&Re(n)&&(r[a]=ge._data(n,"olddisplay",D(n.nodeName)))):(o=Re(n),(i&&"none"!==i||!o)&&ge._data(n,"olddisplay",o?i:ge.css(n,"display"))));for(a=0;a<s;a++)n=e[a],n.style&&(t&&"none"!==n.style.display&&""!==n.style.display||(n.style.display=t?r[a]||"":"none"));return e}function q(e,t,i){var n=At.exec(t);return n?Math.max(0,n[1]-(i||0))+(n[2]||"px"):t}function M(e,t,i,n,o){for(var r=i===(n?"border":"content")?4:"width"===t?1:0,a=0;r<4;r+=2)"margin"===i&&(a+=ge.css(e,i+Me[r],!0,o)),n?("content"===i&&(a-=ge.css(e,"padding"+Me[r],!0,o)),"margin"!==i&&(a-=ge.css(e,"border"+Me[r]+"Width",!0,o))):(a+=ge.css(e,"padding"+Me[r],!0,o),"padding"!==i&&(a+=ge.css(e,"border"+Me[r]+"Width",!0,o)));return a}function R(e,t,i){var n=!0,o="width"===t?e.offsetWidth:e.offsetHeight,r=gt(e),a=fe.boxSizing&&"border-box"===ge.css(e,"boxSizing",!1,r);if(o<=0||null==o){if(o=vt(e,t,r),(o<0||null==o)&&(o=e.style[t]),ht.test(o))return o;n=a&&(fe.boxSizingReliable()||o===e.style[t]),o=parseFloat(o)||0}return o+M(e,t,i||(a?"border":"content"),n,r)+"px"}function O(e,t,i,n,o){return new O.prototype.init(e,t,i,n,o)}function H(){return i.setTimeout(function(){Ct=void 0}),Ct=ge.now()}function U(e,t){var i,n={height:e},o=0;for(t=t?1:0;o<4;o+=2-t)i=Me[o],n["margin"+i]=n["padding"+i]=e;return t&&(n.opacity=n.width=e),n}function z(e,t,i){for(var n,o=(X.tweeners[t]||[]).concat(X.tweeners["*"]),r=0,a=o.length;r<a;r++)if(n=o[r].call(i,t,e))return n}function W(e,t,i){var n,o,r,a,s,l,d,c=this,u={},m=e.style,h=e.nodeType&&Re(e),p=ge._data(e,"fxshow");i.queue||(s=ge._queueHooks(e,"fx"),null==s.unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,c.always(function(){c.always(function(){s.unqueued--,ge.queue(e,"fx").length||s.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(i.overflow=[m.overflow,m.overflowX,m.overflowY],d=ge.css(e,"display"),"inline"===("none"===d?ge._data(e,"olddisplay")||D(e.nodeName):d)&&"none"===ge.css(e,"float")&&(fe.inlineBlockNeedsLayout&&"inline"!==D(e.nodeName)?m.zoom=1:m.display="inline-block")),i.overflow&&(m.overflow="hidden",fe.shrinkWrapBlocks()||c.always(function(){m.overflow=i.overflow[0],m.overflowX=i.overflow[1],m.overflowY=i.overflow[2]}));for(n in t)if(o=t[n],Pt.exec(o)){if(delete t[n],r=r||"toggle"===o,o===(h?"hide":"show")){if("show"!==o||!p||void 0===p[n])continue;h=!0}u[n]=p&&p[n]||ge.style(e,n)}else d=void 0;if(ge.isEmptyObject(u))"inline"===("none"===d?D(e.nodeName):d)&&(m.display=d);else{p?"hidden"in p&&(h=p.hidden):p=ge._data(e,"fxshow",{}),r&&(p.hidden=!h),h?ge(e).show():c.done(function(){ge(e).hide()}),c.done(function(){var t;ge._removeData(e,"fxshow");for(t in u)ge.style(e,t,u[t])});for(n in u)a=z(h?p[n]:0,n,c),n in p||(p[n]=a.start,h&&(a.end=a.start,a.start="width"===n||"height"===n?1:0))}}function V(e,t){var i,n,o,r,a;for(i in e)if(n=ge.camelCase(i),o=t[n],r=e[i],ge.isArray(r)&&(o=r[1],r=e[i]=r[0]),i!==n&&(e[n]=r,delete e[i]),(a=ge.cssHooks[n])&&"expand"in a){r=a.expand(r),delete e[n];for(i in r)i in e||(e[i]=r[i],t[i]=o)}else t[n]=o}function X(e,t,i){var n,o,r=0,a=X.prefilters.length,s=ge.Deferred().always(function(){delete l.elem}),l=function(){if(o)return!1;for(var t=Ct||H(),i=Math.max(0,d.startTime+d.duration-t),n=i/d.duration||0,r=1-n,a=0,l=d.tweens.length;a<l;a++)d.tweens[a].run(r);return s.notifyWith(e,[d,r,i]),r<1&&l?i:(s.resolveWith(e,[d]),!1)},d=s.promise({elem:e,props:ge.extend({},t),opts:ge.extend(!0,{specialEasing:{},easing:ge.easing._default},i),originalProperties:t,originalOptions:i,startTime:Ct||H(),duration:i.duration,tweens:[],createTween:function(t,i){var n=ge.Tween(e,d.opts,t,i,d.opts.specialEasing[t]||d.opts.easing);return d.tweens.push(n),n},stop:function(t){var i=0,n=t?d.tweens.length:0;if(o)return this;for(o=!0;i<n;i++)d.tweens[i].run(1);return t?(s.notifyWith(e,[d,1,0]),s.resolveWith(e,[d,t])):s.rejectWith(e,[d,t]),this}}),c=d.props;for(V(c,d.opts.specialEasing);r<a;r++)if(n=X.prefilters[r].call(d,e,c,d.opts))return ge.isFunction(n.stop)&&(ge._queueHooks(d.elem,d.opts.queue).stop=ge.proxy(n.stop,n)),n;return ge.map(c,z,d),ge.isFunction(d.opts.start)&&d.opts.start.call(e,d),ge.fx.timer(ge.extend(l,{elem:e,anim:d,queue:d.opts.queue})),d.progress(d.opts.progress).done(d.opts.done,d.opts.complete).fail(d.opts.fail).always(d.opts.always)}function J(e){return ge.attr(e,"class")||""}function G(e){return function(t,i){"string"!=typeof t&&(i=t,t="*");var n,o=0,r=t.toLowerCase().match(Ee)||[];if(ge.isFunction(i))for(;n=r[o++];)"+"===n.charAt(0)?(n=n.slice(1)||"*",(e[n]=e[n]||[]).unshift(i)):(e[n]=e[n]||[]).push(i)}}function Y(e,t,i,n){function o(s){var l;return r[s]=!0,ge.each(e[s]||[],function(e,s){var d=s(t,i,n);return"string"!=typeof d||a||r[d]?a?!(l=d):void 0:(t.dataTypes.unshift(d),o(d),!1)}),l}var r={},a=e===ei;return o(t.dataTypes[0])||!r["*"]&&o("*")}function K(e,t){var i,n,o=ge.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((o[n]?e:i||(i={}))[n]=t[n]);return i&&ge.extend(!0,e,i),e}function Q(e,t,i){for(var n,o,r,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===o&&(o=e.mimeType||t.getResponseHeader("Content-Type"));if(o)for(a in s)if(s[a]&&s[a].test(o)){l.unshift(a);break}if(l[0]in i)r=l[0];else{for(a in i){if(!l[0]||e.converters[a+" "+l[0]]){r=a;break}n||(n=a)}r=r||n}if(r)return r!==l[0]&&l.unshift(r),i[r]}function Z(e,t,i,n){var o,r,a,s,l,d={},c=e.dataTypes.slice();if(c[1])for(a in e.converters)d[a.toLowerCase()]=e.converters[a];for(r=c.shift();r;)if(e.responseFields[r]&&(i[e.responseFields[r]]=t),!l&&n&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=r,r=c.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(a=d[l+" "+r]||d["* "+r]))for(o in d)if(s=o.split(" "),s[1]===r&&(a=d[l+" "+s[0]]||d["* "+s[0]])){!0===a?a=d[o]:!0!==d[o]&&(r=s[0],c.unshift(s[1]));break}if(!0!==a)if(a&&e.throws)t=a(t);else try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+r}}}return{state:"success",data:t}}function ee(e){return e.style&&e.style.display||ge.css(e,"display")}function te(e){if(!ge.contains(e.ownerDocument||se,e))return!0;for(;e&&1===e.nodeType;){if("none"===ee(e)||"hidden"===e.type)return!0;e=e.parentNode}return!1}function ie(e,t,i,n){var o;if(ge.isArray(t))ge.each(t,function(t,o){i||ri.test(e)?n(e,o):ie(e+"["+("object"==typeof o&&null!=o?t:"")+"]",o,i,n)});else if(i||"object"!==ge.type(t))n(e,t);else for(o in t)ie(e+"["+o+"]",t[o],i,n)}function ne(){try{return new i.XMLHttpRequest}catch(e){}}function oe(){try{return new i.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}function re(e){return ge.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}var ae=[],se=i.document,le=ae.slice,de=ae.concat,ce=ae.push,ue=ae.indexOf,me={},he=me.toString,pe=me.hasOwnProperty,fe={},ge=function(e,t){return new ge.fn.init(e,t)},ve=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,we=/^-ms-/,be=/-([\da-z])/gi,ye=function(e,t){return t.toUpperCase()};ge.fn=ge.prototype={jquery:"1.12.4",constructor:ge,selector:"",length:0,toArray:function(){return le.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:le.call(this)},pushStack:function(e){var t=ge.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e){return ge.each(this,e)},map:function(e){return this.pushStack(ge.map(this,function(t,i){return e.call(t,i,t)}))},slice:function(){return this.pushStack(le.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,i=+e+(e<0?t:0);return this.pushStack(i>=0&&i<t?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:ce,sort:ae.sort,splice:ae.splice},ge.extend=ge.fn.extend=function(){var e,t,i,n,o,r,a=arguments[0]||{},s=1,l=arguments.length,d=!1;for("boolean"==typeof a&&(d=a,a=arguments[s]||{},s++),"object"==typeof a||ge.isFunction(a)||(a={}),s===l&&(a=this,s--);s<l;s++)if(null!=(o=arguments[s]))for(n in o)e=a[n],i=o[n],a!==i&&(d&&i&&(ge.isPlainObject(i)||(t=ge.isArray(i)))?(t?(t=!1,r=e&&ge.isArray(e)?e:[]):r=e&&ge.isPlainObject(e)?e:{},a[n]=ge.extend(d,r,i)):void 0!==i&&(a[n]=i));return a},ge.extend({expando:"jQuery"+("1.12.4"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===ge.type(e)},isArray:Array.isArray||function(e){return"array"===ge.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){var t=e&&e.toString();return!ge.isArray(e)&&t-parseFloat(t)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==ge.type(e)||e.nodeType||ge.isWindow(e))return!1;try{if(e.constructor&&!pe.call(e,"constructor")&&!pe.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}if(!fe.ownFirst)for(t in e)return pe.call(e,t);for(t in e);return void 0===t||pe.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?me[he.call(e)]||"object":typeof e},globalEval:function(e){e&&ge.trim(e)&&(i.execScript||function(e){i.eval.call(i,e)})(e)},camelCase:function(e){return e.replace(we,"ms-").replace(be,ye)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t){var i,n=0;if(a(e))for(i=e.length;n<i&&!1!==t.call(e[n],n,e[n]);n++);else for(n in e)if(!1===t.call(e[n],n,e[n]))break;return e},trim:function(e){return null==e?"":(e+"").replace(ve,"")},makeArray:function(e,t){var i=t||[];return null!=e&&(a(Object(e))?ge.merge(i,"string"==typeof e?[e]:e):ce.call(i,e)),i},inArray:function(e,t,i){var n;if(t){if(ue)return ue.call(t,e,i);for(n=t.length,i=i?i<0?Math.max(0,n+i):i:0;i<n;i++)if(i in t&&t[i]===e)return i}return-1},merge:function(e,t){for(var i=+t.length,n=0,o=e.length;n<i;)e[o++]=t[n++];if(i!==i)for(;void 0!==t[n];)e[o++]=t[n++];return e.length=o,e},grep:function(e,t,i){for(var n=[],o=0,r=e.length,a=!i;o<r;o++)!t(e[o],o)!==a&&n.push(e[o]);return n},map:function(e,t,i){var n,o,r=0,s=[];if(a(e))for(n=e.length;r<n;r++)null!=(o=t(e[r],r,i))&&s.push(o);else for(r in e)null!=(o=t(e[r],r,i))&&s.push(o);return de.apply([],s)},guid:1,proxy:function(e,t){var i,n,o;if("string"==typeof t&&(o=e[t],t=e,e=o),ge.isFunction(e))return i=le.call(arguments,2),n=function(){return e.apply(t||this,i.concat(le.call(arguments)))},n.guid=e.guid=e.guid||ge.guid++,n},now:function(){return+new Date},support:fe}),"function"==typeof Symbol&&(ge.fn[Symbol.iterator]=ae[Symbol.iterator]),ge.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){me["[object "+t+"]"]=t.toLowerCase()});var xe=/*!
 * Sizzle CSS Selector Engine v2.2.1
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2015-10-17
 */
function(e){function t(e,t,i,n){var o,r,a,s,d,u,m,h,p=t&&t.ownerDocument,f=t?t.nodeType:9;if(i=i||[],"string"!=typeof e||!e||1!==f&&9!==f&&11!==f)return i;if(!n&&((t?t.ownerDocument||t:q)!==L&&P(t),t=t||L,N)){if(11!==f&&(u=fe.exec(e)))if(o=u[1]){if(9===f){if(!(a=t.getElementById(o)))return i;if(a.id===o)return i.push(a),i}else if(p&&(a=p.getElementById(o))&&j(t,a)&&a.id===o)return i.push(a),i}else{if(u[2])return Y.apply(i,t.getElementsByTagName(e)),i;if((o=u[3])&&b.getElementsByClassName&&t.getElementsByClassName)return Y.apply(i,t.getElementsByClassName(o)),i}if(b.qsa&&!U[e+" "]&&(!I||!I.test(e))){if(1!==f)p=t,h=e;else if("object"!==t.nodeName.toLowerCase()){for((s=t.getAttribute("id"))?s=s.replace(ve,"\\$&"):t.setAttribute("id",s=B),m=F(e),r=m.length,d=ce.test(s)?"#"+s:"[id='"+s+"']";r--;)m[r]=d+" "+c(m[r]);h=m.join(","),p=ge.test(e)&&l(t.parentNode)||t}if(h)try{return Y.apply(i,p.querySelectorAll(h)),i}catch(e){}finally{s===B&&t.removeAttribute("id")}}}return T(e.replace(re,"$1"),t,i,n)}function i(){function e(i,n){return t.push(i+" ")>y.cacheLength&&delete e[t.shift()],e[i+" "]=n}var t=[];return e}function n(e){return e[B]=!0,e}function o(e){var t=L.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function r(e,t){for(var i=e.split("|"),n=i.length;n--;)y.attrHandle[i[n]]=t}function a(e,t){var i=t&&e,n=i&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||W)-(~e.sourceIndex||W);if(n)return n;if(i)for(;i=i.nextSibling;)if(i===t)return-1;return e?1:-1}function s(e){return n(function(t){return t=+t,n(function(i,n){for(var o,r=e([],i.length,t),a=r.length;a--;)i[o=r[a]]&&(i[o]=!(n[o]=i[o]))})})}function l(e){return e&&void 0!==e.getElementsByTagName&&e}function d(){}function c(e){for(var t=0,i=e.length,n="";t<i;t++)n+=e[t].value;return n}function u(e,t,i){var n=t.dir,o=i&&"parentNode"===n,r=R++;return t.first?function(t,i,r){for(;t=t[n];)if(1===t.nodeType||o)return e(t,i,r)}:function(t,i,a){var s,l,d,c=[M,r];if(a){for(;t=t[n];)if((1===t.nodeType||o)&&e(t,i,a))return!0}else for(;t=t[n];)if(1===t.nodeType||o){if(d=t[B]||(t[B]={}),l=d[t.uniqueID]||(d[t.uniqueID]={}),(s=l[n])&&s[0]===M&&s[1]===r)return c[2]=s[2];if(l[n]=c,c[2]=e(t,i,a))return!0}}}function m(e){return e.length>1?function(t,i,n){for(var o=e.length;o--;)if(!e[o](t,i,n))return!1;return!0}:e[0]}function h(e,i,n){for(var o=0,r=i.length;o<r;o++)t(e,i[o],n);return n}function p(e,t,i,n,o){for(var r,a=[],s=0,l=e.length,d=null!=t;s<l;s++)(r=e[s])&&(i&&!i(r,n,o)||(a.push(r),d&&t.push(s)));return a}function f(e,t,i,o,r,a){return o&&!o[B]&&(o=f(o)),r&&!r[B]&&(r=f(r,a)),n(function(n,a,s,l){var d,c,u,m=[],f=[],g=a.length,v=n||h(t||"*",s.nodeType?[s]:s,[]),w=!e||!n&&t?v:p(v,m,e,s,l),b=i?r||(n?e:g||o)?[]:a:w;if(i&&i(w,b,s,l),o)for(d=p(b,f),o(d,[],s,l),c=d.length;c--;)(u=d[c])&&(b[f[c]]=!(w[f[c]]=u));if(n){if(r||e){if(r){for(d=[],c=b.length;c--;)(u=b[c])&&d.push(w[c]=u);r(null,b=[],d,l)}for(c=b.length;c--;)(u=b[c])&&(d=r?Q(n,u):m[c])>-1&&(n[d]=!(a[d]=u))}}else b=p(b===a?b.splice(g,b.length):b),r?r(null,a,b,l):Y.apply(a,b)})}function g(e){for(var t,i,n,o=e.length,r=y.relative[e[0].type],a=r||y.relative[" "],s=r?1:0,l=u(function(e){return e===t},a,!0),d=u(function(e){return Q(t,e)>-1},a,!0),h=[function(e,i,n){var o=!r&&(n||i!==_)||((t=i).nodeType?l(e,i,n):d(e,i,n));return t=null,o}];s<o;s++)if(i=y.relative[e[s].type])h=[u(m(h),i)];else{if(i=y.filter[e[s].type].apply(null,e[s].matches),i[B]){for(n=++s;n<o&&!y.relative[e[n].type];n++);return f(s>1&&m(h),s>1&&c(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(re,"$1"),i,s<n&&g(e.slice(s,n)),n<o&&g(e=e.slice(n)),n<o&&c(e))}h.push(i)}return m(h)}function v(e,i){var o=i.length>0,r=e.length>0,a=function(n,a,s,l,d){var c,u,m,h=0,f="0",g=n&&[],v=[],w=_,b=n||r&&y.find.TAG("*",d),x=M+=null==w?1:Math.random()||.1,A=b.length;for(d&&(_=a===L||a||d);f!==A&&null!=(c=b[f]);f++){if(r&&c){for(u=0,a||c.ownerDocument===L||(P(c),s=!N);m=e[u++];)if(m(c,a||L,s)){l.push(c);break}d&&(M=x)}o&&((c=!m&&c)&&h--,n&&g.push(c))}if(h+=f,o&&f!==h){for(u=0;m=i[u++];)m(g,v,a,s);if(n){if(h>0)for(;f--;)g[f]||v[f]||(v[f]=J.call(l));v=p(v)}Y.apply(l,v),d&&!n&&v.length>0&&h+i.length>1&&t.uniqueSort(l)}return d&&(M=x,_=w),g};return o?n(a):a}var w,b,y,x,A,F,k,T,_,C,S,P,L,E,N,I,D,$,j,B="sizzle"+1*new Date,q=e.document,M=0,R=0,O=i(),H=i(),U=i(),z=function(e,t){return e===t&&(S=!0),0},W=1<<31,V={}.hasOwnProperty,X=[],J=X.pop,G=X.push,Y=X.push,K=X.slice,Q=function(e,t){for(var i=0,n=e.length;i<n;i++)if(e[i]===t)return i;return-1},Z="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ee="[\\x20\\t\\r\\n\\f]",te="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",ie="\\["+ee+"*("+te+")(?:"+ee+"*([*^$|!~]?=)"+ee+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+te+"))|)"+ee+"*\\]",ne=":("+te+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ie+")*)|.*)\\)|)",oe=new RegExp(ee+"+","g"),re=new RegExp("^"+ee+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ee+"+$","g"),ae=new RegExp("^"+ee+"*,"+ee+"*"),se=new RegExp("^"+ee+"*([>+~]|"+ee+")"+ee+"*"),le=new RegExp("="+ee+"*([^\\]'\"]*?)"+ee+"*\\]","g"),de=new RegExp(ne),ce=new RegExp("^"+te+"$"),ue={ID:new RegExp("^#("+te+")"),CLASS:new RegExp("^\\.("+te+")"),TAG:new RegExp("^("+te+"|[*])"),ATTR:new RegExp("^"+ie),PSEUDO:new RegExp("^"+ne),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ee+"*(even|odd|(([+-]|)(\\d*)n|)"+ee+"*(?:([+-]|)"+ee+"*(\\d+)|))"+ee+"*\\)|)","i"),bool:new RegExp("^(?:"+Z+")$","i"),needsContext:new RegExp("^"+ee+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ee+"*((?:-\\d)?\\d*)"+ee+"*\\)|)(?=[^-]|$)","i")},me=/^(?:input|select|textarea|button)$/i,he=/^h\d$/i,pe=/^[^{]+\{\s*\[native \w/,fe=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ge=/[+~]/,ve=/'|\\/g,we=new RegExp("\\\\([\\da-f]{1,6}"+ee+"?|("+ee+")|.)","ig"),be=function(e,t,i){var n="0x"+t-65536;return n!==n||i?t:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)},ye=function(){P()};try{Y.apply(X=K.call(q.childNodes),q.childNodes),X[q.childNodes.length].nodeType}catch(e){Y={apply:X.length?function(e,t){G.apply(e,K.call(t))}:function(e,t){for(var i=e.length,n=0;e[i++]=t[n++];);e.length=i-1}}}b=t.support={},A=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},P=t.setDocument=function(e){var t,i,n=e?e.ownerDocument||e:q;return n!==L&&9===n.nodeType&&n.documentElement?(L=n,E=L.documentElement,N=!A(L),(i=L.defaultView)&&i.top!==i&&(i.addEventListener?i.addEventListener("unload",ye,!1):i.attachEvent&&i.attachEvent("onunload",ye)),b.attributes=o(function(e){return e.className="i",!e.getAttribute("className")}),b.getElementsByTagName=o(function(e){return e.appendChild(L.createComment("")),!e.getElementsByTagName("*").length}),b.getElementsByClassName=pe.test(L.getElementsByClassName),b.getById=o(function(e){return E.appendChild(e).id=B,!L.getElementsByName||!L.getElementsByName(B).length}),b.getById?(y.find.ID=function(e,t){if(void 0!==t.getElementById&&N){var i=t.getElementById(e);return i?[i]:[]}},y.filter.ID=function(e){var t=e.replace(we,be);return function(e){return e.getAttribute("id")===t}}):(delete y.find.ID,y.filter.ID=function(e){var t=e.replace(we,be);return function(e){var i=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return i&&i.value===t}}),y.find.TAG=b.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):b.qsa?t.querySelectorAll(e):void 0}:function(e,t){var i,n=[],o=0,r=t.getElementsByTagName(e);if("*"===e){for(;i=r[o++];)1===i.nodeType&&n.push(i);return n}return r},y.find.CLASS=b.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&N)return t.getElementsByClassName(e)},D=[],I=[],(b.qsa=pe.test(L.querySelectorAll))&&(o(function(e){E.appendChild(e).innerHTML="<a id='"+B+"'></a><select id='"+B+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&I.push("[*^$]="+ee+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||I.push("\\["+ee+"*(?:value|"+Z+")"),e.querySelectorAll("[id~="+B+"-]").length||I.push("~="),e.querySelectorAll(":checked").length||I.push(":checked"),e.querySelectorAll("a#"+B+"+*").length||I.push(".#.+[+~]")}),o(function(e){var t=L.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&I.push("name"+ee+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||I.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),I.push(",.*:")})),(b.matchesSelector=pe.test($=E.matches||E.webkitMatchesSelector||E.mozMatchesSelector||E.oMatchesSelector||E.msMatchesSelector))&&o(function(e){b.disconnectedMatch=$.call(e,"div"),$.call(e,"[s!='']:x"),D.push("!=",ne)}),I=I.length&&new RegExp(I.join("|")),D=D.length&&new RegExp(D.join("|")),t=pe.test(E.compareDocumentPosition),j=t||pe.test(E.contains)?function(e,t){var i=9===e.nodeType?e.documentElement:e,n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(i.contains?i.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},z=t?function(e,t){if(e===t)return S=!0,0;var i=!e.compareDocumentPosition-!t.compareDocumentPosition;return i||(i=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&i||!b.sortDetached&&t.compareDocumentPosition(e)===i?e===L||e.ownerDocument===q&&j(q,e)?-1:t===L||t.ownerDocument===q&&j(q,t)?1:C?Q(C,e)-Q(C,t):0:4&i?-1:1)}:function(e,t){if(e===t)return S=!0,0;var i,n=0,o=e.parentNode,r=t.parentNode,s=[e],l=[t];if(!o||!r)return e===L?-1:t===L?1:o?-1:r?1:C?Q(C,e)-Q(C,t):0;if(o===r)return a(e,t);for(i=e;i=i.parentNode;)s.unshift(i);for(i=t;i=i.parentNode;)l.unshift(i);for(;s[n]===l[n];)n++;return n?a(s[n],l[n]):s[n]===q?-1:l[n]===q?1:0},L):L},t.matches=function(e,i){return t(e,null,null,i)},t.matchesSelector=function(e,i){if((e.ownerDocument||e)!==L&&P(e),i=i.replace(le,"='$1']"),b.matchesSelector&&N&&!U[i+" "]&&(!D||!D.test(i))&&(!I||!I.test(i)))try{var n=$.call(e,i);if(n||b.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return t(i,L,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==L&&P(e),j(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==L&&P(e);var i=y.attrHandle[t.toLowerCase()],n=i&&V.call(y.attrHandle,t.toLowerCase())?i(e,t,!N):void 0;return void 0!==n?n:b.attributes||!N?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,i=[],n=0,o=0;if(S=!b.detectDuplicates,C=!b.sortStable&&e.slice(0),e.sort(z),S){for(;t=e[o++];)t===e[o]&&(n=i.push(o));for(;n--;)e.splice(i[n],1)}return C=null,e},x=t.getText=function(e){var t,i="",n=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)i+=x(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[n++];)i+=x(t);return i},y=t.selectors={cacheLength:50,createPseudo:n,match:ue,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(we,be),e[3]=(e[3]||e[4]||e[5]||"").replace(we,be),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,i=!e[6]&&e[2];return ue.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":i&&de.test(i)&&(t=F(i,!0))&&(t=i.indexOf(")",i.length-t)-i.length)&&(e[0]=e[0].slice(0,t),e[2]=i.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(we,be).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=O[e+" "];return t||(t=new RegExp("(^|"+ee+")"+e+"("+ee+"|$)"))&&O(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,i,n){return function(o){var r=t.attr(o,e);return null==r?"!="===i:!i||(r+="","="===i?r===n:"!="===i?r!==n:"^="===i?n&&0===r.indexOf(n):"*="===i?n&&r.indexOf(n)>-1:"$="===i?n&&r.slice(-n.length)===n:"~="===i?(" "+r.replace(oe," ")+" ").indexOf(n)>-1:"|="===i&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,i,n,o){var r="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===n&&0===o?function(e){return!!e.parentNode}:function(t,i,l){var d,c,u,m,h,p,f=r!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),w=!l&&!s,b=!1;if(g){if(r){for(;f;){for(m=t;m=m[f];)if(s?m.nodeName.toLowerCase()===v:1===m.nodeType)return!1;p=f="only"===e&&!p&&"nextSibling"}return!0}if(p=[a?g.firstChild:g.lastChild],a&&w){for(m=g,u=m[B]||(m[B]={}),c=u[m.uniqueID]||(u[m.uniqueID]={}),d=c[e]||[],h=d[0]===M&&d[1],b=h&&d[2],m=h&&g.childNodes[h];m=++h&&m&&m[f]||(b=h=0)||p.pop();)if(1===m.nodeType&&++b&&m===t){c[e]=[M,h,b];break}}else if(w&&(m=t,u=m[B]||(m[B]={}),c=u[m.uniqueID]||(u[m.uniqueID]={}),d=c[e]||[],h=d[0]===M&&d[1],b=h),!1===b)for(;(m=++h&&m&&m[f]||(b=h=0)||p.pop())&&((s?m.nodeName.toLowerCase()!==v:1!==m.nodeType)||!++b||(w&&(u=m[B]||(m[B]={}),c=u[m.uniqueID]||(u[m.uniqueID]={}),c[e]=[M,b]),m!==t)););return(b-=o)===n||b%n==0&&b/n>=0}}},PSEUDO:function(e,i){var o,r=y.pseudos[e]||y.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return r[B]?r(i):r.length>1?(o=[e,e,"",i],y.setFilters.hasOwnProperty(e.toLowerCase())?n(function(e,t){for(var n,o=r(e,i),a=o.length;a--;)n=Q(e,o[a]),e[n]=!(t[n]=o[a])}):function(e){return r(e,0,o)}):r}},pseudos:{not:n(function(e){var t=[],i=[],o=k(e.replace(re,"$1"));return o[B]?n(function(e,t,i,n){for(var r,a=o(e,null,n,[]),s=e.length;s--;)(r=a[s])&&(e[s]=!(t[s]=r))}):function(e,n,r){return t[0]=e,o(t,null,r,i),t[0]=null,!i.pop()}}),has:n(function(e){return function(i){return t(e,i).length>0}}),contains:n(function(e){return e=e.replace(we,be),function(t){return(t.textContent||t.innerText||x(t)).indexOf(e)>-1}}),lang:n(function(e){return ce.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(we,be).toLowerCase(),function(t){var i;do{if(i=N?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(i=i.toLowerCase())===e||0===i.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var i=e.location&&e.location.hash;return i&&i.slice(1)===t.id},root:function(e){return e===E},focus:function(e){return e===L.activeElement&&(!L.hasFocus||L.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!y.pseudos.empty(e)},header:function(e){return he.test(e.nodeName)},input:function(e){return me.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:s(function(){return[0]}),last:s(function(e,t){return[t-1]}),eq:s(function(e,t,i){return[i<0?i+t:i]}),even:s(function(e,t){for(var i=0;i<t;i+=2)e.push(i);return e}),odd:s(function(e,t){for(var i=1;i<t;i+=2)e.push(i);return e}),lt:s(function(e,t,i){for(var n=i<0?i+t:i;--n>=0;)e.push(n);return e}),gt:s(function(e,t,i){for(var n=i<0?i+t:i;++n<t;)e.push(n);return e})}},y.pseudos.nth=y.pseudos.eq;for(w in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})y.pseudos[w]=function(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}(w);for(w in{submit:!0,reset:!0})y.pseudos[w]=function(e){return function(t){var i=t.nodeName.toLowerCase();return("input"===i||"button"===i)&&t.type===e}}(w);return d.prototype=y.filters=y.pseudos,y.setFilters=new d,F=t.tokenize=function(e,i){var n,o,r,a,s,l,d,c=H[e+" "];if(c)return i?0:c.slice(0);for(s=e,l=[],d=y.preFilter;s;){n&&!(o=ae.exec(s))||(o&&(s=s.slice(o[0].length)||s),l.push(r=[])),n=!1,(o=se.exec(s))&&(n=o.shift(),r.push({value:n,type:o[0].replace(re," ")}),s=s.slice(n.length));for(a in y.filter)!(o=ue[a].exec(s))||d[a]&&!(o=d[a](o))||(n=o.shift(),r.push({value:n,type:a,matches:o}),s=s.slice(n.length));if(!n)break}return i?s.length:s?t.error(e):H(e,l).slice(0)},k=t.compile=function(e,t){var i,n=[],o=[],r=U[e+" "];if(!r){for(t||(t=F(e)),i=t.length;i--;)r=g(t[i]),r[B]?n.push(r):o.push(r);r=U(e,v(o,n)),r.selector=e}return r},T=t.select=function(e,t,i,n){var o,r,a,s,d,u="function"==typeof e&&e,m=!n&&F(e=u.selector||e);if(i=i||[],1===m.length){if(r=m[0]=m[0].slice(0),r.length>2&&"ID"===(a=r[0]).type&&b.getById&&9===t.nodeType&&N&&y.relative[r[1].type]){if(!(t=(y.find.ID(a.matches[0].replace(we,be),t)||[])[0]))return i;u&&(t=t.parentNode),e=e.slice(r.shift().value.length)}for(o=ue.needsContext.test(e)?0:r.length;o--&&(a=r[o],!y.relative[s=a.type]);)if((d=y.find[s])&&(n=d(a.matches[0].replace(we,be),ge.test(r[0].type)&&l(t.parentNode)||t))){if(r.splice(o,1),!(e=n.length&&c(r)))return Y.apply(i,n),i;break}}return(u||k(e,m))(n,t,!N,i,!t||ge.test(e)&&l(t.parentNode)||t),i},b.sortStable=B.split("").sort(z).join("")===B,b.detectDuplicates=!!S,P(),b.sortDetached=o(function(e){return 1&e.compareDocumentPosition(L.createElement("div"))}),o(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||r("type|href|height|width",function(e,t,i){if(!i)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),b.attributes&&o(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||r("value",function(e,t,i){if(!i&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),o(function(e){return null==e.getAttribute("disabled")})||r(Z,function(e,t,i){var n;if(!i)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),t}(i);ge.find=xe,ge.expr=xe.selectors,ge.expr[":"]=ge.expr.pseudos,ge.uniqueSort=ge.unique=xe.uniqueSort,ge.text=xe.getText,ge.isXMLDoc=xe.isXML,ge.contains=xe.contains;var Ae=function(e,t,i){for(var n=[],o=void 0!==i;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&ge(e).is(i))break;n.push(e)}return n},Fe=function(e,t){for(var i=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&i.push(e);return i},ke=ge.expr.match.needsContext,Te=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,_e=/^.[^:#\[\.,]*$/;ge.filter=function(e,t,i){var n=t[0];return i&&(e=":not("+e+")"),1===t.length&&1===n.nodeType?ge.find.matchesSelector(n,e)?[n]:[]:ge.find.matches(e,ge.grep(t,function(e){return 1===e.nodeType}))},ge.fn.extend({find:function(e){var t,i=[],n=this,o=n.length;if("string"!=typeof e)return this.pushStack(ge(e).filter(function(){for(t=0;t<o;t++)if(ge.contains(n[t],this))return!0}));for(t=0;t<o;t++)ge.find(e,n[t],i);return i=this.pushStack(o>1?ge.unique(i):i),i.selector=this.selector?this.selector+" "+e:e,i},filter:function(e){return this.pushStack(s(this,e||[],!1))},not:function(e){return this.pushStack(s(this,e||[],!0))},is:function(e){return!!s(this,"string"==typeof e&&ke.test(e)?ge(e):e||[],!1).length}});var Ce,Se=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(ge.fn.init=function(e,t,i){var n,o;if(!e)return this;if(i=i||Ce,"string"==typeof e){if(!(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:Se.exec(e))||!n[1]&&t)return!t||t.jquery?(t||i).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof ge?t[0]:t,ge.merge(this,ge.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:se,!0)),Te.test(n[1])&&ge.isPlainObject(t))for(n in t)ge.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if((o=se.getElementById(n[2]))&&o.parentNode){if(o.id!==n[2])return Ce.find(e);this.length=1,this[0]=o}return this.context=se,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):ge.isFunction(e)?void 0!==i.ready?i.ready(e):e(ge):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),ge.makeArray(e,this))}).prototype=ge.fn,Ce=ge(se);var Pe=/^(?:parents|prev(?:Until|All))/,Le={children:!0,contents:!0,next:!0,prev:!0};ge.fn.extend({has:function(e){var t,i=ge(e,this),n=i.length;return this.filter(function(){for(t=0;t<n;t++)if(ge.contains(this,i[t]))return!0})},closest:function(e,t){for(var i,n=0,o=this.length,r=[],a=ke.test(e)||"string"!=typeof e?ge(e,t||this.context):0;n<o;n++)for(i=this[n];i&&i!==t;i=i.parentNode)if(i.nodeType<11&&(a?a.index(i)>-1:1===i.nodeType&&ge.find.matchesSelector(i,e))){r.push(i);break}return this.pushStack(r.length>1?ge.uniqueSort(r):r)},index:function(e){return e?"string"==typeof e?ge.inArray(this[0],ge(e)):ge.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(ge.uniqueSort(ge.merge(this.get(),ge(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),ge.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return Ae(e,"parentNode")},parentsUntil:function(e,t,i){return Ae(e,"parentNode",i)},next:function(e){return l(e,"nextSibling")},prev:function(e){return l(e,"previousSibling")},nextAll:function(e){return Ae(e,"nextSibling")},prevAll:function(e){return Ae(e,"previousSibling")},nextUntil:function(e,t,i){return Ae(e,"nextSibling",i)},prevUntil:function(e,t,i){return Ae(e,"previousSibling",i)},siblings:function(e){return Fe((e.parentNode||{}).firstChild,e)},children:function(e){return Fe(e.firstChild)},contents:function(e){return ge.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:ge.merge([],e.childNodes)}},function(e,t){ge.fn[e]=function(i,n){var o=ge.map(this,t,i);return"Until"!==e.slice(-5)&&(n=i),n&&"string"==typeof n&&(o=ge.filter(n,o)),this.length>1&&(Le[e]||(o=ge.uniqueSort(o)),Pe.test(e)&&(o=o.reverse())),this.pushStack(o)}});var Ee=/\S+/g;ge.Callbacks=function(e){e="string"==typeof e?d(e):ge.extend({},e);var t,i,n,o,r=[],a=[],s=-1,l=function(){for(o=e.once,n=t=!0;a.length;s=-1)for(i=a.shift();++s<r.length;)!1===r[s].apply(i[0],i[1])&&e.stopOnFalse&&(s=r.length,i=!1);e.memory||(i=!1),t=!1,o&&(r=i?[]:"")},c={add:function(){return r&&(i&&!t&&(s=r.length-1,a.push(i)),function t(i){ge.each(i,function(i,n){ge.isFunction(n)?e.unique&&c.has(n)||r.push(n):n&&n.length&&"string"!==ge.type(n)&&t(n)})}(arguments),i&&!t&&l()),this},remove:function(){return ge.each(arguments,function(e,t){for(var i;(i=ge.inArray(t,r,i))>-1;)r.splice(i,1),i<=s&&s--}),this},has:function(e){return e?ge.inArray(e,r)>-1:r.length>0},empty:function(){return r&&(r=[]),this},disable:function(){return o=a=[],r=i="",this},disabled:function(){return!r},lock:function(){return o=!0,i||c.disable(),this},locked:function(){return!!o},fireWith:function(e,i){return o||(i=i||[],i=[e,i.slice?i.slice():i],a.push(i),t||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!n}};return c},ge.extend({Deferred:function(e){var t=[["resolve","done",ge.Callbacks("once memory"),"resolved"],["reject","fail",ge.Callbacks("once memory"),"rejected"],["notify","progress",ge.Callbacks("memory")]],i="pending",n={state:function(){return i},always:function(){return o.done(arguments).fail(arguments),this},then:function(){var e=arguments;return ge.Deferred(function(i){ge.each(t,function(t,r){var a=ge.isFunction(e[t])&&e[t];o[r[1]](function(){var e=a&&a.apply(this,arguments);e&&ge.isFunction(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[r[0]+"With"](this===n?i.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?ge.extend(e,n):n}},o={};return n.pipe=n.then,ge.each(t,function(e,r){var a=r[2],s=r[3];n[r[1]]=a.add,s&&a.add(function(){i=s},t[1^e][2].disable,t[2][2].lock),o[r[0]]=function(){return o[r[0]+"With"](this===o?n:this,arguments),this},o[r[0]+"With"]=a.fireWith}),n.promise(o),e&&e.call(o,o),o},when:function(e){var t,i,n,o=0,r=le.call(arguments),a=r.length,s=1!==a||e&&ge.isFunction(e.promise)?a:0,l=1===s?e:ge.Deferred(),d=function(e,i,n){return function(o){i[e]=this,n[e]=arguments.length>1?le.call(arguments):o,n===t?l.notifyWith(i,n):--s||l.resolveWith(i,n)}};if(a>1)for(t=new Array(a),i=new Array(a),n=new Array(a);o<a;o++)r[o]&&ge.isFunction(r[o].promise)?r[o].promise().progress(d(o,i,t)).done(d(o,n,r)).fail(l.reject):--s;return s||l.resolveWith(n,r),l.promise()}});var Ne;ge.fn.ready=function(e){return ge.ready.promise().done(e),this},ge.extend({isReady:!1,readyWait:1,holdReady:function(e){e?ge.readyWait++:ge.ready(!0)},ready:function(e){(!0===e?--ge.readyWait:ge.isReady)||(ge.isReady=!0,!0!==e&&--ge.readyWait>0||(Ne.resolveWith(se,[ge]),ge.fn.triggerHandler&&(ge(se).triggerHandler("ready"),ge(se).off("ready"))))}}),ge.ready.promise=function(e){if(!Ne)if(Ne=ge.Deferred(),"complete"===se.readyState||"loading"!==se.readyState&&!se.documentElement.doScroll)i.setTimeout(ge.ready);else if(se.addEventListener)se.addEventListener("DOMContentLoaded",u),i.addEventListener("load",u);else{se.attachEvent("onreadystatechange",u),i.attachEvent("onload",u);var t=!1;try{t=null==i.frameElement&&se.documentElement}catch(e){}t&&t.doScroll&&function e(){if(!ge.isReady){try{t.doScroll("left")}catch(t){return i.setTimeout(e,50)}c(),ge.ready()}}()}return Ne.promise(e)},ge.ready.promise();var Ie;for(Ie in ge(fe))break;fe.ownFirst="0"===Ie,fe.inlineBlockNeedsLayout=!1,ge(function(){var e,t,i,n;(i=se.getElementsByTagName("body")[0])&&i.style&&(t=se.createElement("div"),n=se.createElement("div"),n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",i.appendChild(n).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",fe.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(i.style.zoom=1)),i.removeChild(n))}),function(){var e=se.createElement("div");fe.deleteExpando=!0;try{delete e.test}catch(e){fe.deleteExpando=!1}e=null}();var De=function(e){var t=ge.noData[(e.nodeName+" ").toLowerCase()],i=+e.nodeType||1;return(1===i||9===i)&&(!t||!0!==t&&e.getAttribute("classid")===t)},$e=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,je=/([A-Z])/g;ge.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return!!(e=e.nodeType?ge.cache[e[ge.expando]]:e[ge.expando])&&!h(e)},data:function(e,t,i){return p(e,t,i)},removeData:function(e,t){return f(e,t)},_data:function(e,t,i){return p(e,t,i,!0)},_removeData:function(e,t){return f(e,t,!0)}}),ge.fn.extend({data:function(e,t){var i,n,o,r=this[0],a=r&&r.attributes;if(void 0===e){if(this.length&&(o=ge.data(r),1===r.nodeType&&!ge._data(r,"parsedAttrs"))){for(i=a.length;i--;)a[i]&&(n=a[i].name,0===n.indexOf("data-")&&(n=ge.camelCase(n.slice(5)),m(r,n,o[n])));ge._data(r,"parsedAttrs",!0)}return o}return"object"==typeof e?this.each(function(){ge.data(this,e)}):arguments.length>1?this.each(function(){ge.data(this,e,t)}):r?m(r,e,ge.data(r,e)):void 0},removeData:function(e){return this.each(function(){ge.removeData(this,e)})}}),ge.extend({queue:function(e,t,i){var n;if(e)return t=(t||"fx")+"queue",n=ge._data(e,t),i&&(!n||ge.isArray(i)?n=ge._data(e,t,ge.makeArray(i)):n.push(i)),n||[]},dequeue:function(e,t){t=t||"fx";var i=ge.queue(e,t),n=i.length,o=i.shift(),r=ge._queueHooks(e,t),a=function(){ge.dequeue(e,t)};"inprogress"===o&&(o=i.shift(),n--),o&&("fx"===t&&i.unshift("inprogress"),delete r.stop,o.call(e,a,r)),!n&&r&&r.empty.fire()},_queueHooks:function(e,t){var i=t+"queueHooks";return ge._data(e,i)||ge._data(e,i,{empty:ge.Callbacks("once memory").add(function(){ge._removeData(e,t+"queue"),ge._removeData(e,i)})})}}),ge.fn.extend({queue:function(e,t){var i=2;return"string"!=typeof e&&(t=e,e="fx",i--),arguments.length<i?ge.queue(this[0],e):void 0===t?this:this.each(function(){var i=ge.queue(this,e,t);ge._queueHooks(this,e),"fx"===e&&"inprogress"!==i[0]&&ge.dequeue(this,e)})},dequeue:function(e){return this.each(function(){ge.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var i,n=1,o=ge.Deferred(),r=this,a=this.length,s=function(){--n||o.resolveWith(r,[r])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(i=ge._data(r[a],e+"queueHooks"))&&i.empty&&(n++,i.empty.add(s));return s(),o.promise(t)}}),function(){var e;fe.shrinkWrapBlocks=function(){if(null!=e)return e;e=!1;var t,i,n;return(i=se.getElementsByTagName("body")[0])&&i.style?(t=se.createElement("div"),n=se.createElement("div"),n.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",i.appendChild(n).appendChild(t),void 0!==t.style.zoom&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(se.createElement("div")).style.width="5px",e=3!==t.offsetWidth),i.removeChild(n),e):void 0}}();var Be=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,qe=new RegExp("^(?:([+-])=|)("+Be+")([a-z%]*)$","i"),Me=["Top","Right","Bottom","Left"],Re=function(e,t){return e=t||e,"none"===ge.css(e,"display")||!ge.contains(e.ownerDocument,e)},Oe=function(e,t,i,n,o,r,a){var s=0,l=e.length,d=null==i;if("object"===ge.type(i)){o=!0;for(s in i)Oe(e,t,s,i[s],!0,r,a)}else if(void 0!==n&&(o=!0,ge.isFunction(n)||(a=!0),d&&(a?(t.call(e,n),t=null):(d=t,t=function(e,t,i){return d.call(ge(e),i)})),t))for(;s<l;s++)t(e[s],i,a?n:n.call(e[s],s,t(e[s],i)));return o?e:d?t.call(e):l?t(e[0],i):r},He=/^(?:checkbox|radio)$/i,Ue=/<([\w:-]+)/,ze=/^$|\/(?:java|ecma)script/i,We=/^\s+/,Ve="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";!function(){var e=se.createElement("div"),t=se.createDocumentFragment(),i=se.createElement("input");e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",fe.leadingWhitespace=3===e.firstChild.nodeType,fe.tbody=!e.getElementsByTagName("tbody").length,fe.htmlSerialize=!!e.getElementsByTagName("link").length,fe.html5Clone="<:nav></:nav>"!==se.createElement("nav").cloneNode(!0).outerHTML,i.type="checkbox",i.checked=!0,t.appendChild(i),fe.appendChecked=i.checked,e.innerHTML="<textarea>x</textarea>",fe.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,t.appendChild(e),i=se.createElement("input"),i.setAttribute("type","radio"),i.setAttribute("checked","checked"),i.setAttribute("name","t"),e.appendChild(i),fe.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,fe.noCloneEvent=!!e.addEventListener,e[ge.expando]=1,fe.attributes=!e.getAttribute(ge.expando)}();var Xe={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:fe.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};Xe.optgroup=Xe.option,Xe.tbody=Xe.tfoot=Xe.colgroup=Xe.caption=Xe.thead,Xe.th=Xe.td;var Je=/<|&#?\w+;/,Ge=/<tbody/i;!function(){var e,t,n=se.createElement("div");for(e in{submit:!0,change:!0,focusin:!0})t="on"+e,(fe[e]=t in i)||(n.setAttribute(t,"t"),fe[e]=!1===n.attributes[t].expando);n=null}();var Ye=/^(?:input|select|textarea)$/i,Ke=/^key/,Qe=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ze=/^(?:focusinfocus|focusoutblur)$/,et=/^([^.]*)(?:\.(.+)|)/;ge.event={global:{},add:function(e,t,i,n,o){var r,a,s,l,d,c,u,m,h,p,f,g=ge._data(e);if(g){for(i.handler&&(l=i,i=l.handler,o=l.selector),i.guid||(i.guid=ge.guid++),(a=g.events)||(a=g.events={}),(c=g.handle)||(c=g.handle=function(e){return void 0===ge||e&&ge.event.triggered===e.type?void 0:ge.event.dispatch.apply(c.elem,arguments)},c.elem=e),t=(t||"").match(Ee)||[""],s=t.length;s--;)r=et.exec(t[s])||[],h=f=r[1],p=(r[2]||"").split(".").sort(),h&&(d=ge.event.special[h]||{},h=(o?d.delegateType:d.bindType)||h,d=ge.event.special[h]||{},u=ge.extend({type:h,origType:f,data:n,handler:i,guid:i.guid,selector:o,needsContext:o&&ge.expr.match.needsContext.test(o),namespace:p.join(".")},l),(m=a[h])||(m=a[h]=[],m.delegateCount=0,d.setup&&!1!==d.setup.call(e,n,p,c)||(e.addEventListener?e.addEventListener(h,c,!1):e.attachEvent&&e.attachEvent("on"+h,c))),d.add&&(d.add.call(e,u),u.handler.guid||(u.handler.guid=i.guid)),o?m.splice(m.delegateCount++,0,u):m.push(u),ge.event.global[h]=!0);e=null}},remove:function(e,t,i,n,o){var r,a,s,l,d,c,u,m,h,p,f,g=ge.hasData(e)&&ge._data(e);if(g&&(c=g.events)){for(t=(t||"").match(Ee)||[""],d=t.length;d--;)if(s=et.exec(t[d])||[],h=f=s[1],p=(s[2]||"").split(".").sort(),h){for(u=ge.event.special[h]||{},h=(n?u.delegateType:u.bindType)||h,m=c[h]||[],s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=r=m.length;r--;)a=m[r],!o&&f!==a.origType||i&&i.guid!==a.guid||s&&!s.test(a.namespace)||n&&n!==a.selector&&("**"!==n||!a.selector)||(m.splice(r,1),a.selector&&m.delegateCount--,u.remove&&u.remove.call(e,a));l&&!m.length&&(u.teardown&&!1!==u.teardown.call(e,p,g.handle)||ge.removeEvent(e,h,g.handle),delete c[h])}else for(h in c)ge.event.remove(e,h+t[d],i,n,!0);ge.isEmptyObject(c)&&(delete g.handle,ge._removeData(e,"events"))}},trigger:function(e,t,n,o){var r,a,s,l,d,c,u,m=[n||se],h=pe.call(e,"type")?e.type:e,p=pe.call(e,"namespace")?e.namespace.split("."):[];if(s=c=n=n||se,3!==n.nodeType&&8!==n.nodeType&&!Ze.test(h+ge.event.triggered)&&(h.indexOf(".")>-1&&(p=h.split("."),h=p.shift(),p.sort()),a=h.indexOf(":")<0&&"on"+h,e=e[ge.expando]?e:new ge.Event(h,"object"==typeof e&&e),e.isTrigger=o?2:3,e.namespace=p.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:ge.makeArray(t,[e]),d=ge.event.special[h]||{},o||!d.trigger||!1!==d.trigger.apply(n,t))){if(!o&&!d.noBubble&&!ge.isWindow(n)){for(l=d.delegateType||h,Ze.test(l+h)||(s=s.parentNode);s;s=s.parentNode)m.push(s),c=s;c===(n.ownerDocument||se)&&m.push(c.defaultView||c.parentWindow||i)}for(u=0;(s=m[u++])&&!e.isPropagationStopped();)e.type=u>1?l:d.bindType||h,r=(ge._data(s,"events")||{})[e.type]&&ge._data(s,"handle"),r&&r.apply(s,t),(r=a&&s[a])&&r.apply&&De(s)&&(e.result=r.apply(s,t),!1===e.result&&e.preventDefault());if(e.type=h,!o&&!e.isDefaultPrevented()&&(!d._default||!1===d._default.apply(m.pop(),t))&&De(n)&&a&&n[h]&&!ge.isWindow(n)){c=n[a],c&&(n[a]=null),ge.event.triggered=h;try{n[h]()}catch(e){}ge.event.triggered=void 0,c&&(n[a]=c)}return e.result}},dispatch:function(e){e=ge.event.fix(e);var t,i,n,o,r,a=[],s=le.call(arguments),l=(ge._data(this,"events")||{})[e.type]||[],d=ge.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!d.preDispatch||!1!==d.preDispatch.call(this,e)){for(a=ge.event.handlers.call(this,e,l),t=0;(o=a[t++])&&!e.isPropagationStopped();)for(e.currentTarget=o.elem,i=0;(r=o.handlers[i++])&&!e.isImmediatePropagationStopped();)e.rnamespace&&!e.rnamespace.test(r.namespace)||(e.handleObj=r,e.data=r.data,void 0!==(n=((ge.event.special[r.origType]||{}).handle||r.handler).apply(o.elem,s))&&!1===(e.result=n)&&(e.preventDefault(),e.stopPropagation()));return d.postDispatch&&d.postDispatch.call(this,e),e.result}},handlers:function(e,t){var i,n,o,r,a=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&("click"!==e.type||isNaN(e.button)||e.button<1))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(!0!==l.disabled||"click"!==e.type)){for(n=[],i=0;i<s;i++)r=t[i],o=r.selector+" ",void 0===n[o]&&(n[o]=r.needsContext?ge(o,this).index(l)>-1:ge.find(o,this,null,[l]).length),n[o]&&n.push(r);n.length&&a.push({elem:l,handlers:n})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[ge.expando])return e;var t,i,n,o=e.type,r=e,a=this.fixHooks[o];for(a||(this.fixHooks[o]=a=Qe.test(o)?this.mouseHooks:Ke.test(o)?this.keyHooks:{}),n=a.props?this.props.concat(a.props):this.props,e=new ge.Event(r),t=n.length;t--;)i=n[t],e[i]=r[i];return e.target||(e.target=r.srcElement||se),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,r):e},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var i,n,o,r=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(n=e.target.ownerDocument||se,o=n.documentElement,i=n.body,e.pageX=t.clientX+(o&&o.scrollLeft||i&&i.scrollLeft||0)-(o&&o.clientLeft||i&&i.clientLeft||0),e.pageY=t.clientY+(o&&o.scrollTop||i&&i.scrollTop||0)-(o&&o.clientTop||i&&i.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===r||(e.which=1&r?1:2&r?3:4&r?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==k()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){if(this===k()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if(ge.nodeName(this,"input")&&"checkbox"===this.type&&this.click)return this.click(),!1},_default:function(e){return ge.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,i){var n=ge.extend(new ge.Event,i,{type:e,isSimulated:!0});ge.event.trigger(n,null,t),n.isDefaultPrevented()&&i.preventDefault()}},ge.removeEvent=se.removeEventListener?function(e,t,i){e.removeEventListener&&e.removeEventListener(t,i)}:function(e,t,i){var n="on"+t;e.detachEvent&&(void 0===e[n]&&(e[n]=null),e.detachEvent(n,i))},ge.Event=function(e,t){if(!(this instanceof ge.Event))return new ge.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?A:F):this.type=e,t&&ge.extend(this,t),this.timeStamp=e&&e.timeStamp||ge.now(),this[ge.expando]=!0},ge.Event.prototype={constructor:ge.Event,isDefaultPrevented:F,isPropagationStopped:F,isImmediatePropagationStopped:F,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=A,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=A,e&&!this.isSimulated&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=A,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},ge.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){ge.event.special[e]={delegateType:t,bindType:t,handle:function(e){var i,n=this,o=e.relatedTarget,r=e.handleObj;return o&&(o===n||ge.contains(n,o))||(e.type=r.origType,i=r.handler.apply(this,arguments),e.type=t),i}}}),fe.submit||(ge.event.special.submit={setup:function(){if(ge.nodeName(this,"form"))return!1;ge.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,i=ge.nodeName(t,"input")||ge.nodeName(t,"button")?ge.prop(t,"form"):void 0;i&&!ge._data(i,"submit")&&(ge.event.add(i,"submit._submit",function(e){e._submitBubble=!0}),ge._data(i,"submit",!0))})},postDispatch:function(e){e._submitBubble&&(delete e._submitBubble,this.parentNode&&!e.isTrigger&&ge.event.simulate("submit",this.parentNode,e))},teardown:function(){if(ge.nodeName(this,"form"))return!1;ge.event.remove(this,"._submit")}}),fe.change||(ge.event.special.change={setup:function(){if(Ye.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(ge.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._justChanged=!0)}),ge.event.add(this,"click._change",function(e){this._justChanged&&!e.isTrigger&&(this._justChanged=!1),ge.event.simulate("change",this,e)})),!1;ge.event.add(this,"beforeactivate._change",function(e){var t=e.target;Ye.test(t.nodeName)&&!ge._data(t,"change")&&(ge.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||ge.event.simulate("change",this.parentNode,e)}),ge._data(t,"change",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return ge.event.remove(this,"._change"),!Ye.test(this.nodeName)}}),fe.focusin||ge.each({focus:"focusin",blur:"focusout"},function(e,t){var i=function(e){ge.event.simulate(t,e.target,ge.event.fix(e))};ge.event.special[t]={setup:function(){var n=this.ownerDocument||this,o=ge._data(n,t);o||n.addEventListener(e,i,!0),ge._data(n,t,(o||0)+1)},teardown:function(){var n=this.ownerDocument||this,o=ge._data(n,t)-1;o?ge._data(n,t,o):(n.removeEventListener(e,i,!0),ge._removeData(n,t))}}}),ge.fn.extend({on:function(e,t,i,n){return T(this,e,t,i,n)},one:function(e,t,i,n){return T(this,e,t,i,n,1)},off:function(e,t,i){var n,o;if(e&&e.preventDefault&&e.handleObj)return n=e.handleObj,ge(e.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof e){for(o in e)this.off(o,t,e[o]);return this}return!1!==t&&"function"!=typeof t||(i=t,t=void 0),!1===i&&(i=F),this.each(function(){ge.event.remove(this,e,i,t)})},trigger:function(e,t){return this.each(function(){ge.event.trigger(e,t,this)})},triggerHandler:function(e,t){var i=this[0];if(i)return ge.event.trigger(e,t,i,!0)}});var tt=/ jQuery\d+="(?:null|\d+)"/g,it=new RegExp("<(?:"+Ve+")[\\s/>]","i"),nt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,ot=/<script|<style|<link/i,rt=/checked\s*(?:[^=]|=\s*.checked.)/i,at=/^true\/(.*)/,st=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,lt=v(se),dt=lt.appendChild(se.createElement("div"));ge.extend({htmlPrefilter:function(e){return e.replace(nt,"<$1></$2>")},clone:function(e,t,i){var n,o,r,a,s,l=ge.contains(e.ownerDocument,e);if(fe.html5Clone||ge.isXMLDoc(e)||!it.test("<"+e.nodeName+">")?r=e.cloneNode(!0):(dt.innerHTML=e.outerHTML,dt.removeChild(r=dt.firstChild)),!(fe.noCloneEvent&&fe.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||ge.isXMLDoc(e)))for(n=w(r),s=w(e),a=0;null!=(o=s[a]);++a)n[a]&&L(o,n[a]);if(t)if(i)for(s=s||w(e),n=n||w(r),a=0;null!=(o=s[a]);a++)P(o,n[a]);else P(e,r);return n=w(r,"script"),n.length>0&&b(n,!l&&w(e,"script")),n=s=o=null,r},cleanData:function(e,t){for(var i,n,o,r,a=0,s=ge.expando,l=ge.cache,d=fe.attributes,c=ge.event.special;null!=(i=e[a]);a++)if((t||De(i))&&(o=i[s],r=o&&l[o])){if(r.events)for(n in r.events)c[n]?ge.event.remove(i,n):ge.removeEvent(i,n,r.handle);l[o]&&(delete l[o],d||void 0===i.removeAttribute?i[s]=void 0:i.removeAttribute(s),ae.push(o))}}}),ge.fn.extend({domManip:E,detach:function(e){return N(this,e,!0)},remove:function(e){return N(this,e)},text:function(e){return Oe(this,function(e){return void 0===e?ge.text(this):this.empty().append((this[0]&&this[0].ownerDocument||se).createTextNode(e))},null,e,arguments.length)},append:function(){return E(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){_(this,e).appendChild(e)}})},prepend:function(){return E(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=_(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return E(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return E(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&ge.cleanData(w(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&ge.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return ge.clone(this,e,t)})},html:function(e){return Oe(this,function(e){var t=this[0]||{},i=0,n=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(tt,""):void 0;if("string"==typeof e&&!ot.test(e)&&(fe.htmlSerialize||!it.test(e))&&(fe.leadingWhitespace||!We.test(e))&&!Xe[(Ue.exec(e)||["",""])[1].toLowerCase()]){e=ge.htmlPrefilter(e);try{for(;i<n;i++)t=this[i]||{},1===t.nodeType&&(ge.cleanData(w(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=[];return E(this,arguments,function(t){var i=this.parentNode;ge.inArray(this,e)<0&&(ge.cleanData(w(this)),i&&i.replaceChild(t,this))},e)}}),ge.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){ge.fn[e]=function(e){for(var i,n=0,o=[],r=ge(e),a=r.length-1;n<=a;n++)i=n===a?this:this.clone(!0),ge(r[n])[t](i),ce.apply(o,i.get());return this.pushStack(o)}});var ct,ut={HTML:"block",BODY:"block"},mt=/^margin/,ht=new RegExp("^("+Be+")(?!px)[a-z%]+$","i"),pt=function(e,t,i,n){var o,r,a={};for(r in t)a[r]=e.style[r],e.style[r]=t[r];o=i.apply(e,n||[]);for(r in t)e.style[r]=a[r];return o},ft=se.documentElement;!function(){function e(){var e,c,u=se.documentElement;u.appendChild(l),d.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",t=o=s=!1,n=a=!0,i.getComputedStyle&&(c=i.getComputedStyle(d),t="1%"!==(c||{}).top,s="2px"===(c||{}).marginLeft,o="4px"===(c||{width:"4px"}).width,d.style.marginRight="50%",n="4px"===(c||{marginRight:"4px"}).marginRight,e=d.appendChild(se.createElement("div")),e.style.cssText=d.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",e.style.marginRight=e.style.width="0",d.style.width="1px",a=!parseFloat((i.getComputedStyle(e)||{}).marginRight),d.removeChild(e)),d.style.display="none",r=0===d.getClientRects().length,r&&(d.style.display="",d.innerHTML="<table><tr><td></td><td>t</td></tr></table>",d.childNodes[0].style.borderCollapse="separate",e=d.getElementsByTagName("td"),e[0].style.cssText="margin:0;border:0;padding:0;display:none",(r=0===e[0].offsetHeight)&&(e[0].style.display="",e[1].style.display="none",r=0===e[0].offsetHeight)),u.removeChild(l)}var t,n,o,r,a,s,l=se.createElement("div"),d=se.createElement("div");d.style&&(d.style.cssText="float:left;opacity:.5",fe.opacity="0.5"===d.style.opacity,fe.cssFloat=!!d.style.cssFloat,d.style.backgroundClip="content-box",d.cloneNode(!0).style.backgroundClip="",fe.clearCloneStyle="content-box"===d.style.backgroundClip,l=se.createElement("div"),l.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",d.innerHTML="",l.appendChild(d),fe.boxSizing=""===d.style.boxSizing||""===d.style.MozBoxSizing||""===d.style.WebkitBoxSizing,ge.extend(fe,{reliableHiddenOffsets:function(){return null==t&&e(),r},boxSizingReliable:function(){return null==t&&e(),o},pixelMarginRight:function(){return null==t&&e(),n},pixelPosition:function(){return null==t&&e(),t},reliableMarginRight:function(){return null==t&&e(),a},reliableMarginLeft:function(){return null==t&&e(),s}}))}();var gt,vt,wt=/^(top|right|bottom|left)$/;i.getComputedStyle?(gt=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=i),t.getComputedStyle(e)},vt=function(e,t,i){var n,o,r,a,s=e.style;return i=i||gt(e),a=i?i.getPropertyValue(t)||i[t]:void 0,""!==a&&void 0!==a||ge.contains(e.ownerDocument,e)||(a=ge.style(e,t)),i&&!fe.pixelMarginRight()&&ht.test(a)&&mt.test(t)&&(n=s.width,o=s.minWidth,r=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=i.width,s.width=n,s.minWidth=o,s.maxWidth=r),void 0===a?a:a+""}):ft.currentStyle&&(gt=function(e){return e.currentStyle},vt=function(e,t,i){var n,o,r,a,s=e.style;return i=i||gt(e),a=i?i[t]:void 0,null==a&&s&&s[t]&&(a=s[t]),ht.test(a)&&!wt.test(t)&&(n=s.left,o=e.runtimeStyle,r=o&&o.left,r&&(o.left=e.currentStyle.left),s.left="fontSize"===t?"1em":a,a=s.pixelLeft+"px",s.left=n,r&&(o.left=r)),void 0===a?a:a+""||"auto"});var bt=/alpha\([^)]*\)/i,yt=/opacity\s*=\s*([^)]*)/i,xt=/^(none|table(?!-c[ea]).+)/,At=new RegExp("^("+Be+")(.*)$","i"),Ft={position:"absolute",visibility:"hidden",display:"block"},kt={letterSpacing:"0",fontWeight:"400"},Tt=["Webkit","O","Moz","ms"],_t=se.createElement("div").style;ge.extend({cssHooks:{opacity:{get:function(e,t){if(t){var i=vt(e,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:fe.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,i,n){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,r,a,s=ge.camelCase(t),l=e.style;if(t=ge.cssProps[s]||(ge.cssProps[s]=j(s)||s),a=ge.cssHooks[t]||ge.cssHooks[s],void 0===i)return a&&"get"in a&&void 0!==(o=a.get(e,!1,n))?o:l[t];if(r=typeof i,"string"===r&&(o=qe.exec(i))&&o[1]&&(i=g(e,t,o),r="number"),null!=i&&i===i&&("number"===r&&(i+=o&&o[3]||(ge.cssNumber[s]?"":"px")),fe.clearCloneStyle||""!==i||0!==t.indexOf("background")||(l[t]="inherit"),!(a&&"set"in a&&void 0===(i=a.set(e,i,n)))))try{l[t]=i}catch(e){}}},css:function(e,t,i,n){var o,r,a,s=ge.camelCase(t);return t=ge.cssProps[s]||(ge.cssProps[s]=j(s)||s),a=ge.cssHooks[t]||ge.cssHooks[s],a&&"get"in a&&(r=a.get(e,!0,i)),void 0===r&&(r=vt(e,t,n)),"normal"===r&&t in kt&&(r=kt[t]),""===i||i?(o=parseFloat(r),!0===i||isFinite(o)?o||0:r):r}}),ge.each(["height","width"],function(e,t){ge.cssHooks[t]={get:function(e,i,n){if(i)return xt.test(ge.css(e,"display"))&&0===e.offsetWidth?pt(e,Ft,function(){return R(e,t,n)}):R(e,t,n)},set:function(e,i,n){var o=n&&gt(e);return q(e,i,n?M(e,t,n,fe.boxSizing&&"border-box"===ge.css(e,"boxSizing",!1,o),o):0)}}}),fe.opacity||(ge.cssHooks.opacity={get:function(e,t){return yt.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var i=e.style,n=e.currentStyle,o=ge.isNumeric(t)?"alpha(opacity="+100*t+")":"",r=n&&n.filter||i.filter||"";i.zoom=1,(t>=1||""===t)&&""===ge.trim(r.replace(bt,""))&&i.removeAttribute&&(i.removeAttribute("filter"),""===t||n&&!n.filter)||(i.filter=bt.test(r)?r.replace(bt,o):r+" "+o)}}),ge.cssHooks.marginRight=$(fe.reliableMarginRight,function(e,t){if(t)return pt(e,{display:"inline-block"},vt,[e,"marginRight"])}),ge.cssHooks.marginLeft=$(fe.reliableMarginLeft,function(e,t){if(t)return(parseFloat(vt(e,"marginLeft"))||(ge.contains(e.ownerDocument,e)?e.getBoundingClientRect().left-pt(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}):0))+"px"}),ge.each({margin:"",padding:"",border:"Width"},function(e,t){ge.cssHooks[e+t]={expand:function(i){for(var n=0,o={},r="string"==typeof i?i.split(" "):[i];n<4;n++)o[e+Me[n]+t]=r[n]||r[n-2]||r[0];return o}},mt.test(e)||(ge.cssHooks[e+t].set=q)}),ge.fn.extend({css:function(e,t){return Oe(this,function(e,t,i){var n,o,r={},a=0;if(ge.isArray(t)){for(n=gt(e),o=t.length;a<o;a++)r[t[a]]=ge.css(e,t[a],!1,n);return r}return void 0!==i?ge.style(e,t,i):ge.css(e,t)},e,t,arguments.length>1)},show:function(){return B(this,!0)},hide:function(){return B(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Re(this)?ge(this).show():ge(this).hide()})}}),ge.Tween=O,O.prototype={constructor:O,init:function(e,t,i,n,o,r){this.elem=e,this.prop=i,this.easing=o||ge.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=n,this.unit=r||(ge.cssNumber[i]?"":"px")},cur:function(){var e=O.propHooks[this.prop];return e&&e.get?e.get(this):O.propHooks._default.get(this)},run:function(e){var t,i=O.propHooks[this.prop];return this.options.duration?this.pos=t=ge.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):O.propHooks._default.set(this),this}},O.prototype.init.prototype=O.prototype,O.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=ge.css(e.elem,e.prop,""),t&&"auto"!==t?t:0)},set:function(e){ge.fx.step[e.prop]?ge.fx.step[e.prop](e):1!==e.elem.nodeType||null==e.elem.style[ge.cssProps[e.prop]]&&!ge.cssHooks[e.prop]?e.elem[e.prop]=e.now:ge.style(e.elem,e.prop,e.now+e.unit)}}},O.propHooks.scrollTop=O.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},ge.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},ge.fx=O.prototype.init,ge.fx.step={};var Ct,St,Pt=/^(?:toggle|show|hide)$/,Lt=/queueHooks$/;ge.Animation=ge.extend(X,{tweeners:{"*":[function(e,t){var i=this.createTween(e,t);return g(i.elem,e,qe.exec(t),i),i}]},tweener:function(e,t){ge.isFunction(e)?(t=e,e=["*"]):e=e.match(Ee);for(var i,n=0,o=e.length;n<o;n++)i=e[n],X.tweeners[i]=X.tweeners[i]||[],X.tweeners[i].unshift(t)},prefilters:[W],prefilter:function(e,t){t?X.prefilters.unshift(e):X.prefilters.push(e)}}),ge.speed=function(e,t,i){var n=e&&"object"==typeof e?ge.extend({},e):{complete:i||!i&&t||ge.isFunction(e)&&e,duration:e,easing:i&&t||t&&!ge.isFunction(t)&&t};return n.duration=ge.fx.off?0:"number"==typeof n.duration?n.duration:n.duration in ge.fx.speeds?ge.fx.speeds[n.duration]:ge.fx.speeds._default,null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){ge.isFunction(n.old)&&n.old.call(this),n.queue&&ge.dequeue(this,n.queue)},n},ge.fn.extend({fadeTo:function(e,t,i,n){return this.filter(Re).css("opacity",0).show().end().animate({opacity:t},e,i,n)},animate:function(e,t,i,n){var o=ge.isEmptyObject(e),r=ge.speed(t,i,n),a=function(){var t=X(this,ge.extend({},e),r);(o||ge._data(this,"finish"))&&t.stop(!0)};return a.finish=a,o||!1===r.queue?this.each(a):this.queue(r.queue,a)},stop:function(e,t,i){var n=function(e){var t=e.stop;delete e.stop,t(i)};return"string"!=typeof e&&(i=t,t=e,e=void 0),t&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,o=null!=e&&e+"queueHooks",r=ge.timers,a=ge._data(this);if(o)a[o]&&a[o].stop&&n(a[o]);else for(o in a)a[o]&&a[o].stop&&Lt.test(o)&&n(a[o]);for(o=r.length;o--;)r[o].elem!==this||null!=e&&r[o].queue!==e||(r[o].anim.stop(i),t=!1,r.splice(o,1));!t&&i||ge.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,i=ge._data(this),n=i[e+"queue"],o=i[e+"queueHooks"],r=ge.timers,a=n?n.length:0;for(i.finish=!0,ge.queue(this,e,[]),o&&o.stop&&o.stop.call(this,!0),t=r.length;t--;)r[t].elem===this&&r[t].queue===e&&(r[t].anim.stop(!0),r.splice(t,1));for(t=0;t<a;t++)n[t]&&n[t].finish&&n[t].finish.call(this);delete i.finish})}}),ge.each(["toggle","show","hide"],function(e,t){var i=ge.fn[t];ge.fn[t]=function(e,n,o){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(U(t,!0),e,n,o)}}),ge.each({slideDown:U("show"),slideUp:U("hide"),slideToggle:U("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){ge.fn[e]=function(e,i,n){return this.animate(t,e,i,n)}}),ge.timers=[],ge.fx.tick=function(){var e,t=ge.timers,i=0;for(Ct=ge.now();i<t.length;i++)(e=t[i])()||t[i]!==e||t.splice(i--,1);t.length||ge.fx.stop(),Ct=void 0},ge.fx.timer=function(e){ge.timers.push(e),e()?ge.fx.start():ge.timers.pop()},ge.fx.interval=13,ge.fx.start=function(){St||(St=i.setInterval(ge.fx.tick,ge.fx.interval))},ge.fx.stop=function(){i.clearInterval(St),St=null},ge.fx.speeds={slow:600,fast:200,_default:400},ge.fn.delay=function(e,t){return e=ge.fx?ge.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var o=i.setTimeout(t,e);n.stop=function(){i.clearTimeout(o)}})},function(){var e,t=se.createElement("input"),i=se.createElement("div"),n=se.createElement("select"),o=n.appendChild(se.createElement("option"));i=se.createElement("div"),i.setAttribute("className","t"),i.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",e=i.getElementsByTagName("a")[0],t.setAttribute("type","checkbox"),i.appendChild(t),e=i.getElementsByTagName("a")[0],e.style.cssText="top:1px",fe.getSetAttribute="t"!==i.className,fe.style=/top/.test(e.getAttribute("style")),fe.hrefNormalized="/a"===e.getAttribute("href"),fe.checkOn=!!t.value,fe.optSelected=o.selected,fe.enctype=!!se.createElement("form").enctype,n.disabled=!0,fe.optDisabled=!o.disabled,t=se.createElement("input"),t.setAttribute("value",""),fe.input=""===t.getAttribute("value"),t.value="t",t.setAttribute("type","radio"),fe.radioValue="t"===t.value}();var Et=/\r/g,Nt=/[\x20\t\r\n\f]+/g;ge.fn.extend({val:function(e){var t,i,n,o=this[0];{if(arguments.length)return n=ge.isFunction(e),this.each(function(i){var o;1===this.nodeType&&(o=n?e.call(this,i,ge(this).val()):e,null==o?o="":"number"==typeof o?o+="":ge.isArray(o)&&(o=ge.map(o,function(e){return null==e?"":e+""})),(t=ge.valHooks[this.type]||ge.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,o,"value")||(this.value=o))});if(o)return(t=ge.valHooks[o.type]||ge.valHooks[o.nodeName.toLowerCase()])&&"get"in t&&void 0!==(i=t.get(o,"value"))?i:(i=o.value,"string"==typeof i?i.replace(Et,""):null==i?"":i)}}}),ge.extend({valHooks:{option:{get:function(e){var t=ge.find.attr(e,"value");return null!=t?t:ge.trim(ge.text(e)).replace(Nt," ")}},select:{get:function(e){for(var t,i,n=e.options,o=e.selectedIndex,r="select-one"===e.type||o<0,a=r?null:[],s=r?o+1:n.length,l=o<0?s:r?o:0;l<s;l++)if(i=n[l],(i.selected||l===o)&&(fe.optDisabled?!i.disabled:null===i.getAttribute("disabled"))&&(!i.parentNode.disabled||!ge.nodeName(i.parentNode,"optgroup"))){if(t=ge(i).val(),r)return t;a.push(t)}return a},set:function(e,t){for(var i,n,o=e.options,r=ge.makeArray(t),a=o.length;a--;)if(n=o[a],ge.inArray(ge.valHooks.option.get(n),r)>-1)try{n.selected=i=!0}catch(e){n.scrollHeight}else n.selected=!1;return i||(e.selectedIndex=-1),o}}}}),ge.each(["radio","checkbox"],function(){ge.valHooks[this]={set:function(e,t){if(ge.isArray(t))return e.checked=ge.inArray(ge(e).val(),t)>-1}},fe.checkOn||(ge.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var It,Dt,$t=ge.expr.attrHandle,jt=/^(?:checked|selected)$/i,Bt=fe.getSetAttribute,qt=fe.input;ge.fn.extend({attr:function(e,t){return Oe(this,ge.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){ge.removeAttr(this,e)})}}),ge.extend({attr:function(e,t,i){var n,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===e.getAttribute?ge.prop(e,t,i):(1===r&&ge.isXMLDoc(e)||(t=t.toLowerCase(),o=ge.attrHooks[t]||(ge.expr.match.bool.test(t)?Dt:It)),void 0!==i?null===i?void ge.removeAttr(e,t):o&&"set"in o&&void 0!==(n=o.set(e,i,t))?n:(e.setAttribute(t,i+""),i):o&&"get"in o&&null!==(n=o.get(e,t))?n:(n=ge.find.attr(e,t),null==n?void 0:n))},attrHooks:{type:{set:function(e,t){if(!fe.radioValue&&"radio"===t&&ge.nodeName(e,"input")){var i=e.value;return e.setAttribute("type",t),i&&(e.value=i),t}}}},removeAttr:function(e,t){var i,n,o=0,r=t&&t.match(Ee);if(r&&1===e.nodeType)for(;i=r[o++];)n=ge.propFix[i]||i,ge.expr.match.bool.test(i)?qt&&Bt||!jt.test(i)?e[n]=!1:e[ge.camelCase("default-"+i)]=e[n]=!1:ge.attr(e,i,""),e.removeAttribute(Bt?i:n)}}),Dt={set:function(e,t,i){return!1===t?ge.removeAttr(e,i):qt&&Bt||!jt.test(i)?e.setAttribute(!Bt&&ge.propFix[i]||i,i):e[ge.camelCase("default-"+i)]=e[i]=!0,i}},ge.each(ge.expr.match.bool.source.match(/\w+/g),function(e,t){var i=$t[t]||ge.find.attr;qt&&Bt||!jt.test(t)?$t[t]=function(e,t,n){var o,r;return n||(r=$t[t],$t[t]=o,o=null!=i(e,t,n)?t.toLowerCase():null,$t[t]=r),o}:$t[t]=function(e,t,i){if(!i)return e[ge.camelCase("default-"+t)]?t.toLowerCase():null}}),qt&&Bt||(ge.attrHooks.value={set:function(e,t,i){if(!ge.nodeName(e,"input"))return It&&It.set(e,t,i);e.defaultValue=t}}),Bt||(It={set:function(e,t,i){var n=e.getAttributeNode(i);if(n||e.setAttributeNode(n=e.ownerDocument.createAttribute(i)),n.value=t+="","value"===i||t===e.getAttribute(i))return t}},$t.id=$t.name=$t.coords=function(e,t,i){var n;if(!i)return(n=e.getAttributeNode(t))&&""!==n.value?n.value:null},ge.valHooks.button={get:function(e,t){var i=e.getAttributeNode(t);if(i&&i.specified)return i.value},set:It.set},ge.attrHooks.contenteditable={set:function(e,t,i){It.set(e,""!==t&&t,i)}},ge.each(["width","height"],function(e,t){ge.attrHooks[t]={set:function(e,i){if(""===i)return e.setAttribute(t,"auto"),i}}})),fe.style||(ge.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var Mt=/^(?:input|select|textarea|button|object)$/i,Rt=/^(?:a|area)$/i;ge.fn.extend({prop:function(e,t){return Oe(this,ge.prop,e,t,arguments.length>1)},removeProp:function(e){return e=ge.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(e){}})}}),ge.extend({prop:function(e,t,i){var n,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&ge.isXMLDoc(e)||(t=ge.propFix[t]||t,o=ge.propHooks[t]),void 0!==i?o&&"set"in o&&void 0!==(n=o.set(e,i,t))?n:e[t]=i:o&&"get"in o&&null!==(n=o.get(e,t))?n:e[t]},propHooks:{tabIndex:{get:function(e){var t=ge.find.attr(e,"tabindex");return t?parseInt(t,10):Mt.test(e.nodeName)||Rt.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),fe.hrefNormalized||ge.each(["href","src"],function(e,t){ge.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),fe.optSelected||(ge.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),ge.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){ge.propFix[this.toLowerCase()]=this}),fe.enctype||(ge.propFix.enctype="encoding");var Ot=/[\t\r\n\f]/g;ge.fn.extend({addClass:function(e){var t,i,n,o,r,a,s,l=0;if(ge.isFunction(e))return this.each(function(t){ge(this).addClass(e.call(this,t,J(this)))});if("string"==typeof e&&e)for(t=e.match(Ee)||[];i=this[l++];)if(o=J(i),n=1===i.nodeType&&(" "+o+" ").replace(Ot," ")){for(a=0;r=t[a++];)n.indexOf(" "+r+" ")<0&&(n+=r+" ");s=ge.trim(n),o!==s&&ge.attr(i,"class",s)}return this},removeClass:function(e){var t,i,n,o,r,a,s,l=0;if(ge.isFunction(e))return this.each(function(t){ge(this).removeClass(e.call(this,t,J(this)))});if(!arguments.length)return this.attr("class","");if("string"==typeof e&&e)for(t=e.match(Ee)||[];i=this[l++];)if(o=J(i),n=1===i.nodeType&&(" "+o+" ").replace(Ot," ")){for(a=0;r=t[a++];)for(;n.indexOf(" "+r+" ")>-1;)n=n.replace(" "+r+" "," ");s=ge.trim(n),o!==s&&ge.attr(i,"class",s)}return this},toggleClass:function(e,t){var i=typeof e;return"boolean"==typeof t&&"string"===i?t?this.addClass(e):this.removeClass(e):ge.isFunction(e)?this.each(function(i){ge(this).toggleClass(e.call(this,i,J(this),t),t)}):this.each(function(){var t,n,o,r;if("string"===i)for(n=0,o=ge(this),r=e.match(Ee)||[];t=r[n++];)o.hasClass(t)?o.removeClass(t):o.addClass(t);else void 0!==e&&"boolean"!==i||(t=J(this),t&&ge._data(this,"__className__",t),ge.attr(this,"class",t||!1===e?"":ge._data(this,"__className__")||""))})},hasClass:function(e){var t,i,n=0;for(t=" "+e+" ";i=this[n++];)if(1===i.nodeType&&(" "+J(i)+" ").replace(Ot," ").indexOf(t)>-1)return!0;return!1}}),ge.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){ge.fn[t]=function(e,i){return arguments.length>0?this.on(t,null,e,i):this.trigger(t)}}),ge.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});var Ht=i.location,Ut=ge.now(),zt=/\?/,Wt=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;ge.parseJSON=function(e){if(i.JSON&&i.JSON.parse)return i.JSON.parse(e+"");var t,n=null,o=ge.trim(e+"");return o&&!ge.trim(o.replace(Wt,function(e,i,o,r){return t&&i&&(n=0),0===n?e:(t=o||i,n+=!r-!o,"")}))?Function("return "+o)():ge.error("Invalid JSON: "+e)},ge.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{i.DOMParser?(n=new i.DOMParser,t=n.parseFromString(e,"text/xml")):(t=new i.ActiveXObject("Microsoft.XMLDOM"),t.async="false",t.loadXML(e))}catch(e){t=void 0}return t&&t.documentElement&&!t.getElementsByTagName("parsererror").length||ge.error("Invalid XML: "+e),t};var Vt=/#.*$/,Xt=/([?&])_=[^&]*/,Jt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Gt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Yt=/^(?:GET|HEAD)$/,Kt=/^\/\//,Qt=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Zt={},ei={},ti="*/".concat("*"),ii=Ht.href,ni=Qt.exec(ii.toLowerCase())||[];ge.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ii,type:"GET",isLocal:Gt.test(ni[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":ti,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":ge.parseJSON,"text xml":ge.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?K(K(e,ge.ajaxSettings),t):K(ge.ajaxSettings,e)},ajaxPrefilter:G(Zt),ajaxTransport:G(ei),ajax:function(e,t){function n(e,t,n,o){var r,u,w,b,x,F=t;2!==y&&(y=2,l&&i.clearTimeout(l),c=void 0,s=o||"",A.readyState=e>0?4:0,r=e>=200&&e<300||304===e,n&&(b=Q(m,A,n)),b=Z(m,b,A,r),r?(m.ifModified&&(x=A.getResponseHeader("Last-Modified"),x&&(ge.lastModified[a]=x),(x=A.getResponseHeader("etag"))&&(ge.etag[a]=x)),204===e||"HEAD"===m.type?F="nocontent":304===e?F="notmodified":(F=b.state,u=b.data,w=b.error,r=!w)):(w=F,!e&&F||(F="error",e<0&&(e=0))),A.status=e,A.statusText=(t||F)+"",r?f.resolveWith(h,[u,F,A]):f.rejectWith(h,[A,F,w]),A.statusCode(v),v=void 0,d&&p.trigger(r?"ajaxSuccess":"ajaxError",[A,m,r?u:w]),g.fireWith(h,[A,F]),d&&(p.trigger("ajaxComplete",[A,m]),--ge.active||ge.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var o,r,a,s,l,d,c,u,m=ge.ajaxSetup({},t),h=m.context||m,p=m.context&&(h.nodeType||h.jquery)?ge(h):ge.event,f=ge.Deferred(),g=ge.Callbacks("once memory"),v=m.statusCode||{},w={},b={},y=0,x="canceled",A={readyState:0,getResponseHeader:function(e){var t;if(2===y){if(!u)for(u={};t=Jt.exec(s);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===y?s:null},setRequestHeader:function(e,t){var i=e.toLowerCase();return y||(e=b[i]=b[i]||e,w[e]=t),this},overrideMimeType:function(e){return y||(m.mimeType=e),this},statusCode:function(e){var t;if(e)if(y<2)for(t in e)v[t]=[v[t],e[t]];else A.always(e[A.status]);return this},abort:function(e){var t=e||x;return c&&c.abort(t),n(0,t),this}};if(f.promise(A).complete=g.add,A.success=A.done,A.error=A.fail,m.url=((e||m.url||ii)+"").replace(Vt,"").replace(Kt,ni[1]+"//"),m.type=t.method||t.type||m.method||m.type,m.dataTypes=ge.trim(m.dataType||"*").toLowerCase().match(Ee)||[""],null==m.crossDomain&&(o=Qt.exec(m.url.toLowerCase()),m.crossDomain=!(!o||o[1]===ni[1]&&o[2]===ni[2]&&(o[3]||("http:"===o[1]?"80":"443"))===(ni[3]||("http:"===ni[1]?"80":"443")))),m.data&&m.processData&&"string"!=typeof m.data&&(m.data=ge.param(m.data,m.traditional)),Y(Zt,m,t,A),2===y)return A;d=ge.event&&m.global,d&&0==ge.active++&&ge.event.trigger("ajaxStart"),m.type=m.type.toUpperCase(),m.hasContent=!Yt.test(m.type),a=m.url,m.hasContent||(m.data&&(a=m.url+=(zt.test(a)?"&":"?")+m.data,delete m.data),!1===m.cache&&(m.url=Xt.test(a)?a.replace(Xt,"$1_="+Ut++):a+(zt.test(a)?"&":"?")+"_="+Ut++)),m.ifModified&&(ge.lastModified[a]&&A.setRequestHeader("If-Modified-Since",ge.lastModified[a]),ge.etag[a]&&A.setRequestHeader("If-None-Match",ge.etag[a])),(m.data&&m.hasContent&&!1!==m.contentType||t.contentType)&&A.setRequestHeader("Content-Type",m.contentType),A.setRequestHeader("Accept",m.dataTypes[0]&&m.accepts[m.dataTypes[0]]?m.accepts[m.dataTypes[0]]+("*"!==m.dataTypes[0]?", "+ti+"; q=0.01":""):m.accepts["*"]);for(r in m.headers)A.setRequestHeader(r,m.headers[r]);if(m.beforeSend&&(!1===m.beforeSend.call(h,A,m)||2===y))return A.abort();x="abort";for(r in{success:1,error:1,complete:1})A[r](m[r]);if(c=Y(ei,m,t,A)){if(A.readyState=1,d&&p.trigger("ajaxSend",[A,m]),2===y)return A;m.async&&m.timeout>0&&(l=i.setTimeout(function(){A.abort("timeout")},m.timeout));try{y=1,c.send(w,n)}catch(e){if(!(y<2))throw e;n(-1,e)}}else n(-1,"No Transport");return A},getJSON:function(e,t,i){return ge.get(e,t,i,"json")},getScript:function(e,t){return ge.get(e,void 0,t,"script")}}),ge.each(["get","post"],function(e,t){ge[t]=function(e,i,n,o){return ge.isFunction(i)&&(o=o||n,n=i,i=void 0),ge.ajax(ge.extend({url:e,type:t,dataType:o,data:i,success:n},ge.isPlainObject(e)&&e))}}),ge._evalUrl=function(e){return ge.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},ge.fn.extend({wrapAll:function(e){if(ge.isFunction(e))return this.each(function(t){ge(this).wrapAll(e.call(this,t))});if(this[0]){var t=ge(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return ge.isFunction(e)?this.each(function(t){ge(this).wrapInner(e.call(this,t))}):this.each(function(){var t=ge(this),i=t.contents();i.length?i.wrapAll(e):t.append(e)})},wrap:function(e){var t=ge.isFunction(e);return this.each(function(i){ge(this).wrapAll(t?e.call(this,i):e)})},unwrap:function(){return this.parent().each(function(){ge.nodeName(this,"body")||ge(this).replaceWith(this.childNodes)}).end()}}),ge.expr.filters.hidden=function(e){return fe.reliableHiddenOffsets()?e.offsetWidth<=0&&e.offsetHeight<=0&&!e.getClientRects().length:te(e)},ge.expr.filters.visible=function(e){return!ge.expr.filters.hidden(e)};var oi=/%20/g,ri=/\[\]$/,ai=/\r?\n/g,si=/^(?:submit|button|image|reset|file)$/i,li=/^(?:input|select|textarea|keygen)/i;ge.param=function(e,t){var i,n=[],o=function(e,t){t=ge.isFunction(t)?t():null==t?"":t,n[n.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=ge.ajaxSettings&&ge.ajaxSettings.traditional),ge.isArray(e)||e.jquery&&!ge.isPlainObject(e))ge.each(e,function(){o(this.name,this.value)});else for(i in e)ie(i,e[i],t,o);return n.join("&").replace(oi,"+")},ge.fn.extend({serialize:function(){return ge.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=ge.prop(this,"elements");return e?ge.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!ge(this).is(":disabled")&&li.test(this.nodeName)&&!si.test(e)&&(this.checked||!He.test(e))}).map(function(e,t){var i=ge(this).val();return null==i?null:ge.isArray(i)?ge.map(i,function(e){return{name:t.name,value:e.replace(ai,"\r\n")}}):{name:t.name,value:i.replace(ai,"\r\n")}}).get()}}),ge.ajaxSettings.xhr=void 0!==i.ActiveXObject?function(){return this.isLocal?oe():se.documentMode>8?ne():/^(get|post|head|put|delete|options)$/i.test(this.type)&&ne()||oe()}:ne;var di=0,ci={},ui=ge.ajaxSettings.xhr();i.attachEvent&&i.attachEvent("onunload",function(){for(var e in ci)ci[e](void 0,!0)}),fe.cors=!!ui&&"withCredentials"in ui,ui=fe.ajax=!!ui,ui&&ge.ajaxTransport(function(e){if(!e.crossDomain||fe.cors){var t;return{send:function(n,o){var r,a=e.xhr(),s=++di;if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)a[r]=e.xhrFields[r];e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(r in n)void 0!==n[r]&&a.setRequestHeader(r,n[r]+"");a.send(e.hasContent&&e.data||null),t=function(i,n){var r,l,d;if(t&&(n||4===a.readyState))if(delete ci[s],t=void 0,a.onreadystatechange=ge.noop,n)4!==a.readyState&&a.abort();else{d={},r=a.status,"string"==typeof a.responseText&&(d.text=a.responseText);try{l=a.statusText}catch(e){l=""}r||!e.isLocal||e.crossDomain?1223===r&&(r=204):r=d.text?200:404}d&&o(r,l,d,a.getAllResponseHeaders())},e.async?4===a.readyState?i.setTimeout(t):a.onreadystatechange=ci[s]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),ge.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return ge.globalEval(e),e}}}),ge.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),ge.ajaxTransport("script",function(e){if(e.crossDomain){var t,i=se.head||ge("head")[0]||se.documentElement;return{send:function(n,o){t=se.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,i){(i||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,i||o(200,"success"))},i.insertBefore(t,i.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var mi=[],hi=/(=)\?(?=&|$)|\?\?/;ge.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=mi.pop()||ge.expando+"_"+Ut++;return this[e]=!0,e}}),ge.ajaxPrefilter("json jsonp",function(e,t,n){var o,r,a,s=!1!==e.jsonp&&(hi.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&hi.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return o=e.jsonpCallback=ge.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(hi,"$1"+o):!1!==e.jsonp&&(e.url+=(zt.test(e.url)?"&":"?")+e.jsonp+"="+o),e.converters["script json"]=function(){return a||ge.error(o+" was not called"),a[0]},e.dataTypes[0]="json",r=i[o],i[o]=function(){a=arguments},n.always(function(){void 0===r?ge(i).removeProp(o):i[o]=r,e[o]&&(e.jsonpCallback=t.jsonpCallback,mi.push(o)),a&&ge.isFunction(r)&&r(a[0]),a=r=void 0}),"script"}),ge.parseHTML=function(e,t,i){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(i=t,t=!1),t=t||se;var n=Te.exec(e),o=!i&&[];return n?[t.createElement(n[1])]:(n=x([e],t,o),o&&o.length&&ge(o).remove(),ge.merge([],n.childNodes))};var pi=ge.fn.load;ge.fn.load=function(e,t,i){if("string"!=typeof e&&pi)return pi.apply(this,arguments);var n,o,r,a=this,s=e.indexOf(" ");return s>-1&&(n=ge.trim(e.slice(s,e.length)),e=e.slice(0,s)),ge.isFunction(t)?(i=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&ge.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){r=arguments,a.html(n?ge("<div>").append(ge.parseHTML(e)).find(n):e)}).always(i&&function(e,t){a.each(function(){i.apply(this,r||[e.responseText,t,e])})}),this},ge.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){ge.fn[t]=function(e){return this.on(t,e)}}),ge.expr.filters.animated=function(e){return ge.grep(ge.timers,function(t){return e===t.elem}).length},ge.offset={setOffset:function(e,t,i){var n,o,r,a,s,l,d,c=ge.css(e,"position"),u=ge(e),m={};"static"===c&&(e.style.position="relative"),s=u.offset(),r=ge.css(e,"top"),l=ge.css(e,"left"),d=("absolute"===c||"fixed"===c)&&ge.inArray("auto",[r,l])>-1,d?(n=u.position(),a=n.top,o=n.left):(a=parseFloat(r)||0,o=parseFloat(l)||0),ge.isFunction(t)&&(t=t.call(e,i,ge.extend({},s))),null!=t.top&&(m.top=t.top-s.top+a),null!=t.left&&(m.left=t.left-s.left+o),"using"in t?t.using.call(e,m):u.css(m)}},ge.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){ge.offset.setOffset(this,e,t)});var t,i,n={top:0,left:0},o=this[0],r=o&&o.ownerDocument;if(r)return t=r.documentElement,ge.contains(t,o)?(void 0!==o.getBoundingClientRect&&(n=o.getBoundingClientRect()),i=re(r),{top:n.top+(i.pageYOffset||t.scrollTop)-(t.clientTop||0),left:n.left+(i.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):n},position:function(){if(this[0]){var e,t,i={top:0,left:0},n=this[0];return"fixed"===ge.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),ge.nodeName(e[0],"html")||(i=e.offset()),i.top+=ge.css(e[0],"borderTopWidth",!0),i.left+=ge.css(e[0],"borderLeftWidth",!0)),{top:t.top-i.top-ge.css(n,"marginTop",!0),left:t.left-i.left-ge.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&!ge.nodeName(e,"html")&&"static"===ge.css(e,"position");)e=e.offsetParent;return e||ft})}}),ge.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var i=/Y/.test(t);ge.fn[e]=function(n){return Oe(this,function(e,n,o){var r=re(e);if(void 0===o)return r?t in r?r[t]:r.document.documentElement[n]:e[n];r?r.scrollTo(i?ge(r).scrollLeft():o,i?o:ge(r).scrollTop()):e[n]=o},e,n,arguments.length,null)}}),ge.each(["top","left"],function(e,t){ge.cssHooks[t]=$(fe.pixelPosition,function(e,i){if(i)return i=vt(e,t),ht.test(i)?ge(e).position()[t]+"px":i})}),ge.each({Height:"height",Width:"width"},function(e,t){ge.each({padding:"inner"+e,content:t,"":"outer"+e},function(i,n){ge.fn[n]=function(n,o){var r=arguments.length&&(i||"boolean"!=typeof n),a=i||(!0===n||!0===o?"margin":"border");return Oe(this,function(t,i,n){var o;return ge.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===n?ge.css(t,i,a):ge.style(t,i,n,a)},t,r?n:void 0,r,null)}})}),ge.fn.extend({bind:function(e,t,i){return this.on(e,null,t,i)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,i,n){return this.on(t,e,i,n)},undelegate:function(e,t,i){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",i)}}),ge.fn.size=function(){return this.length},ge.fn.andSelf=ge.fn.addBack,n=[],void 0!==(o=function(){return ge}.apply(t,n))&&(e.exports=o);var fi=i.jQuery,gi=i.$;return ge.noConflict=function(e){return i.$===ge&&(i.$=gi),e&&i.jQuery===ge&&(i.jQuery=fi),ge},r||(i.jQuery=i.$=ge),ge})},256:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={dateFormat:function(e,t){void 0===t&&(t=e,e=new Date);var i={M:e.getMonth()+1,d:e.getDate(),h:e.getHours(),m:e.getMinutes(),s:e.getSeconds(),q:Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};return t=t.replace(/([yMdhmsqS])+/g,function(t,n){var o=i[n];return void 0!==o?(t.length>1&&(o="0"+o,o=o.substr(o.length-2)),o):"y"===n?(e.getFullYear()+"").substr(4-t.length):t})},getUrlParam:function(e,t){var i=new RegExp("\\?(?:.+&)?"+t+"=(.*?)(?:[?&].*)?$"),n=e.match(i);return n?n[1]:""}}},290:function(e,t){e.exports="data:image/gif;base64,R0lGODlhBgABAKEAAP///wAAADY2Nv///yH+GkNyZWF0ZWQgd2l0aCBhamF4bG9hZC5pbmZvACwAAAAABgABAAACA4SFBQA7"},291:function(e,t){e.exports="data:image/gif;base64,R0lGODlhAQAGAKEAAP///wAAADY2Nv///yH+GkNyZWF0ZWQgd2l0aCBhamF4bG9hZC5pbmZvACwAAAAAAQAGAAACA4SFBQA7"},298:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=[{week:0,name:"星期一",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]},{week:1,name:"星期二",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]},{week:2,name:"星期三",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]},{week:3,name:"星期四",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]},{week:4,name:"星期五",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]},{week:5,name:"星期六",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]},{week:6,name:"星期日",time:[{show:!1,time:0},{show:!1,time:1},{show:!1,time:2},{show:!1,time:3},{show:!1,time:4},{show:!1,time:5},{show:!1,time:6},{show:!1,time:7},{show:!1,time:8},{show:!1,time:9},{show:!1,time:10},{show:!1,time:11},{show:!1,time:12},{show:!1,time:13},{show:!1,time:14},{show:!1,time:15},{show:!1,time:16},{show:!1,time:17},{show:!1,time:18},{show:!1,time:19},{show:!1,time:20},{show:!1,time:21},{show:!1,time:22},{show:!1,time:23},{show:!1,time:24},{show:!1,time:25},{show:!1,time:26},{show:!1,time:27},{show:!1,time:28},{show:!1,time:29},{show:!1,time:30},{show:!1,time:31},{show:!1,time:32},{show:!1,time:33},{show:!1,time:34},{show:!1,time:35},{show:!1,time:36},{show:!1,time:37},{show:!1,time:38},{show:!1,time:39},{show:!1,time:40},{show:!1,time:41},{show:!1,time:42},{show:!1,time:43},{show:!1,time:44},{show:!1,time:45},{show:!1,time:46},{show:!1,time:47}]}];t.default={props:{timeBucket1:{type:Array,default:n}},watch:{timeBucket1:function(e){this.timeBucket=e}},data:function(){return{thead:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],schedule:{start:{x:0,y:0,time:0,week:0},end:{x:0,y:0,time:0,week:0}},timeBucket:this.timeBucket1}},methods:{kan:function(){console.log(this.timeBucket1)},addColor:function(e){var t=(this.$refs.schedule,e.target.dataset.week),i=e.target.dataset.time;e.target.offsetWidth,e.target.offsetHeight;this.timeBucket[t].time[i].show=!this.timeBucket[t].time[i].show},mouseDown:function(e){var t=this.$refs.schedule;t.style.opacity=1,t.style.display="block",t.style.left=e.clientX+"px",t.style.top=e.clientY+"px",this.schedule.start.x=e.clientX,this.schedule.start.y=e.clientY,this.schedule.start.time=e.target.dataset.time,this.schedule.start.week=e.target.dataset.week},mouseUp:function(e){var t=this.$refs.schedule;this.schedule.end.x=e.clientX,this.schedule.end.y=e.clientY,this.schedule.end.time=e.target.dataset.time,this.schedule.end.week=e.target.dataset.week,t.style.width=0,t.style.height=0,t.style.display="none",t.style.opacity=0;var i=Number(this.schedule.start.time),n=(this.schedule.start.week,this.schedule.end.time),o=this.schedule.end.week;if(this.schedule.end.week-this.schedule.start.week>0||this.schedule.end.time-this.schedule.start.time>0)for(var r=this.schedule.start.week;r<=o;r++)for(var a=i;a<=n;a++)this.timeBucket[r].time[a].show=!this.timeBucket[r].time[a].show},mouseMove:function(e){if("block"==this.$refs.schedule.style.display){var t=this.$refs.schedule;this.schedule.end.x=e.clientX,this.schedule.end.y=e.clientY,t.style.width=this.schedule.end.x-this.schedule.start.x+"px",t.style.height=this.schedule.end.y-this.schedule.start.y+"px"}},reseting:function(){for(var e=0;e<this.timeBucket.length;e++)for(var t=0;t<this.timeBucket[e].time.length;t++)this.timeBucket[e].time[t].show=!1}},computed:{weekList:function(){for(var e=[],t=[{name:"星期一",Time:[]},{name:"星期二",Time:[]},{name:"星期三",Time:[]},{name:"星期四",Time:[]},{name:"星期五",Time:[]},{name:"星期六",Time:[]},{name:"星期日",Time:[]}],i=[[],[],[],[],[],[],[]],n=[[],[],[],[],[],[],[]],o=[[],[],[],[],[],[],[]],r=[[],[],[],[],[],[],[]],a=[{name:"星期一",Time:[]},{name:"星期二",Time:[]},{name:"星期三",Time:[]},{name:"星期四",Time:[]},{name:"星期五",Time:[]},{name:"星期六",Time:[]},{name:"星期日",Time:[]}],s=0;s<this.timeBucket.length;s++)for(var l=0;l<this.timeBucket[s].time.length;l++)this.timeBucket[s].time[l].show&&(this.timeBucket[s].time[l].week=s,e.push(this.timeBucket[s].time[l]));for(var d=0;d<e.length;d++)0==e[d].week&&t[0].Time.push(e[d]),1==e[d].week&&t[1].Time.push(e[d]),2==e[d].week&&t[2].Time.push(e[d]),3==e[d].week&&t[3].Time.push(e[d]),4==e[d].week&&t[4].Time.push(e[d]),5==e[d].week&&t[5].Time.push(e[d]),6==e[d].week&&t[6].Time.push(e[d]);for(var c=0;c<t.length;c++)for(var u=(t[c].Time,0);u<t[c].Time.length;u++){t[c].Time[u].value=.5*(t[c].Time[u].time+1),t[c].Time[u].value1=t[c].Time[u].value-.5;var m=parseInt(t[c].Time[u].value),h=t[c].Time[u].value-m,p=parseInt(t[c].Time[u].value1),f=t[c].Time[u].value1-p;t[c].Time[u].value=m<10?h>0?"0"+m+":30":"0"+m+":00":h>0?m+":30":m+":00",t[c].Time[u].value1=p<10?f>0?"0"+p+":30":"0"+p+":00":f>0?p+":30":p+":00",0==t[c].Time[u].week&&(i[0].push(t[c].Time[u].value1),i[0].push(t[c].Time[u].value)),1==t[c].Time[u].week&&(i[1].push(t[c].Time[u].value1),i[1].push(t[c].Time[u].value)),2==t[c].Time[u].week&&(i[2].push(t[c].Time[u].value1),i[2].push(t[c].Time[u].value)),3==t[c].Time[u].week&&(i[3].push(t[c].Time[u].value1),i[3].push(t[c].Time[u].value)),4==t[c].Time[u].week&&(i[4].push(t[c].Time[u].value1),i[4].push(t[c].Time[u].value)),5==t[c].Time[u].week&&(i[5].push(t[c].Time[u].value1),i[5].push(t[c].Time[u].value)),6==t[c].Time[u].week&&(i[6].push(t[c].Time[u].value1),i[6].push(t[c].Time[u].value))}for(var g=0;g<i.length;g++)for(var v=0;v<i[g].length;v++){var w=n[g].indexOf(i[g][v]);w<0?n[g].push(i[g][v]):n[g].splice(w,1)}for(var b=0;b<n.length;b++)for(var y=0;y<n[b].length;y++)y%2==0?o[b].push(n[b][y]):r[b].push(n[b][y]);for(var x=0;x<o.length;x++)for(var A=0;A<o[x].length;A++){var F=new Object;F.start=o[x][A],F.end=r[x][A],F.show=0,a[x].Time.push(F)}return a}}}},299:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e){var n=i(234),o=i.n(n),r=i(358),a=i.n(r),s=i(351),l=(i.n(s),i(331)),d=(i.n(l),i(256)),c=i.n(d),u=[{week:0,name:"星期一",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]},{week:1,name:"星期二",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]},{week:2,name:"星期三",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]},{week:3,name:"星期四",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]},{week:4,name:"星期五",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]},{week:5,name:"星期六",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]},{week:6,name:"星期日",time:[{show:!0,time:0},{show:!0,time:1},{show:!0,time:2},{show:!0,time:3},{show:!0,time:4},{show:!0,time:5},{show:!0,time:6},{show:!0,time:7},{show:!0,time:8},{show:!0,time:9},{show:!0,time:10},{show:!0,time:11},{show:!0,time:12},{show:!0,time:13},{show:!0,time:14},{show:!0,time:15},{show:!0,time:16},{show:!0,time:17},{show:!0,time:18},{show:!0,time:19},{show:!0,time:20},{show:!0,time:21},{show:!0,time:22},{show:!0,time:23},{show:!0,time:24},{show:!0,time:25},{show:!0,time:26},{show:!0,time:27},{show:!0,time:28},{show:!0,time:29},{show:!0,time:30},{show:!0,time:31},{show:!0,time:32},{show:!0,time:33},{show:!0,time:34},{show:!0,time:35},{show:!0,time:36},{show:!0,time:37},{show:!0,time:38},{show:!0,time:39},{show:!0,time:40},{show:!0,time:41},{show:!0,time:42},{show:!0,time:43},{show:!0,time:44},{show:!0,time:45},{show:!0,time:46},{show:!0,time:47}]}],m="";t.default={data:function(){return{brands:[],regionList:[{key:1,val:"受限区"},{key:2,val:"非受限区"},{key:0,val:"不限制"}],lockAreaList:[{key:1,val:"锁区"},{key:2,val:"非锁区"},{key:0,val:"不限制"}],lockActionPointList:[{key:0,val:"所有锁区生效"},{key:1,val:"一道锁生效"},{key:2,val:"二道锁生效"}],adPosOptions:[{key:"",label:""}],adPosList:[{code:"",name:""}],queryAdParams:{adName:"",adPlan:"",adId:"",adType:"",pageNo:1,pageSize:10,startDate:"",endDate:"",status:"",adPos:[],adPosStr:"",userPkg:"",appIdThird:"",posIdThird:""},queryAdPlanParams:{name:"",pageNo:1,pageSize:10},adPosDisabled:!1,adListNum:null,adPlanListNum:null,activeTab:"first",planList:[],planList2:[],innerPlanList:[],industryList:[],isShowEditPlanForm:!1,editPlanForm:{id:"",name:"",open:"",clientName:"",productName:""},isShowAddPlanForm:!1,addPlanForm:{id:"",name:"",open:"",clientName:"",productName:""},isAddAdForm:!0,isShowAdForm:!1,adList:[],areaList:[{id:11,name:"北京"},{id:12,name:"天津"},{id:13,name:"河北"},{id:14,name:"山西"},{id:15,name:"内蒙古"},{id:21,name:"辽宁"},{id:22,name:"吉林"},{id:23,name:"黑龙江"},{id:31,name:"上海"},{id:32,name:"江苏"},{id:33,name:"浙江"},{id:34,name:"安徽"},{id:35,name:"福建"},{id:36,name:"江西"},{id:37,name:"山东"},{id:41,name:"河南"},{id:42,name:"湖北"},{id:43,name:"湖南"},{id:44,name:"广东"},{id:45,name:"广西"},{id:46,name:"海南"},{id:50,name:"重庆"},{id:51,name:"四川"},{id:52,name:"贵州"},{id:53,name:"云南"},{id:54,name:"西藏"},{id:61,name:"陕西"},{id:62,name:"甘肃"},{id:63,name:"青海"},{id:64,name:"宁夏"},{id:65,name:"新疆"},{id:71,name:"台湾"},{id:81,name:"香港"},{id:91,name:"澳门"}],areaListId:[11,12,13,14,15,21,22,23,31,31,32,33,34,35,36,37,41,42,43,44,45,46,50,51,52,53,54,61,62,63,64,65,71,81,91],adPosMessage:"",editAdForm:{timeBucketVOList:u,adPos:[],budget:{budget:null,enableBudget:!1,noIdleTime:!1,noPerMaxTimes:!1,idleTime:null,perMaxTimes:null,noExposureIdleTime:!1,exposureIdleTime:null,noExposureMaxTimes:!1,exposureMaxTimes:null,ecpm:null},noEndDate:!1,endDate:"",type:1021,ext:{selfEndCard:0,activationTime:null,androidPosId:"",androidBakPosId:"",iosBakPosId:"",iosPosId:"",iosAppId:"",androidAppId:"",preType:0,downloadDrive:!1,extendType:0,template:!1,templateImgSize:0,mintegralType:0,style:101,title:"",content:"",clkUrl:"",imgUrls:[],impTrackUrl:"",clkTrackUrl:"",appPkgName:"",downloadUrl:"",adm:"",videoStartTrackUrl:[],videoFinishTrackUrl:[],videoPauseTrackUrl:[],itid:"",width:0,height:0,endCardReward:0,duration:0,countDownGift:0,shareImgUrl:"",shareType:1,styleType:1,noAppId:!0,industry:"",sharePrice:0,detailPageShare:!1,newuserShare:!1,replaceShareDomain:"",word:"",pr:0,twiceJumpTimes:2,url:"",sourceFrom:1,fetchStrategy:1,hotWordAdUrl:"",fetchUrl:"",deeplinkUrl:"",deeplinkOpenType:0,deeplinkPkgName:"",deeplinkAppType:1,miniProgramName:"",miniIcon:"",miniShareTitle:"",miniShareImage:"",miniMiddleBtnText:"",miniMiddleImage:"",adAppId:"",adOriginalId:"",adMiniPath:"",adMiniAppId:"",adPackageName:"",ownAppId:"",ownOriginalId:"",ownMiniPath:"",ownMiniAppId:"",ownPackageName:"",miniStartWay:0,otherBrowser:0,gdtUnitId:"",adTag:"",adTagRatio:"",showAdLabel:!1,adLabel:"",defaultAdTwiceJump:!1,jsDomainName:"",jsAdPosClose:{channelId:"",channelSource:"",jsStyle:101},jsAdPosOpen:{channelId:"",channelSource:"",jsStyle:201},jsAdPosRecommend:[{channelId:"",channelSource:"",jsStyle:301}]},id:null,name:"",open:!1,orientation:{noAndroid:!1,noIos:!1,noRegion:!1,maxAndroidVersion:null,maxIosVersion:null,minAndroidVersion:null,minIosVersion:null,tailNumber:null,position:null,regions:[],filterRegion:0,lockArea:0,noIncomeLimit:!1,minIncome:null,maxIncome:null,noRegistTimeLimit:!1,minRegistTime:null,maxRegistTime:null,noRegistLongerLimit:!1,minRegistLonger:null,maxRegistLonger:null,containsPkg:null,notContainsPkg:null,userPkg:null,channelId:null,dsp:null,abPoint:null,lockActionPoint:0,brandLimit:!1,brandLimitList:[]},plan:{id:"",name:"",open:""},startDate:"",state:1},putDate:"",registDate:"",active:0,checkAll:!0,isIndeterminate:!0,labelPosition:"left",imageUrl:"",dialogImageUrl:"",dialogVisible:!1,dialogImageLength:0,adTypeList:[],imgList1:"",imgList2:"",imgList3:"",qrImageModal:!1,addQrUrl:!1,queryTime:[c.a.dateFormat(new Date,"yyyy-MM-dd"),c.a.dateFormat(new Date,"yyyy-MM-dd")],jsAdChannelSource:[],adPosTypes:[],editBiddingForm:{id:"",adPosType:"",adId:"",startEcpm:0,endEcpm:0,priority:0,biddingType:2,playType:1,adType:""},addBiddingForm:{adPosType:"",adId:"",startEcpm:0,endEcpm:0,priority:0,biddingType:2,playType:1,adType:""},isShowEditBiddingForm:!1,isShowAddBiddingForm:!1}},created:function(){this.queryTime=[c.a.dateFormat(new Date,"yyyy-MM-dd"),c.a.dateFormat(new Date,"yyyy-MM-dd")],this.queryAdParams.startDate=c.a.dateFormat(new Date,"yyyy-MM-dd"),this.queryAdParams.endDate=c.a.dateFormat(new Date,"yyyy-MM-dd"),this.getAdTypeList(),this.getPlanList(),this.getAdList(1),this.getJsAdChannelSourceList(),this.getAdPosList(),this.getBrands(),this.queryAdPosTypes()},components:{"time-frame":a.a},methods:{queryAdPosTypes:function(){var e=this;e.$axios.get("config/getAdPosType").then(function(t){if(200===t.status){var i=t.data;0===i.code?e.adPosTypes=i.result:e.$message.error("加载数据失败...")}else e.$message.error("服务器异常！")})},saveAdPos:function(){var e=this;e.$refs.editBiddingForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("bidding/updateOne",{},{params:e.editBiddingForm}).then(function(t){if(200===t.status){var i=t.data;0===i.ret?(e.isShowEditBiddingForm=!1,e.$message({message:"Bidding信息更新成功",type:"success"}),e.getAdList(e.queryAdParams.pageNo)):e.$message.error(i.data)}else e.$message.error("服务器异常！")})})},addAdPosPre:function(e,t){this.clearAddForm(),this.addBiddingForm.adId=t.id,this.addBiddingForm.type=t.type,this.isShowAddBiddingForm=!0},addAdPos:function(){var e=this;e.$refs.addBiddingForm.validate(function(t){if(!t)return console.log("error submit!!"),!1;e.$axios.post("bidding/addOne",{},{params:e.addBiddingForm}).then(function(t){if(200===t.status){var i=t.data;0===i.ret?(e.isShowAddBiddingForm=!1,e.$message({message:"新增Bidding配置成功",type:"success"}),e.getAdList(e.queryAdParams.pageNo)):e.$message.error(i.data)}else e.$message.error("服务器异常！")})})},clearAddForm:function(){this.addBiddingForm.adPosType="",this.addBiddingForm.adId="",this.addBiddingForm.startEcpm=0,this.addBiddingForm.endEcpm=0,this.addBiddingForm.priority=0},updateAdPosPre:function(e,t){this.editBiddingForm.id=t.biddingVo.id,this.editBiddingForm.adPosType=t.biddingVo.adPosType,this.editBiddingForm.adId=t.biddingVo.adId,this.editBiddingForm.startEcpm=t.biddingVo.startEcpm,this.editBiddingForm.endEcpm=t.biddingVo.endEcpm,this.editBiddingForm.priority=t.biddingVo.priority,this.editBiddingForm.biddingType=t.biddingVo.biddingType,this.editBiddingForm.playType=t.biddingVo.playType,this.editBiddingForm.adType=t.type,this.isShowEditBiddingForm=!0},getBrands:function(){var e=this,t=this;t.$axios.post("ad/brandList",{},{}).then(function(i){if(200===i.status){var n=i.data;0===n.ret?e.brands=n.data:t.$message.error("初始化机型失败")}else t.$message.error("服务器异常！")})},getAdTypeList:function(){var e=this;e.$axios.post("adType/list",{},{}).then(function(t){if(200===t.status){var i=t.data;if(0===i.ret)for(var n=i.data,o=0;o<n.length;o++)e.adTypeList.push({type:n[o].code,name:n[o].name});else e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},getAdPosList:function(){var e=this;e.$axios.post("adpos/listByApp",{},{}).then(function(t){if(200===t.status){var i=t.data;if(0===i.ret){console.info(o()(i.data));for(var n=i.data,r=[],a=[],s=0;s<n.length;s++)r.push({key:n[s].id,label:n[s].name}),a.push({code:n[s].id,name:n[s].name});e.adPosList=a,e.adPosOptions=r}else e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},getJsAdChannelSourceList:function(){var e=this;this.$http.post("config/getJsAdChannelSourceList").then(function(t){if(t.ok){var i=t.data;0===i.ret?e.jsAdChannelSource=i.data:e.$message.error("初始化JS广告渠道来源失败")}})},getPlanList:function(){var e=this;this.$axios.post("ad/listPlan",{},{params:this.queryAdPlanParams}).then(function(t){if(200===t.status){var i=t.data;0===i.ret?(e.planList=i.data.page.items,e.planList2=i.data.planList,e.adPlanListNum=i.data.page.count,e.innerPlanList=i.data.planList,e.industryList=i.data.industryVOList):e.$message.error("初始化数据失败")}else e.$message.error("服务器异常！")})},getResetAdForm:function(){return{timeBucketVOList:u,adPos:[],budget:{budget:null,enableBudget:!1,noIdleTime:!1,noPerMaxTimes:!1,idleTime:null,perMaxTimes:null,noExposureIdleTime:!1,exposureIdleTime:null,noExposureMaxTimes:!1,exposureMaxTimes:null},noEndDate:!1,endDate:"",type:1001,ext:{selfEndCard:0,activationTime:null,androidPosId:"",androidBakPosId:"",iosBakPosId:"",iosPosId:"",iosAppId:"",androidAppId:"",preType:0,downloadDrive:!1,extendType:0,template:!1,templateImgSize:0,mintegralType:0,style:101,showVideoCard:0,title:"",content:"",clkUrl:"",imgUrls:[""],impTrackUrl:"",clkTrackUrl:"",appPkgName:"",downloadUrl:"",adm:"",videoStartTrackUrl:[""],videoFinishTrackUrl:[""],videoPauseTrackUrl:[""],itid:"",width:0,height:0,endCardReward:0,duration:0,countDownGift:0,shareImgUrl:"",shareType:1,styleType:1,noAppId:!0,industry:"",sharePrice:0,detailPageShare:!1,newuserShare:!1,replaceShareDomain:"",word:"",pr:0,twiceJumpTimes:2,url:"",sourceFrom:1,fetchStrategy:1,hotWordAdUrl:"",fetchUrl:"",deeplinkUrl:"",deeplinkOpenType:0,deeplinkPkgName:"",deeplinkAppType:1,gdtUnitId:"",adTag:"",adTagRatio:"",showAdLabel:!1,adLabel:"",defaultAdTwiceJump:!1,jsDomainName:"",jsAdPosClose:{channelId:"",channelSource:"",jsStyle:101},jsAdPosOpen:{channelId:"",channelSource:"",jsStyle:201},jsAdPosRecommend:[{channelId:"",channelSource:"",jsStyle:301}]},id:null,name:"",open:!1,orientation:{noAndroid:!1,noIos:!1,noRegion:!1,maxAndroidVersion:null,maxIosVersion:null,minAndroidVersion:null,minIosVersion:null,regions:[],tailNumber:null,position:null,noIncomeLimit:!1,minIncome:null,maxIncome:null,noRegistTimeLimit:!1,minRegistTime:null,maxRegistTime:null,noRegistLongerLimit:!1,minRegistLonger:null,maxRegistLonger:null,containsPkg:null,notContainsPkg:null,userPkg:null,channelId:null,brandLimit:!1,dsp:"",lockActionPoint:0,brandLimitList:[]},plan:{id:"",name:"",open:""},startDate:"",state:1}},getAdList:function(e){var t=this;this.queryAdParams.pageNo=e,this.queryAdParams.adPosStr=o()(this.queryAdParams.adPos),this.$axios.post("ad/listAd",{},{params:this.queryAdParams}).then(function(e){if(200===e.status){for(var i=e.data,n=0;n<i.data.items.length;n++)null==i.data.items[n].timeBucketVOList&&(i.data.items[n].timeBucketVOList=u);0===i.ret?(t.adList=i.data.items,t.adListNum=i.data.count):t.$message.error("初始化数据失败")}else t.$message.error("服务器异常！")})},resetAddPlanForm:function(){this.addPlanForm.id="",this.addPlanForm.name="",this.addPlanForm.open=""},addPlan:function(){var e=this,t=this.addPlanForm,i=o()(t);this.$http.post("ad/addPlan",i,{}).then(function(t){if(t.ok){var i=t.data;e.$message({message:"新建广告成功",type:"success"}),e.planList.push(i.data),e.resetAddPlanForm()}else e.$message.error("新建广告失败")}),this.isShowAddPlanForm=!1},showEditPlanForm:function(e,t){this.editPlanForm=t,this.isShowEditPlanForm=!0},editPlan:function(e,t){var i=this,n={};n=e||0===e?t:this.editPlanForm;var r=o()(n);this.$http.post("ad/updatePlan",r,{}).then(function(e){e.ok?i.$message({message:"修改广告成功",type:"success"}):i.$message.error("修改广告失败")}),this.isShowEditPlanForm=!1},prev:function(){this.active--},next:function(){this.active++},closeAdForm:function(){this.active=0,this.editAdForm=this.getResetAdForm()},showAddAdForm:function(){console.log("hahaha"),this.isShowAdForm=!0,this.isAddAdForm=!0,this.addQrUrl=!1},showEditAdForm:function(e,t){null==t.ext.imgUrls&&(t.ext.imgUrls=[]),-1!=t.ext.shareImgUrl.indexOf("?")?this.addQrUrl=!0:this.addQrUrl=!1,this.isShowAdForm=!0,this.isAddAdForm=!1,this.editAdForm=t,this.editAdForm.noEndDate?this.putDate=this.editAdForm.startDate:this.putDate=[this.editAdForm.startDate,this.editAdForm.endDate],this.editAdForm.orientation.noRegistTimeLimit?this.registDate="":this.registDate=[this.editAdForm.orientation.minRegistTime,this.editAdForm.orientation.maxRegistTime]},handleEditAdClick:function(){this.isAddAdForm?this.addAd():this.editAd("edit")},addAd:function(){var e=this,t=this.editAdForm;if(void 0!==this.editAdForm.budget.ecpm&&null!==this.editAdForm.budget.ecpm&&""!==this.editAdForm.budget.ecpm&&!/(^[0-9]\d*$)/.test(this.editAdForm.budget.ecpm))return void this.$message.error("ECPM填写异常...");var i=o()(t);this.$http.post("ad/addAd",i,{}).then(function(t){if(t.ok){var i=t.data;0===i.ret?(e.$message({message:"添加广告成功",type:"success"}),e.adList.push(i.data),e.closeAdForm()):e.$message.error("添加广告失败, "+i.data)}else e.$message.error("添加广告失败")}),this.isShowAdForm=!1},editAd:function(e,t){var i=this,n={};if(n="edit"==e?this.editAdForm:t,void 0!==this.editAdForm.budget.ecpm&&null!==this.editAdForm.budget.ecpm&&""!==this.editAdForm.budget.ecpm&&!/(^[0-9]\d*$)/.test(this.editAdForm.budget.ecpm))return void this.$message.error("ECPM填写异常...");var r=o()(n);this.$confirm("将改广告配置，确定继续吗","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){i.$http.post("ad/updateAd",r,{}).then(function(e){if(e.ok){var t=e.data;i.$message({message:"修改广告成功 请刷新页面查看更新结果",type:"success"}),n.state=t.data.state}else i.$message.error("修改广告失败")})}).catch(function(){"edit"!==e&&(t.open=!n.open),i.$message({type:"info",message:"已取消"})}),this.isShowAdForm=!1},dataFormat:function(e){var t=new Date(e);return t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()},putDateFormat:function(){this.editAdForm.noEndDate?this.editAdForm.startDate=this.dataFormat(this.putDate):(this.editAdForm.startDate=this.dataFormat(this.putDate[0]),this.editAdForm.endDate=this.dataFormat(this.putDate[1]))},registTimeFormat:function(){this.editAdForm.orientation.noRegistTimeLimit?(this.editAdForm.orientation.minRegistTime="",this.editAdForm.orientation.maxRegistTime=""):(this.editAdForm.orientation.minRegistTime=this.dataFormat(this.registDate[0]),this.editAdForm.orientation.maxRegistTime=this.dataFormat(this.registDate[1]))},handleNoEndDateChange:function(e){this.editAdForm.noEndDate?this.putDate=this.editAdForm.startDate:this.putDate=[this.editAdForm.startDate,this.editAdForm.endDate]},handleNoIosChange:function(e){this.editAdForm.orientation.noIos&&(this.editAdForm.orientation.minIosVersion="",this.editAdForm.orientation.maxIosVersion="")},handleNoAndroidChange:function(e){this.editAdForm.orientation.noAndroid&&(this.editAdForm.orientation.minAndroidVersion="",this.editAdForm.orientation.maxAndroidVersion="")},handleCheckAllChange:function(e){this.editAdForm.orientation.regions=e?this.areaListId:[],this.isIndeterminate=!1},handleCheckedCitiesChange:function(e){var t=e.length;this.checkAll=t===this.areaList.length,this.isIndeterminate=t>0&&t<this.areaList.length},handleAvatarSuccess:function(e,t){var i=JSON.parse(e);this.editAdForm.ext.imgUrls=[],this.editAdForm.ext.imgUrls[0]=i.data},handleMiniShareImageSuccess:function(e,t){var i=JSON.parse(e);this.editAdForm.ext.miniShareImage=[],this.editAdForm.ext.miniShareImage=i.data},handleMiniIconSuccess:function(e,t){var i=JSON.parse(e);this.editAdForm.ext.miniIcon=[],this.editAdForm.ext.miniIcon=i.data},handleMiniShareImageSuccess1:function(e,t){var i=JSON.parse(e);this.editAdForm.ext.miniMiddleImage=[],this.editAdForm.ext.miniMiddleImage=i.data},handleShareImgSuccess:function(e,t){var i=JSON.parse(e);this.editAdForm.ext.shareImgUrl=i.data},handleSuccess1:function(e,t){var i=JSON.parse(e);this.imgList1="",this.editAdForm.ext.imgUrls[0]=i.data},handleSuccess2:function(e,t){var i=JSON.parse(e);this.imgList2="",this.editAdForm.ext.imgUrls[1]=i.data},handleSuccess3:function(e,t){var i=JSON.parse(e);this.imgList3="",this.editAdForm.ext.imgUrls[2]=i.data},handleUploadVideo:function(e,t){var i=JSON.parse(e);this.editAdForm.ext.adm=i.data},beforeAvatarUpload1:function(e){this.imgList1=URL.createObjectURL(e)},beforeMiniShareImageUpload:function(e){this.miniShareImage=URL.createObjectURL(e)},beforeMiniShareImageUpload1:function(e){this.miniMiddleImage=URL.createObjectURL(e)},beforeMiniIconUpload:function(e){this.miniIcon=URL.createObjectURL(e)},beforeAvatarUpload2:function(e){this.imgList2=URL.createObjectURL(e)},beforeAvatarUpload3:function(e){this.imgList3=URL.createObjectURL(e)},beforeAvatarUpload:function(e){},handleCurrentChange:function(e){this.getAdList(e)},handleCurrentChangeForPlan:function(e){this.queryAdPlanParams.pageNo=e,this.getPlanList()},downloadAdList:function(){var e=this.queryAdParams;delete e.pageNo,delete e.pageSize;var t="ad/export?adName="+e.adName+"&adPlan="+e.adPlan+"&adId="+e.adId+"&adType="+e.adType+"&startDate="+e.startDate+"&endDate="+e.endDate+"&status="+e.status;window.open(t)},handleRegistTimeLimitChange:function(e){this.editAdForm.orientation.noRegistTimeLimit?this.registDate="":this.registDate=[this.editAdForm.orientation.startDate,this.editAdForm.orientation.endDate]},handleIncomeLimitChange:function(e){this.editAdForm.orientation.noIncomeLimit&&(this.editAdForm.orientation.minIncome="",this.editAdForm.orientation.maxIncome="")},handleRegistLongerLimitChange:function(e){this.editAdForm.orientation.noRegistLongerLimit&&(this.editAdForm.orientation.minRegistLonger="",this.editAdForm.orientation.maxRegistLonger="")},handleImageUrl:function(e){e.target.checked?this.qrImageModal=!0:this.editAdForm.ext.shareImgUrl=this.editAdForm.ext.shareImgUrl.substr(0,this.editAdForm.ext.shareImgUrl.indexOf("?"))},openImageModal:function(){e(".share-img-qrcode").find("img").imgAreaSelect({handles:!0,aspectRatio:"1:1",onSelectEnd:function(t,i){var n=e(".share-img-qrcode").find("img").css("width").replace("px",""),o=e(".share-img-qrcode").find("img").css("height").replace("px",""),r=i.width,a=i.x1,s=i.y1;m="?pW="+n+"&pH="+o+"&qrSize="+r+"&qrX="+a+"&qrY="+s}})},closeImageModal:function(){e(".share-img-qrcode").find("img").imgAreaSelect({hide:!0})},hideImageModal:function(){this.qrImageModal=!1,this.editAdForm.ext.shareImgUrl=this.editAdForm.ext.shareImgUrl+m},queryTimeFormat:function(){this.queryTime&&null!=this.queryTime[0]?(this.queryAdParams.startDate=this.dataFormat(this.queryTime[0]),this.queryAdParams.endDate=this.dataFormat(this.queryTime[1])):(this.queryAdParams.startDate="",this.queryAdParams.endDate="")},removeRecommend:function(e){var t=this.editAdForm.ext.jsAdPosRecommend.indexOf(e);-1!==t&&this.editAdForm.ext.jsAdPosRecommend.splice(t,1)},addRecommend:function(){this.editAdForm.ext.jsAdPosRecommend.push({channelId:"",channelSource:"",jsStyle:101})},renderFunc:function(e,t){var i=this;return e("span",{attrs:{id:t.label},domProps:{innerHTML:t.label},on:{mouseover:function(){i.adPosMessage=t.label}}})}}}}.call(t,i(253))},331:function(e,t,i){"use strict";(function(e){!function(e){function t(){return e("<div/>")}var i=Math.abs,n=Math.max,o=Math.min,r=Math.round;e.imgAreaSelect=function(a,s){function l(e){return e+ge.left-ve.left}function d(e){return e+ge.top-ve.top}function c(e){return e-ge.left+ve.left}function u(e){return e-ge.top+ve.top}function m(e){return e.pageX-ve.left}function h(e){return e.pageY-ve.top}function p(e){var t=e||z,i=e||W;return{x1:r(ye.x1*t),y1:r(ye.y1*i),x2:r(ye.x2*t),y2:r(ye.y2*i),width:r(ye.x2*t)-r(ye.x1*t),height:r(ye.y2*i)-r(ye.y1*i)}}function f(e,t,i,n,o){var a=o||z,s=o||W;ye={x1:r(e/a||0),y1:r(t/s||0),x2:r(i/a||0),y2:r(n/s||0)},ye.width=ye.x2-ye.x1,ye.height=ye.y2-ye.y1}function g(){$&&ce.width()&&(ge={left:r(ce.offset().left),top:r(ce.offset().top)},M=ce.innerWidth(),R=ce.innerHeight(),ge.top+=ce.outerHeight()-R>>1,ge.left+=ce.outerWidth()-M>>1,X=r(s.minWidth/z)||0,J=r(s.minHeight/W)||0,G=r(o(s.maxWidth/z||1<<24,M)),Y=r(o(s.maxHeight/W||1<<24,R)),"1.3.2"!=e().jquery||"fixed"!=be||xe.getBoundingClientRect||(ge.top+=n(document.body.scrollTop,xe.scrollTop),ge.left+=n(document.body.scrollLeft,xe.scrollLeft)),ve=/absolute|relative/.test(O.css("position"))?{left:r(O.offset().left)-O.scrollLeft(),top:r(O.offset().top)-O.scrollTop()}:"fixed"==be?{left:e(document).scrollLeft(),top:e(document).scrollTop()}:{left:0,top:0},B=l(0),q=d(0),(ye.x2>M||ye.y2>R)&&k())}function v(t){if(Q){switch(ue.css({left:l(ye.x1),top:d(ye.y1)}).add(me).width(se=ye.width).height(le=ye.height),me.add(he).add(fe).css({left:0,top:0}),he.width(n(se-he.outerWidth()+he.innerWidth(),0)).height(n(le-he.outerHeight()+he.innerHeight(),0)),e(pe[0]).css({left:B,top:q,width:ye.x1,height:R}),e(pe[1]).css({left:B+ye.x1,top:q,width:se,height:ye.y1}),e(pe[2]).css({left:B+ye.x2,top:q,width:M-ye.x2,height:R}),e(pe[3]).css({left:B+ye.x1,top:q+ye.y2,width:se,height:R-ye.y2}),se-=fe.outerWidth(),le-=fe.outerHeight(),fe.length){case 8:e(fe[4]).css({left:se>>1}),e(fe[5]).css({left:se,top:le>>1}),e(fe[6]).css({left:se>>1,top:le}),e(fe[7]).css({top:le>>1});case 4:fe.slice(1,3).css({left:se}),fe.slice(2,4).css({top:le})}!1!==t&&(e.imgAreaSelect.onKeyPress!=Fe&&e(document).unbind(e.imgAreaSelect.keyPress,e.imgAreaSelect.onKeyPress),s.keys&&e(document)[e.imgAreaSelect.keyPress](e.imgAreaSelect.onKeyPress=Fe)),ke&&he.outerWidth()-he.innerWidth()==2&&(he.css("margin",0),setTimeout(function(){he.css("margin","auto")},0))}}function w(e){g(),v(e),Z=l(ye.x1),ee=d(ye.y1),te=l(ye.x2),ie=d(ye.y2)}function b(e,t){s.fadeSpeed?e.fadeOut(s.fadeSpeed,t):e.hide()}function y(e){var t=c(m(e))-ye.x1,i=u(h(e))-ye.y1;de||(g(),de=!0,ue.one("mouseout",function(){de=!1})),V="",s.resizable&&(i<=s.resizeMargin?V="n":i>=ye.height-s.resizeMargin&&(V="s"),t<=s.resizeMargin?V+="w":t>=ye.width-s.resizeMargin&&(V+="e")),ue.css("cursor",V?V+"-resize":s.movable?"move":""),j&&j.toggle()}function x(t){e("body").css("cursor",""),(s.autoHide||ye.width*ye.height==0)&&b(ue.add(pe),function(){e(this).hide()}),e(document).unbind("mousemove",T),ue.mousemove(y),s.onSelectEnd(a,p())}function A(t){return 1==t.which&&(g(),V?(e("body").css("cursor",V+"-resize"),Z=l(ye[/w/.test(V)?"x2":"x1"]),ee=d(ye[/n/.test(V)?"y2":"y1"]),e(document).mousemove(T).one("mouseup",x),ue.unbind("mousemove",y)):s.movable?(H=B+ye.x1-m(t),U=q+ye.y1-h(t),ue.unbind("mousemove",y),e(document).mousemove(C).one("mouseup",function(){s.onSelectEnd(a,p()),e(document).unbind("mousemove",C),ue.mousemove(y)})):ce.mousedown(t),!1)}function F(e){K&&(e?(te=n(B,o(B+M,Z+i(ie-ee)*K*(te>Z||-1))),ie=r(n(q,o(q+R,ee+i(te-Z)/K*(ie>ee||-1)))),te=r(te)):(ie=n(q,o(q+R,ee+i(te-Z)/K*(ie>ee||-1))),te=r(n(B,o(B+M,Z+i(ie-ee)*K*(te>Z||-1)))),ie=r(ie)))}function k(){Z=o(Z,B+M),ee=o(ee,q+R),i(te-Z)<X&&(te=Z-X*(te<Z||-1),te<B?Z=B+X:te>B+M&&(Z=B+M-X)),i(ie-ee)<J&&(ie=ee-J*(ie<ee||-1),ie<q?ee=q+J:ie>q+R&&(ee=q+R-J)),te=n(B,o(te,B+M)),ie=n(q,o(ie,q+R)),F(i(te-Z)<i(ie-ee)*K),i(te-Z)>G&&(te=Z-G*(te<Z||-1),F()),i(ie-ee)>Y&&(ie=ee-Y*(ie<ee||-1),F(!0)),ye={x1:c(o(Z,te)),x2:c(n(Z,te)),y1:u(o(ee,ie)),y2:u(n(ee,ie)),width:i(te-Z),height:i(ie-ee)},v(),s.onSelectChange(a,p())}function T(e){return te=/w|e|^$/.test(V)||K?m(e):l(ye.x2),ie=/n|s|^$/.test(V)||K?h(e):d(ye.y2),k(),!1}function _(t,i){te=(Z=t)+ye.width,ie=(ee=i)+ye.height,e.extend(ye,{x1:c(Z),y1:u(ee),x2:c(te),y2:u(ie)}),v(),s.onSelectChange(a,p())}function C(e){return Z=n(B,o(H+m(e),B+M-ye.width)),ee=n(q,o(U+h(e),q+R-ye.height)),_(Z,ee),e.preventDefault(),!1}function S(){e(document).unbind("mousemove",S),g(),te=Z,ie=ee,k(),V="",pe.is(":visible")||ue.add(pe).hide().fadeIn(s.fadeSpeed||0),Q=!0,e(document).unbind("mouseup",P).mousemove(T).one("mouseup",x),ue.unbind("mousemove",y),s.onSelectStart(a,p())}function P(){e(document).unbind("mousemove",S).unbind("mouseup",P),b(ue.add(pe)),f(c(Z),u(ee),c(Z),u(ee)),this instanceof e.imgAreaSelect||(s.onSelectChange(a,p()),s.onSelectEnd(a,p()))}function L(t){return 1==t.which&&!pe.is(":animated")&&(g(),H=Z=m(t),U=ee=h(t),e(document).mousemove(S).mouseup(P),!1)}function E(){w(!1)}function N(){$=!0,D(s=e.extend({classPrefix:"imgareaselect",movable:!0,parent:"body",resizable:!0,resizeMargin:10,onInit:function(){},onSelectStart:function(){},onSelectChange:function(){},onSelectEnd:function(){}},s)),ue.add(pe).css({visibility:""}),s.show&&(Q=!0,g(),v(),ue.add(pe).hide().fadeIn(s.fadeSpeed||0)),setTimeout(function(){s.onInit(a,p())},0)}function I(e,t){for(var i in t)void 0!==s[i]&&e.css(t[i],s[i])}function D(i){if(i.parent&&(O=e(i.parent)).append(ue.add(pe)),e.extend(s,i),g(),null!=i.handles){for(fe.remove(),fe=e([]),re=i.handles?"corners"==i.handles?4:8:0;re--;)fe=fe.add(t());fe.addClass(s.classPrefix+"-handle").css({position:"absolute",fontSize:0,zIndex:we+1||1}),!parseInt(fe.css("width"))>=0&&fe.width(5).height(5),(ae=s.borderWidth)&&fe.css({borderWidth:ae,borderStyle:"solid"}),I(fe,{borderColor1:"border-color",borderColor2:"background-color",borderOpacity:"opacity"})}for(z=s.imageWidth/M||1,W=s.imageHeight/R||1,null!=i.x1&&(f(i.x1,i.y1,i.x2,i.y2),i.show=!i.hide),i.keys&&(s.keys=e.extend({shift:1,ctrl:"resize"},i.keys)),pe.addClass(s.classPrefix+"-outer"),me.addClass(s.classPrefix+"-selection"),re=0;re++<4;)e(he[re-1]).addClass(s.classPrefix+"-border"+re);I(me,{selectionColor:"background-color",selectionOpacity:"opacity"}),I(he,{borderOpacity:"opacity",borderWidth:"border-width"}),I(pe,{outerColor:"background-color",outerOpacity:"opacity"}),(ae=s.borderColor1)&&e(he[0]).css({borderStyle:"solid",borderColor:ae}),(ae=s.borderColor2)&&e(he[1]).css({borderStyle:"dashed",borderColor:ae}),ue.append(me.add(he).add(j)).append(fe),ke&&((ae=(pe.css("filter")||"").match(/opacity=(\d+)/))&&pe.css("opacity",ae[1]/100),(ae=(he.css("filter")||"").match(/opacity=(\d+)/))&&he.css("opacity",ae[1]/100)),i.hide?b(ue.add(pe)):i.show&&$&&(Q=!0,ue.add(pe).fadeIn(s.fadeSpeed||0),w()),K=(oe=(s.aspectRatio||"").split(/:/))[0]/oe[1],ce.add(pe).unbind("mousedown",L),s.disable||!1===s.enable?(ue.unbind("mousemove",y).unbind("mousedown",A),e(window).unbind("resize",E)):((s.enable||!1===s.disable)&&((s.resizable||s.movable)&&ue.mousemove(y).mousedown(A),e(window).resize(E)),s.persistent||ce.add(pe).mousedown(L)),s.enable=s.disable=void 0}var $,j,B,q,M,R,O,H,U,z,W,V,X,J,G,Y,K,Q,Z,ee,te,ie,ne,oe,re,ae,se,le,de,ce=e(a),ue=t(),me=t(),he=t().add(t()).add(t()).add(t()),pe=t().add(t()).add(t()).add(t()),fe=e([]),ge={left:0,top:0},ve={left:0,top:0},we=0,be="absolute",ye={x1:0,y1:0,x2:0,y2:0,width:0,height:0},xe=document.documentElement,Ae=navigator.userAgent,Fe=function(e){var t,i,r=s.keys,a=e.keyCode;if(t=isNaN(r.alt)||!e.altKey&&!e.originalEvent.altKey?!isNaN(r.ctrl)&&e.ctrlKey?r.ctrl:!isNaN(r.shift)&&e.shiftKey?r.shift:isNaN(r.arrows)?10:r.arrows:r.alt,"resize"==r.arrows||"resize"==r.shift&&e.shiftKey||"resize"==r.ctrl&&e.ctrlKey||"resize"==r.alt&&(e.altKey||e.originalEvent.altKey)){switch(a){case 37:t=-t;case 39:i=n(Z,te),Z=o(Z,te),te=n(i+t,Z),F();break;case 38:t=-t;case 40:i=n(ee,ie),ee=o(ee,ie),ie=n(i+t,ee),F(!0);break;default:return}k()}else switch(Z=o(Z,te),ee=o(ee,ie),a){case 37:_(n(Z-t,B),ee);break;case 38:_(Z,n(ee-t,q));break;case 39:_(Z+o(t,M-c(te)),ee);break;case 40:_(Z,ee+o(t,R-u(ie)));break;default:return}return!1};this.remove=function(){D({disable:!0}),ue.add(pe).remove()},this.getOptions=function(){return s},this.setOptions=D,this.getSelection=p,this.setSelection=f,this.cancelSelection=P,this.update=w;var ke=(/msie ([\w.]+)/i.exec(Ae)||[])[1],Te=/opera/i.test(Ae),_e=/webkit/i.test(Ae)&&!/chrome/i.test(Ae);for(ne=ce;ne.length;)we=n(we,isNaN(ne.css("z-index"))?we:ne.css("z-index")),"fixed"==ne.css("position")&&(be="fixed"),ne=ne.parent(":not(body)");we=s.zIndex||we,ke&&ce.attr("unselectable","on"),e.imgAreaSelect.keyPress=ke||_e?"keydown":"keypress",Te&&(j=t().css({width:"100%",height:"100%",position:"absolute",zIndex:we+9999||9999})),ue.add(pe).css({visibility:"hidden",position:be,overflow:"hidden",zIndex:we||"9999"}),ue.css({zIndex:we+9999||9999}),me.add(he).css({position:"absolute",fontSize:0}),a.complete||"complete"==a.readyState||!ce.is("img")?N():ce.one("load",N),!$&&ke&&ke>=7&&(a.src=a.src)},e.fn.imgAreaSelect=function(t){return t=t||{},this.each(function(){e(this).data("imgAreaSelect")?t.remove?(e(this).data("imgAreaSelect").remove(),e(this).removeData("imgAreaSelect")):e(this).data("imgAreaSelect").setOptions(t):t.remove||(void 0===t.enable&&void 0===t.disable&&(t.enable=!0),e(this).data("imgAreaSelect",new e.imgAreaSelect(this,t)))}),t.instance?e(this).data("imgAreaSelect"):this}}(e)}).call(t,i(253))},333:function(e,t,i){t=e.exports=i(29)(),t.push([e.i,"/*\n * imgAreaSelect default style\n */\n\n.imgareaselect-border1 {\n\tbackground: url("+i(291)+") repeat-y left top;\n}\n\n.imgareaselect-border2 {\n    background: url("+i(290)+") repeat-x left top;\n}\n\n.imgareaselect-border3 {\n    background: url("+i(291)+") repeat-y right top;\n}\n\n.imgareaselect-border4 {\n    background: url("+i(290)+") repeat-x left bottom;\n}\n\n.imgareaselect-border1, .imgareaselect-border2,\n.imgareaselect-border3, .imgareaselect-border4 {\n    filter: alpha(opacity=50);\n\topacity: 0.5;\n}\n\n.imgareaselect-handle {\n    background-color: #fff;\n    border: solid 1px #000;\n    filter: alpha(opacity=50);\n    opacity: 0.5;\n}\n\n.imgareaselect-outer {\n    background-color: #000;\n    filter: alpha(opacity=50);\n    opacity: 0.5;\n}\n\n.imgareaselect-selection {\n}\n",""])},337:function(e,t,i){t=e.exports=i(29)(),t.push([e.i,"li{list-style:none}table{display:table;-webkit-user-select:none;-ms-user-select:none;user-select:none}td,th{display:table-cell;padding:0}.schedule{background:rgba(100,255,0,.5);width:0;height:0;position:fixed;top:0;left:0;pointer-events:none}.calendar{position:relative}.calendar-table{border-collapse:collapse}.calendar-table thead{display:table-header-group;vertical-align:middle;border-color:inherit}.calendar-table td,.calendar-table th,.calendar-table tr{border:1px solid #ddd;font-size:12px;text-align:center;min-width:12px;line-height:2.8em}.calendar-body,.calendar-head,.table{box-sizing:border-box}.calendar-body{display:table-row-group;vertical-align:middle;border-color:inherit}.calendar-time-color{background:#f5f5f5;border:1px solid #ddd;font-size:12px;text-align:center;min-width:12px;line-height:1.8em;transition:background .5s linear}.selectTime{background:rgba(255,100,0,.3);transition:background .5s linear}.calendar-weekTime{font-size:12px;line-height:2em;max-width:640px}.calendar-weekTime-cancel{float:right}.calendar-weekTime ul{display:block;padding-left:0;padding-right:90px}.calendar-weekTime ul li{display:block;text-align:left}.timeList{margin:0 5px}.del{color:#333}.del :hover{display:block;color:red}",""])},342:function(e,t,i){t=e.exports=i(29)(),t.push([e.i,".el-row{margin-bottom:20px}.el-input{width:350px}.adForm,.el-steps{position:relative;left:30%;margin-bottom:20px}.timePicker{display:inline-block}.areaCheckbox{width:500px;height:200px;overflow-y:scroll;border:1px solid #d5d5d5;margin-top:10px;padding:15px}.areaCheckbox .el-checkbox{width:23%;margin:0;margin-right:10px;height:20px;line-height:20px}.areaCheckbox .el-checkbox .el-checkbox__input{float:right}.areaCheckbox .el-checkbox .el-checkbox__label{float:left}.width-100{width:100px}.width-150{width:150px}.width-300{width:300px}.select-150 .el-input{width:150px}.inline-block{display:inline-block}.budgetSelect,.budgetSelect .el-input{width:100px}.text-center{text-align:center!important}.avatar-uploader .el-upload{border:1px solid #d9d9d9;border-radius:6px;cursor:pointer;position:relative;overflow:hidden}.big-pic .el-upload{width:640px;height:246px}.small-other-pic .el-upload{width:230px;height:152px}.avatar-uploader .el-upload:hover{border-color:#20a0ff}.font-color{color:#ff0}.avatar-uploader-icon{font-size:28px;color:#8c939d;width:100%;height:100%;line-height:178px;text-align:center}.avatar{width:100%;height:100%;display:block}.list-image .el-upload{width:170px;height:170px;margin-right:15px;float:left}.small-desc{height:152px;width:300px}.small-desc .el-textarea,textarea{height:100%}textarea{width:100%}.big-desc{width:640px}.big-desc .el-textarea{width:100%;height:100%}.combination-desc{width:550px}.combination-desc .el-textarea{margin-top:10px}.video-input .el-input{float:left}.upload-video{width:80px;float:left;margin-left:10px}.upload-video .el-upload--text{width:auto;height:auto}.share-img .el-upload--text{width:180px;height:200px}.share-img-qrcode{text-align:center}.el-input.is-disabled /deep/ .el-input__inner{color:#8a2be2}.el-table__body{user-select:auto}",""])},351:function(e,t,i){var n=i(333);"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);i(88)("519eb41a",n,!0)},358:function(e,t,i){i(402);var n=i(87)(i(298),i(376),null,null);e.exports=n.exports},376:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"calendar"},[i("div",{ref:"schedule",staticClass:"schedule"}),e._v(" "),i("table",{staticClass:"calendar-table"},[i("thead",{staticClass:"calendar-head"},[e._m(0),e._v(" "),i("tr",e._l(e.thead,function(t){return i("td",{attrs:{colspan:"2"}},[e._v(e._s(t))])}),0)]),e._v(" "),i("tbody",{staticClass:"calendar-body"},e._l(e.timeBucket1,function(t){return i("tr",[i("td",{attrs:{colspan:"2",onselectstart:"return false",ondragstart:"return false"}},[e._v(e._s(t.name))]),e._v(" "),e._l(t.time,function(n){return i("td",{staticClass:"calendar-time-color",class:{selectTime:n.show},attrs:{"data-week":t.week,"data-time":n.time},on:{mouseup:e.mouseUp,mousemove:e.mouseMove,click:e.addColor,mousedown:e.mouseDown}})})],2)}),0)])]),e._v(" "),i("div",{staticClass:"calendar-weekTime"},[i("span",{staticClass:"calendar-weekTime-cancel",on:{click:e.reseting}},[e._v("撤销所有操怍")]),e._v(" "),i("span",{on:{click:e.kan}},[e._v("查看元素")]),e._v(" "),i("ul",e._l(e.weekList,function(t){return i("li",{directives:[{name:"show",rawName:"v-show",value:t.Time.length>0,expression:"item.Time.length>0"}]},[i("span",[e._v(e._s(t.name)+":")]),e._v(" "),e._l(t.Time,function(t){return i("span",{staticClass:"timeList"},[i("span",{attrs:{"data-show":t.show}},[i("time",[e._v(e._s(t.start))]),e._v("~"),i("time",[e._v(e._s(t.end))]),e._v(" "),i("span",{directives:[{name:"show",rawName:"v-show",value:1==t.show,expression:"time.show==1"}],staticClass:"del"},[e._v("×")])])])})],2)}),0)])])},staticRenderFns:[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("tr",[i("th",{staticClass:"calendar-head-title",attrs:{rowspan:"2",colspan:"2"}},[e._v("星期/时间")]),e._v(" "),i("th",{attrs:{colspan:"24"}},[e._v("上午")]),e._v(" "),i("th",{attrs:{colspan:"24"}},[e._v("下午")])])}]}},385:function(e,t){e.exports={render:function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"table"},[i("div",{staticClass:"crumbs"},[i("el-breadcrumb",{attrs:{separator:"/"}},[i("el-breadcrumb-item",[i("i",{staticClass:"el-icon-menu"}),e._v(" 广告管理")]),e._v(" "),i("el-breadcrumb-item",[e._v("投放管理")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"提示",visible:e.qrImageModal,top:"5%",id:"image-qrcode"},on:{"update:visible":function(t){e.qrImageModal=t},open:e.openImageModal,close:e.closeImageModal}},[i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("div",{staticClass:"share-img-qrcode"},[i("img",{attrs:{src:e.editAdForm.ext.shareImgUrl,alt:"分享图片"}})]),e._v(" "),i("br"),e._v(" "),i("el-button",{on:{click:function(t){e.qrImageModal=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:e.hideImageModal}},[e._v("确 定")])],1)]),e._v(" "),i("el-dialog",{attrs:{title:"新建广告计划",visible:e.isShowAddPlanForm},on:{"update:visible":function(t){e.isShowAddPlanForm=t}}},[i("el-form",{attrs:{model:e.addPlanForm,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"计划名称"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.addPlanForm.name,callback:function(t){e.$set(e.addPlanForm,"name",t)},expression:"addPlanForm.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"客户名称"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.addPlanForm.clientName,callback:function(t){e.$set(e.addPlanForm,"clientName",t)},expression:"addPlanForm.clientName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"产品名称"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.addPlanForm.productName,callback:function(t){e.$set(e.addPlanForm,"productName",t)},expression:"addPlanForm.productName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"销售人员"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.addPlanForm.salesman,callback:function(t){e.$set(e.addPlanForm,"salesman",t)},expression:"addPlanForm.salesman"}})],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowAddPlanForm=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addPlan()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"修改广告计划",visible:e.isShowEditPlanForm},on:{"update:visible":function(t){e.isShowEditPlanForm=t}}},[i("el-form",{attrs:{model:e.editPlanForm,"label-width":"120px"}},[i("el-form-item",{attrs:{label:"计划名称"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editPlanForm.name,callback:function(t){e.$set(e.editPlanForm,"name",t)},expression:"editPlanForm.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"客户名称"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editPlanForm.clientName,callback:function(t){e.$set(e.editPlanForm,"clientName",t)},expression:"editPlanForm.clientName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"产品名称"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editPlanForm.productName,callback:function(t){e.$set(e.editPlanForm,"productName",t)},expression:"editPlanForm.productName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"销售人员"}},[i("el-input",{attrs:{"auto-complete":"off"},model:{value:e.editPlanForm.salesman,callback:function(t){e.$set(e.editPlanForm,"salesman",t)},expression:"editPlanForm.salesman"}})],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowEditPlanForm=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.editPlan()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"编辑Bidding广告位",visible:e.isShowEditBiddingForm},on:{"update:visible":function(t){e.isShowEditBiddingForm=t}}},[i("el-form",{ref:"editBiddingForm",attrs:{model:e.editBiddingForm,"label-width":"200px"}},[i("el-form-item",{attrs:{label:"广告位类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型"},model:{value:e.editBiddingForm.adPosType,callback:function(t){e.$set(e.editBiddingForm,"adPosType",t)},expression:"editBiddingForm.adPosType"}},[i("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adPosTypes,function(e){return i("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),i("el-form-item",{attrs:{label:"Start ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.editBiddingForm.startEcpm,callback:function(t){e.$set(e.editBiddingForm,"startEcpm",t)},expression:"editBiddingForm.startEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"End ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.editBiddingForm.endEcpm,callback:function(t){e.$set(e.editBiddingForm,"endEcpm",t)},expression:"editBiddingForm.endEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"优先级"}},[i("el-input",{staticClass:"width-100",model:{value:e.editBiddingForm.priority,callback:function(t){e.$set(e.editBiddingForm,"priority",t)},expression:"editBiddingForm.priority"}})],1),e._v(" "),e.editBiddingForm.adType.toString().startsWith("1008")?i("div",[i("el-form-item",{attrs:{label:"请求方式"}},[i("el-radio-group",{model:{value:e.editBiddingForm.biddingType,callback:function(t){e.$set(e.editBiddingForm,"biddingType",t)},expression:"editBiddingForm.biddingType"}},[i("el-radio",{attrs:{label:1}},[e._v("C2S")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("S2S")])],1)],1)],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"是否参与打底竞价"}},[i("el-radio-group",{model:{value:e.editBiddingForm.playType,callback:function(t){e.$set(e.editBiddingForm,"playType",t)},expression:"editBiddingForm.playType"}},[i("el-radio",{attrs:{label:1}},[e._v("参与")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("不参与")])],1)],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowEditBiddingForm=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.saveAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{title:"新建Bidding广告位",visible:e.isShowAddBiddingForm},on:{"update:visible":function(t){e.isShowAddBiddingForm=t}}},[i("el-form",{ref:"addBiddingForm",attrs:{model:e.addBiddingForm,"label-width":"200px"}},[i("el-form-item",{attrs:{label:"广告位类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"配置类型"},model:{value:e.addBiddingForm.adPosType,callback:function(t){e.$set(e.addBiddingForm,"adPosType",t)},expression:"addBiddingForm.adPosType"}},[i("el-option",{attrs:{value:""}},[e._v("请选择")]),e._v(" "),e._l(e.adPosTypes,function(e){return i("el-option",{attrs:{label:e.label,value:e.value}})})],2)],1),e._v(" "),i("el-form-item",{attrs:{label:"Start ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.addBiddingForm.startEcpm,callback:function(t){e.$set(e.addBiddingForm,"startEcpm",t)},expression:"addBiddingForm.startEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"End ECPM"}},[i("el-input",{staticClass:"width-100",model:{value:e.addBiddingForm.endEcpm,callback:function(t){e.$set(e.addBiddingForm,"endEcpm",t)},expression:"addBiddingForm.endEcpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"优先级"}},[i("el-input",{staticClass:"width-100",model:{value:e.addBiddingForm.priority,callback:function(t){e.$set(e.addBiddingForm,"priority",t)},expression:"addBiddingForm.priority"}})],1),e._v(" "),e.addBiddingForm.adType.toString().startsWith("1008")?i("div",[i("el-form-item",{attrs:{label:"请求方式"}},[i("el-radio-group",{model:{value:e.addBiddingForm.biddingType,callback:function(t){e.$set(e.addBiddingForm,"biddingType",t)},expression:"addBiddingForm.biddingType"}},[i("el-radio",{attrs:{label:1}},[e._v("C2S")]),e._v(" "),i("el-radio",{attrs:{label:2}},[e._v("S2S")])],1)],1)],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"是否参与打底竞价"}},[i("el-radio-group",{model:{value:e.addBiddingForm.playType,callback:function(t){e.$set(e.addBiddingForm,"playType",t)},expression:"addBiddingForm.playType"}},[i("el-radio",{attrs:{label:1}},[e._v("参与")]),e._v(" "),i("el-radio",{attrs:{label:0}},[e._v("不参与")])],1)],1)],1),e._v(" "),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowAddBiddingForm=!1}}},[e._v("取 消")]),e._v(" "),i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.addAdPos()}}},[e._v("确 定")])],1)],1),e._v(" "),i("el-dialog",{attrs:{visible:e.isShowAdForm,fullscreen:""},on:{"update:visible":function(t){e.isShowAdForm=t},close:e.closeAdForm}},[i("div",{attrs:{slot:"title"},slot:"title"},[e.isAddAdForm?i("span",[e._v("新建广告")]):i("span",[e._v("修改广告")])]),e._v(" "),i("el-steps",{attrs:{space:200,active:e.active,"finish-status":"success"}},[i("el-step",{attrs:{title:"基本信息"}}),e._v(" "),i("el-step",{attrs:{title:"定向设置"}}),e._v(" "),i("el-step",{attrs:{title:"预算设置"}}),e._v(" "),i("el-step",{attrs:{title:"创意设置"}})],1),e._v(" "),0===e.active?i("el-form",{staticClass:"adForm",attrs:{model:e.editAdForm,"label-width":"100px","label-position":e.labelPosition}},[i("el-form-item",{attrs:{label:"所属计划"}},[e.isAddAdForm?i("el-select",{attrs:{filterable:"",placeholder:"请选择所属计划"},model:{value:e.editAdForm.plan.id,callback:function(t){e.$set(e.editAdForm.plan,"id",t)},expression:"editAdForm.plan.id"}},e._l(e.innerPlanList,function(e){return i("el-option",{attrs:{label:e.name,value:e.id}})}),1):i("el-select",{attrs:{placeholder:"请选择所属计划",disabled:""},model:{value:e.editAdForm.plan.id,callback:function(t){e.$set(e.editAdForm.plan,"id",t)},expression:"editAdForm.plan.id"}},[i("el-option",{attrs:{label:e.editAdForm.plan.name,value:e.editAdForm.plan.id}})],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告名称"}},[i("el-input",{attrs:{placeholder:"广告名称（最多50字）"},model:{value:e.editAdForm.name,callback:function(t){e.$set(e.editAdForm,"name",t)},expression:"editAdForm.name"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"投放位置"}},[i("el-transfer",{staticStyle:{"text-align":"left",display:"inline-block","line-height":"initial"},attrs:{data:e.adPosOptions,"render-content":e.renderFunc,filterable:"","filter-placeholder":"请输入广告位名称",titles:["可选广告位","已选广告位"],"button-texts":["移除","添加"]},model:{value:e.editAdForm.adPos,callback:function(t){e.$set(e.editAdForm,"adPos",t)},expression:"editAdForm.adPos"}}),e._v(" "),i("el-divider",{staticClass:"width-300",attrs:{"content-position":"left"}},[e._v("广告位详细信息")]),e._v(" "),i("el-input",{attrs:{disabled:!0},model:{value:e.adPosMessage,callback:function(t){e.adPosMessage=t},expression:"adPosMessage"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"投放日期"}},[e.editAdForm.noEndDate?i("div",{staticClass:"inline-block"},[i("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},on:{change:e.putDateFormat},model:{value:e.putDate,callback:function(t){e.putDate=t},expression:"putDate"}})],1):e._e(),e._v(" "),e.editAdForm.noEndDate?e._e():i("div",{staticClass:"timePicker"},[i("el-date-picker",{attrs:{type:"daterange",placeholder:"选择日期"},on:{change:e.putDateFormat},model:{value:e.putDate,callback:function(t){e.putDate=t},expression:"putDate"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleNoEndDateChange},model:{value:e.editAdForm.noEndDate,callback:function(t){e.$set(e.editAdForm,"noEndDate",t)},expression:"editAdForm.noEndDate"}},[e._v("长期投放，不设置结束日期\n                        ")])]],2),e._v(" "),i("el-form-item",{attrs:{label:"时间段选择"}},[i("time-frame",{attrs:{"time-bucket1":e.editAdForm.timeBucketVOList}})],1)],1):e._e(),e._v(" "),1===e.active?i("el-form",{staticClass:"adForm",attrs:{model:e.editAdForm,"label-width":"100px","label-position":e.labelPosition}},[i("el-form-item",{attrs:{label:"地域"}},[i("el-radio-group",{attrs:{size:"small"},model:{value:e.editAdForm.orientation.noRegion,callback:function(t){e.$set(e.editAdForm.orientation,"noRegion",t)},expression:"editAdForm.orientation.noRegion"}},[i("el-radio-button",{attrs:{label:!1}},[e._v("不限")]),e._v(" "),i("el-radio-button",{attrs:{label:!0}},[e._v("省市")]),e._v(" "),i("el-radio-button",[e._v("区县")])],1),e._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:e.editAdForm.orientation.noRegion,expression:"editAdForm.orientation.noRegion"}]},[i("div",{staticClass:"areaCheckbox"},[[i("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选\n                                ")]),e._v(" "),i("el-checkbox-group",{on:{change:e.handleCheckedCitiesChange},model:{value:e.editAdForm.orientation.regions,callback:function(t){e.$set(e.editAdForm.orientation,"regions",t)},expression:"editAdForm.orientation.regions"}},e._l(e.areaList,function(t){return i("el-checkbox",{attrs:{label:t.id}},[e._v(e._s(t.name))])}),1)]],2)])],1),e._v(" "),i("el-form-item",{attrs:{label:"受限区"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:e.editAdForm.orientation.filterRegion,callback:function(t){e.$set(e.editAdForm.orientation,"filterRegion",t)},expression:"editAdForm.orientation.filterRegion"}},e._l(e.regionList,function(e){return i("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"锁区"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:e.editAdForm.orientation.lockArea,callback:function(t){e.$set(e.editAdForm.orientation,"lockArea",t)},expression:"editAdForm.orientation.lockArea"}},e._l(e.lockAreaList,function(e){return i("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1),e._v(" "),1===e.editAdForm.orientation.lockArea?i("el-form-item",{attrs:{label:"锁区作用点"}},[i("el-select",{staticClass:"width-100",attrs:{placeholder:"请选择锁区作用点"},model:{value:e.editAdForm.orientation.lockActionPoint,callback:function(t){e.$set(e.editAdForm.orientation,"lockActionPoint",t)},expression:"editAdForm.orientation.lockActionPoint"}},e._l(e.lockActionPointList,function(e){return i("el-option",{attrs:{label:e.val,value:e.key}})}),1)],1):e._e(),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"平台版本"}},[i("span",[e._v("IOS:大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noIos},model:{value:e.editAdForm.orientation.minIosVersion,callback:function(t){e.$set(e.editAdForm.orientation,"minIosVersion",t)},expression:"editAdForm.orientation.minIosVersion"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noIos},model:{value:e.editAdForm.orientation.maxIosVersion,callback:function(t){e.$set(e.editAdForm.orientation,"maxIosVersion",t)},expression:"editAdForm.orientation.maxIosVersion"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleNoIosChange},model:{value:e.editAdForm.orientation.noIos,callback:function(t){e.$set(e.editAdForm.orientation,"noIos",t)},expression:"editAdForm.orientation.noIos"}},[e._v("不投放IOS平台\n                    ")])],e._v(" "),i("br"),e._v(" "),i("el-form-item",{staticClass:" inline-block"},[i("span",[e._v("Android:大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noAndroid},model:{value:e.editAdForm.orientation.minAndroidVersion,callback:function(t){e.$set(e.editAdForm.orientation,"minAndroidVersion",t)},expression:"editAdForm.orientation.minAndroidVersion"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noAndroid},model:{value:e.editAdForm.orientation.maxAndroidVersion,callback:function(t){e.$set(e.editAdForm.orientation,"maxAndroidVersion",t)},expression:"editAdForm.orientation.maxAndroidVersion"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleNoAndroidChange},model:{value:e.editAdForm.orientation.noAndroid,callback:function(t){e.$set(e.editAdForm.orientation,"noAndroid",t)},expression:"editAdForm.orientation.noAndroid"}},[e._v("\n                        不投放Android平台\n                    ")])],e._v(" "),i("el-form-item",{attrs:{label:"注册时间定向"}},[i("div",{staticClass:"timePicker"},[i("el-date-picker",{attrs:{type:"daterange",placeholder:"选择日期",disabled:e.editAdForm.orientation.noRegistTimeLimit},on:{change:e.registTimeFormat},model:{value:e.registDate,callback:function(t){e.registDate=t},expression:"registDate"}})],1),e._v(" "),[i("el-checkbox",{on:{change:e.handleRegistTimeLimitChange},model:{value:e.editAdForm.orientation.noRegistTimeLimit,callback:function(t){e.$set(e.editAdForm.orientation,"noRegistTimeLimit",t)},expression:"editAdForm.orientation.noRegistTimeLimit"}},[e._v("\n                            不开启注册时间定向\n                        ")])]],2),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"注册时长定向(单位：天)"}},[i("span",[e._v("大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noRegistLongerLimit},model:{value:e.editAdForm.orientation.minRegistLonger,callback:function(t){e.$set(e.editAdForm.orientation,"minRegistLonger",t)},expression:"editAdForm.orientation.minRegistLonger"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noRegistLongerLimit},model:{value:e.editAdForm.orientation.maxRegistLonger,callback:function(t){e.$set(e.editAdForm.orientation,"maxRegistLonger",t)},expression:"editAdForm.orientation.maxRegistLonger"}}),e._v(" "),[i("el-checkbox",{on:{change:e.handleRegistLongerLimitChange},model:{value:e.editAdForm.orientation.noRegistLongerLimit,callback:function(t){e.$set(e.editAdForm.orientation,"noRegistLongerLimit",t)},expression:"editAdForm.orientation.noRegistLongerLimit"}},[e._v("\n                            不开启注册时长定向\n                        ")])]],2),e._v(" "),i("br"),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"总收入定向"}},[i("span",[e._v("大于等于")]),e._v(" "),i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noIncomeLimit},model:{value:e.editAdForm.orientation.minIncome,callback:function(t){e.$set(e.editAdForm.orientation,"minIncome",t)},expression:"editAdForm.orientation.minIncome"}})],1),e._v(" "),i("el-form-item",{staticClass:"inline-block",attrs:{label:"小于等于"}},[i("el-input",{staticClass:"width-100",attrs:{disabled:e.editAdForm.orientation.noIncomeLimit},model:{value:e.editAdForm.orientation.maxIncome,callback:function(t){e.$set(e.editAdForm.orientation,"maxIncome",t)},expression:"editAdForm.orientation.maxIncome"}}),e._v(" "),[i("el-checkbox",{on:{change:e.handleIncomeLimitChange},model:{value:e.editAdForm.orientation.noIncomeLimit,callback:function(t){e.$set(e.editAdForm.orientation,"noIncomeLimit",t)},expression:"editAdForm.orientation.noIncomeLimit"}},[e._v("\n                            不开启总收入定向\n                        ")])]],2),e._v(" "),i("el-form-item",{attrs:{label:"机型定向"}},[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},model:{value:e.editAdForm.orientation.brandLimit,callback:function(t){e.$set(e.editAdForm.orientation,"brandLimit",t)},expression:"editAdForm.orientation.brandLimit"}})],1),e._v(" "),e.editAdForm.orientation.brandLimit?i("el-form-item",{attrs:{label:"选择机型"}},[i("el-transfer",{attrs:{data:e.brands,titles:["可选机型","已选机型"],"button-texts":["移除","添加"]},model:{value:e.editAdForm.orientation.brandLimitList,callback:function(t){e.$set(e.editAdForm.orientation,"brandLimitList",t)},expression:"editAdForm.orientation.brandLimitList"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"尾号定向"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.tailNumber,callback:function(t){e.$set(e.editAdForm.orientation,"tailNumber",t)},expression:"editAdForm.orientation.tailNumber"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"包含包名定向"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.containsPkg,callback:function(t){e.$set(e.editAdForm.orientation,"containsPkg",t)},expression:"editAdForm.orientation.containsPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"不包含包名定向"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.notContainsPkg,callback:function(t){e.$set(e.editAdForm.orientation,"notContainsPkg",t)},expression:"editAdForm.orientation.notContainsPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"用户APP包名"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.userPkg,callback:function(t){e.$set(e.editAdForm.orientation,"userPkg",t)},expression:"editAdForm.orientation.userPkg"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"渠道名"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.channelId,callback:function(t){e.$set(e.editAdForm.orientation,"channelId",t)},expression:"editAdForm.orientation.channelId"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"投放渠道定向"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.dsp,callback:function(t){e.$set(e.editAdForm.orientation,"dsp",t)},expression:"editAdForm.orientation.dsp"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"策略定向"}},[i("el-input",{staticClass:"width-100",model:{value:e.editAdForm.orientation.abPoint,callback:function(t){e.$set(e.editAdForm.orientation,"abPoint",t)},expression:"editAdForm.orientation.abPoint"}})],1)],2):e._e(),e._v(" "),2===e.active?i("el-form",{staticClass:"adForm",attrs:{model:e.editAdForm,"label-width":"100px","label-position":e.labelPosition}},[i("el-form-item",{attrs:{label:"预算"}},[i("el-input",{attrs:{placeholder:"请输入内容"},model:{value:e.editAdForm.budget.budget,callback:function(t){e.$set(e.editAdForm.budget,"budget",t)},expression:"editAdForm.budget.budget"}},[i("el-select",{staticClass:"budgetSelect",attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:e.editAdForm.budget.enableBudget,callback:function(t){e.$set(e.editAdForm.budget,"enableBudget",t)},expression:"editAdForm.budget.enableBudget"}},[i("el-option",{attrs:{label:"日预算",value:!0}}),e._v(" "),i("el-option",{attrs:{label:"不限",value:!1}})],1)],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"Ecpm分层价格"}},[i("el-input",{attrs:{placeholder:"请输入内容"},model:{value:e.editAdForm.budget.ecpm,callback:function(t){e.$set(e.editAdForm.budget,"ecpm",t)},expression:"editAdForm.budget.ecpm"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"点击间隔"}},[i("el-input",{attrs:{disabled:e.editAdForm.budget.noIdleTime},model:{value:e.editAdForm.budget.idleTime,callback:function(t){e.$set(e.editAdForm.budget,"idleTime",t)},expression:"editAdForm.budget.idleTime"}}),e._v("\n                    分钟\n                    "),[i("el-checkbox",{model:{value:e.editAdForm.budget.noIdleTime,callback:function(t){e.$set(e.editAdForm.budget,"noIdleTime",t)},expression:"editAdForm.budget.noIdleTime"}},[e._v("不限制间隔")])]],2),e._v(" "),i("el-form-item",{attrs:{label:"单人最高点击次数"}},[i("el-input",{attrs:{disabled:e.editAdForm.budget.noPerMaxTimes},model:{value:e.editAdForm.budget.perMaxTimes,callback:function(t){e.$set(e.editAdForm.budget,"perMaxTimes",t)},expression:"editAdForm.budget.perMaxTimes"}}),e._v(" "),[i("el-checkbox",{model:{value:e.editAdForm.budget.noPerMaxTimes,callback:function(t){e.$set(e.editAdForm.budget,"noPerMaxTimes",t)},expression:"editAdForm.budget.noPerMaxTimes"}},[e._v("不限制次数")])]],2),e._v(" "),i("el-form-item",{attrs:{label:"曝光间隔"}},[i("el-input",{attrs:{disabled:e.editAdForm.budget.noExposureIdleTime},model:{value:e.editAdForm.budget.exposureIdleTime,callback:function(t){e.$set(e.editAdForm.budget,"exposureIdleTime",t)},expression:"editAdForm.budget.exposureIdleTime"}}),e._v("\n                    分钟\n                    "),[i("el-checkbox",{model:{value:e.editAdForm.budget.noExposureIdleTime,callback:function(t){e.$set(e.editAdForm.budget,"noExposureIdleTime",t)},expression:"editAdForm.budget.noExposureIdleTime"}},[e._v("不限制间隔")])]],2),e._v(" "),i("el-form-item",{attrs:{label:"单人最高曝光次数"}},[i("el-input",{attrs:{disabled:e.editAdForm.budget.noExposureMaxTimes},model:{value:e.editAdForm.budget.exposureMaxTimes,callback:function(t){e.$set(e.editAdForm.budget,"exposureMaxTimes",t)},expression:"editAdForm.budget.exposureMaxTimes"}}),e._v(" "),[i("el-checkbox",{model:{value:e.editAdForm.budget.noExposureMaxTimes,callback:function(t){e.$set(e.editAdForm.budget,"noExposureMaxTimes",t)},expression:"editAdForm.budget.noExposureMaxTimes"}},[e._v("不限制次数")])]],2)],1):e._e(),e._v(" "),3===e.active?i("el-form",{staticClass:"adForm",attrs:{model:e.editAdForm,"label-width":"100px","label-position":e.labelPosition}},[i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{attrs:{placeholder:"请选择广告类型",filterable:"",disabled:!e.isAddAdForm},model:{value:e.editAdForm.type,callback:function(t){e.$set(e.editAdForm,"type",t)},expression:"editAdForm.type"}},e._l(e.adTypeList,function(e){return i("el-option",{attrs:{label:e.name,value:e.type}})}),1)],1),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"IOS广告位(1)","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.iosPosId,callback:function(t){e.$set(e.editAdForm.ext,"iosPosId",t)},expression:"editAdForm.ext.iosPosId"}})],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"IOS广告位(2)","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.iosBakPosId,callback:function(t){e.$set(e.editAdForm.ext,"iosBakPosId",t)},expression:"editAdForm.ext.iosBakPosId"}})],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"IOS-APPID","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.iosAppId,callback:function(t){e.$set(e.editAdForm.ext,"iosAppId",t)},expression:"editAdForm.ext.iosAppId"}})],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"Android广告位(1)","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.androidPosId,callback:function(t){e.$set(e.editAdForm.ext,"androidPosId",t)},expression:"editAdForm.ext.androidPosId"}})],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"Android广告位(2)","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.androidBakPosId,callback:function(t){e.$set(e.editAdForm.ext,"androidBakPosId",t)},expression:"editAdForm.ext.androidBakPosId"}})],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"Android-APPID","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.androidAppId,callback:function(t){e.$set(e.editAdForm.ext,"androidAppId",t)},expression:"editAdForm.ext.androidAppId"}})],1):e._e(),e._v(" "),"19"===e.editAdForm.type?i("el-form-item",{attrs:{label:"定投域名","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.jsDomainName,callback:function(t){e.$set(e.editAdForm.ext,"jsDomainName",t)},expression:"editAdForm.ext.jsDomainName"}})],1):e._e(),e._v(" "),"19"===e.editAdForm.type?i("el-form-item",{attrs:{label:"广告位配置:","label-width":"125px"}}):e._e(),e._v(" "),"19"===e.editAdForm.type?i("el-form-item",{attrs:{"label-width":"10px"}},[e._v("\n                    查看全文\n                    "),i("el-input",{attrs:{width:"200px"},model:{value:e.editAdForm.ext.jsAdPosClose.channelId,callback:function(t){e.$set(e.editAdForm.ext.jsAdPosClose,"channelId",t)},expression:"editAdForm.ext.jsAdPosClose.channelId"}}),e._v("\n                    渠道来源\n                    "),i("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:e.editAdForm.ext.jsAdPosClose.channelSource,callback:function(t){e.$set(e.editAdForm.ext.jsAdPosClose,"channelSource",t)},expression:"editAdForm.ext.jsAdPosClose.channelSource"}},e._l(e.jsAdChannelSource,function(e){return i("el-option",{key:e,attrs:{label:e,value:e}})}),1),e._v("\n                    样式\n                    "),i("el-radio-group",{attrs:{size:"small"},model:{value:e.editAdForm.ext.jsAdPosClose.jsStyle,callback:function(t){e.$set(e.editAdForm.ext.jsAdPosClose,"jsStyle",t)},expression:"editAdForm.ext.jsAdPosClose.jsStyle"}},[i("el-radio-button",{attrs:{label:"101"}},[e._v("小图")]),e._v(" "),i("el-radio-button",{attrs:{label:"201"}},[e._v("大图")]),e._v(" "),i("el-radio-button",{attrs:{label:"301"}},[e._v("组图")])],1)],1):e._e(),e._v(" "),"19"===e.editAdForm.type?i("el-form-item",{attrs:{"label-width":"10px"}},[e._v("\n                    资讯末尾\n                    "),i("el-input",{model:{value:e.editAdForm.ext.jsAdPosOpen.channelId,callback:function(t){e.$set(e.editAdForm.ext.jsAdPosOpen,"channelId",t)},expression:"editAdForm.ext.jsAdPosOpen.channelId"}}),e._v("\n                    渠道来源\n                    "),i("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:e.editAdForm.ext.jsAdPosOpen.channelSource,callback:function(t){e.$set(e.editAdForm.ext.jsAdPosOpen,"channelSource",t)},expression:"editAdForm.ext.jsAdPosOpen.channelSource"}},e._l(e.jsAdChannelSource,function(e){return i("el-option",{key:e,attrs:{label:e,value:e}})}),1),e._v("\n                    样式\n                    "),i("el-radio-group",{attrs:{size:"small"},model:{value:e.editAdForm.ext.jsAdPosOpen.jsStyle,callback:function(t){e.$set(e.editAdForm.ext.jsAdPosOpen,"jsStyle",t)},expression:"editAdForm.ext.jsAdPosOpen.jsStyle"}},[i("el-radio-button",{attrs:{label:"101"}},[e._v("小图")]),e._v(" "),i("el-radio-button",{attrs:{label:"201"}},[e._v("大图")]),e._v(" "),i("el-radio-button",{attrs:{label:"301"}},[e._v("组图")])],1)],1):e._e(),e._v(" "),e._l(e.editAdForm.ext.jsAdPosRecommend,function(t,n){return"19"===e.editAdForm.type?i("el-form-item",{key:t.key,attrs:{label:"相关推荐"+(n+1)}},[i("el-input",{model:{value:t.channelId,callback:function(i){e.$set(t,"channelId",i)},expression:"recommend.channelId"}}),e._v("\n                    渠道来源\n                    "),i("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:t.channelSource,callback:function(i){e.$set(t,"channelSource",i)},expression:"recommend.channelSource"}},e._l(e.jsAdChannelSource,function(e){return i("el-option",{key:e,attrs:{label:e,value:e}})}),1),e._v("\n                    样式\n                    "),i("el-radio-group",{attrs:{size:"small"},model:{value:t.jsStyle,callback:function(i){e.$set(t,"jsStyle",i)},expression:"recommend.jsStyle"}},[i("el-radio-button",{attrs:{label:"101"}},[e._v("小图")]),e._v(" "),i("el-radio-button",{attrs:{label:"201"}},[e._v("大图")]),e._v(" "),i("el-radio-button",{attrs:{label:"301"}},[e._v("组图")])],1),e._v(" "),i("el-button",{attrs:{size:"small"},on:{click:function(i){return i.preventDefault(),e.removeRecommend(t)}}},[e._v("删除")])],1):e._e()}),e._v(" "),"19"===e.editAdForm.type?i("el-form-item",[i("el-button",{on:{click:e.addRecommend}},[e._v("新增推荐位置")])],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"模版大小图","label-width":"150px"}},[i("el-radio-group",{model:{value:e.editAdForm.ext.templateImgSize,callback:function(t){e.$set(e.editAdForm.ext,"templateImgSize",t)},expression:"editAdForm.ext.templateImgSize"}},[i("el-radio",{staticClass:"radio",attrs:{label:0}},[e._v("大图")]),e._v(" "),i("el-radio",{staticClass:"ratio",attrs:{label:1}},[e._v("小图")]),e._v(" "),i("el-radio",{staticClass:"ratio",attrs:{label:10}},[e._v("组图")])],1)],1):e._e(),e._v(" "),"1009"!==e.editAdForm.type?i("el-form-item",{attrs:{label:"下载广告激活时间","label-width":"125px"}},[i("el-input",{model:{value:e.editAdForm.ext.activationTime,callback:function(t){e.$set(e.editAdForm.ext,"activationTime",t)},expression:"editAdForm.ext.activationTime"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"广告样式"}},[i("el-radio-group",{attrs:{size:"small"},model:{value:e.editAdForm.ext.style,callback:function(t){e.$set(e.editAdForm.ext,"style",t)},expression:"editAdForm.ext.style"}},[i("el-radio-button",{attrs:{label:"101"}},[e._v("小图")]),e._v(" "),i("el-radio-button",{attrs:{label:"201"}},[e._v("大图")]),e._v(" "),i("el-radio-button",{attrs:{label:"301"}},[e._v("组图")]),e._v(" "),i("el-radio-button",{attrs:{label:"401"}},[e._v("视频")]),e._v(" "),i("el-radio-button",{attrs:{label:"501"}},[e._v("Banner")])],1)],1),e._v(" "),"1022"==e.editAdForm.type?i("el-form-item",{attrs:{label:"使用自家endCard","label-width":"125px"}},[i("el-radio-group",{attrs:{size:"small"},model:{value:e.editAdForm.ext.selfEndCard,callback:function(t){e.$set(e.editAdForm.ext,"selfEndCard",t)},expression:"editAdForm.ext.selfEndCard"}},[i("el-radio-button",{attrs:{label:"0"}},[e._v("否")]),e._v(" "),i("el-radio-button",{attrs:{label:"1"}},[e._v("是")])],1)],1):e._e(),e._v(" "),"1022"==e.editAdForm.type?i("el-form-item",{attrs:{label:"endCard引导概率（百分制）","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.endCardReward,callback:function(t){e.$set(e.editAdForm.ext,"endCardReward",t)},expression:"editAdForm.ext.endCardReward"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"标题","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.title,callback:function(t){e.$set(e.editAdForm.ext,"title",t)},expression:"editAdForm.ext.title"}})],1),e._v(" "),"101"==e.editAdForm.ext.style||"501"==e.editAdForm.ext.style?i("el-form-item",[i("el-upload",{staticClass:"avatar-uploader small-other-pic inline-block",staticStyle:{float:"left"},attrs:{action:"ad/upload","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload}},[""!=e.editAdForm.ext.imgUrls?i("img",{staticClass:"avatar",attrs:{src:e.editAdForm.ext.imgUrls[0]}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e._v(" "),i("div",{staticClass:"small-desc inline-block"},[i("el-input",{attrs:{type:"textarea",rows:8,placeholder:"请输入内容"},model:{value:e.editAdForm.ext.content,callback:function(t){e.$set(e.editAdForm.ext,"content",t)},expression:"editAdForm.ext.content"}})],1)],1):e._e(),e._v(" "),"201"==e.editAdForm.ext.style?i("el-form-item",[i("el-upload",{staticClass:"avatar-uploader big-pic",attrs:{action:"ad/upload","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload}},[""!=e.editAdForm.ext.imgUrls?i("img",{staticClass:"avatar",attrs:{src:e.editAdForm.ext.imgUrls[0]}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e._v(" "),i("div",{staticClass:"big-desc"},[i("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入内容"},model:{value:e.editAdForm.ext.content,callback:function(t){e.$set(e.editAdForm.ext,"content",t)},expression:"editAdForm.ext.content"}})],1)],1):e._e(),e._v(" "),"301"==e.editAdForm.ext.style?i("el-form-item",[i("el-upload",{staticClass:"avatar-uploader list-image",attrs:{action:"ad/upload","show-file-list":!1,"before-upload":e.beforeAvatarUpload1,"on-success":e.handleSuccess1}},[""!=e.imgList1?i("img",{staticClass:"avatar",attrs:{src:e.imgList1}}):""!=e.editAdForm.ext.imgUrls[0]?i("img",{staticClass:"avatar",attrs:{src:e.editAdForm.ext.imgUrls[0]}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e._v(" "),i("el-upload",{staticClass:"avatar-uploader list-image",attrs:{action:"ad/upload","show-file-list":!1,"before-upload":e.beforeAvatarUpload2,"on-success":e.handleSuccess2}},[""!=e.imgList2?i("img",{staticClass:"avatar",attrs:{src:e.imgList2}}):""!=e.editAdForm.ext.imgUrls[1]?i("img",{staticClass:"avatar",attrs:{src:e.editAdForm.ext.imgUrls[1]}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e._v(" "),i("el-upload",{staticClass:"avatar-uploader list-image",attrs:{action:"ad/upload","show-file-list":!1,"before-upload":e.beforeAvatarUpload3,"on-success":e.handleSuccess3}},[""!=e.imgList3?i("img",{staticClass:"avatar",attrs:{src:e.imgList3}}):""!=e.editAdForm.ext.imgUrls[2]?i("img",{staticClass:"avatar",attrs:{src:e.editAdForm.ext.imgUrls[2]}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e._v(" "),i("div",{staticClass:"combination-desc"},[i("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入内容"},model:{value:e.editAdForm.ext.content,callback:function(t){e.$set(e.editAdForm.ext,"content",t)},expression:"editAdForm.ext.content"}})],1)],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{staticClass:"video-input",attrs:{label:"视频地址","label-width":"150px"}},[i("el-upload",{staticClass:"avatar-uploader video-pic",attrs:{action:"ad/upload","show-file-list":!1,"on-success":e.handleAvatarSuccess,"before-upload":e.beforeAvatarUpload}},[""!=e.editAdForm.ext.imgUrls?i("img",{staticClass:"avatar",attrs:{src:e.editAdForm.ext.imgUrls[0]}}):i("i",{staticClass:"el-icon-plus avatar-uploader-icon"})]),e._v(" "),i("el-input",{model:{value:e.editAdForm.ext.adm,callback:function(t){e.$set(e.editAdForm.ext,"adm",t)},expression:"editAdForm.ext.adm"}}),e._v(" "),i("el-upload",{staticClass:"upload-video",attrs:{action:"ad/upload","show-file-list":!1,"on-success":e.handleUploadVideo}},[i("el-button",{attrs:{type:"primary"}},[e._v("上传视频")])],1)],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"视频开始播放上报地址","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.videoStartTrackUrl,callback:function(t){e.$set(e.editAdForm.ext,"videoStartTrackUrl",t)},expression:"editAdForm.ext.videoStartTrackUrl"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"视频结束播放上报地址","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.videoFinishTrackUrl,callback:function(t){e.$set(e.editAdForm.ext,"videoFinishTrackUrl",t)},expression:"editAdForm.ext.videoFinishTrackUrl"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"视频暂停播放上报地址","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.videoPauseTrackUrl,callback:function(t){e.$set(e.editAdForm.ext,"videoPauseTrackUrl",t)},expression:"editAdForm.ext.videoPauseTrackUrl"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"iTunes ID","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.itid,callback:function(t){e.$set(e.editAdForm.ext,"itid",t)},expression:"editAdForm.ext.itid"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"视频宽度","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.width,callback:function(t){e.$set(e.editAdForm.ext,"width",t)},expression:"editAdForm.ext.width"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"视频高度","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.height,callback:function(t){e.$set(e.editAdForm.ext,"height",t)},expression:"editAdForm.ext.height"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"视频时长","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.duration,callback:function(t){e.$set(e.editAdForm.ext,"duration",t)},expression:"editAdForm.ext.duration"}})],1):e._e(),e._v(" "),"401"==e.editAdForm.ext.style?i("el-form-item",{attrs:{label:"下载按钮点亮倒计时时间","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.countDownGift,callback:function(t){e.$set(e.editAdForm.ext,"countDownGift",t)},expression:"editAdForm.ext.countDownGift"}})],1):e._e(),e._v(" "),"1013"==e.editAdForm.type?i("el-form-item",{attrs:{label:"落地页地址","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.clkUrl,callback:function(t){e.$set(e.editAdForm.ext,"clkUrl",t)},expression:"editAdForm.ext.clkUrl"}})],1):e._e(),e._v(" "),"1013"==e.editAdForm.type?i("el-form-item",{attrs:{label:"第三方曝光回调地址","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.impTrackUrl,callback:function(t){e.$set(e.editAdForm.ext,"impTrackUrl",t)},expression:"editAdForm.ext.impTrackUrl"}})],1):e._e(),e._v(" "),"1013"==e.editAdForm.type?i("el-form-item",{attrs:{label:"第三方点击回调地址","label-width":"150px"}},[i("el-input",{model:{value:e.editAdForm.ext.clkTrackUrl,callback:function(t){e.$set(e.editAdForm.ext,"clkTrackUrl",t)},expression:"editAdForm.ext.clkTrackUrl"}})],1):e._e(),e._v(" "),i("el-form-item",{attrs:{label:"图片宽度","label-width":"150px"}},[i("el-input",{attrs:{placeholder:"单位：像素"},model:{value:e.editAdForm.ext.width,callback:function(t){e.$set(e.editAdForm.ext,"width",t)},expression:"editAdForm.ext.width"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"图片高度","label-width":"150px"}},[i("el-input",{attrs:{placeholder:"单位：像素"},model:{value:e.editAdForm.ext.height,callback:function(t){e.$set(e.editAdForm.ext,"height",t)},expression:"editAdForm.ext.height"}})],1)],2):e._e(),e._v(" "),i("div",{staticClass:"dialog-footer text-center",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.isShowAdForm=!1}}},[e._v("取 消")]),e._v(" "),0!=e.active?i("el-button",{attrs:{type:"primary"},on:{click:e.prev}},[e._v("上一步")]):e._e(),e._v(" "),3!=e.active?i("el-button",{attrs:{type:"primary"},on:{click:e.next}},[e._v("下一步")]):e._e(),e._v(" "),3==e.active?i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleEditAdClick()}}},[e._v("确 定")]):e._e()],1)],1),e._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-tabs",{attrs:{type:"border-card"},model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[i("el-tab-pane",{attrs:{name:"first"}},[i("span",{attrs:{slot:"label"},slot:"label"},[i("i",{staticClass:"el-icon-document"}),e._v(" 广告计划")]),e._v(" "),i("el-row",{attrs:{span:24}},[i("el-form",{attrs:{inline:"",model:e.queryAdPlanParams,size:"small"}},[i("el-form-item",{attrs:{label:"广告计划名称"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"请输入广告计划名称"},model:{value:e.queryAdPlanParams.name,callback:function(t){e.$set(e.queryAdPlanParams,"name",t)},expression:"queryAdPlanParams.name"}})],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:e.getPlanList}})],1),e._v(" "),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){e.isShowAddPlanForm=!0}}},[e._v("新建广告计划")])],1)],1)],1),e._v(" "),i("el-divider",{attrs:{"content-position":"left"}},[e._v("广告计划列表")]),e._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.planList}},[i("el-table-column",{attrs:{label:"开关",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(i){return e.editPlan(t.$index,t.row)}},model:{value:t.row.open,callback:function(i){e.$set(t.row,"open",i)},expression:"scope.row.open"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"id",label:"广告计划ID",width:"150"}}),e._v(" "),i("el-table-column",{attrs:{prop:"name",label:"广告计划名称"}}),e._v(" "),i("el-table-column",{attrs:{prop:"clientName",label:"客户名称"}}),e._v(" "),i("el-table-column",{attrs:{prop:"productName",label:"产品名称"}}),e._v(" "),i("el-table-column",{attrs:{prop:"salesman",label:"销售人员"}}),e._v(" "),i("el-table-column",{attrs:{label:"操作",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"small"},on:{click:function(i){return e.showEditPlanForm(t.$index,t.row)}}},[e._v("编辑\n                                            ")])]}}])})],1)],1)],1),e._v(" "),i("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryAdPlanParams.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.adPlanListNum,background:""},on:{"current-change":e.handleCurrentChangeForPlan}})],1),e._v(" "),i("el-tab-pane",{attrs:{name:"second"}},[i("span",{attrs:{slot:"label"},slot:"label"},[i("i",{staticClass:"el-icon-position"}),e._v(" 广告")]),e._v(" "),i("el-row",{attrs:{span:24}},[i("el-form",{attrs:{inline:"",model:e.queryAdParams,size:"small"}},[i("el-form-item",{attrs:{label:"广告名称"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"请输入广告名称"},model:{value:e.queryAdParams.adName,callback:function(t){e.$set(e.queryAdParams,"adName",t)},expression:"queryAdParams.adName"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"广告计划"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"广告计划",filterable:""},model:{value:e.queryAdParams.adPlan,callback:function(t){e.$set(e.queryAdParams,"adPlan",t)},expression:"queryAdParams.adPlan"}},e._l(e.planList2,function(e){return i("el-option",{attrs:{label:e.name,value:e.id}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告ID"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"请输入广告ID"},model:{value:e.queryAdParams.adId,callback:function(t){e.$set(e.queryAdParams,"adId",t)},expression:"queryAdParams.adId"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"广告类型"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"广告类型",filterable:""},model:{value:e.queryAdParams.adType,callback:function(t){e.$set(e.queryAdParams,"adType",t)},expression:"queryAdParams.adType"}},e._l(e.adTypeList,function(e){return i("el-option",{attrs:{label:e.name,value:e.type}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告状态"}},[i("el-select",{staticClass:"select-150",attrs:{placeholder:"请选择广告状态"},model:{value:e.queryAdParams.status,callback:function(t){e.$set(e.queryAdParams,"status",t)},expression:"queryAdParams.status"}},[i("el-option",{attrs:{label:"不限制",value:"0"}}),e._v(" "),i("el-option",{attrs:{label:"有效广告",value:"1"}}),e._v(" "),i("el-option",{attrs:{label:"无效广告",value:"2"}})],1)],1),e._v(" "),i("el-form-item",{attrs:{label:"广告位"}},[i("el-select",{staticClass:"select-150",attrs:{multiple:"",placeholder:"请选择"},model:{value:e.queryAdParams.adPos,callback:function(t){e.$set(e.queryAdParams,"adPos",t)},expression:"queryAdParams.adPos"}},e._l(e.adPosList,function(e){return i("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})}),1)],1),e._v(" "),i("el-form-item",{attrs:{label:"三方appId"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"模糊匹配"},model:{value:e.queryAdParams.appIdThird,callback:function(t){e.$set(e.queryAdParams,"appIdThird",t)},expression:"queryAdParams.appIdThird"}})],1),e._v(" "),i("el-form-item",{attrs:{label:"三方posId"}},[i("el-input",{staticClass:"width-150",attrs:{placeholder:"模糊匹配"},model:{value:e.queryAdParams.posIdThird,callback:function(t){e.$set(e.queryAdParams,"posIdThird",t)},expression:"queryAdParams.posIdThird"}})],1),e._v(" "),i("el-form-item",[i("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-search",circle:""},on:{click:function(t){return e.getAdList(1)}}})],1),e._v(" "),i("el-form-item",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary",icon:"plus"},on:{click:e.showAddAdForm}},[e._v("新建广告")]),e._v(" "),i("el-button",{attrs:{type:"primary",icon:"document"},on:{click:e.downloadAdList}},[e._v("导出")])],1)],1)],1),e._v(" "),i("el-divider",{attrs:{"content-position":"left"}},[e._v("广告列表")]),e._v(" "),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-table",{staticStyle:{width:"100%"},attrs:{data:e.adList}},[i("el-table-column",{attrs:{label:"开关","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:function(i){return e.editAd(t.$index,t.row)}},model:{value:t.row.open,callback:function(i){e.$set(t.row,"open",i)},expression:"scope.row.open"}})]}}])}),e._v(" "),i("el-table-column",{attrs:{prop:"id",label:"广告ID","min-width":"130"}}),e._v(" "),i("el-table-column",{attrs:{prop:"name",label:"广告名称","min-width":"150"}}),e._v(" "),i("el-table-column",{attrs:{prop:"plan.name",label:"广告计划","min-width":"140"}}),e._v(" "),i("el-table-column",{attrs:{label:"状态","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.state?i("el-tag",{attrs:{type:"success"}},[e._v("投放中")]):e._e(),e._v(" "),2==t.row.state?i("el-tag",{attrs:{type:"warning"}},[e._v("已暂停")]):e._e(),e._v(" "),3==t.row.state?i("el-tag",{attrs:{type:"danger"}},[e._v("已结束")]):e._e()]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"广告类型","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(e.adTypeList,function(n){return n.type==t.row.type?i("span",[e._v(e._s(n.name))]):e._e()})}}])}),e._v(" "),i("el-table-column",{attrs:{label:"更新时间","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.lastUpdateTime))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"价格策略","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.priceType?i("el-tag",{attrs:{type:"success",effect:"dark"}},[e._v("目标竞价")]):e._e(),e._v(" "),1===t.row.priceType?i("el-tag",{attrs:{type:"",effect:"dark"}},[e._v("实时竞价")]):e._e()]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"APPID","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[""===t.row.ext.androidAppId?i("span",[e._v(" "+e._s(t.row.ext.iosAppId))]):i("span",[e._v(e._s(t.row.ext.androidAppId))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"POSID","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[""===t.row.ext.androidAppId?i("span",[e._v(" "+e._s(t.row.ext.iosPosId))]):i("span",[e._v(e._s(t.row.ext.androidPosId))])]}}])}),e._v(" "),i("el-table-column",{attrs:{label:"操作","min-width":"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{attrs:{size:"small"},on:{click:function(i){return e.showEditAdForm(t.$index,t.row)}}},[e._v("编辑")]),e._v(" "),0===t.row.priceType?i("el-button",{attrs:{size:"small"},on:{click:function(i){return e.addAdPosPre(t.$index,t.row)}}},[e._v("设为实时竞价")]):e._e(),e._v(" "),1===t.row.priceType?i("el-button",{attrs:{size:"small"},on:{click:function(i){return e.updateAdPosPre(t.$index,t.row)}}},[e._v("编辑Bidding")]):e._e()]}}])})],1)],1)],1),e._v(" "),i("el-pagination",{staticStyle:{float:"right"},attrs:{"current-page":e.queryAdParams.pageNo,"page-size":10,layout:"total, prev, pager, next, jumper",total:e.adListNum,background:""},on:{"current-change":e.handleCurrentChange}})],1)],1)],1)],1)],1)},staticRenderFns:[]}},402:function(e,t,i){var n=i(337);"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);i(88)("5b02a2e4",n,!0)},407:function(e,t,i){var n=i(342);"string"==typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);i(88)("1fa0bb88",n,!0)}});