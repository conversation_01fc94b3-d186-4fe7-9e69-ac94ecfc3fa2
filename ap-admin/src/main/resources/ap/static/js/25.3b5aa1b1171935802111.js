webpackJsonp([25],{224:function(t,e,a){var d=a(87)(a(321),a(382),null,null);t.exports=d.exports},321:function(t,e,a){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={data:function(){return{isShowAddByAdId:!1,isShowCancelByAdId:!1,isShowAddByProductOs:!1,isShowAddByProductOsBD:!1,isShowCancelByProductOs:!1,callbackConfigList:[],appList:[],addByProductOs:{product:"",os:"",types:""},addByAdId:{product:"",adId:"",types:""}}},created:function(){this.getAppList(),this.getConfigList()},methods:{getAppList:function(){var t=this;t.$axios.post("app/shortNameList",{},{}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?t.appList=a.data:t.$message.error(a.data)}else t.$message.error("服务器异常！")})},getConfigList:function(){var t=this;t.$axios.post("callback/list",{},{}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?t.callbackConfigList=a.data:t.$message.error(a.data)}else t.$message.error("服务器异常！")})},addByProductAndOsBtn:function(){var t=this;t.$axios.post("callback/byProduct",{},{params:t.addByProductOs}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?(t.$message.success("已提交任务，稍后查看."),t.isShowAddByProductOs=!1):t.$message.error(a.data)}else t.$message.error("服务器异常！")})},addByProductAndOsBtnBD:function(){var t=this;t.$axios.post("callback/BDbyProduct",{},{params:t.addByProductOs}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?(t.$message.success("已提交任务，稍后查看."),t.isShowAddByProductOsBD=!1):t.$message.error(a.data)}else t.$message.error("服务器异常！")})},addByAdIdBtn:function(){var t=this;t.$axios.post("callback/byAdId",{},{params:t.addByAdId}).then(function(e){if(200===e.status){var a=e.data;0===a.ret?(t.$message.success("处理成功"),t.isShowAddByAdId=!1):t.$message.error(a.data)}else t.$message.error("服务器异常！")})}}}},382:function(t,e){t.exports={render:function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"table"},[a("div",{staticClass:"crumbs"},[a("el-breadcrumb",{attrs:{separator:"/"}},[a("el-breadcrumb-item",[a("i",{staticClass:"el-icon-menu"}),t._v(" 管理员")]),t._v(" "),a("el-breadcrumb-item",[t._v("三方回调管理")])],1),t._v(" "),a("el-dialog",{attrs:{title:"按广告位设置回调链接",visible:t.isShowAddByAdId},on:{"update:visible":function(e){t.isShowAddByAdId=e}}},[a("el-form",{ref:"addByAdId",attrs:{model:t.addByAdId,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"所属应用"}},[a("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:t.addByAdId.product,callback:function(e){t.$set(t.addByAdId,"product",e)},expression:"addByAdId.product"}},t._l(t.appList,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"adId"}},[a("el-input",{model:{value:t.addByAdId.adId,callback:function(e){t.$set(t.addByAdId,"adId",e)},expression:"addByAdId.adId"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addByAdId.types,callback:function(e){t.$set(t.addByAdId,"types",e)},expression:"addByAdId.types"}},[a("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),a("el-option",{attrs:{value:"1"}},[t._v("添加回调")]),t._v(" "),a("el-option",{attrs:{value:"2"}},[t._v("取消回调")])],1)],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.isShowAddByAdId=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addByAdIdBtn()}}},[t._v("马上设置")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"按产品批量设置回调链接",visible:t.isShowAddByProductOs},on:{"update:visible":function(e){t.isShowAddByProductOs=e}}},[a("el-form",{ref:"addByProductOs",attrs:{model:t.addByProductOs,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"所属应用"}},[a("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:t.addByProductOs.product,callback:function(e){t.$set(t.addByProductOs,"product",e)},expression:"addByProductOs.product"}},t._l(t.appList,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"OS"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addByProductOs.os,callback:function(e){t.$set(t.addByProductOs,"os",e)},expression:"addByProductOs.os"}},[a("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),a("el-option",{attrs:{value:"1"}},[t._v("Android")]),t._v(" "),a("el-option",{attrs:{value:"2"}},[t._v("IOS")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addByProductOs.types,callback:function(e){t.$set(t.addByProductOs,"types",e)},expression:"addByProductOs.types"}},[a("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),a("el-option",{attrs:{value:"1"}},[t._v("添加回调")]),t._v(" "),a("el-option",{attrs:{value:"2"}},[t._v("取消回调")])],1)],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.isShowAddByProductOs=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addByProductAndOsBtn()}}},[t._v("一键设置")])],1)],1),t._v(" "),a("el-dialog",{attrs:{title:"百度按产品批量设置回调链接",visible:t.isShowAddByProductOsBD},on:{"update:visible":function(e){t.isShowAddByProductOsBD=e}}},[a("el-form",{ref:"addByProductOs",attrs:{model:t.addByProductOs,"label-width":"200px"}},[a("el-form-item",{attrs:{label:"所属应用"}},[a("el-select",{attrs:{placeholder:"请选择",filterable:""},model:{value:t.addByProductOs.product,callback:function(e){t.$set(t.addByProductOs,"product",e)},expression:"addByProductOs.product"}},t._l(t.appList,function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"OS"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addByProductOs.os,callback:function(e){t.$set(t.addByProductOs,"os",e)},expression:"addByProductOs.os"}},[a("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),a("el-option",{attrs:{value:"1"}},[t._v("Android")]),t._v(" "),a("el-option",{attrs:{value:"2"}},[t._v("IOS")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addByProductOs.types,callback:function(e){t.$set(t.addByProductOs,"types",e)},expression:"addByProductOs.types"}},[a("el-option",{attrs:{value:""}},[t._v("请选择")]),t._v(" "),a("el-option",{attrs:{value:"1"}},[t._v("添加回调")]),t._v(" "),a("el-option",{attrs:{value:"2"}},[t._v("取消回调")])],1)],1)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.isShowAddByProductOsBD=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.addByProductAndOsBtnBD()}}},[t._v("一键设置")])],1)],1),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{attrs:{size:"small",inline:""}},[a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){t.isShowAddByAdId=!0}}},[t._v("按广告位")]),t._v(" "),a("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){t.isShowAddByProductOs=!0}}},[t._v("按产品")]),t._v(" "),a("el-button",{attrs:{type:"primary",round:""},on:{click:function(e){t.isShowAddByProductOsBD=!0}}},[t._v("按产品(百度)")])],1)],1)],1)],1),t._v(" "),a("el-divider",{attrs:{"content-position":"left"}},[t._v("已配置回调产品")]),t._v(" "),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.callbackConfigList,stripe:"","highlight-current-row":""}},[a("el-table-column",{attrs:{prop:"product",label:"内部产品Id"}}),t._v(" "),a("el-table-column",{attrs:{prop:"productName",label:"产品名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"count",label:"已设置个数"}})],1)],1)],1)],1)])},staticRenderFns:[]}}});